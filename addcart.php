<?php
	require_once 'init.php';
	require_once dirname(__FILE__).'/visiteur/login.php';
	require_once dirname(__FILE__).'/class/produit_class.php';
	require_once dirname(__FILE__).'/class/categorie_class.php';
	
	// Si on a toutes les variables nécessaires
	if(isset($_GET['action']) && ($_GET['action']=="add") && isset($_GET['id']) && ctype_digit($_GET['id']) && isset($_GET['nb']) && ctype_digit($_GET['nb']) && isset($_SESSION['url_ref']))
	{
		// On récupère le produit par son ID
		$p = produitDB::getProduitById($_GET['id']);
		
		// Si le produit n'est pas sur devis
		if ($p->getPrixHT() != -1)
		{

			// On ajoute le produit dans le panier
			if(isset($_GET['r'])){
				$user->getPanier()->ajouterProduitPro($p, $_GET['nb'], $_GET['r']);
			} else {
				$user->getPanier()->ajouterProduit($p, $_GET['nb']);	
			}
$user->getPanier()->recalculerPro($user->getEmail());
			// On enregistre la session
			$_SESSION['user1'] = serialize($user);
			
			// On enregistre dans une session la désignation du produit et la quantité
			if(isset($_GET['remise'])){
				$_SESSION['designprodadd'] = addslashes($p->getDesignation())." - ".$_GET['remise']."%";
			} else {
				$_SESSION['designprodadd'] = addslashes($p->getDesignation());	
			}
			$_SESSION['qteprodadd'] = $_GET['nb'];
			$_SESSION['prodsdevis'] = 0;
		}
		// Si le produit est sur devis
		else
		{
			// On enregistre dans une session la désignation du produit et la quantité
			$_SESSION['refprodadd'] = addslashes($p->getReference());
			$_SESSION['designprodadd'] = str_replace("\"","'",addslashes($p->getDesignation()));
			$_SESSION['qteprodadd'] = $_GET['nb'];
			$_SESSION['prodsdevis'] = 1;
		}
		
		
		/*$ref64 = urldecode(str_replace(".","%",$_GET['urlref']));
		$ref = base64_decode($ref64);*/
		// On redirige sur la page où on était
		header("Location: http://jeep-dodge-gmc.com".$_SESSION['url_ref']);
	}
	// Sinon on redirige sur l'index
	else
		header("Location: index.php");
		//header("Location : ".$_SESSION['url_ref']);
?>
