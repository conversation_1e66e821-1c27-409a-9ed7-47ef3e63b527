<?php
	require_once dirname(__FILE__).'/../../class/annonce_class.php';
	// affichage du detail d'une annonce
	if(isset($_GET['id'])){
		$a = annonceDB::getAnnonceById($_GET['id']);
		if($a->getStatus() == 1 || $user->isAdmin || $a->getClient()->getEmail() == $user->getEmail()){
			// Affichage du detain de l'annonce
			?>
			<div id="annonceDetail">
				<h2><?php echo $translate->_('Annonce n°'); echo $a->getId(); $translate->_('du'); echo $a->getDate(); echo(':'); echo $translate->_($a->getOperation()),' '; echo $a->getSujet(); ?></h2>
				<table>
					<tr>
						<td rowspan="6" align="right"><a href="<?php echo $basePath; ?>public/images/annonces/<?php echo $a->getImage(); ?>" class="light" title="cliquer pour zoomer"><img src="<?php echo $basePath; ?>public/images/annonces/<?php echo $a->getImage(); ?>" height="200px;" /></a></td>
					</tr>
					<tr>
						<td><?php echo $translate->_('Genre de materiel');?> :</td>
						<td><?php echo $a->getGenre(); ?></td>
					</tr>
					<tr>
						<td><?php echo $translate->_('Type de materiel');?> :</td>
						<td><?php echo $a->getType(); ?></td>
					</tr>
					<tr>
						<td><?php echo $translate->_('Prix');?> :</td>
						<td><?php echo $a->getPrix(); ?> €</td>
						</tr>
					<tr>
						<td colspan="2"><?php echo $translate->_('Détail');?> :</td>
					</tr>
					<tr>
						<td colspan="2"><p class="detail"><?php echo $a->getDetail(); ?></p></td>
					</tr>
					<tr><td colspan="3">&nbsp;</td></tr>
					<tr>
						<td align="left" colspan="3"><h3><img src="<?php echo $basePath; ?>public/images/user_gray.png" /><?php echo $translate->_('Contact');?> :</h3>
							<ul>
								<li><img src="<?php echo $basePath; ?>public/images/telephone.png" /> <?php echo $translate->_('Télephone');?> : <?php echo $a->getTelephone(); ?></li>
								<li><img src="<?php echo $basePath; ?>public/images/fax.png" /> <?php echo $translate->_('Fax');?> :  <?php echo $a->getFax(); ?></li>
								<li><img src="<?php echo $basePath; ?>public/images/email.png" /> <?php echo $translate->_('Email');?> : <a href="mailto:<?php echo $a->getClient()->getEmail() ?>"><?php echo $a->getClient()->getEmail() ?></a></li>
							</ul>
						</td>
					</tr>
				</table>	
				
			</div>		
			<?php
		}
	}
?>
<p class="bouton_retour"><a href="<?php echo $basePath; ?>index.php?page=admin&action=annonces">Retour aux annonces</a></p>