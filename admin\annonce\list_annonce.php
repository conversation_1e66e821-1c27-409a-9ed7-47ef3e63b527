<?php
	$translate = Zend_Registry::get('translate');
	require_once dirname(__FILE__).'/../../class/annonce_class.php';
	require_once(dirname(__FILE__).'/../../class/pagination_class.php');
	
			function stripAccents($str, $charset='utf-8')
		{
			$str = htmlentities($str, ENT_NOQUOTES, $charset);
			$str = preg_replace('#&([A-za-z])(?:acute|cedil|circ|grave|orn|ring|slash|th|tilde|uml);#', '\1', $str);
			$str = preg_replace('#&([A-za-z]{2})(?:lig);#', '\1', $str); // pour les ligatures e.g. '&oelig;'
			$str = preg_replace('#&[^;]+;#', '', $str); // supprime les autres caractères
			$str = str_replace("'", '', $str); // supprime les autres caractères
			$str = str_replace('"', '', $str); // supprime les autres caractères
			
			return $str;
		}

// Update table demande_devis

if(isset($_GET['statut_devis'])){

$statut_devis = $_GET['statut_devis'];
$id_devis = $_GET['id_devis'];

$req = $database->query("

UPDATE demande_devis SET statut_devis = '$statut_devis' WHERE id_devis = '$id_devis'

") or die ("requete update statut_devis");

}

// Update table demande_bourse

if(isset($_GET['statut_bourse'])){

$statut_bourse = $_GET['statut_bourse'];
$id_bourse = $_GET['id_bourse'];

$req = $database->query("

UPDATE bourse SET statut_bourse = '$statut_bourse' WHERE id_bourse = '$id_bourse'

") or die ("requete update statut_bourse");

}

// Delete demande_bourse

if(isset($_GET['delete_bourse'])){

$id_bourse = $_GET['id_bourse'];

$req = $database->query("

DELETE FROM bourse WHERE id_bourse = '$id_bourse'

") or die ("requete delete id_bourse");

}

// Update table demande_photo

if(isset($_GET['statut_photo'])){

$statut_photo = $_GET['statut_photo'];
$id_photo = $_GET['id_photo'];

$req = $database->query("

UPDATE demande_photo SET statut_photo = '$statut_photo' WHERE id_photo = '$id_photo'

") or die ("requete update statut_photo");

}

// Update table demande_desc

if(isset($_GET['statut_desc'])){

$statut_desc = $_GET['statut_desc'];
$id_desc = $_GET['id_desc'];

$req = $database->query("

UPDATE demande_desc SET statut_desc = '$statut_desc' WHERE id_desc = '$id_desc'

") or die ("requete update statut_desc");

}

// Update table demande_prod_usa

if(isset($_GET['statut_dde_prod_us'])){

$statut_dde_prod_us = $_GET['statut_dde_prod_us'];
$id_dde_prod_us = $_GET['id_dde_prod_us'];

$req = $database->query("

UPDATE demande_produit_usa SET statut_dde_prod_us = '$statut_dde_prod_us' WHERE id_dde_prod_us = '$id_dde_prod_us'

") or die ("requete update statut_dde_prod_us");

}

//Fonction upload photo

function Upload($photo,$id)
{
	$dossier = "public/images/produits/";
	
//Grande image

	$listeextensions = array('.jpg');
	$extension = strrchr($_FILES[$photo]["name"], '.');
	$id = $id.$extension;
	$id = strtr($id, "ÀÁÂÃÄÅàáâãäåÒÓÔÕÖØòóôõöøÈÉÊËèéêëÇçÌÍÎÏìíîïÙÚÛÜùúûüÿÑñ\ '", "aaaaaaaaaaaaooooooooooooeeeeeeeecciiiiiiiiuuuuuuuuynn___");
	$taille_max = 600000;
	$tailleimage = filesize($_FILES[$photo]["tmp_name"]);
	

		
	if (!in_array($extension, $listeextensions))
		return -1;
	elseif ($tailleimage > $taille_max)
		return -2;
	else
	{
		if (move_uploaded_file($_FILES[$photo]["tmp_name"],$dossier.$id))
		{
			if (chmod($dossier.$id,0606)){
			//Petite image

				// Fichier et nouvelle taille
					$filename = $id;
					$percent = 0.5;

				// Calcul des nouvelles dimensions
					list($width, $height) = getimagesize($dossier.$filename);
					$newwidth = 60;
					$newheight = 40;

				// Chargement
				
					$thumb = imagecreatetruecolor($newwidth, $newheight);
					$source = imagecreatefromjpeg($dossier.$filename);
				
				// Redimensionnement
					imagecopyresized($thumb, $source, 0, 0, 0, 0, $newwidth, $newheight, $width, $height);
					
				//Sauvegarde
					imagejpeg($thumb, 'public/images/produits/mini/'.$id);
				return 1;
			}else{
				return 0;
			}
		}
		else
			return 0;
	}
}

if (isset($_POST['add_photo']))
{
	if (($_FILES["photopiece"]["name"] == "") || ($_POST['ref_photo'] == ""))
		echo "Il manque la photo ou la r&eacute;f&eacute;rence...<br /><br />";
	else
	{
		$res = Upload("photopiece",$_POST['ref_photo']);
		if ($res == -1)
			echo "Il faut que le format de la photo soit .jpg (jpg en minuscule).<br /><br />";
		if ($res == -2)
			echo "Il faut que la taille de la photo soit inf&eacute;rieure &agrave; 600 ko.<br /><br />";
		if ($res == 1)
			echo "Photo envoy&eacute;e avec succ&eacute;s.<br /><br />";
		if ($res == 0)
			echo "Echec de l'envoie de la photo.<br /><br />";
	}
}

// Module gestion des devis

?>

<h1><img src="<?php echo $basePath; ?>public/images/devis.png" alt="panier"/> <span><?php echo $translate->_('Gérer les demandes de devis'); ?></span></h1>
<table class="table-fill" style="width:95%;">
	<tr>
		<th><?php echo $translate->_('N°'); ?></th>
		<th><?php echo $translate->_('Date'); ?></th>
		<th><?php echo $translate->_('Email'); ?></th>
		<th><?php echo $translate->_('Produit'); ?></th>
		<th><?php echo $translate->_('Qte'); ?></th>
		<th><?php echo $translate->_('Com'); ?></th>
		<th colspan="3"><?php echo $translate->_('Réponse'); ?></th>
		
	</tr>
	<?php
	$result1 = $database->prepare("
	SELECT * FROM demande_devis WHERE statut_devis = '1' ORDER BY date_devis DESC
	") or die ("requete r1 invalid");
	$result1->execute();
	$i = 0;
	while ($tab1 = $result1->fetch()) {	
?>

				<tr class="td_center">
					<td class="text-center"><?php echo $tab1['id_devis']; ?></td>
					<td class="text-center"><?php echo  date('d/m/Y',strtotime($tab1['date_devis'])); ?></td>
					<td><?php echo $tab1['emaildevis']; ?></td>
					<td><?php echo $tab1['produitdevis']; ?></td>
					<td class="text-center"><?php echo $tab1['qtedevis']; ?></td>
					<?php
					if($tab1['commdevis'] == ""){
					echo "<td></td>";
					} else {
					/*	
					echo '<div id="info'.$i.'" style="margin-left: 400px; margin-bottom: 200px; background-color: #FFFFFF; position: fixed; top:300px; height: 21px; visibility: hidden;border: 2px solid #FF6600;width: 300px;padding-top: 3px;padding-bottom: 3px; padding-left: 3px; height: auto;"><strong>Commentaire : </strong>'.$tab1['commdevis'].'</div>';
					echo '<td><div align="center"><img src="'.$basePath.'public/images/comment.png" alt="comment" onmousemove="afficher(\'info'.$i.'\')" onmouseout="masquer(\'info'.$i.'\')"/></div></td>';
					*/
					$commdevis = preg_replace("#\n|\t|\r#","",$tab1['commdevis']);
					?>
					
					<td><div align="center"><img style="cursor:pointer;" src="<?php echo $basePath; ?>public/images/comment.png" onclick="javascript:alert('<?php echo stripAccents($commdevis); ?>');" alt="comment" /></div></td>
					
					<?php
					}
					$message_oui = "
					Bonjour, %0D%0A 
					%0D%0A 
					Vous avez demande un devis le ".date('d/m/Y',strtotime($tab1['date_devis']))." pour :%0D%0A %0D%0A 
					- ".$tab1['produitdevis']."%0D%0A %0D%0A 
					Nous disposons de cette de piece au prix de ... € HT.%0D%0A 
					Si vous souhaitez cette piece, merci de nous contacter par mail : <EMAIL> ou par telephone : ***********.56.%0D%0A 
					%0D%0A 
					Cordialement, %0D%0A 
					Service commercial.%0D%0A 
					%0D%0A 
					-------------------------------------%0D%0A 
					Surplus Militaires et Industriels %0D%0A 
					D1092, La gare %0D%0A 
					38840 La Sone %0D%0A 
					FRANCE %0D%0A 
					Tel : +33 (0) 476 644 356 %0D%0A 
					Fax : +33 (0) 476 644 555 %0D%0A 
					@ : <EMAIL> %0D%0A 
					Web : www.jeep-dodge-gmc.com %0D%0A 
					-------------------------------------  %0D%0A ";
					
					$message_non = "
					Bonjour, %0D%0A 
					%0D%0A 
					Vous avez demande un devis le ".date('d/m/Y',strtotime($tab1['date_devis']))." pour :%0D%0A %0D%0A 
					- ".$tab1['produitdevis']."%0D%0A %0D%0A 
					Hélas nous n'avons pas cette pièce en stock. Toute fois nous vous conseillons de suivre notre newsletter ou nos derniers arrivages.
					%0D%0A 
					Cordialement, %0D%0A 
					Service commercial.%0D%0A 
					%0D%0A 
					-------------------------------------%0D%0A 
					Surplus Militaires et Industriels %0D%0A 
					D1092, La gare %0D%0A 
					38840 La Sone %0D%0A 
					FRANCE %0D%0A 
					Tel : +33 (0) 476 644 356 %0D%0A 
					Fax : +33 (0) 476 644 555 %0D%0A 
					@ : <EMAIL> %0D%0A 
					Web : www.jeep-dodge-gmc.com %0D%0A 
					-------------------------------------  %0D%0A ";
					
					?>
					<td><a style="color: #0000FF; font-weight: bold;" href="mailto:<?php echo $tab1['emaildevis']; ?>?subject=Demande de devis&body=<?php echo $message_oui; ?>">&nbsp;&nbsp;OUI&nbsp;&nbsp;</a></td>
					<td><a style="color: #FF0000; font-weight: bold;" href="mailto:<?php echo $tab1['emaildevis']; ?>?subject=Demande de devis&body=<?php echo $message_non; ?>">&nbsp;&nbsp;NON&nbsp;&nbsp;</a></td>
					<td><a onclick="return confirm('Supprimer')" href="<?php echo $basePath; ?>index.php?page=admin&action=annonces&statut_devis=2&id_devis=<?php echo $tab1['id_devis']; ?>" title="supprimer">&nbsp;<img src="<?php echo $basePath.'public/images/cross.png'; ?>" alt="SUPPRIMER" />&nbsp;</a></td>
				</tr>
			<?php 
			$i++;
			}
?>
</table>
<?php

// Module gestion des demandes de description

?>

<h1><img src="<?php echo $basePath; ?>public/images/description.png" alt="panier"/> <span><?php echo $translate->_('Gérer les demandes de description'); ?></span></h1>
<table class="table-fill" style="width:95%;">
	<tr>
		<th><?php echo $translate->_('N°'); ?></th>
		<th><?php echo $translate->_('Date'); ?></th>
		<th><?php echo $translate->_('Email'); ?></th>
		<th><?php echo $translate->_('Référence'); ?></th>
		<th><?php echo $translate->_('Désignation'); ?></th>
		<th><?php echo $translate->_('Com'); ?></th>
		<th><?php echo $translate->_('Description'); ?></th>
		<th colspan="2"><?php echo $translate->_('Réponse'); ?></th>
		
	</tr>
	<?php
	
	$result3 = $database->prepare("
	SELECT * FROM demande_desc WHERE statut_desc = '1' ORDER BY date_desc DESC
	") or die ("requete r3 invalid");
	$result3->execute();
	$j = 100;
	while ($tab3 = $result3->fetch()) {
?>

				<tr>
					<td class="text-center"><?php echo $tab3['id_desc']; ?></td>
					<td class="text-center"><?php echo date('d/m/Y',strtotime($tab3['date_desc'])); ?></td>
					<td><?php echo substr($tab3['email_desc'], 0, 10); ?>...</td>
					<td class="text-center"><?php echo $tab3['ref_desc']; ?></td>
					<td style="font-size:10px;"><?php echo $tab3['nom_desc']; ?></td>
					<?php
					if($tab3['com_desc'] == ""){
					echo "<td></td>";
					} else {
					/*	
					echo '<div id="info'.$j.'" 
					style="margin-left: 400px; margin-bottom: 200px; background-color: #FFFFFF; 
					position: fixed; top:200px; height: 21px; visibility: hidden; 
					border: 2px solid #FF6600;width: 300px;padding-top: 3px;padding-bottom: 3px; 
					padding-left: 3px; height: auto;"><strong>Commentaire : </strong>'.$tab3['com_desc'].'</div>';
					echo '<td><div align="center"><img src="'.$basePath.'public/images/comment.png" alt="comment" onmousemove="afficher(\'info'.$j.'\')" onmouseout="masquer(\'info'.$j.'\')"/></div></td>';
					*/
					$com_desc = preg_replace("#\n|\t|\r#","",$tab3['com_desc']);
					?>
					
					<td><div align="center"><img style="cursor:pointer;" src="<?php echo $basePath; ?>public/images/comment.png" onclick="alert('<?php echo stripAccents($com_desc); ?>');" alt="comment"/></div></td>
					
					<?php
					}
					$ref_desc = $tab3['ref_desc'];
				$result2 = $database->prepare("
				SELECT COUNT(*) AS nb_desc FROM description WHERE ref_prod = '$ref_desc' 
				") or die ("requete r1 invalid");
				$result2->execute();
				
				while ($tab2 = $result2->fetch()) {
				
					if(isset($tab2['nb_desc'])){
					
					echo '<td class="text-center" style="font-size:11px;"><a href="'.$basePath.'index.php?page=admin&action=gererprod&type=modif&valuetosearch='.$tab3['ref_desc'].'&ref='.$tab3['ref_desc'].'">Faire la description</a></td>';
					
					} else {

					echo '<td class="text-center" style="font-size:11px;"><a href="'.$basePath.'index.php?page=admin&action=gererprod&type=description&categ='.strtolower($tab3['genre_desc']).'&ref='.$tab3['ref_desc'].'">Voir la description</a></td>';
					
					}
				
				}
					
				?>
				
					<?php
					
					$message_desc = "
					Bonjour, %0D%0A %0D%0A 
					Vous avez demande la description de la piece ".stripAccents($tab3['nom_desc'])." reference ".$tab3['ref_desc']." %0D%0A 
					%0D%0A %0D%0A 
					Commentaire : ".stripAccents($tab3['com_desc'])."%0D%0A 
					%0D%0A %0D%0A 
					Cliquer sur le lien pour voir la description : %0D%0A
					%0D%0A
					http://jeep-dodge-gmc.com/smi/description/".stripAccents($tab3['genre_desc'])."/".$tab3['ref_desc']." %0D%0A %0D%0A
					Cordialement, %0D%0A 
					Service commercial.%0D%0A 
					%0D%0A 
					-------------------------------------%0D%0A 
					Surplus Militaires et Industriels %0D%0A 
					D1092, La gare %0D%0A 
					38840 La Sone %0D%0A 
					FRANCE %0D%0A 
					Tel : +33 (0) 476 644 356 %0D%0A 
					Fax : +33 (0) 476 644 555 %0D%0A 
					@ : <EMAIL> %0D%0A 
					Web : www.jeep-dodge-gmc.com %0D%0A 
					-------------------------------------  %0D%0A ";
					
					?>
					<td class="text-center"><a style="color: #0000FF; font-weight: bold;" href="mailto:<?php echo $tab3['email_desc']; ?>?subject=Demande de description <?php echo $tab3['ref_desc']; ?> &body=<?php echo $message_desc; ?>">&nbsp;<img src="<?php echo $basePath.'public/images/email.png'; ?>" alt="MAIL" />&nbsp;</a></td>
					<td class="text-center"><a onclick="return confirm('Supprimer')" href="<?php echo $basePath; ?>index.php?page=admin&action=annonces&statut_desc=2&id_desc=<?php echo $tab3['id_desc']; ?>" title="supprimer">&nbsp;<img src="<?php echo $basePath.'public/images/cross.png'; ?>" alt="SUPPRIMER" />&nbsp;</a></td>
				</tr>
			<?php 
			$j++;
			}
?>
</table>
<h1><img src="<?php echo $basePath; ?>public/images/annonces.png" alt="panier"/> <span><?php echo $translate->_('Gérer les annonces'); ?></span></h1>
<?php
// Affichage de la liste des annonces
$annonces = annonceDB::getAnnonces();

// Affichage de la liste des annonces du clients

if(count($annonces) != 0):?>
<table class="table-fill" style="width:95%;">
	<tr>
		<th><?php echo $translate->_('Id'); ?></th>
		<th><?php echo $translate->_('Date'); ?></th>
		<th><?php echo $translate->_('Opé'); ?></th>
		<th><?php echo $translate->_('Sujet'); ?></th>
		<th><?php echo $translate->_('Prix'); ?></th>
		<th colspan="3">Actions</th>
		
	</tr>
	
	<?php foreach($annonces as $annonce): ?>
				<tr>
					<td class="text-center"><?php echo $annonce->getId(); ?></td>
					<td class="text-center"><?php echo  date('d/m/Y',strtotime($annonce->getDate())); ?></td>
					<td class="text-center"><?php echo $annonce->getOperation(); ?></td>
					<td><?php echo $annonce->getSujet(); ?></td>
					<td class="text-right"><?php echo $annonce->getPrix(); ?>€</td>
					
					<td class="text-center">
						<?php if($annonce->getStatus() == 0): ?>
							<a href="<?php echo $basePath; ?>index.php?page=admin&action=annonces&act=valider&id=<?php echo $annonce->getId(); ?>" title="valider l'annonce"><img src="<?php echo $basePath; ?>public/images/tick.png" /></a>
						<?php endif; ?>
					</td>
					<td class="text-center"><a href="<?php echo $basePath; ?>index.php?page=admin&action=annonces&act=detail&id=<?php echo $annonce->getId(); ?>" title="voir les détails"><img src="<?php echo $basePath; ?>public/images/zoom.png" /></a></td>
					<td class="text-center"><a onclick="return confirm('Supprimer')" href="<?php echo $basePath; ?>index.php?page=admin&action=annonces&act=suppr&id=<?php echo $annonce->getId(); ?>" title="supprimer"><img src="<?php echo $basePath.'public/images/cross.png' ?>" /></a></td>
				</tr>
	<?php endforeach; ?>
</table>
<?php else: ?>
	<div class="info messageBox"><p><?php echo $translate->_('Il n\'y a pas d\'annonces'); ?></p></div>
<?php endif; ?>

	
<?php

// Module gestion des bourse

?>

<h1><img src="<?php echo $basePath; ?>public/images/agenda.png" alt="panier"/> <span><?php echo $translate->_('Gérer les demandes de bourse'); ?></span></h1>
<table class="table-fill" style="width:95%;">
	<tr>
		<th style="font-size:16px; padding:3px;"><?php echo $translate->_('N°'); ?></th>
		<th><?php echo $translate->_('Date'); ?></th>
		<th><?php echo $translate->_('Type'); ?></th>
		<th><?php echo $translate->_('Ville'); ?></th>
		<th><?php echo $translate->_('Dep/Pays'); ?></th>
		<th><?php echo $translate->_('Contact'); ?></th>
		<th><?php echo $translate->_('E-mail'); ?></th>
		<th colspan="2"></th>
		
	</tr>
	<?php

	$result4 = $database->prepare("
	SELECT * FROM bourse WHERE date_bourse > DATE(NOW()) ORDER BY date_bourse DESC, dep_bourse, ville_bourse
	") or die ("requete r1 invalid");
	$result4->execute();
	$i = 0;
	while ($tab4 = $result4->fetch()) {
			
?>

				<tr class="td_center">
					<td style="font-size:11px; padding:3px;"><?php echo $tab4['id_bourse']; ?></td>
					<td class="text-center" style="font-size:11px; padding:3px;"><?php echo  date('d/m/Y',strtotime($tab4['date_bourse'])); ?></td>
					<td style="font-size:11px; padding:3px;"><?php echo $tab4['type_bourse']; ?></td>
					<td style="font-size:11px; padding:3px;"><?php echo $tab4['ville_bourse']; ?></td>
<?php
if ($tab4['dep_bourse'] != "sans"){
?>					
					<td style="font-size:11px; padding:3px;"><?php echo $tab4['dep_bourse']; ?></td>
<?php
} else {
?>					
					<td style="font-size:11px; padding:3px;"><?php echo $tab4['pays_bourse']; ?></td>
<?php
}
?>	
					<td style="font-size:11px; padding:3px;"><?php echo $tab4['contact_bourse']; ?></td>
					<td style="font-size:11px; padding:3px;"><?php echo $tab4['email_bourse']; ?></td>
<?php
if ($tab4['statut_bourse'] == "0"){
?>
					<td style="font-size:11px; padding:3px;"><a href="<?php echo $basePath; ?>index.php?page=admin&action=annonces&statut_bourse=1&id_bourse=<?php echo $tab4['id_bourse']; ?>" title="valider">&nbsp;<img src="<?php echo $basePath.'public/images/tick.png'; ?>" alt="VALIDER" />&nbsp;</a></td>
<?php
} else {
?>		
					<td style="font-size:11px; padding:3px;"></td>
<?php
}
?>	
					<td style="font-size:11px; padding:3px;"><a onclick="return confirm('Supprimer')" href="<?php echo $basePath; ?>index.php?page=admin&action=annonces&delete_bourse=1&id_bourse=<?php echo $tab4['id_bourse']; ?>" title="supprimer">&nbsp;<img src="<?php echo $basePath.'public/images/cross.png'; ?>" alt="SUPPRIMER" />&nbsp;</a></td>
				</tr>
			<?php 
			}
		
?>
</table>	
<?php	

// Module gestion des demandes de produit_usa
/*
?>
		<script type="text/javascript">

			function hide(comment)
		{
			var tooltip = document.getElementById(comment) ;
			tooltip.style.visibility = "hidden";
			run= false;
		}
			function view(comment)
		{
			var tooltip = document.getElementById(comment);
			tooltip.style.visibility = "visible";
			run= true;
		}
		</script>

<h1><img src="<?php echo $basePath; ?>public/images/drap_usa.png" alt="drap_usa" width="48" height="48" /> <span><?php echo $translate->_('Gérer les pré commandes USA'); ?></span></h1>
<table class="liste">
	<tr>
		<th><?php echo $translate->_('Statut'); ?></th>
		<th><?php echo $translate->_('N°'); ?></th>
		<th><?php echo $translate->_('Date'); ?></th>
		<th><?php echo $translate->_('Email'); ?></th>
		<th><?php echo $translate->_('Référence'); ?></th>
		<th><?php echo $translate->_('Désignation'); ?></th>
		<th><?php echo $translate->_('Qté'); ?></th>
		<th><?php echo $translate->_('PUHT'); ?></th>
		<th><?php echo $translate->_('Com'); ?></th>
		<th><?php echo $translate->_('Reponse'); ?></th>
		<th colspan="4"><?php echo $translate->_('Action'); ?></th>
		
	</tr>
	<?php
	if(isset($_GET['numpage']))
		$numpage = $_GET['numpage'];
	else
		$numpage = 1;
				
	//création d'un objet pour paginer la page
	$pagination = new Pagination(100, 200, $numpage);
	
	$result4 = $database->prepare("
	SELECT * FROM demande_produit_usa INNER JOIN produits_usa ON ref_us = ref_dde_prod_us WHERE statut_dde_prod_us != '4' ORDER BY statut_dde_prod_us, date_dde_prod_us DESC
	") or die ("requete r4 invalid");
	$result4->execute();
	$i = 1000;
	while ($tab4 = $result4->fetch()) {
	
			$pagination->incremente();
			if($pagination->isTimeToRender()){ 
			
?>

				<tr class="td_center<?php echo (($pagination->getCount()%2)?' imp':''); ?>">
					<?php
					
					if($tab4['statut_dde_prod_us'] == "1"){
					
					?>
					<td><img src="<?php echo $basePath.'public/images/rond_jaune.png'; ?>" alt="A VALIDER" title="A VALIDER" /></td>
					<?php
					
					} elseif($tab4['statut_dde_prod_us'] == "2"){
					
					?>
					<td><img src="<?php echo $basePath.'public/images/rond_bleu.png'; ?>" alt="ATTENTE CONFIRMATION CLIENT" title="ATTENTE CONFIRMATION CLIENT" /></td>
					<?php
					
					} elseif($tab4['statut_dde_prod_us'] == "3"){
					
					?>
					<td><img src="<?php echo $basePath.'public/images/rond_vert.png'; ?>" alt="PRÉCOMMANDE VALIDÉE" title="PRÉCOMMANDE VALIDÉE" /></td>
					<?php
					
					}
					
					?>
					<td><?php echo $tab4['id_dde_prod_us']; ?></td>
					<td><?php echo date('d/m/Y',strtotime($tab4['date_dde_prod_us'])); ?></td>
					<td><a href="<?php echo $basePath; ?>index.php?page=admin&action=gererclient&type=modif&id=<?php echo $tab4['email_dde_prod_us']; ?>" ><?php echo $tab4['email_dde_prod_us']; ?></a></td>
					<td><?php echo $tab4['ref_dde_prod_us']; ?></td>
					<td><a href="<?php echo $tab4['image_us']; ?>" rel="lytebox" title="<?php echo $tab['ref_us']; ?>" ><?php echo $tab4['desc_dde_prod_us']; ?></a></td>
					<td><?php echo $tab4['qte_dde_prod_us']; ?></td>
					<td><?php echo number_format(($tab4['prix_us']*1.20),2, '.', ''); ?> &euro;</td>
					<?php
					if($tab4['com_dde_prod_us'] == ""){
					echo "<td></td>";
					} else {
					echo '<div id="comment'.$i.'" style="margin-left: 400px; margin-bottom: 200px; background-color: #FFFFFF; position: fixed; top:400px; height: 21px; visibility: hidden;border: 2px solid #FF6600;width: 300px;padding-top: 3px;padding-bottom: 3px; padding-left: 3px; height: auto;"><strong>Commentaire : </strong>'.$tab4['com_dde_prod_us'].'</div>';
					echo '<td><div align="center"><img src="'.$basePath.'public/images/comment.png" alt="comment" onmousemove="view(\'comment'.$i.'\')" onmouseout="hide(\'comment'.$i.'\')"/></div></td>';
					}
					
				?>
				
					<?php
					$message_dde_prod_usa = "
					Bonjour, %0D%0A %0D%0A 
					Vous avez fait la pre-commande de l article suivant : %0D%0A 
					".$tab4['desc_dde_prod_us']." reference ".$tab4['ref_dde_prod_us']." %0D%0A 
					%0D%0A %0D%0A 
					Commentaire : ".$tab4['com_dde_prod_us']."%0D%0A 
					%0D%0A %0D%0A 
					Pour valider votre precommande, merci de nous transmettre un règlement par CB, Cheque, Virement ou Paypal. %0D%0A 
					Vous serez debite qu au moment de l expedition de la commande. %0D%0A 
					%0D%0A %0D%0A 
					Cordialement, %0D%0A 
					Service commercial.%0D%0A 
					%0D%0A 
					-------------------------------------%0D%0A 
					Surplus Militaires et Industriels %0D%0A 
					D1092, La gare %0D%0A 
					38840 La Sone %0D%0A 
					FRANCE %0D%0A 
					Tel : +33 (0) 476 644 356 %0D%0A 
					Fax : +33 (0) 476 644 555 %0D%0A 
					@ : <EMAIL> %0D%0A 
					Web : www.jeep-dodge-gmc.com %0D%0A 
					-------------------------------------  %0D%0A ";
					
					?>
					<td><a style="color: #0000FF; font-weight: bold;" href="mailto:<?php echo $tab4['email_dde_prod_us']; ?>?subject=Precommande USA&body=<?php echo $message_dde_prod_usa; ?>">&nbsp;<img src="<?php echo $basePath.'public/images/email.png'; ?>" alt="MAIL" />&nbsp;</a></td>
					<td><a href="<?php echo $basePath; ?>index.php?page=admin&action=annonces&statut_dde_prod_us=2&id_dde_prod_us=<?php echo $tab4['id_dde_prod_us']; ?>" title="ATTENTE">&nbsp;<img src="<?php echo $basePath.'public/images/time.png'; ?>" alt="ATTENTE" />&nbsp;</a></td>
					<td><a href="<?php echo $basePath; ?>index.php?page=admin&action=annonces&statut_dde_prod_us=3&id_dde_prod_us=<?php echo $tab4['id_dde_prod_us']; ?>" title="VALIDER">&nbsp;<img src="<?php echo $basePath.'public/images/tick.png'; ?>" alt="VALIDER" />&nbsp;</a></td>
					<td><a onclick="return confirm('Supprimer la pré-commande ?')" href="<?php echo $basePath; ?>index.php?page=admin&action=annonces&statut_dde_prod_us=4&id_dde_prod_us=<?php echo $tab4['id_dde_prod_us']; ?>" title="SUPPRIMER">&nbsp;<img src="<?php echo $basePath.'public/images/cross.png'; ?>" alt="SUPPRIMER" />&nbsp;</a></td>					
				</tr>
			<?php 
			}
		$i++;
		}
?>

	<tr>
		<th colspan=13><?php $pagination->printPagesLinks($basePath.'index.php?page=admin&action=annonces', true); ?></th>
	</tr>
</table>
<?php

*/

?>

<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>index.php?page=admin&action=administration"><?php echo $translate->_('Retour');?></a>
</p>