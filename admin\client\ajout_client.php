<?php

if(isset($_POST['ajout'])){

		function stripAccents($str, $charset='utf-8')
		{
			$str = htmlentities($str, ENT_NOQUOTES, $charset);
			
			$str = preg_replace('#&([A-za-z])(?:acute|cedil|circ|grave|orn|ring|slash|th|tilde|uml);#', '\1', $str);
			$str = preg_replace('#&([A-za-z]{2})(?:lig);#', '\1', $str); // pour les ligatures e.g. '&oelig;'
			$str = preg_replace('#&[^;]+;#', '', $str); // supprime les autres caractères
			
			return $str;
		}

		function delSpace($str2)
		{

			$str2 = preg_replace('#&[^;]+;-_.#', '', $str2); // supprime les caractères speciaux
			$str2 = preg_replace('# #', '', $str2); // supprime les espaces
			
			return $str2;

		}

$email = $_POST['email'];
$nom = strtoupper(stripAccents($_POST['nom']));
$prenom = strtolower(stripAccents($_POST['prenom']));
$telephone = delSpace($_POST['telephone']);
$fax = delSpace($_POST['fax']);

$lettre_t = "0";
if(isset($_POST['lettre_t'])){
	$lettre_t = $_POST['lettre_t'];
}

$tva_intra_com = $_POST['tva_intra_com'];
$vehicule = $_POST['vehicule'];
$infos = stripAccents($_POST['infos']);

$af_raisonsocial = stripAccents($_POST['af_raisonsocial']);
$af_nomrue = strtolower(stripAccents($_POST['af_nomrue']));
$af_codepostal = delSpace($_POST['af_codepostal']);
$ville = strtoupper(stripAccents($_POST['ville']));
$af_pays = $_POST['af_pays'];

$acces_fact = "0";
$acces_four = "0";
$acces_cli = "0";
$acces_cde = "0";
$acces_prod = "0";
$acces_lien = "0";

if(isset($_POST['acces_fact'])){
	$acces_fact = $_POST['acces_fact'];
}
if(isset($_POST['acces_four'])){
	$acces_four = $_POST['acces_four'];
}
if(isset($_POST['acces_cli'])){
	$acces_cli = $_POST['acces_cli'];
}
if(isset($_POST['acces_cde'])){
	$acces_cde = $_POST['acces_cde'];
}
if(isset($_POST['acces_prod'])){
	$acces_prod = $_POST['acces_prod'];
}
if(isset($_POST['acces_lien'])){
	$acces_lien = $_POST['acces_lien'];
}

$email_secondaire = NULL;
$passwd = md5('000000');

if ($_POST['indice'] ==  "6"){
$isadmin = "1";
$indice = "1";
} else {
$isadmin = "0";
$indice = $_POST['indice'];
}
$uid = NULL;
$code_promo = NULL;
$datecreate = date('Y-m-d');

// AJOUT les données CLIENTS
	$req = $database->query("
	INSERT INTO clients (
	emailclient,nomclient,prenomclient,passwdclient,isadmin,uid,telclient,faxclient,infos,indiceclient,code_promo,
	email_secondaire,lettre_T,datecreate,vehiculeprefer,tva_intra_com,acces_fact,acces_four,acces_cli,
	acces_cde,acces_prod,acces_lien	
	) 
	
	VALUES (
	'".$email."','".$nom."','".$prenom."','".$passwd."','".$isadmin."','".$uid."','".$telephone."','".$fax."',
	'".$infos."','".$indice."','".$code_promo."','".$email_secondaire."','".$lettre_t."','".$datecreate."','".$vehicule."',
	'".$tva_intra_com."','".$acces_fact."','".$acces_four."','".$acces_cli."','".$acces_cde."','".$acces_prod."',
	'".$acces_lien."'	
	)") or die ("requete insert client");
	
//AJOUT les données adresses_facturation
	$req2 = $database->query("
	INSERT INTO adresses_facturation (
	af_emailclient,af_raisonsocial,af_nomrue,af_codepostal,af_ville,af_pays	
	) 
	
	VALUES (
	'".$email."','".$af_raisonsocial."','".$af_nomrue."','".$af_codepostal."','".$ville."','".$af_pays."'	
	)") or die ("requete adresse_facturation client");
	
	include("modif_client.php");

} else {

?>
<script type="text/javascript">
//<![CDATA[
	function test_email(str)
	{
		if (str=="")
		  {
		  document.getElementById("txtHint").innerHTML="";
		  return;
		  }
		if (window.XMLHttpRequest)
		  {// code for IE7+, Firefox, Chrome, Opera, Safari
		  xmlhttp=new XMLHttpRequest();
		  }
		else
		  {// code for IE6, IE5
		  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
		  }
		xmlhttp.onreadystatechange=function()
		  {
		  if (xmlhttp.readyState==4 && xmlhttp.status==200)
			{
			document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
			}
		  }
		xmlhttp.open("GET","<?php echo $basePath; ?>client/test_email.php?mail="+str,true);
		xmlhttp.send();
	} 
	function afficheacces() {
		alert("Merci de choisir les acces autorises !");
		document.getElementById("acces").style.display = "inline"; 
		document.getElementById("titreacces").style.display = "inline"; 
	}
	function copie()
	{

	document.forms['form1'].elements['af_raisonsocial'].value = document.forms['form1'].elements['nom'].value.toUpperCase() + " " + document.forms['form1'].elements['prenom'].value.toLowerCase();

	}
	function choix_pays(pays)
	{
		if (pays=="France"){
				
			var cp = document.getElementById("af_codepostal").value;
			var xmlhttp = null;
			if (cp=="")
			  {
			  document.getElementById("view_ville").innerHTML="Veuillez saisir un code postal";
			  return;
			  }
			if (window.XMLHttpRequest)
			  {// code for IE7+, Firefox, Chrome, Opera, Safari
			  xmlhttp=new XMLHttpRequest();
			  }
			else
			  {// code for IE6, IE5
			  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
			  }
			xmlhttp.onreadystatechange=function()
			  {
			  if (xmlhttp.readyState==4 && xmlhttp.status==200)
				{
				document.getElementById("view_ville").innerHTML=xmlhttp.responseText;
				}
			  }
			xmlhttp.open("GET","<?php echo $basePath; ?>client/choix_ville.php?cp="+cp,true);
			xmlhttp.send();
		}
		else
		{
			document.getElementById("view_ville").innerHTML = '<input id="f7" type="text" name="ville" value="" class="indispensable"/><b class="reditalique">* </b>';
		}
	} 
//]]>
</script>

<h1><img src="<?php echo $basePath; ?>public/images/clients_modifier.jpg" height="30" alt="clients"/>Ajout un client</h1>
<div class="info messageBox">
- Les champs avec * sont obligatoires<br/ >
- Le client est automatiquement créé avec un mot de passe : 000000
</div>
<div style="margin-left:25px;" class="form">
<form name="form1" action="<?php echo $basePath; ?>index.php?page=admin&action=gererclient&type=ajout" method="post">
<fieldset class="input">
<input type="hidden" name="ajout" value= "1"/>
<table style="width:850px;">
<th colspan="2">Infos Principales</th>
<th colspan="2">Infos Secondaires</th>
  <tr>
    <td>Email <b class="reditalique">* </b></td>
    <td><input onchange="test_email(this.value)" type="text" name="email" id="email" size="25" value= ""/>
		<script type="text/javascript">
			var email = new LiveValidation('email');
			email.add( Validate.Email );
			email.add( Validate.Presence );
	    </script>
		
	</td>
    <td style="width:150px;">Vehicule préféré <b class="reditalique">* </b></td>
    <td rowspan="2">
        <table style="text-align:center;">
          <tr>
            <td>Jeep</td>
            <td><input class="validate[required] radio" style="width:14px; margin-left:10px;" type="radio" name="vehicule" value= "jeep"/></td>
			<td>R2087</td>
            <td><input style="width:14px; margin-left:10px;" type="radio" name="vehicule" value= "r2087"/></td>
          </tr>
          <tr>
            <td>Dodge</td>
            <td><input style="width:14px; margin-left:10px;" type="radio" name="vehicule" value= "dodge"/></td>
			<td>Blindé</td>
            <td><input style="width:14px; margin-left:10px;" type="radio" name="vehicule" value= "blinde"/></td>
          </tr>
          <tr>
            <td>GMC</td>
            <td><input style="width:14px; margin-left:10px;" type="radio" name="vehicule" value= "gmc"/></td>
			<td>Autre</td>
            <td><input style="width:14px; margin-left:10px;" type="radio" name="vehicule" value= "autre"/></td>
          </tr>
        </table>
    </td>
  </tr>
  <tr>
		<td colspan="2"><span style="margin-left:0px;" id="txtHint"></span></td>
  </tr>
  <tr>
    <td>Nom <b class="reditalique">* </b></td>
    <td><input type="text" name="nom" id="nom" size="50" value= ""/>
		<script type="text/javascript">
			var nom = new LiveValidation('nom');
			nom.add( Validate.Presence );
	    </script>
	</td>	
  </tr>
  <tr>
    <td>Prenom <b class="reditalique">* </b></td>
    <td><input type="text" name="prenom" id="prenom" size="6" onchange="copie()" value= ""/>
		<script type="text/javascript">
			var prenom = new LiveValidation('prenom');
			prenom.add( Validate.Presence );
	    </script>
	</td>
    <td>Numero TVA</td>
    <td><input type="text" name="tva_intra_com" size="20" value= ""/></td>
  </tr>
  <tr>
    <td>Téléphone <b class="reditalique">* </b></td>
    <td><input type="text" name="telephone" id="telephone" size="20" value= ""/>
		<script type="text/javascript">
			var telephone = new LiveValidation('telephone');
			telephone.add( Validate.Presence );
			telephone.add( Validate.Numericality );
	    </script>
	</td>
	<td>Lettre T</td>
    <td align="center" ><input type="checkbox" name="lettre_t" style="width:14px; margin-left:20px;"></td>
  </tr>
  <tr>
    <td>Fax</td>
    <td><input type="text" name="fax" size="20" value= ""/></td>
    <td>&nbsp;</td>
  </tr>
  <tr>
    <td colspan="2" style="font-weight:bold; vertical-align:bottom;">Adresse de facturation</td>
    <td>Indice <b class="reditalique">* </b></td>
    <td>&nbsp;
    <table>
     <tr>
        
		<?php

				echo '<td><img src="'.$basePath.'public/images/rond_vert.png" checked alt="vert"/><input class="validate[required] radio" style="width:14px; margin-left:10px;" type="radio" name="indice" value= "1"/></td>';
				echo '<td>R.A.S.</td>';

		?>	    

		<?php

				echo '<td><img src="'.$basePath.'public/images/medaille.png" alt="medaille"/><input style="width:14px; margin-left:10px;" type="radio" name="indice" value= "4"/></td>';
				echo '<td>Bon client</td>';

		?>
  	 </tr>
     <tr> 
		<?php

				echo '<td><img src="'.$basePath.'public/images/rond_rouge.png" alt="rouge"/><input style="width:14px; margin-left:10px;" type="radio" name="indice" value= "2"/></td>';
				echo '<td>A surveiller<br /><span style="font-style:italique; font-size:11px;">(Pb SAV)</span></td>';



				echo '<td><img src="'.$basePath.'public/images/pro.png" alt="pro"/><input style="width:14px; margin-left:10px;" type="radio" name="indice" value= "5"/></td>';
				echo '<td>Pro</td>';

		?>
  	  </tr>
      <tr>
        

		<?php

				echo '<td><img src="'.$basePath.'public/images/rond_noir.png" alt="noir"/><input style="width:14px; margin-left:10px;" type="radio" name="indice" value= "3"/></td>';
				echo '<td>Ne pas servir<br /><span style="font-style:italique; font-size:11px;">(Pb paiement)</span></td>';
				
				echo '<td><img src="'.$basePath.'public/images/user_gray.png" alt="admin"/><input style="width:14px; margin-left:10px;" onclick="afficheacces(acces);" type="radio" name="indice" value= "6"/></td>';
				echo '<td>Admin<br /></td>';

		?>

		
  	  </tr>
    </table>
    </td>
  </tr>
  <tr>
	<td></td>
	<td></td>
	<td id="titreacces" style="display: none;"><strong>Accés : </strong></td>
    <td>&nbsp;
    <table style="display: none; text-align:center;" id="acces" >
		  <tr style="font-size:8px;" width="100">
			<td width="10">Facture</td>
			<td>Fournisseur</td>
			<td>Client</td>
			<td>Commande</td>
			<td>Produit</td>
			<td>Lien</td>
		  </tr>
		  <tr>
			<td><input style="width:14px;" type="checkbox" name="acces_fact" value= ""/></td>
			<td><input style="width:14px;" type="checkbox" name="acces_four" value= ""/></td>
			<td><input style="width:14px;" type="checkbox" name="acces_cli" value= ""/></td>
			<td><input style="width:14px;" type="checkbox" name="acces_cde" value= ""/></td>
			<td><input style="width:14px;" type="checkbox" name="acces_prod" value= ""/></td>
			<td><input style="width:14px;" type="checkbox" name="acces_lien" value= ""/></td>
		  </tr>
    </table>
    </td>
  </tr>
  <tr>
    <td>Raison social <b class="reditalique">* </b></td>
    <td><input type="text" name="af_raisonsocial" id="af_raisonsocial" size="25" value= ""/>
		<script type="text/javascript">
			var af_raisonsocial = new LiveValidation('af_raisonsocial');
			af_raisonsocial.add( Validate.Presence );
		</script>
	</td>
    <td>Divers</td>
    <td rowspan="3"><textarea name="infos" rows="4" cols="25"></textarea></td>
  </tr>
  <tr>
    <td>Adresse <b class="reditalique">* </b></td>
    <td><input type="text" name="af_nomrue" id="af_nomrue" size="50" value= ""/>
		<script type="text/javascript">
			var af_nomrue = new LiveValidation('af_nomrue');
			af_nomrue.add( Validate.Presence );
		</script>
	</td>
  </tr>
  <tr>
    <td>Code postal <b class="reditalique">* </b></td>
    <td><input type="text" onchange="choix_pays(document.getElementById('af_pays').options[document.getElementById('af_pays').selectedIndex].value)"  name="af_codepostal" size="6" id="af_codepostal" value= ""/>
		<script type="text/javascript">
			var af_codepostal = new LiveValidation('af_codepostal');
			af_codepostal.add( Validate.Presence );
		</script>
	</td>
  </tr>
  <tr>
    <td>Pays <b class="reditalique">* </b></td>
    <td><select id="af_pays" onchange="choix_pays(this.options[this.selectedIndex].value)" name="af_pays">';
	<?php
								$result8 = $database->prepare("SELECT * FROM pays order by nom_pays ASC") or die ("requete r1 invalid");
								$result8->execute();
								echo '<option value="vide" >---</option>';
								while ($tab7 = $result8->fetch()) {
								
									echo '<option style="width: 225px;" value="'.$tab7['nom_pays'].'">'.$tab7['nom_pays'].'</option>';
								
									}
								
								echo '</select>';
	?>
				
				<script type="text/javascript">			
					var af_pays = new LiveValidation('af_pays');
		            af_pays.add(Validate.Exclusion, { within: ['vide'], failureMessage: " "});
		        </script>
	</td>
  </tr>
  <tr>
    <td>Ville</td>
    <td><span name="ville" id="view_ville"></span>
	</td>
  </tr>
</table>
				</fieldset>
				<fieldset style="width:850px;" class="submit">
					<p>	
						<input type="submit" name="ajouter" value="<?php echo $translate->_('Ajouter'); ?>" class="btn_submit"/>
					</p>
				</fieldset>
			
		</div>

</form>

		<!-- on affiche les informations relive au client -->
		<p class="bouton_retour">
			<a href="<?php echo $basePath ?>index.php?page=admin&action=gererclient"><?php echo $translate->_('Retour'); ?></a>
		</p>
		
<?php

}

?>		