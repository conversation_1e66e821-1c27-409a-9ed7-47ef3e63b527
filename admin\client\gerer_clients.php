<?php
	require_once(dirname(__FILE__).'/../../class/client_class.php');
	require_once(dirname(__FILE__).'/../../class/pagination_class.php');
?>	
<h1><img src="<?php echo $basePath; ?>public/images/clients.jpg" alt="clients"/> <span><?php echo $translate->_('Gérer les clients');?></span></h1>

<?php

include("recherche_client.php");

if(isset($_GET['valuetosearch'])) {

/*
?>
	
		<p style="margin-left:10px;">Recherchez un client par Email, Nom, Prenom, CP ou Ville</p>
		<br />
		<form method="get" action="<?php echo $basePath,'index.php'?>">
		<input type="hidden" name="page" value="admin"/>
		<input type="hidden" name="action" value="gererclient" />
		<input type="hidden" name="action2" value="rechercher" />	
      	<input style="border:1px solid #cccccc; margin-left:10px;" type="text" name="recherche" value="" />		
		<input type="submit" value="Ok" /><br />
		
		</form>
		<br />
		<a style="margin-left:10px; border:1px solid #cccccc; float:left; padding:5px;" href="<?php echo $basePath; ?>index.php?page=admin&action=gererclient&action2=recherche_avancee">Recherche avancée</a>
		<br /><br />
<?php

if (isset($_GET['action2']) && ($_GET['action2'] == "recherche_avancee")){

?>
<form method="get" action="<?php echo $basePath,'index.php'?>">
<input type="hidden" name="page" value="admin"/>
<input type="hidden" name="action" value="gererclient" />
<input type="hidden" name="action2" value="result_recherche_avancee" />

<input type="radio" name="choix_adresse" value="adresse_fact" style="margin-left:10px;" checked="checked" id="adresse_fac" /> <label for="adresse_fac">Adresse de facturation</label>
<input type="radio" name="choix_adresse" value="adresse_liv" id="adresse_liv" /> <label for="adresse_liv">Adresse de livraison</label>
<br /><br />

<table class="liste">
	<tr>
		<th><?php echo $translate->_('Email'); ?></th>
		<th><?php echo $translate->_('Raison social'); ?></th>
		<th><?php echo $translate->_('Adresse'); ?></th>
		<th><?php echo $translate->_('Code postal'); ?></th>
		<th><?php echo $translate->_('Ville'); ?></th>
		<th><?php echo $translate->_('Pays'); ?></th>
		<th>action</th>
	</tr>
	<tr>
		<td><input type="text" style="width:120px" name="email" value=""/></td>
		<td><input type="text" style="width:120px" name="raison_social" value=""/></td>
		<td><input type="text" style="width:120px" name="adresse" value=""/></td>
		<td><input type="text" style="width:120px" name="cp" value=""/></td>
		<td><input type="text" style="width:120px" name="ville" value=""/></td>
		<td><input type="text" style="width:120px" name="pays" value=""/></td>
		<td><input type="submit" name="valider" value="ok"/><td>
	</tr>
</table>
</form>
<br />

<form method="get" style="margin-left:10px;" action="<?php echo $basePath,'index.php'?>">
Trier par type d'indice : <br />
<input type="hidden" name="page" value="admin"/>
<input type="hidden" name="action" value="gererclient" />
<input type="hidden" name="action2" value="result_recherche_avancee_2" />
<input type="radio" name="indice" value="2" style="margin-left:10px;" id="indice_2" /> <label for="indice_2"><img src="<?php echo $basePath; ?>public/images/rond_rouge.png" alt="rouge"/></label><br />
<input type="radio" name="indice" value="3" style="margin-left:10px;" id="indice_3" /> <label for="indice_3"><img src="<?php echo $basePath; ?>public/images/rond_noir.png" alt="noir"/></label><br />
<input type="radio" name="indice" value="4" style="margin-left:10px;" id="indice_4" /> <label for="indice_4"><img src="<?php echo $basePath; ?>public/images/medaille.png" alt="medaille"/></label><br />
<input type="radio" name="indice" value="5" style="margin-left:10px;" id="indice_5" /> <label for="indice_5"><img src="<?php echo $basePath; ?>public/images/pro.png" alt="pro"/></label><br />
<input type="radio" name="indice" value="9" style="margin-left:10px;" id="indice_9" /> <label for="indice_9"><img src="<?php echo $basePath; ?>public/images/rond_bleu.png" alt="bleu"/></label><br />
<input type="submit" name="valider" value="ok"/>
<br /><br />
</form>

<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>index.php?page=admin&action=gererclient"><?php echo $translate->_('Retour');?></a>
</p>
<?php

} elseif (isset($_GET['action2']) && ($_GET['action2'] == "result_recherche_avancee")){

?>
<table class="liste">
	<tr>
		<th><?php echo $translate->_('Email'); ?></th>
		<th><?php echo $translate->_('Indice'); ?></th>
		<th><?php echo $translate->_('Raison social'); ?></th>
		<th><?php echo $translate->_('Adresse'); ?></th>
		<th><?php echo $translate->_('Code postal'); ?></th>
		<th><?php echo $translate->_('Ville'); ?></th>
		<th><?php echo $translate->_('Pays'); ?></th>
		<th><?php echo $translate->_('Actions'); ?></th>
	</tr>

	<?php

		if(isset($_GET['numpage']))
			$numpage = $_GET['numpage'];
		else
			$numpage = 1;
			
		//création d'un objet pour paginer la page
		$pagination = new Pagination(0, 30, 20, $numpage);
		
		$or = 0;
		$sql_email = "";
		$sql_raison_social = "";
		$sql_adresse = "";
		$sql_cp = "";
		$sql_ville = "";
		$sql_pays = "";
		
		$email = $_GET['email'];
		$raison_social = $_GET['raison_social'];
		$adresse = $_GET['adresse'];
		$cp = $_GET['cp'];
		$ville = $_GET['ville'];
		$pays = $_GET['pays'];
		$choix_adresse = $_GET['choix_adresse'];
		
		if ($choix_adresse == 'adresse_fact'){
		
		$champ_email = "af_emailclient";
		$champ_raison_social = "af_raisonsocial";
		$champ_adresse = "af_nomrue";
		$champ_cp = "af_codepostal";
		$champ_ville = "af_ville";
		$champ_pays = "af_pays";
		
		} else {
		
		$champ_email = "emailclient";
		$champ_raison_social = "raisonsocial";
		$champ_adresse = "nomrue";
		$champ_cp = "codepostal";
		$champ_ville = "ville";
		$champ_pays = "pays";
		
		}
		
		
		if (!empty($_GET['email'])){
			$sql_email = "UPPER(".$champ_email.") LIKE UPPER('%".$email."%')";
		$or = 1;
		}
		
		if (!empty($_GET['raison_social'])){
		
			if ($or == 1){
			 $sql_raison_social = "AND UPPER(".$champ_raison_social.") LIKE UPPER('%".$raison_social."%')";
			$or =1;
			} else {
			 $sql_raison_social = "UPPER(".$champ_raison_social.") LIKE UPPER('%".$raison_social."%')";
			$or =1;
			}
		
		}
		
		if (!empty($_GET['adresse'])){
		
			if ($or == 1){
			 $sql_adresse = "AND UPPER(".$champ_adresse.") LIKE UPPER('%".$adresse."%')";
			$or =1;
			} else {
			 $sql_adresse = "UPPER(".$champ_adresse.") LIKE UPPER('%".$adresse."%')";
			$or =1;
			}
		
		}
		
		if (!empty($_GET['cp'])){
		
			if ($or == 1){
			 $sql_cp = "AND UPPER(".$champ_cp.") LIKE UPPER('%".$cp."%')";
			$or =1;
			} else {
			 $sql_cp = "UPPER(".$champ_cp.") LIKE UPPER('%".$cp."%')";
			$or =1;
			}
		
		}
		
		if (!empty($_GET['ville'])){
		
			if ($or == 1){
			 $sql_ville = "AND UPPER(".$champ_ville.") LIKE UPPER('%".$ville."%')";
			$or =1;
			} else {
			 $sql_ville = "UPPER(".$champ_ville.") LIKE UPPER('%".$ville."%')";
			$or =1;
			}
		
		}
		
		if (!empty($_GET['pays'])){
		
			if ($or == 1){
			 $sql_pays = "AND UPPER(".$champ_pays.") LIKE UPPER('%".$pays."%')";
			$or =1;
			} else {
			 $sql_pays = "UPPER(".$champ_pays.") LIKE UPPER('%".$pays."%')";
			$or =1;
			}
		
		}
		
		if ($choix_adresse == 'adresse_fact'){
		$result = $database->prepare("
		SELECT * FROM adresses_facturation 
		WHERE ".$sql_email.$sql_raison_social.$sql_adresse.$sql_cp.$sql_ville.$sql_pays."
		order by af_raisonsocial
		") or die ("requete r1 invalid");
		
		} else {
		
		$result = $database->prepare("
		SELECT * FROM adresses 
		WHERE ".$sql_email.$sql_raison_social.$sql_adresse.$sql_cp.$sql_ville.$sql_pays."
		GROUP BY emailclient ORDER BY raisonsocial
		") or die ("requete r1 invalid");
		
		}
		
		$result->execute();
		
		while ($row = $result->fetch()) {
			$pagination->incremente();
			if($pagination->isTimeToRender())
			{
				?>
				<tr <?php echo (($pagination->getCount()%2)?'class="imp"':''); ?>>
					<td>
						<?php echo $row[''.$champ_email.'']; ?>
					</td>
					<td>
						<?php
						
							$idcde = $row[''.$champ_email.''];
							$result5 = $database->prepare("SELECT * FROM clients WHERE emailclient = '$idcde'") or die ("requete r5 invalid");
							$result5->execute();
							
							while ($tab4 = $result5->fetch()) {
								if($tab4['indiceclient'] == "1"){
								echo '<div align="center"><img src="'.$basePath.'public/images/rond_vert.png" alt="vert"/></div>';
								}
								if($tab4['indiceclient'] == "2"){
								echo '<div align="center"><img src="'.$basePath.'public/images/rond_rouge.png" alt="rouge"/></div>';
								}
								if($tab4['indiceclient'] == "3"){
								echo '<div align="center"><img src="'.$basePath.'public/images/rond_noir.png" alt="noir"/></div>';
								}
								if($tab4['indiceclient'] == "4"){
								echo '<div align="center"><img src="'.$basePath.'public/images/medaille.png" alt="medaille"/></div>';
								}
								if($tab4['indiceclient'] == "5"){
								echo '<div align="center"><img src="'.$basePath.'public/images/pro.png" alt="pro"/></div>';
								}
								if($tab4['indiceclient'] == "9"){
								echo '<div align="center"><img src="'.$basePath.'public/images/rond_bleu.png" alt="pro"/></div>';
								}								
							}
						
						?>
					</td>
					<td>
						<?php echo $row[''.$champ_raison_social.'']; ?>
					</td>
					<td>
						<?php echo $row[''.$champ_adresse.'']; ?>
					</td>
					<td>
						<?php echo $row[''.$champ_cp.'']; ?>
					</td>
					<td>
						<?php echo $row[''.$champ_ville.'']; ?>
					</td>		
					<td>
						<?php echo $row[''.$champ_pays.'']; ?>
					</td>

					<td class="td_center">
						<a href="<?php echo $basePath; ?>index.php?page=admin&action=gererclient&type=modif&id=<?php echo $row[''.$champ_email.'']; ?>" title="Modifier">
							<img src="<?php echo $basePath; ?>public/images/b_edit.png" alt="edit"/>
						</a>&nbsp;&nbsp;
						<a href="<?php echo $basePath; ?>index.php?page=admin&action=gererclient&type=delete&id=<?php echo $row[''.$champ_email.'']; ?>" onclick="return confirm('<?php echo $translate->_('Voulez-vous vraiment supprimer ce client ?'); ?>');" title="Supprimer">
							<img src="<?php echo $basePath; ?>public/images/b_drop.png" alt="delete"/>
						</a>
					</td>
				</tr>
				<?php
			}
		}
	?>
	<tr>
		<th colspan=8><?php $pagination->printPagesLinks($basePath.'index.php?page=admin&action=gererclient', true); ?></th>
		
	</tr>
</table>
<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>index.php?page=admin&action=administration"><?php echo $translate->_('Retour');?></a>
</p>

<?php

} elseif (isset($_GET['action2']) && ($_GET['action2'] == "result_recherche_avancee_2")){

?>

<table class="liste">
	<tr>
		<th><?php echo $translate->_('Email'); ?></th>
		<th><?php echo $translate->_('Indice'); ?></th>
		<th><?php echo $translate->_('Raison social'); ?></th>
		<th><?php echo $translate->_('Nom'); ?></th>
		<th><?php echo $translate->_('Adresse'); ?></th>
		<th><?php echo $translate->_('Code postal'); ?></th>
		<th><?php echo $translate->_('Ville'); ?></th>
		<th><?php echo $translate->_('Pays'); ?></th>
		<th><?php echo $translate->_('Actions'); ?></th>
	</tr>

	<?php

		if(isset($_GET['numpage']))
			$numpage = $_GET['numpage'];
		else
			$numpage = 1;
			
		//création d'un objet pour paginer la page
		$pagination = new Pagination(0, 30, 20, $numpage);
		
		$indice = $_GET['indice'];
		$result = $database->prepare("
		SELECT * FROM adresses_facturation
		INNER JOIN clients  
		ON emailclient = af_emailclient
		WHERE indiceclient = '$indice'
		order by af_raisonsocial
		") or die ("requete r1 invalid");
		$result->execute();
		
		while ($row = $result->fetch()) {
			$pagination->incremente();
			if($pagination->isTimeToRender())
			{
				?>
				<tr <?php echo (($pagination->getCount()%2)?'class="imp"':''); ?>>
					<td>
						<?php echo $row['af_emailclient']; ?>
					</td>
					<td>
						<?php
						
							$idcde = $row['af_emailclient'];
							$result5 = $database->prepare("SELECT * FROM clients WHERE emailclient = '$idcde'") or die ("requete r5 invalid");
							$result5->execute();
							
							while ($tab4 = $result5->fetch()) {
								if($tab4['indiceclient'] == "1"){
								echo '<div align="center"><img src="'.$basePath.'public/images/rond_vert.png" alt="vert"/></div>';
								}
								if($tab4['indiceclient'] == "2"){
								echo '<div align="center"><img src="'.$basePath.'public/images/rond_rouge.png" alt="rouge"/></div>';
								}
								if($tab4['indiceclient'] == "3"){
								echo '<div align="center"><img src="'.$basePath.'public/images/rond_noir.png" alt="noir"/></div>';
								}
								if($tab4['indiceclient'] == "4"){
								echo '<div align="center"><img src="'.$basePath.'public/images/medaille.png" alt="medaille"/></div>';
								}
								if($tab4['indiceclient'] == "5"){
								echo '<div align="center"><img src="'.$basePath.'public/images/pro.png" alt="pro"/></div>';
								}
								if($tab4['indiceclient'] == "9"){
								echo '<div align="center"><img src="'.$basePath.'public/images/rond_bleu.png" alt="pro"/></div>';
								}

}
						
						?>
					</td>
					<td>
						<?php echo $row['af_raisonsocial']; ?>
					</td>
					<td>
						<?php echo $row['nomclient']; ?>
					</td>
					<td>
						<?php echo $row['af_nomrue']; ?>
					</td>
					<td>
						<?php echo $row['af_codepostal']; ?>
					</td>
					<td>
						<?php echo $row['af_ville']; ?>
					</td>		
					<td>
						<?php echo $row['af_pays']; ?>
					</td>

					<td class="td_center">
						<a href="<?php echo $basePath; ?>index.php?page=admin&action=gererclient&type=modif&id=<?php echo $row['af_emailclient']; ?>" title="Modifier">
							<img src="<?php echo $basePath; ?>public/images/b_edit.png" alt="edit"/>
						</a>&nbsp;&nbsp;
						<a href="<?php echo $basePath; ?>index.php?page=admin&action=gererclient&type=delete&id=<?php echo $row['af_emailclient']; ?>" onclick="return confirm('<?php echo $translate->_('Voulez-vous vraiment supprimer ce client ?'); ?>');" title="Supprimer">
							<img src="<?php echo $basePath; ?>public/images/b_drop.png" alt="delete"/>
						</a>
					</td>
				</tr>
				<?php
			}
		}
	?>
	<tr>
	
		<th colspan=9><?php $pagination->printPagesLinks($basePath.'index.php?page=admin&action=gererclient', true); ?></th>
		
	</tr>
</table>
<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>index.php?page=admin&action=administration"><?php echo $translate->_('Retour');?></a>
</p>

<?php

} elseif (isset($_GET['action2']) && ($_GET['action2'] == "rechercher")){

?>

<table class="liste">
	<tr>
		<th><?php echo $translate->_('Email'); ?></th>
		<th><?php echo $translate->_('Indice'); ?></th>
		<th><?php echo $translate->_('Raison social'); ?></th>
		<th><?php echo $translate->_('Nom'); ?></th>
		<th><?php echo $translate->_('Adresse'); ?></th>
		<th><?php echo $translate->_('Code postal'); ?></th>
		<th><?php echo $translate->_('Ville'); ?></th>
		<th><?php echo $translate->_('Pays'); ?></th>
		<th><?php echo $translate->_('Actions'); ?></th>
	</tr>

	<?php

		if(isset($_GET['numpage']))
			$numpage = $_GET['numpage'];
		else
			$numpage = 1;
			
		//création d'un objet pour paginer la page
		$pagination = new Pagination(0, 30, 20, $numpage);
		
		$recherche = $_GET['recherche'];
		$result = $database->prepare("
		SELECT * FROM adresses_facturation
		INNER JOIN clients  
		ON emailclient = af_emailclient
		WHERE UPPER(af_emailclient) LIKE UPPER('%".$recherche."%')
		OR UPPER(af_raisonsocial) LIKE UPPER('%".$recherche."%')
		OR UPPER(af_codepostal) LIKE UPPER('%".$recherche."%')
		OR UPPER(af_ville) LIKE UPPER('%".$recherche."%')
		OR UPPER(af_pays) LIKE UPPER('%".$recherche."%')
		OR UPPER(emailclient) LIKE UPPER('%".$recherche."%')
		OR UPPER(nomclient) LIKE UPPER('%".$recherche."%')
		OR UPPER(prenomclient) LIKE UPPER('%".$recherche."%')
		order by af_raisonsocial
		") or die ("requete r1 invalid");
		$result->execute();
		
		while ($row = $result->fetch()) {
			$pagination->incremente();
			if($pagination->isTimeToRender())
			{
				?>
				<tr <?php echo (($pagination->getCount()%2)?'class="imp"':''); ?>>
					<td>
						<?php echo $row['af_emailclient']; ?>
					</td>
					<td>
						<?php
						
							$idcde = $row['af_emailclient'];
							$result5 = $database->prepare("SELECT * FROM clients WHERE emailclient = '$idcde'") or die ("requete r5 invalid");
							$result5->execute();
							
							while ($tab4 = $result5->fetch()) {
								if($tab4['indiceclient'] == "1"){
								echo '<div align="center"><img src="'.$basePath.'public/images/rond_vert.png" alt="vert"/></div>';
								}
								if($tab4['indiceclient'] == "2"){
								echo '<div align="center"><img src="'.$basePath.'public/images/rond_rouge.png" alt="rouge"/></div>';
								}
								if($tab4['indiceclient'] == "3"){
								echo '<div align="center"><img src="'.$basePath.'public/images/rond_noir.png" alt="noir"/></div>';
								}
								if($tab4['indiceclient'] == "4"){
								echo '<div align="center"><img src="'.$basePath.'public/images/medaille.png" alt="medaille"/></div>';
								}
								if($tab4['indiceclient'] == "5"){
								echo '<div align="center"><img src="'.$basePath.'public/images/pro.png" alt="pro"/></div>';
								}
								if($tab4['indiceclient'] == "9"){
								echo '<div align="center"><img src="'.$basePath.'public/images/rond_bleu.png" alt="pro"/></div>';
								}
}
						
						?>
					</td>
					<td>
						<?php echo $row['af_raisonsocial']; ?>
					</td>
					<td>
						<?php echo $row['nomclient']; ?>
					</td>
					<td>
						<?php echo $row['af_nomrue']; ?>
					</td>
					<td>
						<?php echo $row['af_codepostal']; ?>
					</td>
					<td>
						<?php echo $row['af_ville']; ?>
					</td>		
					<td>
						<?php echo $row['af_pays']; ?>
					</td>

					<td class="td_center">
						<a href="<?php echo $basePath; ?>index.php?page=admin&action=gererclient&type=modif&id=<?php echo $row['af_emailclient']; ?>" title="Modifier">
							<img src="<?php echo $basePath; ?>public/images/b_edit.png" alt="edit"/>
						</a>&nbsp;&nbsp;
						<a href="<?php echo $basePath; ?>index.php?page=admin&action=gererclient&type=delete&id=<?php echo $row['af_emailclient']; ?>" onclick="return confirm('<?php echo $translate->_('Voulez-vous vraiment supprimer ce client ?'); ?>');" title="Supprimer">
							<img src="<?php echo $basePath; ?>public/images/b_drop.png" alt="delete"/>
						</a>
					</td>
				</tr>
				<?php
			}
		}
	?>
	<tr>
		<th colspan=9><?php $pagination->printPagesLinks($basePath.'index.php?page=admin&action=gererclient&action2=rechercher&recherche='.$recherche.'', true); ?></th>
		
	</tr>
</table>
<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>index.php?page=admin&action=administration"><?php echo $translate->_('Retour');?></a>
</p>

<?php

}

*/
} else {
?>

<table style="margin-left: 10px;">
	<tr>
		<td><a href="<?php echo $basePath;?>index.php?page=admin&action=gererclient&type=ajout"><img src="<?php echo $basePath; ?>public/images/clients_modifier.jpg"/></a></td>
	</tr>
	<tr>
		<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererclient&ajout=1">Ajout</a></td>
	</tr>
</table>

<table class="liste">
	<tr>
		<th><?php echo $translate->_('Email'); ?></th>
		<th><?php echo $translate->_('Indice'); ?></th>
		<th><?php echo $translate->_('Nom'); ?></th>
		<th><?php echo $translate->_('Prénom'); ?></th>
		<th><?php echo $translate->_('Adresse'); ?></th>
		<th><?php echo $translate->_('Actions'); ?></th>
	</tr>
	<?php
	
		if(isset($_GET['numpage']))
			$numpage = $_GET['numpage'];
		else
			$numpage = 1;
			
		//création d'un objet pour paginer la page
		$pagination = new Pagination(0, 10, 20, $numpage);
		
		$clients = ClientDB::getClientsList();
		
		foreach($clients as $c) {
			$pagination->incremente();
			if($pagination->isTimeToRender())
			{
				?>
				<tr <?php echo (($pagination->getCount()%2)?'class="imp"':''); ?>>
					<td>
						<?php echo $c->getEmail(); ?>
					</td>
					<td>
						<?php 
							$idcde = $c->getEmail();
							$result4 = $database->prepare("SELECT * FROM clients WHERE emailclient = '$idcde'") or die ("requete r4 invalid");
							$result4->execute();
							
							while ($tab4 = $result4->fetch()) {
								if($tab4['indiceclient'] == "1"){
								echo '<div align="center"><img src="'.$basePath.'public/images/rond_vert.png" alt="vert"/></div>';
								}
								if($tab4['indiceclient'] == "2"){
								echo '<div align="center"><img src="'.$basePath.'public/images/rond_rouge.png" alt="rouge"/></div>';
								}
								if($tab4['indiceclient'] == "3"){
								echo '<div align="center"><img src="'.$basePath.'public/images/rond_noir.png" alt="noir"/></div>';
								}
								if($tab4['indiceclient'] == "4"){
								echo '<div align="center"><img src="'.$basePath.'public/images/medaille.png" alt="medaille"/></div>';
								}
								if($tab4['indiceclient'] == "5"){
								echo '<div align="center"><img src="'.$basePath.'public/images/pro.png" alt="pro"/></div>';
								}
								if($tab4['indiceclient'] == "6"){
								echo '<div align="center"><img src="'.$basePath.'public/images/garage.png" alt="garage"/></div>';
								}
								if($tab4['indiceclient'] == "9"){
								echo '<div align="center"><img src="'.$basePath.'public/images/rond_bleu.png" alt="bleu"/></div>';
								}
							}
						?>
					</td>
					<td>
						<?php echo $c->getNom(); ?>
					</td>
					<td>
						<?php echo $c->getPrenom(); ?>
					</td>
					<td>
						<?php 
						
							$result3 = $database->prepare("SELECT * FROM adresses_facturation WHERE af_emailclient = '$idcde'") or die ("requete r3 invalid");
							$result3->execute();
							
							while ($tab3 = $result3->fetch()) {
							echo $tab3['af_codepostal'];
							echo " ";
							echo $tab3['af_ville'];
							}
						
						?>
					</td>
					<td class="td_center">
						<a href="<?php echo $basePath; ?>index.php?page=admin&action=gererclient&type=modif&id=<?php echo $c->getEmail(); ?>" title="Modifier">
							<img src="<?php echo $basePath; ?>public/images/b_edit.png" alt="edit"/>
						</a>&nbsp;&nbsp;
						<a href="<?php echo $basePath; ?>index.php?page=admin&action=gererclient&type=delete&id=<?php echo $c->getEmail(); ?>" onclick="return confirm('<?php echo $translate->_('Voulez-vous vraiment supprimer ce client ?'); ?>');" title="Supprimer">
							<img src="<?php echo $basePath; ?>public/images/b_drop.png" alt="delete"/>
						</a>
					</td>
				</tr>
				<?php
			}
		}
	?>
	<tr>
		<th colspan=6><?php $pagination->printPagesLinks($basePath.'index.php?page=admin&action=gererclient', true); ?></th>
	</tr>
</table>
<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>index.php?page=admin&action=administration"><?php echo $translate->_('Retour');?></a>
</p>
<?php
}
?>