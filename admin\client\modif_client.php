<?php
require_once(dirname(__FILE__).'/../../class/client_class.php');
require_once(dirname(__FILE__).'/../../class/adresse_class.php');
require_once(dirname(__FILE__).'/../../class/commande_static_class.php');
if(isset($_POST['superadmin'])){

$superpw = md5($_POST['superpw']);
$superuser = $_POST['superuser'];

$result1 = $database->prepare("SELECT COUNT(*) AS superadminok FROM clients WHERE emailclient = '$superuser' AND passwdclient = '$superpw' AND isadmin = '2'") or die ("requete r1 invalid");
$result1->execute();
$superadmin = $result1->fetch();
$superadminok = $superadmin['superadminok'];
$_SESSION['superadmin'] = $superadmin['superadminok'];

//////// AFFICHE LE FORMULAIRE DU SUPER ADMIN //////////

}

	if(isset($_GET['id']) || isset($_POST['email']) || isset($_GET['valuetosearch'])):
		if(isset($_GET['id'])){
		$id = $_GET['id'];
		}
		if(isset($_POST['email'])){
		$id = $_POST['email'];
		}
		if(isset($_GET['valuetosearch'])){
		$valuetosearch = strtoupper($_GET['valuetosearch']);
		$result9 = $database->prepare("
		SELECT * FROM clients WHERE UCASE(emailclient) LIKE '%$valuetosearch%' OR UCASE(nomclient) LIKE '%$valuetosearch%' OR UCASE(prenomclient) LIKE '%$valuetosearch%'
		") or die ("requete r2 invalid");
		$result9->execute();
		$tab9 = $result9->fetch();
		$id = $tab9['emailclient'];
		}
		$client = clientDB::getClientByEmail($id);
		$info = "";
		$result = $database->prepare("SELECT * FROM adresses_facturation WHERE af_emailclient='".$id."'") or die ("r1 invalid");
		$result->execute();
		$af = $result->fetch();
		$result7 = $database->prepare("SELECT * FROM adresses WHERE emailclient='".$id."'") or die ("r1 invalid");
		$result7->execute();
		$liv = $result7->fetch();
		$result2 = $database->prepare("SELECT * FROM clients WHERE emailclient='".$id."'") or die ("r1 invalid");
		$result2->execute();
		$cli = $result2->fetch();
		
	if(isset($_GET['nd_del'])){
			$nd_del = $_GET['nd_del'];
			$delete_nd = $database->query("DELETE FROM non_dispo WHERE id_nd = '$nd_del'") or die ("delete_nd invalid");
	}
	
	if(isset($_POST['password'])) {
	$password = md5($_POST['password']);
	$database->query("UPDATE clients SET passwdclient = '$password' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
	$info = "<div class='valide messageBox'><p>Le mot de passe a été ré-initialisé !</p></div>";
	}
		
		if(isset($_POST['submit'])):
		
				function stripAccents($str, $charset='utf-8')
				{
					$str = htmlentities($str, ENT_NOQUOTES, $charset);
					
					$str = preg_replace('#&([A-za-z])(?:acute|cedil|circ|grave|orn|ring|slash|th|tilde|uml);#', '\1', $str);
					$str = preg_replace('#&([A-za-z]{2})(?:lig);#', '\1', $str); // pour les ligatures e.g. '&oelig;'
					$str = preg_replace('#&[^;]+;#', '', $str); // supprime les autres caractères
					
					return $str;
				}

				function delSpace($str2)
				{

					$str2 = preg_replace('#&[^;]+;-_.#', '', $str2); // supprime les caractères speciaux
					$str2 = preg_replace('# #', '', $str2); // supprime les espaces
					
					return $str2;

				}
			// traitement du formulaire
			if($_POST['nom'] != '' && $_POST['prenom'] != ''){
				// seul les champs nom et prenom sont obligatoire
				$nbChampModif = 0;
				$nb_ad_fact_modif = 0;
				if($_POST['nom'] != $client->getNom()) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$nom = strtoupper(stripAccents($_POST['nom']));
					$database->query("UPDATE clients SET nomclient = '$nom' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if(isset($_POST['password'])) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$password = md5($_POST['password']);
					$database->query("UPDATE clients SET passwdclient = '$password' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($_POST['prenom'] != $client->getPrenom()) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$prenom = strtolower(stripAccents($_POST['prenom']));
					$database->query("UPDATE clients SET prenomclient = '$prenom' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($_POST['fax'] != $cli['faxclient']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$fax = delSpace($_POST['fax']);
					$database->query("UPDATE clients SET faxclient = '$fax' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($_POST['telephone'] != $cli['telclient']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$telephone = delSpace($_POST['telephone']);
					$database->query("UPDATE clients SET telclient = '$telephone' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($_POST['infos'] != $cli['infos']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$infos = stripAccents($_POST['infos']);
					$database->query("UPDATE clients SET infos = '$infos' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($_POST['indice'] != $cli['indiceclient']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$indice = $_POST['indice'];
					$database->query("UPDATE clients SET indiceclient = '$indice' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($_POST['email_secondaire'] != $cli['email_secondaire']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$email_secondaire = $_POST['email_secondaire'];
					$database->query("UPDATE clients SET email_secondaire = '$email_secondaire' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if(isset($_POST['vehicule']) && $_POST['vehicule'] != $cli['vehiculeprefer']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$vehicule = $_POST['vehicule'];
					$database->query("UPDATE clients SET vehiculeprefer = '$vehicule' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($_POST['datecreate'] != $cli['datecreate']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$datecreate = $_POST['datecreate'];
					$database->query("UPDATE clients SET datecreate = '$datecreate' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($_POST['tva_intra_com'] != $cli['tva_intra_com']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$tva_intra_com = stripAccents($_POST['tva_intra_com']);
					$database->query("UPDATE clients SET tva_intra_com = '$tva_intra_com' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($_POST['code_promo'] != $cli['code_promo']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$code_promo = $_POST['code_promo'];
					$database->query("UPDATE clients SET code_promo = '$code_promo' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if(isset($_POST['lettre_T'])) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$lettre_T = 1;
					$database->query("UPDATE clients SET lettre_T = '$lettre_T' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
				} else {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$lettre_T = 0;
					$database->query("UPDATE clients SET lettre_T = '$lettre_T' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($_POST['af_raisonsocial'] != $af['af_raisonsocial']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$af_raisonsocial = stripAccents($_POST['af_raisonsocial']);
					$database->query("UPDATE adresses_facturation SET af_raisonsocial = '$af_raisonsocial' WHERE af_emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($_POST['af_nomrue'] != $af['af_nomrue']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$af_nomrue = strtolower(stripAccents($_POST['af_nomrue']));
					$database->query("UPDATE adresses_facturation SET af_nomrue = '$af_nomrue' WHERE af_emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($_POST['af_codepostal'] != $af['af_codepostal']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$af_codepostal = delSpace($_POST['af_codepostal']);
					$database->query("UPDATE adresses_facturation SET af_codepostal = '$af_codepostal' WHERE af_emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($_POST['af_ville'] != $af['af_ville']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$af_ville = strtoupper(stripAccents($_POST['af_ville']));
					$database->query("UPDATE adresses_facturation SET af_ville = '$af_ville' WHERE af_emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($_POST['af_pays'] != $af['af_pays']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$af_pays = $_POST['af_pays'];
					$database->query("UPDATE adresses_facturation SET af_pays = '$af_pays' WHERE af_emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if(isset($_POST['raisonsocial']) && $_POST['raisonsocial'] != $liv['raisonsocial']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$raisonsocial = stripAccents($_POST['raisonsocial']);
					$database->query("UPDATE adresses SET raisonsocial = '$raisonsocial' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if(isset($_POST['nomrue']) && $_POST['nomrue'] != $liv['nomrue']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$nomrue = strtolower(stripAccents($_POST['nomrue']));
					$database->query("UPDATE adresses SET nomrue = '$nomrue' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if(isset($_POST['raisonsocial']) && $_POST['codepostal'] != $liv['codepostal']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$codepostal = delSpace($_POST['codepostal']);
					$database->query("UPDATE adresses SET codepostal = '$codepostal' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if(isset($_POST['ville']) && $_POST['ville'] != $liv['ville']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$ville = strtoupper(stripAccents($_POST['ville']));
					$database->query("UPDATE adresses SET ville = '$ville' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if(isset($_POST['pays']) && $_POST['pays'] != $liv['pays']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$pays = $_POST['pays'];
					$database->query("UPDATE adresses SET pays = '$pays' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($nbChampModif != 0) {
					if($nb_ad_fact_modif != 0){
						// sauvegarde ok
						// on affiche un message comme quoi le client a bien été mis à jour
						$info = "<div class='valide messageBox'><p>Les informations du client ont bien été mis à jour.</p></div>";
					} else {
						// echec de la mise à jour
						$info = "<div class='error messageBox'>
									<p>Erreur lors de la mise a jour, Une des information saisie est incorrecte</p>
								</div>";
					}
				} else {
					$info = "<div class='info messageBox'><p>Les informations du client n'ont pas été mis à jour car vous n'avez modifié aucun champs</p></div>";
				}
			} else {
				$info = "<div class='error messageBox'><p>Des champs obligatoire n'ont pas été renseigné</p></div>";
			}
		endif;
		
if($info != ''){
 echo $info; 
}
						
$result5 = $database->prepare("SELECT * FROM clients WHERE emailclient='".$id."'") or die ("r4 invalid");
$result5->execute();
						
while ($tab4 = $result5->fetch()) {	

$result4 = $database->prepare("SELECT * FROM adresses_facturation WHERE af_emailclient='".$id."'") or die ("r4 invalid");
$result4->execute();
						
while ($tab3 = $result4->fetch()) {

?>
<form action="<?php echo $basePath; ?>index.php?page=admin&action=gererclient&type=modif&id=<?php echo $id; ?>" method="post">

<div class="form-style-10">	
<h1 style="padding-top:8px; padding-bottom:22px;" >Modifier un client</h1>
   <div class="section"><span>¤</span>INFO PRINCIPAL</div>
    <table>
    	<tr>
        <td>	
<div class="inner-wrap" style="width:350px; height:550px;">
<table>
  <tr>
    <td><label>E-mail <a href="mailto:<?php echo $tab4['emailclient']; ?>" ><img src="<?php echo $basePath; ?>public/images/email.png"/></a> : </label>
    <input type="text" name="email" size="25" disabled="disabled" value= "<?php echo $tab4['emailclient']; ?>"/></td>
</tr>
  <tr>
    <td><label>Nom : </label>
    <input type="text" name="nom" size="50" value= "<?php echo $tab4['nomclient']; ?>"/></td>
  </tr>
  <tr>
    <td><label>Prénom : </label>
    <input type="text" name="prenom" size="6" value= "<?php echo $tab4['prenomclient']; ?>"/></td>
 </tr>
  <tr>
    <td><label>Téléphone : </label>
    <input type="text" name="telephone" size="20" value= "<?php echo $tab4['telclient']; ?>"/></td>
  </tr>
  <tr>
    <td><label>Fax : </label>
    <input type="text" name="fax" size="20" value= "<?php echo $tab4['faxclient']; ?>"/></td>
  </tr>
  <tr>
    <td><label>Commentaire : </label>
	<textarea style="font-size:11px;" name="infos" rows="10" cols="25"><?php echo $tab4['infos']; ?></textarea></td>
  </tr>  
</table>
     </td>
     <td>
</div>	 
<div class="inner-wrap" style="width:350px; height:550px;">	 
<table>
	<tr>
		<td><label>E-mail secondaire : </label>
		<input type="text" name="email_secondaire" size="20" value= "<?php echo $tab4['email_secondaire']; ?>"/></td>
	</tr>
	<tr>
		<td><label>Lettre T :
		<input type="checkbox" style="width:14px; margin-left:20px;" 
		<?php
									if ($tab4['lettre_T'] == "1"){
									echo 'checked="checked"';
									}
		?>
		
		>
		
		</label>
	</tr>
	<tr>		
		<td><label>Numero TVA Intra-Communautaire : </label>
		<input type="text" name="tva_intra_com" size="20" value= "<?php echo $tab4['tva_intra_com']; ?>"/></td>
	</tr>
	<tr>		
		<td><label>Vehicule préféré : </label>

			<table style="width:325px; text-align:center;" >
			  <tr>
				<td>Jeep</td>
				<td><input style="width:14px; margin-left:10px;" type="radio" name="vehicule" <?php if ($tab4['vehiculeprefer'] == "jeep") echo "CHECKED" ?> value= "jeep"/></td>
				<td>R2087</td>
				<td><input style="width:14px; margin-left:10px;" type="radio" name="vehicule" <?php if ($tab4['vehiculeprefer'] == "r2087") echo "CHECKED" ?> value= "r2087"/></td>
			  </tr>
			  <tr>
				<td>Dodge</td>
				<td><input style="width:14px; margin-left:10px;" type="radio" name="vehicule" <?php if ($tab4['vehiculeprefer'] == "dodge") echo "CHECKED" ?> value= "dodge"/></td>
				<td>Blindé</td>
				<td><input style="width:14px; margin-left:10px;" type="radio" name="vehicule" <?php if ($tab4['vehiculeprefer'] == "blinde") echo "CHECKED"?> value= "blinde"/></td>
			  </tr>
			  <tr>
				<td>GMC</td>
				<td><input style="width:14px; margin-left:10px;" type="radio" name="vehicule" <?php if ($tab4['vehiculeprefer'] == "gmc") echo "CHECKED" ?> value= "gmc"/></td>
				<td>Autre</td>
				<td><input style="width:14px; margin-left:10px;" type="radio" name="vehicule" <?php if ($tab4['vehiculeprefer'] == "autre") echo "CHECKED" ?> value= "autre"/></td>
			  </tr>
			</table>
		</td>
	</tr>
	<tr>			
			<td><label>Indice : </label>	
		    <table style="width : 350px; text-align:center;">
			<tr>
        
					<?php
						if($tab4['indiceclient'] == "1"){
							echo '<td><img src="'.$basePath.'public/images/rond_vert.png" alt="vert"/></td>'; 
							echo '<td style="height:35px;" ><input style="width:14px;" type="radio" CHECKED name="indice" value= "1"/></td>';
							echo '<td><span style="color:green; font-weight:bold;">R.A.S.</span></td>';
						} else {
							echo '<td><img src="'.$basePath.'public/images/rond_vert.png" alt="vert"/></td>'; 							
							echo '<td><input style="width:14px; " type="radio" name="indice" value= "1"/></td>';
							echo '<td>R.A.S.</td>';
						}
					?>	    

					<?php
						if($tab4['indiceclient'] == "4"){
							echo '<td><img src="'.$basePath.'public/images/medaille.png" alt="medaille"/></td>'; 	
							echo '<td style="height:35px;"><input style="width:14px; " type="radio" CHECKED name="indice" value= "4"/></td>';
							echo '<td><span style="color:blue; font-weight:bold;">Bon client</span></td>';
						} else {
							echo '<td><img src="'.$basePath.'public/images/medaille.png" alt="medaille"/></td>'; 								
							echo '<td><input style="width:14px; " type="radio" name="indice" value= "4"/></td>';
							echo '<td>Bon client</td>';
						}
					?>	
				 </tr>
				 <tr> 
					<?php
						if($tab4['indiceclient'] == "2"){
							echo '<td><img src="'.$basePath.'public/images/rond_rouge.png" alt="rond_rouge"/></td>';
							echo '<td><input style="width:14px; " type="radio" CHECKED name="indice" value= "2"/></td>';
							echo '<td><span style="color:red; font-weight:bold;">A surveiller</span><br /><span style="font-style:italique; font-size:11px;">(Pb SAV)</span></td>';
						} else {
							echo '<td><img src="'.$basePath.'public/images/rond_rouge.png" alt="rond_rouge"/></td>';
							echo '<td><input style="width:14px; " type="radio" name="indice" value= "2"/></td>';
							echo '<td>A surveiller<br /><span style="font-style:italique; font-size:11px;">(Pb SAV)</span></td>';
						}

						if($tab4['indiceclient'] == "5"){
							if(isset($_SESSION['superadmin']) && $_SESSION['superadmin'] == "1"){
								echo '<td><img src="'.$basePath.'public/images/pro.png" alt="pro"/></td>';
								echo '<td><input style="width:14px; " type="radio" CHECKED name="indice" value= "5"/></td>';
								echo '<td><span style="color:orange; font-weight:bold;">Revendeur</span><br /><span style="font-style:italique; font-size:10px;">(MAX : -50% NOS / -20% NEW)</span></td>';
							} else {
								echo '<td><img src="'.$basePath.'public/images/pro.png" alt="pro"/></td>';
								echo '<td><img src="'.$basePath.'public/images/accept.png" alt="pro"/></td>';
								echo '<td><span style="color:orange; font-weight:bold;">Revendeur</span><br /><span style="font-style:italique; font-size:10px;">(MAX : -50% NOS / -20% NEW)</span></td>';
							}
						} else {
							if(isset($_SESSION['superadmin']) && $_SESSION['superadmin'] == "1"){
								echo '<td><img src="'.$basePath.'public/images/pro.png" alt="pro"/></td>';
								echo '<td><input style="width:14px; " type="radio" name="indice" value= "5"/></td>';
								echo '<td>Revendeur<br /><span style="font-style:italique; font-size:10px;">(MAX : -50% NOS / -20% NEW)</span></td>';
							} else {
								echo '<td><img src="'.$basePath.'public/images/pro.png" alt="pro"/></td>';
								echo '<td><a href="#oModalSuperADM" ><img src="'.$basePath.'public/images/rond_vide.jpg"/></a></td>';
								echo '<td>Revendeur<br /><span style="font-style:italique; font-size:10px;">(MAX : -50% NOS / -20% NEW)</span></td>';
							}
						}
					?>
				  </tr>

				  <tr>
					

					<?php
						if($tab4['indiceclient'] == "3"){
							echo '<td><img src="'.$basePath.'public/images/rond_noir.png" alt="rond_noir"/></td>';
							echo '<td><input style="width:14px; " type="radio" CHECKED name="indice" value= "3"/></td>';
							echo '<td><span style="color:black; font-weight:bold;">Ne pas servir</span><br /><span style="font-style:italique; font-size:11px;">(Pb paiement)</span><img src="'.$basePath.'public/images/attention.png" alt="attention"/></td>';
						} else {
							echo '<td><img src="'.$basePath.'public/images/rond_noir.png" alt="rond_noir"/></td>';
							echo '<td><input style="width:14px; " type="radio" name="indice" value= "3"/></td>';
							echo '<td>Ne pas servir<br /><span style="font-style:italique; font-size:11px;">(Pb paiement)</span></td>';
						}
						
						if($tab4['indiceclient'] == "6"){
							echo '<td><img src="'.$basePath.'public/images/garage.png" alt="garage"/></td>';
							echo '<td><input style="width:14px; " type="radio" CHECKED name="indice" value= "6"/></td>';
							echo '<td><span style="color:orange; font-weight:bold;">Garage</span><br /><span style="font-style:italique; font-size:10px;">(MAX : -30% NOS / -10% NEW)</span></td>';
						} else {
							echo '<td><img src="'.$basePath.'public/images/garage.png" alt="garage"/></td>';
							echo '<td><input style="width:14px; " type="radio" name="indice" value= "6"/></td>';
							echo '<td>Garage<br /><span style="font-style:italique; font-size:10px;">(MAX : -30% NOS / -10% NEW)</span></td>';
						}
						
					?>
					</tr>
									  <tr>
					

					<?php
						if($tab4['indiceclient'] == "3"){
							echo '<td></td>';
							echo '<td></td>';
							echo '<td></td>';
						} else {
							echo '<td></td>';
							echo '<td></td>';
							echo '<td></td>';
						}
				
						if($tab4['indiceclient'] == "7"){
							echo '<td><img width="14px;" src="'.$basePath.'public/images/zzz.png" alt="inactif"/></td>';
							echo '<td><input style="width:14px; " type="radio" CHECKED name="indice" value= "7"/></td>';
							echo '<td><span style="color:Grey; font-weight:bold;">Inactif</span></td>';
						} else {
							echo '<td><img width="14px;" src="'.$basePath.'public/images/zzz.png" alt="inactif"/></td>';
							echo '<td><input style="width:14px; " type="radio" name="indice" value= "7"/></td>';
							echo '<td>Inactif</td>';
						}
						
					?>
					</tr>
					<tr>
										<?php
						if($tab4['indiceclient'] == "9"){
							echo '<td><img src="'.$basePath.'public/images/rond_bleu.png" alt="bleu"/></td>'; 	
							echo '<td style="height:35px;"><input style="width:14px; " type="radio" CHECKED name="indice" value= "9"/></td>';
							echo '<td><span style="color:blue; font-weight:bold;">Avoir?</span></td>';
						} else {
							echo '<td><img src="'.$basePath.'public/images/rond_bleu.png" alt="bleu"/></td>'; 								
							echo '<td><input style="width:14px; " type="radio" name="indice" value= "9"/></td>';
							echo '<td>Avoir?</td>';
						}
					?>		
					</tr>
					
				</table>
			</td>
		</tr>
		<tr>
			<td><label>Code promo : </label>
			<input type="text" name="code_promo" size="20" value= "<?php echo $tab4['code_promo']; ?>"/></td>
		</tr>
		<tr>
		    <td><label>Date création client : </label>
			<?php
			if ($tab4['datecreate'] == null){
			
			$datecreate = null;
			
			} else {
			
			$createdate = explode("-",$tab4['datecreate']); 
			$datecreate = $createdate[2]."/".$createdate[1]."/".$createdate[0];
			
			}
			
			?>
			<input type="hidden" name="datecreate" size="20" value= "<?php echo $datecreate; ?>"/>
			<?php echo $datecreate; ?>
			<tr>
			</td>
			<td>
			<label>Solde <b>(Ajout à valider)</b></label>
			</td>
			<tr><td>
			<?php
				$sql001 = $database->prepare("SELECT SUM(commande_montantotalttc) AS TotalCde FROM static_commandes WHERE client_email='".$id."'") or die ("sql001 invalid");
				$sql001->execute();
				$sql001Result = $sql001->fetch();
				$sql002 = $database->prepare("select SUM(ttc) AS ttc, sum(trop_percu) AS TropPerçu, sum(remboursement) AS Remboursement from facture WHERE emailclient='".$id."'") or die ("sql002 invalid");
				$sql002->execute();
				$sql002Result = $sql002->fetch();				
//				echo 'Montants : ' . number_format($sql001Result['TotalCde'], 2) . '€ / ' . number_format($sql002Result['ttc'], 2) . '€ / ' . number_format($sql002Result['TropPerçu'], 2) . '€ / ' . number_format($sql002Result['Remboursement'], 2).'€';			
				echo number_format( $sql001Result['TotalCde'] - $sql002Result['ttc'] - $sql002Result['TropPerçu'] - $sql002Result['Remboursement'], 2) .'€';
			?>
			</td></tr>
			</tr>
		</tr>
  </table>
</td>
</tr>	
</table>  

<div class="section"><span>¤</span>Adresses</div>

<table>
<tr>
<td>

<div class="inner-wrap" style="width:350px; height:280px;">	
<h1 style="padding-top:8px; padding-bottom:22px;" >Facturation</h1>  
<table>  
  <tr>
    <td style="text-align:right;"><label>Raison social :</label></td>
    <td style="width:258px;"><input type="text" name="af_raisonsocial" size="25" value= "<?php echo $tab3['af_raisonsocial']; ?>"/></td>
</tr>
  <tr>
    <td style="text-align:right;"><label>Rue :</label></td>
    <td><input type="text" name="af_nomrue" size="50" value= "<?php echo $tab3['af_nomrue']; ?>"/></td>
  </tr>
  <tr>
    <td style="text-align:right;"><label>Code postal :</label></td>
    <td><input type="text" name="af_codepostal" size="6" value= "<?php echo $tab3['af_codepostal']; ?>"/></td>
  </tr>
  <tr>
    <td style="text-align:right;"><label>Ville :</label></td>
    <td><input type="text" name="af_ville" size="20" value= "<?php echo $tab3['af_ville']; ?>"/></td>
 </tr>
  <tr>
    <td style="text-align:right;"><label>Pays :</label></td>
    <td><select name="af_pays">';
	<?php
								$result8 = $database->prepare("SELECT * FROM pays order by nom_pays ASC") or die ("requete r1 invalid");
								$result8->execute();
								echo '<option value="'.$tab3['af_pays'].'" selected="selected">'.$tab3['af_pays'].'</option>';
								while ($tab7 = $result8->fetch()) {
								
									echo '<option style="width: 225px;" value="'.$tab7['nom_pays'].'">'.$tab7['nom_pays'].'</option>';
								
									}
								
								echo '</select>';
	?>
	</td>
  </tr>
</table>
</div>


</td><td>


<?php
}
?>
			

<?php
}

$result5 = $database->prepare("SELECT * FROM adresses WHERE emailclient ='$id' order by idadresse DESC LIMIT 1") or die ("r5 invalid");
$result5->execute();

$result6 = $database->prepare("SELECT * FROM pays order by nom_pays ASC") or die ("requete r1 invalid");
$result6->execute();

?>
<div class="inner-wrap" style="width:350px; height:280px;">	 
<h1 style="padding-top:8px; padding-bottom:22px;" >Livraison</h1>
<table>
<?php
while ($tab4 = $result5->fetch()) {
?>	
<tr>
	<td style="text-align:right;">
	<label for="Raison social">Raison social : </label>
	</td><td style="width:258px;">
	<input type="text" name="raisonsocial" size="25" value= "<?php echo $tab4['raisonsocial']; ?>"/>
	</td>
</tr>
<tr>
	<td style="text-align:right;">
	<label for="Adresse">Rue : </label>
	</td><td>
	<input type="text" name="nomrue" size="50" value= "<?php echo $tab4['nomrue']; ?>"/>
	</td>
</tr>
<tr>
	<td style="text-align:right;">	
	<label for="Code postal">Code postal : </label>
	</td><td>
	<input type="text" name="codepostal" size="6" value= "<?php echo $tab4['codepostal']; ?>"/>
	</td>
</tr>
<tr>
	<td style="text-align:right;" >
	<label for="Ville">Ville : </label>
	</td><td>
	<input type="text" name="ville" size="20" value= "<?php echo $tab4['ville']; ?>"/>
	</td>
</tr>
<tr>
	<td style="text-align:right;" >	
	<label for="Pays">Pays : </label>
	</td><td>
	<select name="pays">
	<option value="<?php echo $tab4['pays']; ?>" selected="selected"><?php echo $tab4['pays']; ?></option>';
<?php		
	while ($tab5 = $result6->fetch()) {
?>								
			<option value="<?php echo $tab5['nom_pays']; ?>"><?php echo $tab5['nom_pays']; ?></option>';
<?php									
		}	
	}	
?>
	</td>
</tr>
</table>
</div>

</td>
</tr>
</table>


<div class="button-section">
      <input type="submit" name="submit" value="Modifier" />
</div>	

</div>
</form>	

	
<form action="<?php echo $basePath; ?>index.php?page=admin&action=gererclient&type=modif&id=<?php echo $id; ?>" method="post">
<div class="form-style-10">
	<div align="center" style="font-size: 20px; font-weight: bold;" >Ré-initialiser le mot de passe par défaut !</div>
	<input type="hidden" name="password" size="25" value= "000000"/>
	<br />
	<div class="button-section" style="margin:auto; text-align:center;">
		  <input type="submit" name="modifier" value="Ré-initialiser" />
	</div>
</form>	
</div>
<div class="form-style-10">	
<div class="section"><span>¤</span>Listes des commandes:</div>
			<?php
				$commandes = commande_staticDB::getCommandesByClient($client);
				if(count($commandes) == 0):
					?><p>Ce client n'a pas de commande pour le moment.</p><?php
				else:
					?>
					<table class="table-fill">
						<tr>
							<th>Numéro</th>
							<th>Date</th>
							<th>Montant TTC</th>
							<th>Statut</th>
							<th></th>
						</tr>
					
					<?php
					foreach ($commandes as $commande):
						?>
						<tr>
							<td class="text-center"><?php echo $commande->getId(); ?></td>
							<td class="text-center"><?php echo date('d/m/Y',strtotime($commande->getDate())); ?></td>
							<td class="text-right"><?php printf('%.2f €',$commande->getMontanttotalTTC()); ?></td>
							<td class="text-center"><?php echo $commande->getStatut(); ?></td>
							<td class="text-center">
								<a href="<?php echo $basePath; ?>index.php?page=admin&action=commandes&id=<?php echo $commande->getId(); ?>">
									<img src="<?php echo $basePath; ?>public/images/zoom.png" />
								</a>
							</td>
						</tr>
						<?php
					endforeach;
					?></table><?php
				endif;
			 ?>
		</div>	 
		<div class="form-style-10">	
		<div class="section"><span>¤</span>Listes des factures:</div>

			<?php
				if(count($commandes) == 0):
					?><p>Ce client n'a pas de facture pour le moment.</p><?php
				else:
					?>
					<table class="table-fill">
						<tr>

							<th>Numéro</th>
							<th>Date</th>
							<th>Montant HT</th>
							<th>Montant TTC</th>
							<th>Genre</th>
							<th></th>
						</tr>
					
					<?php
						$i=0;
						$j=0;
						$result9 = $database->prepare("SELECT * FROM facture WHERE emailclient ='$id' ORDER BY id_facture DESC") or die ("requete r9 invalid");
						$result9->execute();
						
						while ($tab8 = $result9->fetch()) {
						
						?>
						<tr>
							<td class="text-center"><?php echo $tab8['id_facture']; ?></td>
							<td class="text-center"><?php echo date('d/m/Y',strtotime($tab8['date_facture'])); ?></td>
							<td class="text-right"><?php printf('%.2f €',$tab8['totht']); ?></td>
							<td class="text-right"><?php printf('%.2f €',$tab8['ttc']); ?></td>
							<td class="text-center"><?php echo $tab8['genre']; ?></td>
							<td class="text-center">
								<a href="<?php echo $basePath; ?>client/commande/impression_facture.php?fact=<?php echo $tab8['id_facture']; ?>" target='_blank'>
									<img src="<?php echo $basePath; ?>public/images/zoom.png" />
								</a>
							</td>
						</tr>
						<?php
						$j+=$tab8['totht'];
						$i+=$tab8['ttc'];
						}

					?>
						<tr>
							<td style="border-top-style:solid;"></td>
							<td class="text-right" style="border-top-style:solid;">TOTAL : </td>
							<td class="text-center" style=" color: #FF6600; border-top-style:solid;"><strong><?php printf('%.2f €', $j); ?></strong></td>
							<td class="text-center" style="border-top-style:solid;"><strong><?php printf('%.2f €', $i); ?></strong></td>
							<td class="text-center" style="border-top-style:solid;"></td>
							<td style="border-top-style:solid;"></td>
						</tr>
					</table>
					<?php
				endif;
			 ?>
		</div>
		<div class="form-style-10">	
		<div class="section"><span>¤</span>Listes des Non-dispo:</div>
				<?php
				if(count($commandes) == 0):
					?><p>Ce client n'a pas de ND pour le moment.</p><?php
				else:
					?>
					<table class="table-fill" >
						<tr>
							<th></th>
							<th>Date</th>
							<th>Reference</th>
							<th>Groupe</th>
							<th>Designation</th>
							<th>Qté</th>
							<th>Prix</th>
							<th></th>
							<th></th>
							<th></th>
						</tr>
						<script type="text/javascript">
						//<![CDATA[
							function addtocart(produit, id) {
								qte = document.getElementById('p'+id).value;
								
								/*document.location = "<?php echo Zend_registry::get("basePath"); ?>index.php?page=panier&action=add&id="+produit+"&nb="+qte;*/
								<?php
									//$url64 = str_replace("=","_",base64_encode($_SERVER['REQUEST_URI']));
								?>
								document.location = "<?php echo Zend_registry::get("basePath"); ?>panier/ajouter/"+produit+"/quantite/"+qte;
							}
							
							function update_qte(id, val) {
								quantite = document.getElementById('p'+id).value;
								quantite = parseInt(quantite) + parseInt(val);
								if (quantite < 1) { quantite = 1; }
								if (quantite > 99) { quantite = 99; }
								document.getElementById('p'+id).value = quantite;
							}
						//]]>
						</script>
					
					<?php
					
						$result10 = $database->prepare("
						
						SELECT * FROM non_dispo INNER JOIN static_commandes ON non_dispo.id_command = static_commandes.id WHERE static_commandes.client_email ='$id'
						
						") or die ("requete r10 invalid");
						$result10->execute();
						$i = 0;
						while ($tab9 = $result10->fetch()) {
						
						if($tab9['dispo'] == "1"){
						
							echo '<tr>';
							echo '<td style="font-size:12px; padding:3px;"><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=4&id='.$tab9['reference'].'">ND</a></div></td>';
							} else {
							echo '<tr style="color: #FF6600; font-weight: bold;">';
							echo '<td style="font-size:12px; padding:3px;"><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=5&id='.$tab9['reference'].'" style="color: #FF6600;" >Dispo</a></div></td>';
							}
							echo "<td style='font-size:10px; padding:3px;'><div align='center'>".$tab9['date_nd']."</div></td>";
							echo "<td style='font-size:11px; padding:3px;'><div align='center'>".$tab9['reference']."</div></td>";
							echo '<td style="font-size:10px; padding:3px;"><div align="center">'.$tab9['genrename'].'</a></div></td>';
							echo "<td style='font-size:10px; padding:3px;' ><div align='center'>".substr($tab9['designation'],0 ,45)."...</div></td>";
							echo "<td style='font-size:10px; padding:3px;'><div align='center'>".$tab9['qteproduit']."</div></td>";
							$reference = $tab9['reference'];
			
							$result2 = $database->prepare("
							SELECT * FROM produits WHERE referenceproduit = '$reference'
							") or die ("requete r2 invalid");
							$result2->execute();
							while ($tab2 = $result2->fetch()) {
							echo "<td style='font-size:10px; padding:3px;'><div align='center'>".number_format($tab2['prixproduiteuro'],2, '.', '')."€</div></td>";
							?><td>
							<input type="hidden" maxlength="2" size="2" value="<?php echo $tab9['qteproduit']; ?>" id="p<?php echo $i; ?>"/>
							<a href="javascript:addtocart('<?php echo $tab2['idproduit']; ?>','<?php echo $i; ?>');" title="Ajouter au panier"><img src="<?php echo $basePath; ?>public/images/cart.png" alt="panier"/></a></td>
							<?php
							$prix = number_format($tab2['prixproduiteuro'],2, '.', '');
							}
							echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=3&genre='.$tab9['genrename'].'&id='.$tab9['reference'].'" target="_blank"><img src="'.$basePath.'public/images/zoom.png" alt="Voir" title="Voir"/></a></div></td>';
							echo '<td><div align="center"><a onclick="return(confirm(\'Etes-vous sur de vouloir supprimer ce non-dispo?\'));" href="'.$basePath.'index.php?page=admin&action=gererclient&type=modif&nd_del='.$tab9['id_nd'].'&id='.$id.'&ref='.$tab9['reference'].'&idcde='.$tab9['id'].'"> <img src="'.$basePath.'public/images/cross.png" alt="delete" title="Supprimer"/></a></div></td></tr>';
							$i++;
						}
					?>
					
					</table>
					<?php
				endif;
			 ?>
		</div>
		<!-- on affiche les informations relive au client -->
		<p class="bouton_retour">
			<a href="<?php echo $basePath ?>index.php?page=admin&action=gererclient"><?php echo $translate->_('Retour'); ?></a>
		</p>
<?php endif; ?>
		<div id="oModalSuperADM" class="oModal">
				  <div style=" margin: 10% auto;transition: all 0.4s ease-in-out;-moz-transition: all 0.4s ease-in-out;-webkit-transition: all 0.4s ease-in-out;">
					<header>  
					   <h2>Connexion au Super Administrateur requis !</h2>
					   <a href="#fermer" title="Fermer la fenêtre" class="droite"><img src="<?php echo $basePath.'public/images/cross.png'; ?>" alt="SUPPRIMER" /></a>
					 </header>
					 <section>
						<form action="<?php echo $basePath ?>index.php?page=admin&action=gererclient&type=modif&id=<?php echo $id; ?>" method="post">
								<input class="inputlogin" type="text" name="superuser" value="" placeholder="Utilisateur" />
								<input class="inputpassword" type="password" name="superpw" value="" placeholder="Mot de passe" />
								<input type="submit" name="superadmin" value="<?php echo $translate->_('Ok');?>" />
						</form>
						<br />
						<hr />
						<br />
					 <section>
				  </div>
		</div>

	
	