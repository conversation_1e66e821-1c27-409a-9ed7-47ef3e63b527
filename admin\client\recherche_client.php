<?php
/**
 * Programme de recherche d'une expression dans la liste de tous les produits
 */
?>

<link href="../../public/styles/style_vercorps_list.css" rel="stylesheet" type="text/css" />

	<form style="margin-left:70px;" action="<?php echo $basePath,'index.php'; ?>" method="get">
		<input type="hidden" name="page" value="admin">
		<input type="hidden" name="action" value="gererclient">
		Rechercher : <input type="text" class="inputnewstyle" name="valuetosearch" value="<?php if(isset($_GET['valuetosearch'])){ echo $_GET['valuetosearch']; }  ?>" />
		dans : 	<select style="width:75px;" class="inputnewstyle" name="in">
					<option value="ALL">tout</option>
				</select>
		en utilisant : 	<select class="inputnewstyle" style="width:150px;" name="method">
						<option value="AND">tous les mots</option>
						<option value="OR">au moins un des mots</option>
				</select>
	<input class="submitnewstyle" type="submit" value="Ok" />
	</form>
	<br /><br />
<?php

if (isset($_GET['valuetosearch']) && $_GET['valuetosearch'] != ""){
	$valuetosearch = strtoupper($_GET['valuetosearch']);
	$result8 = $database->prepare("
		SELECT * FROM clients WHERE UCASE(emailclient) LIKE '%$valuetosearch%' OR UCASE(nomclient) LIKE '%$valuetosearch%' OR UCASE(prenomclient) LIKE '%$valuetosearch%'
		") or die ("requete r2 invalid");
	$result8->execute();
		
	$nb_result8 = $result8->rowCount();
	
	if ($nb_result8 == 1){
		include ("admin/client/modif_client.php");
	} else {

echo '<h1><img src="',$basePath,'public/images/find.png" /> <span>',$translate->_('Résultats de la recherche'),'</span></h1>';

	ini_set("memory_limit","300M");
	if(isset($_GET['numpage']))
		$numpage = $_GET['numpage'];
	else
		$numpage = 1;
		
	//création d'un objet pour paginer la page
	$pagination = new Pagination(0, 100, 20, $numpage);
			
	$search = $_GET['valuetosearch'];
	$prodlist = clientDB::getClientsList();
	
	if(isset($_GET['in'])){
		$in=null;
		if($_GET['in'] == "DESC")
			$in = "les désignations des produits";
		elseif($_GET['in'] == "ALL")
			$in = "tout";
	}
	if(isset($_GET['method'])){
		$method=null;
		if($_GET['method'] == "AND")
			$method = "tous les mots";
		elseif($_GET['method'] == "OR")
			$method = "au moins un des mots";
	}
					
	echo '<p align="center">Nombre de résultats pour la recherche \'',$search, '\' dans <b>',$in,'</b> en utilisant <b>',$method,'</b> : <input style="font-weight:bold;border:none;width:50px;" type="text" id="nbres" value=""/></p></br>';
	
	//découpe tous les mots à recherchés
	$search_array = explode(' ',$search);
	//construction de l'expression régulière
	$regexp = '#'.$search_array[0];
	
	$nbres=0;
	

	foreach($prodlist as $prod) {
		//concatène le groupe le genre et le nom, pour ne faire qu'une chaine de caractère
		//plus facile pour rechercher
		//on regarde les options, pour savoir dans quoi on doit rechercher, si rien de définit par défaut on chercher dans tout
		if(isset($_GET['in'])) {

			$idenfifiant_prod = $prod->getEmail().' '.$prod->getNom().' '.$prod->getPrenom().' '.$prod->getTel().' '.$prod->getFax().' '.$prod->getAf_Raisonsocial().' '.$prod->getAf_Nomrue().' '.$prod->getAf_Codepostal().' '.$prod->getAf_Ville().' '.$prod->getAf_Pays();

		}
		$trouve = false;
		$i=0;
		
		//on regarde la méthode de recherche, par défaut, tous les mots donc : AND
		if(isset($_GET['method'])) {
			$method = $_GET['method'];
		} else {
			$method = "AND";
		}
		
		if($method == "AND") {
			//on recherche tous les mots, dc on s'arrete dès qu'on a pas trouvé un mot
			while($i<count($search_array) && ($trouve=preg_match('#'.$search_array[$i].'#i', $idenfifiant_prod)))
				$i++;
		} elseif($method == "OR" ){
			//on recherche au moins un des mots, dc on s'arrete dès qu'on à trouvé un mot
			while($i<count($search_array) && !preg_match('#'.$search_array[$i].'#i', $idenfifiant_prod))
				$i++;
		}
		//on est à la fin, on a donc trouvé tous les mots recherché !
		//ou alors on est pas à la fin
		if(($i==count($search_array) && $method=="AND") || ($i<count($search_array) && $method=="OR"))
		{
	// indice
								if($prod->getIndice() == "1"){
								$indice = "rond_vert";
								}
								if($prod->getIndice() == "2"){
								$indice = "rond_rouge";
								}
								if($prod->getIndice() == "3"){
								$indice = "rond_noir";
								}
								if($prod->getIndice() == "4"){
								$indice = "medaille";
								}
								if($prod->getIndice() == "5"){
								$indice = "pro";
								}
								if($prod->getIndice() == "6"){
								$indice = "garage";
								}
								if($prod->getIndice() == "7"){
								$indice = "zzz";
								}								
								if($prod->getIndice() == "9"){
								$indice = "rond_bleu";
								}

if ($indice == "zzz"){
?>
<div class="form-style-10" style="opacity: 0.2;" >
<?php
}else{
				
?>
<div class="form-style-10">
<?php
}
?>
<table style="width:800px;">
<tr>
<td>

<h3 style="margin-top: 5px; margin-left:5px; border:0; margin-bottom:10px;" ><span style="font-size: 16px; margin:0px;">
<img style="width:14px;" src="<?php echo Zend_Registry::get('basePath'); ?>public/images/<?php echo $indice; ?>.png" />
&nbsp;<?php echo $prod->getNom();?></span> <?php echo $prod->getPrenom();?>&nbsp;
<img style="width:14px;" src="<?php echo Zend_Registry::get('basePath'); ?>public/images/<?php echo $indice; ?>.png" />
</h3>
</td><td style="text-align:right;">
<img style width="16px;" src="<?php echo Zend_Registry::get('basePath'); ?>public/images/global.png" />
<?php
// TOTAL DES VENTES DU CLIENT

$emailclient = $prod->getEmail();

$result11 = $database->prepare("SELECT emailclient, SUM(totht) AS 'TOTALCLICA' FROM facture WHERE emailclient = '$emailclient' GROUP BY emailclient") or die ("requete rc4 invalid");
$result11->execute();
$TOTALCLICA = $result11->fetch();

echo " VENTE GLOBAL : ". number_format($TOTALCLICA['TOTALCLICA'],2, '.', '')."€";

// VENTES DU CLIENT ANNEE EN COURS
$annee = date("Y");
$result12 = $database->prepare("SELECT emailclient, SUM(totht) AS 'ANNEETOTCLI' FROM facture WHERE emailclient = '$emailclient' AND YEAR(date_facture) = '$annee' GROUP BY emailclient") or die ("requete rc4 invalid");
$result12->execute();
$ANNEETOTCLI = $result12->fetch();

echo " / <span style='color:blue;'><strong>".$annee."</strong></span> : ". number_format($ANNEETOTCLI['ANNEETOTCLI'],2, '.', '')."€";

?>

</td>
</tr>
</table>
	<div id="content_cli" style="display:flex;" >
	<div class="inner-wrap" style="width:260px; height:70px; font-size:11px;">
		<p><img style="border: 0px;" src="<?php echo Zend_Registry::get('basePath'); ?>public/images/email.png" /> : <a style="text-decoration: underline; color: #0000FF;" href="mailto:<?php echo $prod->getEmail();?>" ><?php echo $prod->getEmail();?></a></p>
		<p><img style="border: 0px;" src="<?php echo Zend_Registry::get('basePath'); ?>public/images/tel.png" /> : <?php echo $prod->getTel();?></p>
		<p><img style="border: 0px;" src="<?php echo Zend_Registry::get('basePath'); ?>public/images/fax.png" /> : <?php echo $prod->getFax();?></p>
		<p><img style="border: 0px;" src="<?php echo Zend_Registry::get('basePath'); ?>public/images/comment.png" /> : <?php echo $prod->getInfos();?></p>
	</div>

	<div class="inner-wrap" style="width:230px; font-size:11px; height:70px; margin-left: 5px;">

		<h1 style="font-size:14px; padding:8px; text-align:center; margin-bottom:5px;">FACTURATION</h1>
		<?php echo $prod->getAf_Raisonsocial();?><br />
		<?php echo $prod->getAf_Nomrue();?><br />
		<?php echo $prod->getAf_Codepostal();?><br />
		<?php echo $prod->getAf_Ville();?><br />
<?php		
		
		$result10 = $database->prepare("SELECT * FROM adresses_facturation INNER JOIN pays ON adresses_facturation.af_pays LIKE CONCAT ('%', pays.nom_pays, '%') WHERE af_emailclient = '$emailclient'") or die ("requete rc4 invalid");
		$result10->execute();
		$flag = $result10->fetch();
		echo '<img style="margin-left:5px;" src="'.$basePath.'public/images/pays/'.$flag['alpha2'].'.png"  width="16" height="14 alt="'.$flag['alpha2'].'"/>';
?>
		
		<?php echo $prod->getAf_Pays();?><br />
				
	</div>
	
	<div class="inner-wrap" style="width:230px; font-size:11px; height:70px; margin-left: 5px;">
		<h1 style="font-size:14px; padding:8px; text-align:center; margin-bottom:5px;">LIVRAISON</h1>
		<?php echo $prod->getRaisonsocial();?><br />
		<?php echo $prod->getNomrue();?><br />
		<?php echo $prod->getCodepostal();?><br />
		<?php echo $prod->getVille();?><br />
		<?php		
		
		$result13 = $database->prepare("SELECT * FROM adresses INNER JOIN pays ON adresses.pays LIKE CONCAT ('%', pays.nom_pays, '%') WHERE emailclient = '$emailclient'") or die ("requete rc4 invalid");
		$result13->execute();
		$flag2 = $result13->fetch();
		echo '<img style="margin-left:5px;" src="'.$basePath.'public/images/pays/'.$flag2['alpha2'].'.png"  width="16" height="14 alt="'.$flag2['alpha2'].'"/>';
		?>
		<?php echo $prod->getPays();?><br />
				
	</div>
	</div>
	<table style="width:800px;">
	<tr>
	<td style="text-align:left;">
		<a style="color:white;" href="<?php echo Zend_Registry::get('basePath') ;?>index.php?page=admin&action=gererclient&type=modif&id=<?php echo $prod->getEmail();?>" ><div class="add_card_autre" style="width:150px; margin:0px; border-radius:5px;"><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/modif.png" /> Modifier</div></a>
    </td>
	<td style="text-align:right;">
		<div class="add_card_autre" style="width:150px; margin-left:auto; margin-right:0px; margin-top:0px; border-radius:5px; background-color:grey; text-align:center;"><a style="color:white; padding-right:10px;" href="<?php echo Zend_Registry::get('basePath') ;?>index.php?page=admin&action=gererclient&type=delete&id=<?php echo $prod->getEmail();?>" onclick="return confirm('<?php echo $translate->_('Voulez-vous vraiment supprimer ce client ?'); ?>');" ><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/cross.png" /> Supprimer</a></div>
	</td>
	</tr>
	</table>
</div>

	<?php
		}
		}	

	}

}
	




?>