<?php
require_once dirname(__FILE__).'/../../class/produit_class.php';
require_once dirname(__FILE__).'/../../class/categorie_class.php';
?>
<h1>Scanner un produit</h1>


<script type="text/javascript">
window.onload = function() {
  document.getElementById("id_scan").focus();
}
</script>

<div class="form">
	<form method="POST" action="<?php echo $basePath; ?>index.php?page=admin&action=scannette&action2=scan" enctype="multipart/form-data">
		<fieldset class="input">
			<ol><li>
				<label>R&eacute;f&eacute;rence : </label>
				<input type="text" id="id_scan" onfocus name="id_scan" value="" />
			</li></ol>
			<ol><li>
				<label>Quantit&eacute; : </label>
				<input type="text" name="qte_scan" value="1" />
			</li></ol>
		</fieldset>
		<fieldset class="submit">	
				<input class="btn_submit" type="submit" value="Ajout&eacute;" />
		</fieldset>	
	</form>
</div>
<?php

if(isset($_GET['action2']) && $_GET['action2'] === 'scan'){
		
				$ref = str_replace("%O", "_", $_POST['id_scan']);
				$qte = $_POST['qte_scan'];
				
							$r2 = $database->prepare("SELECT * FROM produits WHERE referenceproduit = '$ref'") or die ("requete r1 invalid");
							$r2->execute();
							
							while ($tab2 = $r2->fetch()) {
				
							$p = produitDB::getProduitById($tab2['idproduit']);
							
							$user->getPanier()->ajouterProduit($p, $qte);
							
							}
				
				}
	
include dirname(__FILE__).'/../../visiteur/view/viewPanier.php';