<?php
/**
 * programme qui supprime un client de la base de donnée
 */
?>
<h1><img src="<?php echo $basePath; ?>public/images/clients_suppr.jpg" height="30" alt="clients"/><?php echo $translate->_('Suppression d\'un client'); ?></h1><br />
<?php
	// on supprime le client de la bdd et on redirige l'admin sur la page ou il etait
	require_once(dirname(__FILE__).'/../../class/adresse_class.php');
	require_once 'commande_static_class.php';
	$client = clientDB::getClientByEmail($_GET["id"]);
	$client2 = $_GET["id"];
	if(count(commande_staticDB::getCommandesByClient($client)) == 0)
	{
		if($client instanceof Client && $client->isAdmin == false){
			$res = ClientDB::delete($client);
			$res2 = $database->query("DELETE FROM adresses_facturation WHERE af_emailclient = '$client2'") or die ("requete update res2 invalid");
		}else{
			$res = false;
		}
	}
	else{
		$res = false;
	}
	
	if($res == true) {
		//suppression réussi
		?>
		<div class="valide messageBox">
			<?php echo $translate->_('Le client a bien été supprimé'); ?> : <br /><br />
			<p>
				<?php echo $client->getNom()?>
				<?php echo $client->getPrenom()?><br />
				<?php echo $client->getEmail()?>
			</p>
		</div>
		<?php
	} else {
		//erreur de suppression
		echo '<div class="error messageBox">',$translate->_('Erreur lors de la suppression du client.'),'</div>';
	}
?>
<p class="bouton_retour">
<a href="<?php echo $basePath; ?>index.php?page=admin&action=gererclient"><?php echo $translate->_('Retour'); ?></a>
</p>