<?php

	//pagination
	require_once dirname(__FILE__).'/../../class/pagination_class.php';
	
	// affichage de la liste des commandes du client connecter
	require_once dirname(__FILE__).'/../../class/commande_static_class.php';

	//$commandes  = commandeDB::getCommandes();	
	
	if (isset($_GET['tri']) && ($_GET['tri'] != ""))
	{
		$commandes = commande_staticDB::getCommandesByTri($_GET['tri'],$_GET['sens']);
	} elseif (isset($_GET['filtre']) && ($_GET['filtre'] != "") && ($_GET['filtre'] != 0))
	{
		$commandes = commande_staticDB::getCommandesByFiltre($_GET['filtre']);
	} else {
		$commandes  = commande_staticDB::getCommandes();
	}

?>
	<script>
	
function imprim(str, idcde)
	{
		var xmlhttp = null;
		if (window.XMLHttpRequest)
		  {// code for IE7+, Firefox, Chrome, Opera, Safari
		  xmlhttp=new XMLHttpRequest();
		  }
		else
		  {// code for IE6, IE5
		  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
		  }
		xmlhttp.onreadystatechange=function()
		  {
		  if (xmlhttp.readyState==4 && xmlhttp.status==200)
			{
			document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
			}
		  }
		xmlhttp.open("GET","<?php echo $basePath; ?>/client/modif_imprim.php?imprim="+str+"&idcde="+idcde,true);
		xmlhttp.send();
	} 
	
	</script>

<h1><img src="<?php echo $basePath; ?>public/images/commande.gif" alt="commandes"/><span>Gérer les commandes</span></h1>
		<center>
			<table><tr><td>
				<form action="<?php echo $basePath; ?>index.php" method="GET">
					<input type="hidden" value="admin" name="page" />
					<input type="hidden" value="commandes" name="action" />
					<fieldset><legend>Tris :</legend>
						<table>
						<tr>
							<td>
								<select style="width:100px;" class="inputnewstyle" name="tri" size="1">
									<option value="id" selected>Numéro</option>
									<option value="client_email" <?php if(isset($_GET['tri']) && ($_GET['tri'] == "client_email")) echo "selected"; ?>>Client</option>
									<option value="commande_montantotalttc" <?php if(isset($_GET['tri']) && ($_GET['tri'] == "commande_montantotalttc")) echo "selected"; ?>>Total commande TTC</option>
									<option value="commande_modepaiement" <?php if(isset($_GET['tri']) && ($_GET['tri'] == "commande_modepaiement")) echo "selected"; ?>>Mode de paiement</option>
									<option value="commande_statut" <?php if(isset($_GET['tri']) && ($_GET['tri'] == "commande_statut")) echo "selected"; ?>>Statut</option>
								</select>
							</td>
							<td>
								<select class="inputnewstyle" style="width:100px;" name="sens" size="1">
									<option value="DESC" selected>Décroissant</option>
									<option value="ASC" <?php if(isset($_GET['sens']) && ($_GET['sens'] == "ASC")) echo "selected"; ?>>Croissant</option>
								</select>
							</td>
							<td>
								<input class="submitnewstyle" type="submit" value="Ok" width="20"/>
							</td>
						</tr>
						</table>
					</fieldset>
				</form></td><td>
				<form action="<?php echo $basePath; ?>index.php" method="GET">
					<input type="hidden" value="admin" name="page" />
					<input type="hidden" value="commandes" name="action" />
					<fieldset><legend>Filtre statut de commande :</legend>
						<table><tr><td>
							<select class="inputnewstyle" name="filtre" size="1">
								<option value="2" selected>Paiement validé</option>
								<option value="1">Attente paiement</option>
								<option value="3">Préparation</option>
								<option value="4">Envoyées</option>
								<option value="5">Réappro</option>
								<option value="6">Sur place</option>
								<option value="-1">Annulées</option>
								<option value="7">Réponse client</option>
								<option value="8">Scellius Annule</option>
							</select>
						</td><td>
							<input class="submitnewstyle" type="submit" value="Ok" width="20"/>
						</td></tr></table>
					</fieldset>
				</form></td><td>
		
			<form method="get" action="<?php echo $basePath,'index.php'?>">
				<fieldset><legend>Rechercher une commande :</legend>
				<table><tr><td>
					<input type="hidden" name="page" value="admin"/>
					<input type="hidden" name="action" value="commandes" />
					<input type="text" class="inputnewstyle" name="id"/>			
					</td><td>
					<input type="submit" class="submitnewstyle" value="Ok" /><br />
				</td></tr></table>
				</fieldset>
			</form>
			</td></tr></table>
			<table><tr><td>
			<fieldset><legend>Tableau recap des statuts :</legend>
				<table><tr><td>
<?php

// compte les statuts des commande

$result_count1 = $database->prepare('SELECT COUNT(*) AS attente_paiement FROM static_commandes WHERE commande_statut = "Attente de paiement"') or die ("requete rc1 invalid");
$result_count1->execute();
$attente_paiement = $result_count1->fetch();

$result_count2 = $database->prepare('SELECT COUNT(*) AS paiement_valide FROM static_commandes WHERE commande_statut = "Paiement validé"') or die ("requete rc2 invalid");
$result_count2->execute();
$paiement_valide = $result_count2->fetch();

$result_count3 = $database->prepare('SELECT COUNT(*) AS reponse_client FROM static_commandes WHERE commande_statut = "Réponse client"') or die ("requete rc3 invalid");
$result_count3->execute();
$reponse_client = $result_count3->fetch();

$result_count4 = $database->prepare('SELECT COUNT(*) AS cours_preparation FROM static_commandes WHERE commande_statut = "En cours de préparation"') or die ("requete rc4 invalid");
$result_count4->execute();
$cours_preparation = $result_count4->fetch();

echo "<span style='color: #00CCCC; margin-right:5px;'>En attente de paiement : <strong>".$attente_paiement['attente_paiement']."</strong></span><span style='color: #FF0000; margin-right:5px;'> Paiement validé : <strong>".$paiement_valide['paiement_valide']."</strong></span><span style='color: #009900; margin-right:5px;'> Attente réponse client : <strong>".$reponse_client['reponse_client']."</strong></span><span style='color: #FF9900; margin-right:5px;'> En cours de préparation : <strong>".$cours_preparation['cours_preparation']."</strong></span>";

?>
				</td></tr></table>
			</fieldset>
			</td></tr></table>
		</center><br />
<?php								

	
	if(count($commandes) == 0){
		// il n'y a pas de commande
		?><div class="info messageBox"><p>Il n'y a pas de commande</p></div><?php
	}
	else{
		// il y a des commandes
		?>
		
		<table class="liste">
			<tr>
				<th>Numéro</th>
				<th>Indice</th>
				<th></th>
				<th>Client</th>
				<th></th>
				<th>CP + Ville (livraison)</th>
				<th>Facture</th>
				<th>Date de commande</th>
				<th>Total cde TTC</th>
				<th>Mode de paiement</th>
				<th>Statut</th>
				<th>Imprimé</th>
				<th></th>
			</tr>
<?php

		// Pagination
			if(isset($_GET['numpage']))
			$numpage = $_GET['numpage'];
			else
			$numpage = 1;
			
		//création d'un objet pour paginer la page
		$pagination = new Pagination(0, 50, 20, $numpage);
		
			foreach($commandes as $commande){
			
			//Affiche si le client a cde en attente de paiement

$emailclient = $commande->getEmail();
$result7 = $database->prepare("SELECT COUNT(*) AS cde_attente_paiement FROM static_commandes WHERE commande_statut = 'Attente de paiement' AND client_email = '$emailclient'") or die ("requete r7 invalid");
$result7->execute();
$cde_attente_paiement = $result7->fetch();

$result10 = $database->prepare("SELECT * FROM adresses_facturation INNER JOIN pays ON adresses_facturation.af_pays LIKE CONCAT ('%', pays.nom_pays, '%') WHERE af_emailclient = '$emailclient'") or die ("requete rc4 invalid");
$result10->execute();
$flag = $result10->fetch();

$div_style = "";
$div_style_end = "";

if ($cde_attente_paiement['cde_attente_paiement'] != 0){

$div_style = "<strong>";
$div_style_end = '( '.$cde_attente_paiement['cde_attente_paiement'].' ) <img src="'.$basePath.'public/images/cde_attente_paiement.png" alt="cde_attente_paiement" title="Commande en attente paiement"/></strong>';

}

$result8 = $database->prepare("SELECT COUNT(*) AS cde_attente_reponse FROM static_commandes WHERE commande_statut = 'Réponse client' AND client_email = '$emailclient'") or die ("requete r7 invalid");
$result8->execute();
$cde_attente_reponse = $result8->fetch();

$div_style2 = "";
$div_style_end2 = "";

if ($cde_attente_reponse['cde_attente_reponse'] != 0){

$div_style2 = "<strong>";
$div_style_end2 = '( '.$cde_attente_reponse['cde_attente_reponse'].' ) <img src="'.$basePath.'public/images/cde_attente_rep.png" alt="cde_attente_reponse" title="Commande en attente réponse"/></strong>';

}
			
				$pagination->incremente();
				if($pagination->isTimeToRender()){
				
				?>
				<tr  style="text-align:center; height:50px; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #666;">
				<td><?php echo $commande->getId(); ?></td>
							<?php 
							$idcde = $commande->getId();

								if($commande->getIndiceclient() == "1"){
								$indice = $basePath.'public/images/rond_vert.png" alt="vert"/>';
								}
								if($commande->getIndiceclient() == "2"){
								$indice = $basePath.'public/images/rond_rouge.png" alt="rouge"/>';
								}
								if($commande->getIndiceclient() == "3"){
								$indice = $basePath.'public/images/rond_noir.png" alt="noir"/>';
								}
								if($commande->getIndiceclient() == "4"){
								$indice = $basePath.'public/images/medaille.png" alt="medaille"/>';
								}
								if($commande->getIndiceclient() == "5"){
								$indice = $basePath.'public/images/pro.png" alt="pro"/>';
								}
								if($commande->getIndiceclient() == "6"){
								$indice = $basePath.'public/images/garage.png" alt="pro"/>';
								}
							echo '<td><div align="center"><img src="'.$indice.'</div></td>';
							echo '<td><img src="'.$basePath.'public/images/pays/'.$flag['alpha2'].'.png" title="'.$flag['nom_pays'].'" style="box-shadow: 0px 0px 3px 1px rgba(119, 119, 119, 0.75);" width="16" height="14" alt="'.$flag['alpha2'].'"/></td>';
							echo '<td><a href="'.$basePath.'index.php?page=admin&action=gererclient&type=modif&id='.$commande->getEmail().'">'.$commande->getNomclient().'</a></td>';
							echo'<td>'.$div_style2.$div_style.$div_style_end.$div_style_end2.'</td>';
							
							?>
					<?php 
							$tab = explode("<br />",$commande->getAdressefact());
							?>
					<td><?php echo stripslashes($tab[2]); ?></td>
				
					<?php
					$result = $database->prepare("SELECT count(*) AS cal_idcde,id_facture, remboursement FROM facture WHERE id_command = '$idcde'") or die ("requete r1 invalid");
					$result->execute();
					$cal_idcde = $result->fetch();
					$remboursement = "";
					if ($cal_idcde['remboursement'] <> 0) $remboursement = 'RB='.$cal_idcde['remboursement'].'€';
					
					if ($cal_idcde['cal_idcde'] >= "1"){

					echo '<td><a href="admin/facture/modifier/imprim_fact.php?id_facture='.$cal_idcde['id_facture'].'">'.$cal_idcde['id_facture'].'</a></td>';
							
					}
						
					else{
					
					echo "<td><a href='".$basePath."index.php?page=admin&action=facture&type=edit&idcommande=".$idcde."'><img src='".$basePath."public/images/facture.png'/></a></td>";
						
						}
					
					?>
					<td><?php echo date('d/m/Y',strtotime($commande->getDate())); ?></td>
					<td><?php printf('%.2f €',$commande->getMontanttotalTTC()); ?></td>
					<td><?php echo $commande->getModepaiement().' '.$remboursement; ?></td>
					<?php 
					if (isset($_GET['filtre']) && $_GET['filtre'] != "0"){
					?>
					<form action="<?php echo $basePath; ?>admin/commande/modif_commande.php" method="post" class="form">
						<td><select name="stat">
						
							<option value="1" onClick="this.form.submit();" <?php echo ($commande->getStatut() == "Attente de paiement")?"selected='selected'":''; ?>>Attente de paiement</option>
							<option value="2" onClick="this.form.submit();" <?php echo ($commande->getStatut() == "Paiement validé")?"selected='selected'":''; ?>>Paiement validé</option>
							<option value="3" onClick="this.form.submit();" <?php echo ($commande->getStatut() == "En cours de préparation")?"selected='selected'":''; ?>>En cours de préparation</option>
							<option value="4" onClick="this.form.submit();" <?php echo ($commande->getStatut() == "Envoyée")?"selected='selected'":''; ?>>Envoyée</option>
							<option value="5" onClick="this.form.submit();" <?php echo ($commande->getStatut() == "En cours de réapprovisionnement")?"selected='selected'":''; ?>>Attente de réappro</option>
							<option value="6" onClick="this.form.submit();" <?php echo ($commande->getStatut() == "Sur place")?"selected='selected'":''; ?>>Sur place</option>
							<option value="-1" onClick="this.form.submit();" <?php echo ($commande->getStatut() == "Annulée")?"selected='selected'":''; ?>>Annulée</option>
							<option value="7" onClick="this.form.submit();" <?php echo ($commande->getStatut() == "Réponse client")?"selected='selected'":''; ?>>Réponse client</option>
							<option value="8" onClick="this.form.submit();" <?php echo ($commande->getStatut() == "Annulée")?"selected='selected'":''; ?>>Scellius Annule</option>
								
						</select></td>
						
					<input type="hidden" name="filtre" value="<?php echo $_GET['filtre']; ?>" />
					<input type="hidden" name="idcommande" value="<?php echo $commande->getId(); ?>" />
					<input type="hidden" name="list" value="1" />
					</form>
					<?php
					} else {
					
										
						if ($commande->getStatut() == "Paiement validé"){
						
						echo '<td style="color: #FF0000;">'.$commande->getStatut().'</td>'; 
						
						} elseif ($commande->getStatut() == "Envoyée" || $commande->getStatut() == "Annulée") {
						
						echo '<td style="color: #CCCCCC;">'.$commande->getStatut().'</td>'; 
						
						}elseif ($commande->getStatut() == "Réponse client") {
						
						echo '<td style="color: #009900;">'.$commande->getStatut().'</td>'; 
						
						} elseif ($commande->getStatut() == "Attente de paiement") {
						
						echo '<td style="color: #00CCCC;">'.$commande->getStatut().'</td>'; 
											
						} elseif ($commande->getStatut() == "En cours de préparation") {
						
						echo '<td style="color: #FF9900;">'.$commande->getStatut().'</td>'; 
						} else {
						
						echo "<td>".$commande->getStatut()."</td>";
						
						}
					
					}
					
					?>

					<?php if ($commande->getImprim() == "1"){
						    echo "<td><a href='".$basePath."client/commande/impression_commande.php?id=".$idcde."' onclick=\"imprim(0,".$idcde.")\" target='_blank'>Oui</a></td>";
						  } else {
						    echo "<td><a href='".$basePath."client/commande/impression_commande.php?id=".$idcde."' onclick=\"imprim(1,".$idcde.")\" target='_blank'><img src='".$basePath."public/images/gtk-print.png'  onclick='this.src=\"".$basePath."public/images/tick.png\"'/></a></td>";
						  }
					?>
					<td>
					<?php
					
					if (isset($_GET['filtre'])){
					
					?>
						<a href="<?php echo $basePath; ?>index.php?page=admin&action=commandes&id=<?php echo $commande->getId(); ?>&filtre=<?php echo $_GET['filtre']; ?>">
							<img src="<?php echo $basePath; ?>public/images/zoom.png" />
						</a>
						
					<?php
					
					} else {
					
					?>
					
						<a href="<?php echo $basePath; ?>index.php?page=admin&action=commandes&id=<?php echo $commande->getId(); ?>">
							<img src="<?php echo $basePath; ?>public/images/zoom.png" />
						</a>
						
					<?php
					
					}
					
					?>
					
					
					</td>
				</tr>
				
				<?php
				}
			}
		?>
				<tr>
					<th colspan=13><?php $pagination->printPagesLinks($basePath.'index.php?page=admin&action=commandes', true); ?></th>
				</tr>
		</table>
		<?php
	}
?>
<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>index.php?page=admin&action=administration"><?php echo $translate->_('Retour');?></a>
</p>