<?php 
	if ($c->getStatut() == "Envoyée"){
	
	include ('email_statut_envoye.php');
	
	} else {
	
	//$server_name = 'http://'.$_SERVER['SERVER_NAME'];
	
	$mail = $c->getEmail(); // Déclaration de l'adresse de destination.
	$test_mail = preg_match("#^[a-z0-9._-]+@(hotmail|live|msn).[a-z]{2,4}$#", $mail);
	if ($test_mail === "1"){ // On filtre les serveurs qui présentent des bogues.
	$passage_ligne = "\r\n";
	}else{
	$passage_ligne = "\n";
	}
	
	if ($c->getStatut() == "Annulée"){
	$statut = "Annul&eacute;e";
	$statut2 = "Annulee";
	$txt_statut = "<p>Votre commande a &eacute;t&eacute; annul&eacute;e. Les raisons de cette annulation peuvent &ecirc;tre : </p>
	<p>- Vous n'avez pas finalis&eacute; votre commande.<br />
	- Vous avez rencontr&eacute; un probl&egrave;me lors de la validation de votre commande.<br />
	- Votre commande date de plus de 3 mois.<br />
	- Vous avez pass&eacute; une nouvelle commande.</p>
	<p>Merci de prendre contact par t&eacute;l&eacute;phone au +33 (0) 476 644 356 ou par e-mail : <EMAIL> pour connaitre le motif de l'annulation de votre commande.</p>";
	}
	if ($c->getStatut() == "En cours de réapprovisionnement"){
	$statut = "En cours de r&eacute;approvisionnement";
	$statut2 = "En cours de reapprovisionnement";
	$txt_statut = "<p>Votre commande est en attente de r&eacute;approvisionnement car il manque une ou plusieurs pi&egrave;ces.</p>
	<p>Merci de prendre contact par t&eacute;l&eacute;phone au +33 (0) 476 644 356 ou par e-mail : <EMAIL> afin de connaitre le d&eacute;lai de r&eacute;approvisionnement des pi&egrave;ces non disponibles de votre commande.</p>";
	}
	
	if ($c->getStatut() == "Attente de paiement"){
	$statut = "Attente de paiement";
	$statut2 = "Attente de paiement";
	$txt_statut = "<p>Votre commande est en attente de paiement.</p>
	<p>Si vous avez rencontr&eacute; un probl&egrave;me lors de la validation de votre commande, <br /> merci de bien vouloir nous contacter par t&eacute;l&eacute;phone au +33 (0) 476 644 356 ou par e-mail : <EMAIL> afin de valider le paiement de votre commande.</p>";
	}
	
	if ($c->getStatut() == "En cours de préparation"){
	$statut = "En cours de pr&eacute;paration";
	$statut2 = "En cours de preparation";
	$txt_statut = "<p>Votre commande est en cours de pr&eacute;paration.</p>
	<p>Elle sera trait&eacute;e sous 48h.</p>";
	}
	
	if ($c->getStatut() == "Paiement validé"){
	$statut = "Paiement valid&eacute;";
	$statut2 = "Paiement valide";
	$txt_statut = "<p>Nous avons bien re&ccedil;u le paiement de votre commande.</p>";
	}
	
	$content_html = '
<table style="font-family:Arial;" cellspacing="0" cellpadding="0" border="0" bgcolor="#ffffff" width="100%" style="font-size:0; margin: 0px; padding: 0px; border-collapse: collapse; border-spacing: 0;">
  <tr>
    <td valign="top" align=center>
<table cellspacing="0" cellpadding="0" border="0" bgcolor="#cccc66" style="font-size:0; margin: 0px; padding: 0px; border-collapse: collapse; border-spacing: 0;">
  <tr>
    <td valign="top" rowspan="3" ><a href="http://jeep-dodge-gmc.com/smi/" ><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_01.gif" style="display:block;"/></a></td>
    <td colspan="3" valign="top"><a href="mailto:<EMAIL>" ><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_02.gif" style="display:block;"/></a></td>
    <td colspan="1" valign="top"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_03.gif" style="display:block;"/></td>
  </tr>
  <tr>
    <td valign="top"><a href="https://twitter.com/jeep_dodge_gmc"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_04.gif" style="display:block;"/></a></td>
    <td valign="top"><a href="https://plus.google.com/u/0/107839788516299581970/about"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_05.gif" style="display:block;"/></a></td>
    <td valign="top"><a href="https://www.facebook.com/surplus.militaitresetindustriels"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_06.gif" style="display:block;"/></a></td>
    <td valign="top"><a href="http://jeep-dodge-gmc.com/smi/index.php?page=newsletter"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_07.gif" style="display:block;"/></a></td>
  </tr>
  <tr>  
    <td colspan="4" valign="top"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_08.gif" style="display:block;"/></td>
  </tr> 
</table>

<table style="background-color:white; margin-left:auto; margin-right:auto; width:650px; border:solid black;">
	<tr>
		<td style="background-color:black; color:white;">
			<h1 style="margin-top:10px; margin-bottom:10px; text-align:center;">Commande n&deg;'.$c->getId().'</h1>
		</td>
	</tr>	
<tr><td>
Commande du : '.date('d/m/Y',strtotime($c->getDate())).'<br />
Mode de paiement : '.$c->getModepaiement().'<br />
Identifiant client : '.$c->getEmail().'<br />
T&eacute;l&eacute;phone : '.$c->getTel().'<br />
Fax : '.$c->getFax().'<br /><br />
</td></tr>
<tr>
<td>
<h2>Statut de la commande : '.$statut.'</h2>

'.$txt_statut.'
</td>
</tr>
</table>

</td>
</tr>
</table>

';

// Envoie d'un mail pour avertir le client qu'il est bien inscrit (permet de vérifier aussi que l'email est bien correct

$exp_mail = "<EMAIL>";
$exp_nom = "Jeep-Dodge-Gmc.com";

//=====Définition du sujet.
$sujet = "Commande ".$c->getId()." : ".$statut2;
//=========
 
//=====Création du header de l'e-mail.
$header = 'MIME-Version: 1.0' . "".$passage_ligne;
$header.= 'Content-type: text/html; charset=iso-8859-1' . "".$passage_ligne;
$header.= "From: \"".$exp_nom."\"<".$exp_mail.">".$passage_ligne;
$header.= "Reply-To: \"".$exp_nom."\"<".$exp_mail.">".$passage_ligne;
//==========

//=====Envoi de l'e-mail.

mail($mail,$sujet,$content_html,$header);

//==========
}
?>