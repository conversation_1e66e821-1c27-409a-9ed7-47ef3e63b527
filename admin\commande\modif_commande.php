<?php
	// fichier permettant a l'admin de modifier une commande
	require_once dirname(__FILE__).'/../../init.php';
	require_once 'commande_static_class.php';
	
	$c = commande_staticDB::getCommandeById($_POST['idcommande']);
	
// changement de statut	
	
	if ($_POST['stat'] == "-1") {
		$statut = "Annulée";
	} else if ($_POST['stat'] == "1") {
		$statut = "Attente de paiement";
	} else if($_POST['stat'] == "2") {
		$statut = "Paiement validé";
	} else if($_POST['stat'] == "3") {
		$statut = "En cours de préparation";
	} else if($_POST['stat'] == "4") {
		$statut = "Envoyée";
	} else if($_POST['stat'] == "5") {
		$statut = "En cours de réapprovisionnement";
	} else if($_POST['stat'] == "6") {
		$statut = "Sur place";
	} else if($_POST['stat'] == "7") {
		$statut = "Réponse client";
	} else if($_POST['stat'] == "8") {
		$statut = "Scellius Annule";
	}
	else {
		$statut = "";
	}

//impression oui ou non
	
	if ($statut == $c->getStatut()){
	
	$no_print = "1";
	
	} else {
	
	$no_print = "0";
	
	}
	
	if (isset($_POST['imprim']) || isset($_GET['imprim'])) {
	$imprim = "1";
	$c->setImprim($imprim);
	} 
	if (isset($_POST['comment'])) {
	$c->setComment($_POST['comment']);
	} 
	

	
// ajout ND

	if (isset($_POST['nd'])){
	
	$idcde = $_POST['idcommande'];
	$non_dispo = $_POST['nd'];
	$date_nd = date("Y-m-d");
	
	for ($i = 0; $i < count($non_dispo); $i++){
	
	$sql2 = $database->prepare("SELECT * FROM static_lignes_commandes WHERE commande_id = '$idcde' AND produit_reference = '".$non_dispo[$i]."'") or die ("requete insert sql invalid");
	$sql2->execute();
	$view = $sql2->fetch();
	
// ajout du nd a la table non_dispo
	
				$sql = $database->query("
				INSERT INTO non_dispo (
				id_command,
				date_nd,
				reference,
				genrename,
				designation,
				qteproduit
				) 
				
				VALUES (
				'".$idcde."',
				'".$date_nd."',
				'".$non_dispo[$i]."',
				'".$view['produit_genregroupe']."',
				'".htmlentities($view['produit_designation'], ENT_QUOTES, "UTF-8")."',
				'".$view['produit_qte']."'
				)") or die ("requete insert sql invalid");
				
// modifie le champ produit_nd de la table commandes_lignes_static
				
				$sql2 = $database->query("
				UPDATE static_lignes_commandes
				SET 
				produit_nd = '1'
				WHERE commande_id = '$idcde' AND produit_reference = '".$non_dispo[$i]."'
				") or die ("requete insert sql2 invalid");
				
				$sql3 = $database->query("
				UPDATE produits
				SET commande = '1'
				WHERE referenceproduit = '".$non_dispo[$i]."'
				") or die ("requete insert sql3 invalid");
			}
		
		}
		
// supprime ND

	if (isset($_POST['dispo'])){
	
	$idcde = $_POST['idcommande'];
	$dispo = $_POST['dispo'];
	$date_nd = date("Y-m-d");
	
	for ($i = 0; $i < count($dispo); $i++){

// supprime le nd a la table non_dispo
	
				$sql = $database->query("
				DELETE FROM non_dispo
				WHERE id_command = '$idcde' AND reference = '".$dispo[$i]."'
				") or die ("requete insert sql invalid");
				
// modifie le champ produit_nd de la table commandes_lignes_static
				
				$sql2 = $database->query("
				UPDATE static_lignes_commandes
				SET 
				produit_nd = '0'
				WHERE commande_id = '$idcde' AND produit_reference = '".$dispo[$i]."'
				") or die ("requete insert sql2 invalid");
				
			}
		
		}
		
// change statut

	$c->setStatut($statut);
		
// envoi email
	
	if (!isset($_POST['no_send']) && $no_print == "0" && $c->getRetraitmagasin() == "0" && $statut != "Réponse client"){
	require_once dirname(__FILE__).'/mail_change_stat_commande.php';
	}				
	
// save commande et renvoi a la liste des cde 

	commande_staticDB::saveCommande($c,false);
	
	header('HTTP/1.1 302 Found');
	header('Location: '.$basePath.'index.php?page=admin&action=commandes&filtre='.$_POST['filtre'].'');

	