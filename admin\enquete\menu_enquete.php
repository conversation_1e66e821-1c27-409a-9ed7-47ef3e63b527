
<h1>Enquête de satisfaction</h1>

<?php

		include ("lib/graph/src/jpgraph.php");
		include ("lib/graph/src/jpgraph_pie.php");
		include ("lib/graph/src/jpgraph_bar.php");
		
// Question 1

echo "<br /><h2>1) Comment nous avez-vous connu ?</h2>";

for ($y = 1; $y < 9; $y++) {
$result2 = $database->prepare("SELECT count(q1_connu) AS q1_connu FROM enquete WHERE q1_connu = '$y'") or die ("requete r2 invalid");
$result2->execute();
$sum_q1 = $result2->fetch();

$sum_q1bis = $sum_q1['q1_connu'];
$nb_q1[] = $sum_q1bis;

if ($y == "1"){$q1[] = "Par notre newsletter";}
if ($y == "2"){$q1[] = "Dans une publicite magazine";}
if ($y == "3"){$q1[] = "Par un ami ou connaissance";}
if ($y == "4"){$q1[] = "Par un moteur de recherche";}
if ($y == "5"){$q1[] = "Sur une bourse ou manifestation";}
if ($y == "6"){$q1[] = "Par le site LeLienLocal";}
if ($y == "7"){$q1[] = "Achat direct dans nos locaux";}
if ($y == "8"){$q1[] = "Autre";}

$temp = $y+1; 
}


		// Construction du graphique
		$graph = new PieGraph(800,300);

		// Créer un graphique secteur (classe PiePlot)
		$oPie = new PiePlot($nb_q1);

		// Légendes qui accompagnent chaque secteur, ici chaque année
		$oPie->SetLegends($q1);

		// position du graphique (légèrement à droite)
		$oPie->SetCenter(0.4); 
		
		// Spécification des couleurs des barres
		$oPie->SetSliceColors(array('blue', 'green', 'red', 'yellow', '#00FFFF' ,'#FF00FF' , '#999999', '#FF6600'));

		$oPie->SetValueType(PIE_VALUE_ABS);

		// Format des valeurs de type entier
		$oPie->value->SetFormat('%d');

		// Ajouter au graphique le graphique secteur
		$graph->Add($oPie);

		// Provoquer l'affichage (renvoie directement l'image au navigateur)
		$graph->Stroke('public/images/produits/q1_connu.png');
		
		echo '<div align="center"><img src="public/images/produits/q1_connu.png" alt=""/></div>';
		
// Question 2

echo "<br /><h2>2) Etes-vous satisfait de l'organisation general du site ?</h2>";

		for ($y = 1; $y < 5; $y++) {
		$result3 = $database->prepare("SELECT count(q2_orga) AS q2_orga FROM enquete WHERE q2_orga = '$y'") or die ("requete r2 invalid");
		$result3->execute();
		$sum_q2 = $result3->fetch();

		$sum_q2bis = $sum_q2['q2_orga'];
		$nb_q2[] = $sum_q2bis;

		if ($y == "1"){$q2[] = "Tres satisfait";}
		if ($y == "2"){$q2[] = "Satisfait";}
		if ($y == "3"){$q2[] = "Peu satisfait";}
		if ($y == "4"){$q2[] = "Tres insatisfait";}

		$temp = $y+1; 
		}

		$graph = new Graph(800,300);

		// Réprésentation linéaire
		$graph->SetScale("textlin");

		// Ajouter une ombre au conteneur
		$graph->SetShadow();

		// Fixer les marges
		$graph->img->SetMargin(40,30,25,40);

		// Création du graphique histogramme
		$bplot = new BarPlot($nb_q2);

		// Spécification des couleurs des barres
		$bplot->SetFillColor(array('blue', 'green', 'red', 'yellow'));
		// Une ombre pour chaque barre
		$bplot->SetShadow();

		// Afficher les valeurs pour chaque barre
		$bplot->value->Show();
		// Modifier le rendu de chaque valeur
		$bplot->value->SetFormat('%d');

		// Ajouter les barres au conteneur
		$graph->Add($bplot);

		// Le titre
		$graph->title->SetFont(FF_FONT1,FS_BOLD);

		// Titre pour l'axe horizontal(axe x) et vertical (axe y)
		$graph->xaxis->title->Set("Avis");
		$graph->yaxis->title->Set("Nombre");

		$graph->yaxis->title->SetFont(FF_FONT1,FS_BOLD);
		$graph->xaxis->title->SetFont(FF_FONT1,FS_BOLD);

		// Légende pour l'axe horizontal
		$graph->xaxis->SetTickLabels($q2);

		// Afficher le graphique
		$graph->Stroke('public/images/produits/q2_orga.png');
		
		echo '<div align="center"><img src="public/images/produits/q2_orga.png" alt=""/></div>';
		
// Question 3

echo "<br /><h2>3) Est-ce votre 1er visite sur notre site ?</h2>";

		for ($y = 1; $y < 3; $y++) {
		$result4 = $database->prepare("SELECT count(q3_visite) AS q3_visite FROM enquete WHERE q3_visite = '$y'") or die ("requete r3 invalid");
		$result4->execute();
		$sum_q3 = $result4->fetch();

		$sum_q3bis = $sum_q3['q3_visite'];
		$nb_q3[] = $sum_q3bis;

		if ($y == "1"){$q3[] = "Oui";}
		if ($y == "2"){$q3[] = "Non";}

		$temp = $y+1; 
		}

		$graph2 = new Graph(800,300);

		// Réprésentation linéaire
		$graph2->SetScale("textlin");

		// Ajouter une ombre au conteneur
		$graph2->SetShadow();

		// Fixer les marges
		$graph2->img->SetMargin(40,30,25,40);

		// Création du graphique histogramme
		$bplot2 = new BarPlot($nb_q3);

		// Spécification des couleurs des barres
		$bplot2->SetFillColor(array('green', 'red'));
		// Une ombre pour chaque barre
		$bplot2->SetShadow();

		// Afficher les valeurs pour chaque barre
		$bplot2->value->Show();
		// Modifier le rendu de chaque valeur
		$bplot2->value->SetFormat('%d');

		// Ajouter les barres au conteneur
		$graph2->Add($bplot2);

		// Le titre
		$graph2->title->SetFont(FF_FONT1,FS_BOLD);

		// Titre pour l'axe horizontal(axe x) et vertical (axe y)
		$graph2->xaxis->title->Set("Reponse");
		$graph2->yaxis->title->Set("Nombre");

		$graph2->yaxis->title->SetFont(FF_FONT1,FS_BOLD);
		$graph2->xaxis->title->SetFont(FF_FONT1,FS_BOLD);

		// Légende pour l'axe horizontal
		$graph2->xaxis->SetTickLabels($q3);

		// Afficher le graphique
		$graph2->Stroke('public/images/produits/q3_visite.png');
		
		echo '<div align="center"><img src="public/images/produits/q3_visite.png" alt=""/></div>';
		
// Question 4

echo "<br /><h2>4) La presentation de la page d'accueil vous parait-elle claire ?</h2>";

		for ($y = 1; $y < 3; $y++) {
		$result5 = $database->prepare("SELECT count(q4_accueil) AS q4_accueil FROM enquete WHERE q4_accueil = '$y'") or die ("requete r4 invalid");
		$result5->execute();
		$sum_q4 = $result5->fetch();

		$sum_q4bis = $sum_q4['q4_accueil'];
		$nb_q4[] = $sum_q4bis;

		if ($y == "1"){$q4[] = "Oui";}
		if ($y == "2"){$q4[] = "Non";}

		$temp = $y+1; 
		}

		$graph3 = new Graph(800,300);

		// Réprésentation linéaire
		$graph3->SetScale("textlin");

		// Ajouter une ombre au conteneur
		$graph3->SetShadow();

		// Fixer les marges
		$graph3->img->SetMargin(40,30,25,40);

		// Création du graphique histogramme
		$bplot3 = new BarPlot($nb_q4);

		// Spécification des couleurs des barres
		$bplot3->SetFillColor(array('green', 'red'));
		// Une ombre pour chaque barre
		$bplot3->SetShadow();

		// Afficher les valeurs pour chaque barre
		$bplot3->value->Show();
		// Modifier le rendu de chaque valeur
		$bplot3->value->SetFormat('%d');

		// Ajouter les barres au conteneur
		$graph3->Add($bplot3);

		// Le titre
		$graph3->title->SetFont(FF_FONT1,FS_BOLD);

		// Titre pour l'axe horizontal(axe x) et vertical (axe y)
		$graph3->xaxis->title->Set("Reponse");
		$graph3->yaxis->title->Set("Nombre");

		$graph3->yaxis->title->SetFont(FF_FONT1,FS_BOLD);
		$graph3->xaxis->title->SetFont(FF_FONT1,FS_BOLD);

		// Légende pour l'axe horizontal
		$graph3->xaxis->SetTickLabels($q4);

		// Afficher le graphique
		$graph3->Stroke('public/images/produits/q4_accueil.png');
		
		echo '<div align="center"><img src="public/images/produits/q4_accueil.png" alt=""/></div>';

// Question 5

echo "<br /><h2>5) La navigation sur le site est-elle ?</h2>";

		for ($y = 1; $y < 5; $y++) {
		$result6 = $database->prepare("SELECT count(q5_navig) AS q5_navig FROM enquete WHERE q5_navig = '$y'") or die ("requete r5 invalid");
		$result6->execute();
		$sum_q5 = $result6->fetch();

		$sum_q5bis = $sum_q5['q5_navig'];
		$nb_q5[] = $sum_q5bis;

		if ($y == "1"){$q5[] = "Facile et intuitive";}
		if ($y == "2"){$q5[] = "Assez facile";}
		if ($y == "1"){$q5[] = "Normal";}
		if ($y == "2"){$q5[] = "Difficile";}

		$temp = $y+1; 
		}

		$graph4 = new Graph(800,300);

		// Réprésentation linéaire
		$graph4->SetScale("textlin");

		// Ajouter une ombre au conteneur
		$graph4->SetShadow();

		// Fixer les marges
		$graph4->img->SetMargin(40,30,25,40);

		// Création du graphique histogramme
		$bplot4 = new BarPlot($nb_q5);

		// Spécification des couleurs des barres
		$bplot4->SetFillColor(array('blue', 'green', 'red', 'yellow'));
		// Une ombre pour chaque barre
		$bplot4->SetShadow();

		// Afficher les valeurs pour chaque barre
		$bplot4->value->Show();
		// Modifier le rendu de chaque valeur
		$bplot4->value->SetFormat('%d');

		// Ajouter les barres au conteneur
		$graph4->Add($bplot4);

		// Le titre
		$graph4->title->Set("5) La navigation sur le site est-elle ?");
		$graph4->title->SetFont(FF_FONT1,FS_BOLD);

		// Titre pour l'axe horizontal(axe x) et vertical (axe y)
		$graph4->xaxis->title->Set("Avis");
		$graph4->yaxis->title->Set("Nombre");

		$graph4->yaxis->title->SetFont(FF_FONT1,FS_BOLD);
		$graph4->xaxis->title->SetFont(FF_FONT1,FS_BOLD);

		// Légende pour l'axe horizontal
		$graph4->xaxis->SetTickLabels($q5);

		// Afficher le graphique
		$graph4->Stroke('public/images/produits/q5_navig.png');
		
		echo '<div align="center"><img src="public/images/produits/q5_navig.png" alt=""/></div>';

//Question 6

echo "<br /><h2>6)Quelle am&eacute;lioration envisagerez vous ?</h2>";

		$result7 = $database->prepare("SELECT * FROM enquete WHERE q6_amelio != '' ORDER BY id_enquete DESC LIMIT 10") or die ("requete r6 invalid");
		$result7->execute();

		?>
		<table style="margin-left: 60px;" width="800" border="1" cellspacing="0" cellpadding="0">
		  <tr>
			<th style="text-align: center;" scope="col">Client</th>
			<th style="text-align: center;"width="528" scope="col">R&eacute;ponse (les 10 derni&egrave;res) </th>
		  </tr>
		  <tr>
		<?php
		
		while ($tab = $result7->fetch()) {	
		
		$nom_cli = $tab['client_enquete'];
		
		$result8 = $database->prepare("SELECT * FROM clients WHERE emailclient = '$nom_cli'") or die ("requete r6 invalid");
		$result8->execute();
		$cli_nom = $result8->fetch();
		$cli_nom2 = $cli_nom['nomclient']." ".$cli_nom['prenomclient'];

			echo "<td style='text-align: center;' >".$cli_nom2."</td>";
			echo "<td style='text-align: left;' >".$tab['q6_amelio']."</td>";
		  echo '</tr>';
		
		}
		echo "</table>";
?>
