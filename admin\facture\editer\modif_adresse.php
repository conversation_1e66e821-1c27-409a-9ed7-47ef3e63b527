<?php

$idcde = $_GET['idcommande'];
$result5 = $database->prepare("SELECT idadresse,emailclient,raisonsocial,nomrue,codepostal,ville,pays FROM adresses INNER JOIN static_commandes ON adresses.emailclient = static_commandes.client_email WHERE id = '$idcde' ORDER BY idadresse DESC LIMIT 1") or die ("requete r5 invalid");
$result6 = $database->prepare("SELECT adresses_facturation.id AS id1,af_emailclient,af_raisonsocial,af_nomrue,af_codepostal,af_ville,af_pays FROM adresses_facturation INNER JOIN static_commandes ON adresses_facturation.af_emailclient = static_commandes.client_email WHERE static_commandes.id = '$idcde' ORDER BY adresses_facturation.id DESC LIMIT 1") or die ("requete r6 invalid");
$result7 = $database->prepare("SELECT af_emailclient FROM adresses_facturation INNER JOIN static_commandes ON adresses_facturation.af_emailclient = static_commandes.client_email WHERE static_commandes.id = '$idcde' ORDER BY adresses_facturation.id DESC LIMIT 1") or die ("requete r6 invalid");
$result7->execute();
$id = $result7->fetch();
$result8 = $database->prepare("SELECT telclient FROM clients WHERE emailclient = '".$id['af_emailclient']."'") or die ("requete r8 invalid");
$result8->execute();
$tel = $result8->fetch();
?>

<div id="corps_fact">

<h1> Modification d'une adresse </h1></br>
<form method="post" action="<?php echo $basePath; ?>index.php?page=admin&action=facture&type=validad&idcommande=<?php echo $idcde; ?>">
<?php 

//modifie adresse livraison client
$result5->execute();
echo '<div id="adresseL">';
echo "<h3>Adresse Livraison</h3>";
while ($tab = $result5->fetch()) {
		echo "Nom Prenom : ";
		echo '<td><input type="text" name="raisonsocial" size="35" value= "'.$tab['raisonsocial'].'"/></td>';
		echo "<br />";
		echo "Adresse : ";
		echo '<td><input type="text" name="nomrue" size="50" value= "'.$tab['nomrue'].'"/></td>';
		echo "<br />";
		echo "Code postal : ";
		echo '<td><input type="text" name="codepostal" size="6" value= "'.$tab['codepostal'].'"/></td>';
		echo "<br />";
		echo "Ville : ";
		echo '<td><input type="text" name="ville" size="20" value= "'.$tab['ville'].'"/></td>';
		echo "<br />";
		echo "Pays : ";
		echo '<td><input type="text" name="pays" size="20" value= "'.$tab['pays'].'"/></td>';

		echo "Tel : ";
		echo '<td><input type="text" name="tel" size="20" value= "'.$tel['telclient'].'"/></td>';
}
echo '</div>';

echo '<div id="adresseF">';
//modifie adresse facturation client
$result6->execute();
echo "<h3>Adresse Facturation</h3>";
while ($tab3 = $result6->fetch()) {
	    echo '<td><input type="hidden" name="af_idadresse" value= "'.$tab3['id1'].'"/></td>';
		echo "Nom Prenom : ";
		echo '<td><input type="text" name="af_raisonsocial" size="35" value= "'.$tab3['af_raisonsocial'].'"/></td>';
		echo "<br />";
		echo "Adresse : ";
		echo '<td><input type="text" name="af_nomrue" size="50" value= "'.$tab3['af_nomrue'].'"/></td>';
		echo "<br />";
		echo "Code postal : ";
		echo '<td><input type="text" name="af_codepostal" size="6" value= "'.$tab3['af_codepostal'].'"/></td>';
		echo "<br />";
		echo "Ville : ";
		echo '<td><input type="text" name="af_ville" size="20" value= "'.$tab3['af_ville'].'"/></td>';
		echo "<br />";
		echo "Pays : ";
		echo '<td><input type="text" name="af_pays" size="20" value= "'.$tab3['af_pays'].'"/></td>';
}
echo '</div>';		

?>
<div id="validation_adresse"><div align="center"><input type="submit" value="valider" /></div></div>
</form>
</div>