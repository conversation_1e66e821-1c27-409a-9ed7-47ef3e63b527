<?php
require_once(dirname(__FILE__).'/../../class/client_class.php');
require_once(dirname(__FILE__).'/../../class/adresse_class.php');
require_once(dirname(__FILE__).'/../../class/commande_static_class.php');
?>
<h1><img src="<?php echo $basePath; ?>public/images/clients_modifier.jpg" height="30" alt="clients"/> Modifier les informations d'un client</h1>
<?php

	if(isset($_GET['id'])):
		$client = clientDB::getClientByEmail($_GET['id']);
		$info = "";
		$id = $_GET['id'];
		$result = $database->prepare("SELECT * FROM adresses_facturation WHERE af_emailclient='".$id."'") or die ("r1 invalid");
		$result->execute();
		$af = $result->fetch();
		$result2 = $database->prepare("SELECT * FROM clients WHERE emailclient='".$id."'") or die ("r1 invalid");
		$result2->execute();
		$cli = $result2->fetch();
		
		if(isset($_POST['submit'])):
			// traitement du formulaire
			if($_POST['nom'] != '' && $_POST['prenom'] != ''){
				// seul les champs nom et prenom sont obligatoire
				$nbChampModif = 0;
				$nb_ad_fact_modif = 0;
				if($_POST['nom'] != $client->getNom() && $_POST['prenom'] != $client->getPrenom()) {
					$nbChampModif++;
					$client->setNom(stripslashes($_POST['nom']),stripslashes($_POST['prenom']));
				}
				if($_POST['fax'] != $client->getFax()) {
					$nbChampModif++;
					$client->setFax($_POST['fax']);
				}
				if($_POST['telephone'] != $client->getTel()) {
					$nbChampModif++;
					$client->setTel($_POST['telephone']);
				}
				if($_POST['infos'] != $cli['infos']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$infos = $_POST['infos'];
					$database->query("UPDATE clients SET infos = '$infos' WHERE emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($_POST['af_raisonsocial'] != $af['af_raisonsocial']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$af_raisonsocial = $_POST['af_raisonsocial'];
					$database->query("UPDATE adresses_facturation SET af_raisonsocial = '$af_raisonsocial' WHERE af_emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($_POST['af_nomrue'] != $af['af_nomrue']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$af_nomrue = $_POST['af_nomrue'];
					$database->query("UPDATE adresses_facturation SET af_nomrue = '$af_nomrue' WHERE af_emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($_POST['af_codepostal'] != $af['af_codepostal']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$af_codepostal = $_POST['af_codepostal'];
					$database->query("UPDATE adresses_facturation SET af_codepostal = '$af_codepostal' WHERE af_emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($_POST['af_ville'] != $af['af_ville']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$af_ville = $_POST['af_ville'];
					$database->query("UPDATE adresses_facturation SET af_ville = '$af_ville' WHERE af_emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($_POST['af_pays'] != $af['af_pays']) {
					$nbChampModif++;
					$nb_ad_fact_modif++;
					$af_pays = $_POST['af_pays'];
					$database->query("UPDATE adresses_facturation SET af_pays = '$af_pays' WHERE af_emailclient='".$id."'") or die ("requete update r3 invalid");
				}
				if($nbChampModif != 0) {
					if($nb_ad_fact_modif != 0){
						// sauvegarde ok
						// on affiche un message comme quoi le client a bien été mis à jour
						$info = "<div class='valide messageBox'><p>Les informations du client ont bien été mis à jour.</p></div>";
					} else {
						// echec de la mise à jour
						$info = "<div class='error messageBox'>
									<p>Erreur lors de la mise a jour, Une des information saisie est incorrecte</p>
								</div>";
					}
				} else {
					$info = "<div class='info messageBox'><p>Les informations du client n'ont pas été mis à jour car vous n'avez modifié aucun champs</p></div>";
				}
			} else {
				$info = "<div class='error messageBox'><p>Des champs obligatoire n'ont pas été renseigné</p></div>";
			}
		endif;
		?>
		<?php if($info != ''): ?>
			<?php echo $info; ?>
		<?php endif; ?>
		<div class="form">
			<form action="" method="post">
				<fieldset class="input">
				Informations client :<br/>
					<ol>
					
						<?php
						
						$result5 = $database->prepare("SELECT * FROM clients WHERE emailclient='".$id."'") or die ("r4 invalid");
						$result5->execute();
						
						while ($tab4 = $result5->fetch()) {
								echo '<li><label for="email">Email :</label><input type="text" name="email" size="25" value= "'.$tab4['emailclient'].'"/></li>';
								echo '<li><label for="nom">Nom :</label><input type="text" name="nom" size="50" value= "'.$tab4['nomclient'].'"/></li>';
								echo '<li><label for="prenom">Prenom :</label><input type="text" name="prenom" size="6" value= "'.$tab4['prenomclient'].'"/></li>';
								echo '<li><label for="telephone">Telephone :</label><input type="text" name="telephone" size="20" value= "'.$tab4['telclient'].'"/></li>';
								echo '<li><label for="fax">Fax :</label><input type="text" name="fax" size="20" value= "'.$tab4['faxclient'].'"/></li>';
								echo '<li><label for="infos">Infos :</label><input type="text" name="infos" size="20" value= "'.$tab4['infos'].'"/></li>';
						}
						?>
						<li>
							<?php
								if(count(commande_staticDB::getCommandesByClient($client)) != 0) {
									$sql = "SELECT commande_date FROM static_commandes WHERE client_email = :eclient ORDER BY commande_date DESC LIMIT 1";
									$db = Zend_Registry::get('database');
									$stmt = $db->prepare($sql);
									$stmt->bindParam(":eclient",$client->getEmail());
									$stmt->execute(); 
									$row = $stmt->fetch();
								
							 	?>
								<label>Date de derniere commande :</label>
								<input type="text" name="dateAchat" disabled="disabled" value="<?php echo date('d/m/Y',strtotime($row['commande_date'])); ?>"/>
								<?php
								}
							 ?>
						</li>
						<li>
							<label class="reditalique">* Champs obligatoires</label>
						</li>
					</ol>
				</fieldset>
				<fieldset class="submit">
					<p>	
						<input type="submit" name="submit" value="<?php echo $translate->_('Modifier'); ?>" class="btn_submit"/>
					</p>
				</fieldset>
			
		</div>
		
		<div class="form">
			
				<fieldset class="input">
				Adresse de facturation :<br/>
					<ol>
						<?php
						
						$result4 = $database->prepare("SELECT * FROM adresses_facturation WHERE af_emailclient='".$id."'") or die ("r2 invalid");
						$result4->execute();
						
						while ($tab3 = $result4->fetch()) {
								echo '<li><label for="Raison social">Raison social :</label><input type="text" name="af_raisonsocial" size="25" value= "'.$tab3['af_raisonsocial'].'"/></li>';
								echo '<li><label for="Adresse">Adresse :</label><input type="text" name="af_nomrue" size="50" value= "'.$tab3['af_nomrue'].'"/></li>';
								echo '<li><label for="Code postal">Code postal :</label><input type="text" name="af_codepostal" size="6" value= "'.$tab3['af_codepostal'].'"/></li>';
								echo '<li><label for="Ville">Ville :</label><input type="text" name="af_ville" size="20" value= "'.$tab3['af_ville'].'"/></li>';
								echo '<li><label for="Pays">Pays :</label><input type="text" name="af_pays" size="20" value= "'.$tab3['af_pays'].'"/></li>';
						}
						?>
					</ol>
				</fieldset>
				<fieldset class="submit">
					<p>	
						<input type="submit" name="submit" value="<?php echo $translate->_('Modifier'); ?>" class="btn_submit"/>
					</p>
				</fieldset>
			</form>
		</div>
		<div class="form">
			<fieldset class="input">
				Liste des adresses de livraison:<br/>
				<?php
					$adresses = adresseDB::getAdressesByClient($client);
					if(count($adresses) == 0): // il n'y a pas d'adresse 
						?><p>Ce client n'a pas d'adresses enregistré pour le moment.</p><?php
					else:
						$i = 0;
						echo '<div class="adresse_ligne">';
						foreach($adresses as $adresse):
							?>
							<div class="adresse">
								<div class="adr">
									<?php echo stripslashes($adresse->getRaisonSocial()); ?><br />
									<?php echo stripslashes($adresse->getNomRue()); ?><br />
									<?php echo stripslashes($adresse->getCodePostal()),' ', stripslashes($adresse->getVille()); ?><br />
									<?php echo stripslashes($adresse->getPays()); ?><br />
								</div>
								<?php $adresse_a = array($adresse->getId(),$adresse->getRaisonSocial(),$adresse->getNomRue(),$adresse->getCodePostal(),$adresse->getVille(),$adresse->getPays()); ?>
							</div>
							<?php
						endforeach;
						echo '</div>';
					endif;
				?><br/>
			</fieldset>
		</div>
		<br/>
		<div class="form">
			<fieldset class="input">
			Listes des commandes:<br/>
			<?php
				$commandes = commande_staticDB::getCommandesByClient($client);
				if(count($commandes) == 0):
					?><p>Ce client n'a pas de commande pour le moment.</p><?php
				else:
					?>
					<table class="liste">
						<tr>
							<th>Numéro</th>
							<th>Date</th>
							<th>Montant TTC</th>
							<th>Statut</th>
							<th></th>
						</tr>
					
					<?php
					foreach ($commandes as $commande):
						?>
						<tr>
							<td class="td_center"><?php echo $commande->getId(); ?></td>
							<td class="td_center"><?php echo date('d/m/Y',strtotime($commande->getDate())); ?></td>
							<td class="td_center"><?php printf('%.2f €',$commande->getMontanttotalTTC()); ?></td>
							<td class="td_center"><?php echo $commande->getStatut(); ?></td>
							<td>
								<a href="<?php echo $basePath; ?>index.php?page=admin&action=commandes&id=<?php echo $commande->getId(); ?>">
									<img src="<?php echo $basePath; ?>public/images/zoom.png" />
								</a>
							</td>
						</tr>
						<?php
					endforeach;
					?></table><?php
				endif;
			 ?>
			</fieldset>
		</div>
		<!-- on affiche les informations relive au client -->
		<p class="bouton_retour">
			<a href="<?php echo $basePath ?>index.php?page=admin&action=gererclient"><?php echo $translate->_('Retour'); ?></a>
		</p>
	<?php endif;