<?php
//definition des variable

$idcde = $_GET['idcommande'];

if (isset($_SESSION['who']) && $_SESSION['who'] != ""){
$who = $_SESSION['who'];
} else {
$who = "";
}

// post la date + le numero de facture

$datefac2 = $_POST['datefac'];
$datefac3 = explode("/", $datefac2);
$datefac = "".$datefac3[2]."-".$datefac3[1]."-".$datefac3[0]."";
$heure = date("H:i:s");

$numfact = $_POST['numfact'];

// post ligne commande dynamique

$idlign = $_POST['idlign'];
$reference = $_POST['reference'];
$genrename = $_POST['genrename'];
$designation = $_POST['designation'];
$puhtproduit = $_POST['puhtproduit'];
$qteproduit = $_POST['qteproduit'];
if(isset($_POST['prix_revient'])){
$prix_revient = $_POST['prix_revient'];
} else {
$prix_revient = "";
}

//post totaux

$tva = $_POST['tva'];
$port = $_POST['port'];
$remise = $_POST['remise'];
$trop_percu = $_POST['trop_percu'];
$remb = $_POST['remb'];

//post reglement

if (isset($_POST['regl'])){
$regl = $_POST['regl'];
}
if (isset($_POST['dateregl'])){
$dateregl = $_POST['dateregl'];
}
$numch = $_POST['numch'];
$banqch = htmlspecialchars($_POST['banqch'], ENT_QUOTES);
$numcb = $_POST['numcb'];
$comMixte = htmlspecialchars($_POST['comMixte'], ENT_QUOTES);
$datedif = $_POST['datedif'];
$numvir = $_POST['numvir'];
$numpp = $_POST['numpp'];

//post information supplementaire

if (isset($_POST['numcompt'])){
$numcompt = $_POST['numcompt'];
}

if (isset($_POST['fdp_reel'])){
$fdp_reel = $_POST['fdp_reel'];
} else {
$fdp_reel = "0";
}

$commentaire = htmlspecialchars($_POST['commentaire'], ENT_QUOTES);
?>
