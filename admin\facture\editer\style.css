*,html{
	margin: 0;	
}

body {
	background-color: #494825; /*vert fonc�*/
	color: #000000;
	
}
img {
	border: 0px;
}

h1 {
	/*position:relative;*/
	margin:10px;
	font-size: 24px;
	color:#494825;
	vertical-align:middle;
	font-weight:normal;
	font-variant:small-caps;
	/*text-transform: uppercase;*/
	border-bottom:1px dotted;
}
h1 span {
	position:absolute;
	margin-top:5px;
	margin-left:2px;
	
}

h2 {
	margin:10px;
	font-size:20px;
	color:#888545;
	vertical-align:middle;
	font-weight:normal;
	font-variant:small-caps;
	text-decoration:underline;

}

a, a:visited {
	text-decoration: none;
	color: #000000;
	background: transparent;
}

a:hover {
	text-decoration: none;
	color: #7FB5D1;
}

.clear{
	clear:both;
	margin-bottom:-1px;
	padding-bottom:1px;
}

#site {
	text-align: left;
	background-color: #c8c665;
	background: #c8c665 url(../images/side_shadow.png) repeat-y ;
	width: 940px;
	min-height: 100%;
	margin: 0 auto;
	position: relative;
}

#popupadd {
	text-align:center;
	background-color:#cec763;
	width:500px;
	height:200px;
	top:200px;
	left:220px;
	z-index:42;
	border:dashed;
	position:absolute;
}
#popupdevis {
	text-align:center;
	background-color:#cec763;
	width:500px;
	height:auto;
	top:200px;
	left:220px;
	z-index:44;
	border:dashed;
	position:absolute;
}
#commentaire_nd {
	text-align:center;
	background-color:#cec763;
	width:500px;
	height:230px;
	top:100px;
	left:220px;
	z-index:44;
	border:dashed;
	position:absolute;
}
#popupmsgdevis {
	text-align:center;
	background-color:#cec763;
	width:500px;
	height:200px;
	top:200px;
	left:220px;
	z-index:45;
	border:dashed;
	position:absolute;
}

#site #top {
	position: relative;
	width: 920px;
	height: 170px;
	left: 10px;
	border-bottom: 1px solid black;
	background: #c8c665;
}

#site #middle #searchbar {
	position:absolute;
	width: 220px;
	right:0px;
	height:24px;
	margin:0;
	padding:0;
	top:0px;
	background: #c8c665;
	border-left:1px solid;
	border-bottom:1px solid;
	margin-right: 0;
}
#site #top #logo {
	position: absolute;
	top: 0px;
	left: 0px;
}

#site #top #mainmenu {
	position: absolute;
	left: 200px;
	top: 110px;
	margin: 0;
	padding: 0;
	height: 60px;
	width: 460px;
	background: url(../images/mainmenu_back.jpg) repeat-x;
	z-index: 10;
}

#mainmenu_bord {
	position: absolute;
	top: 110px;
	left: 180px;
}

#site #top #menu {	
	position: absolute;
	top: 0;
	left:660px;
	height: 170px;
	width: 260px;
	padding: 0;
	margin: 0;
	background: #888545 url(../images/menu_left.jpg) no-repeat;
	text-align: left;	
}

#site #top #menu #user{
	position: absolute;
	top: 0;
	left:30px;
	right: 0;
	width: 220px;
	font-size: 0.8em;
}

#site #top #menu #user #logout{
	position: absolute;
	right: 0;
	top:0;
}

#site #top #menu #user input{
	width: 140px;
}

#site #top #menu #user ul {
  margin: 0;
  margin-top: 0px;
  padding: 0px;  
}

#site #top #menu #user ul li {
  list-style-type: none;
  margin: 0;
  margin-left:5px;
  padding: 0; 
}

#site #top #menu #user ul li a {
  padding-left: 10px;
  padding-top: 0px;
  padding-bottom: 0px;
  margin: 0px;
  text-decoration: none;
  color: #000000;
  background: url(../images/arrow_subCat.png) no-repeat scroll 0px;
}

#site #top #menu #user ul li a:hover {
  padding-left: 10px;
  text-decoration: none;
  color: #7FB5D1;
}

#site #top #menu #panier{
	position: absolute;
	top: 120px;
	right: 10px;
	font-size:0.8em;
}

#site #top #info {
	position: absolute;
	top: 10px;
	left: 220px;
}
#site #top #info p {

	width: 350px;
	margin-left: 30px;
	font-size: 1.1em;
	font-weight: bold;
	text-align: center;
}
#site #top #langues {
	position: absolute;
	left: 2px;
	top: 129px;
	width: 90px;
	font-size: 0.7em;	
}

#site #middle {
	position: relative;
	min-height: 400px;
	height: 100%;
	width: 920px;
	margin-left:10px;
	margin-right:10px;
	background: white;	
	text-align: left;
	padding-bottom: 50px;
	overflow: auto;
	padding-bottom: 50px; 
}
#site #middle #bandeau_dday{
	height: 130px;
	width: 180px;
	background-color: #FFFFFF;
	background-image: url(../images/bandeau_dday.png);
	display: block;
	font-size: 102px;
	padding-top: 20px;
	padding-left: 420px;
	font-family: Arial, Helvetica, sans-serif;
	font-weight: 600;
	color: #FF9900;
	text-shadow: 3px 3px 0px #aaa;
	box-shadow: 3px 3px 0px #aaa;
	background-repeat: no-repeat;
	margin-top: 15px;
	margin-left: 160px;
	border: 3px solid #0099FF;
}

#site #middle #subCategorie {
	position:relative;
	top: 0;
	left: 0;
	width: 280px;
	float: left;
	text-align: left;
}
#site #middle #content {
	margin-left: 280px;
	padding: 10px;
	padding-left: 10px;
	width: 67%;
	text-align: left;
}

#site #bottom {
	height: 20px;
	text-align: center;
	background: #c8c665 url(../images/side_shadow.png) repeat-y ;
	position: absolute;
	width: 100%;
	bottom: 0;
}

#site #middle #annonce_smi {
	float: left;
	width: 270px;
	margin-left: 25px;
	height: auto;
	margin-right: 25px;
	border: 1px solid #666600;
	font-family: Verdana, Arial, Helvetica, sans-serif;
}
#site #middle #consigne_smi {
	float: right;
	width: 270px;
	margin-left: 25px;
	height: 200px;
	margin-right: 25px;
	border: 1px solid #666600;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-size: 11px;
	position:fixed;
	background-color: #E1E1E1;
	padding: 3px;
	display: block;
}
#site #middle #annonce_smi #corps_annonce_smi {
	background-image: url(../images/annonce/fond_annonce.png);
	height: 145px;
	width: 270px;
	border-bottom-width: 1px;
	border-bottom-style: dotted;
	border-bottom-color: #333333;
	margin-bottom: 20px;
}
#site #middle #annonce_smi #corps_annonce_smi img {
	margin-top: 7px;
	margin-left: 5px;
	float: left;
	margin-right: 5px;
	border: 1px solid #333333;
}

#site #middle #annonce_smi #corps_annonce_smi h3 {
	padding-top: 10px;
	padding-right: 0px;
	padding-bottom: 0px;
	padding-left: 0px;
	color: #333333;
	font-size: 14px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	margin-left: 0px;
	border: 0px;
}
#site #middle #annonce_smi #corps_annonce_smi .description a {
	height: 19px;
	width: 105px;
	display: block;
	background-image: url(../images/annonce/fond_description.png);
	color: #333333;
	text-decoration: none;
	padding-top: 2px;
	padding-left: 15px;
	margin-top: 30px;
	font-size: 12px;
	float: left;
}

#site #middle #annonce_smi #corps_annonce_smi .description a:hover {
	background-image: url(../images/annonce/fond_description_hover.png);
}

#site #middle #annonce {
	float: left;
	height: 145px;
	margin-bottom: 10px;
	width: 555px;
	border: 1px solid #666666;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-size: 12px;
	background-image: url(../images/annonce/fond_annonce.png);
	background-repeat: repeat-x;
	display: block;
	
}

#site #middle #annonce #left {
	float: left;
	height: 130px;
	width: 130px;
	margin-top: 6px;
	margin-left: 5px;
}

#site #middle #annonce #left img {
	border: 1px solid #666666;
}
#site #middle #annonce #mid {
	height: 145px;
	width: 275px;
	float: left;
	padding-left: 10px;
}
#site #middle #annonce #mid h3 {
	margin: 0px;
	padding-top: 10px;
	padding-right: 0px;
	padding-bottom: 0px;
	padding-left: 0px;
	color: #333333;
}
#site #middle #annonce #mid h4 {
	text-align: left;
	font-size: 12px;
	vertical-align:middle;
	text-transform: uppercase;
	border-bottom:1px dotted;
	margin: 0px;
	padding-top: 10px;
	padding-right: 0px;
	padding-bottom: 0px;
	padding-left: 0px;
	color: #333333;
}
#site #middle #annonce #mid p {
	padding: 0px;
	margin-top: 10px;
	margin-right: 0px;
	margin-bottom: 0px;
	margin-left: 0px;
	color: #666666;
}
#site #middle #annonce #right {
	float: left;
	height: 145px;
	width: 130px;
}
#site #middle #annonce #right .prix {
	margin-top: 10px;
	font-size: 20px;
	color: #FF0000;
	text-align: right;
	margin-right: 10px;
	font-weight: bold;
}
#site #middle #annonce #right .add a {
	color: #FFFFFF;
	text-decoration: none;
	height: 18px;
	width: 114px;
	display: block;
	padding-top: 2px;
	padding-left: 6px;
	background-image: url(../images/annonce/fond_add.png);
	margin-top: 30px;
}
#site #middle #annonce #right .add a:hover {
	background-image: url(../images/annonce/fond_add_hover.png);
}

#site #middle #annonce #right .description a {
	height: 19px;
	width: 105px;
	display: block;
	background-image: url(../images/annonce/fond_description.png);
	color: #333333;
	text-decoration: none;
	padding-top: 2px;
	padding-left: 15px;
	margin-top: 10px;
	margin-left: 5px;
}
#site #middle #annonce #right .description a:hover {
	background-image: url(../images/annonce/fond_description_hover.png);
}
#site #middle #corps_detail_annonce {
	margin-left: 10px;
	height: 620px;
	width: 890px;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	border: 5px solid #333333;
	background-image: url(../images/annonce/fond.png);
}
#site #middle #corps_detail_annonce #title_detail_annonce {
	height: 38px;
	width: 818px;
	background-color: #CC99CC;
	font-size: 24px;
	font-weight: bold;
	padding-top: 10px;
	text-align: center;
	margin-left: 35px;
	background-image: url(../images/annonce/fond_title.png);
	background-repeat: repeat-x;
	border: 1px solid #666666;
	margin-top: 5px;
}
#site #middle #corps_detail_annonce #top_detail_annonce {
	height: 200px;
	width: 820px;
	margin-left: 35px;
	margin-top: 20px;
}
#site #middle #corps_detail_annonce #top_detail_annonce #left_top_detail_annonce {
	height: 190px;
	width: 290px;
	float: left;
	border: 5px solid #666666;
	margin-right: 20px;
}
#site #middle #corps_detail_annonce #top_detail_annonce #left_top_detail_annonce img {

}

#site #middle #corps_detail_annonce #top_detail_annonce #right_top_detail_annonce {
	height: 180px;
	width: 480px;
	float: left;
	padding-top: 10px;
	padding-left: 10px;
	border: 5px solid #666666;
	background-color: #FFFFFF;
}
#site #middle #corps_detail_annonce #middle_detail_annonce {
	padding-top: 10px;
	height: 130px;
	width: 800px;
	margin-top: 20px;
	margin-left: 35px;
	padding-left: 10px;
	border: 5px solid #666666;
	background-color: #FFFFFF;
}
#site #middle #corps_detail_annonce #foot_detail_annonce {
	padding-top: 10px;
	height: 120px;
	width: 800px;
	margin-top: 20px;
	margin-left: 35px;
	padding-left: 10px;
	border: 5px solid #666666;
	background-color: #FFFFFF;
}


table.table_nomargin tr td {
	border-collapse:collapse;
}

.table_center {
	max-width:600px;
	margin-left:auto;
	margin-right:auto;
}
.td_right{
	text-align:right;
	min-width: 50px;
}
.td_left {
	text-align:left
}

.td_center {
	text-align:center;
}

.reditalique {
	color:red;
	font: italic 75%/125% "Comic Sans MS", cursive;
}

/* Champs obligatoires */

input.indispensable {
	background-color: #FFFFDD;
	border: 1px solid grey;
}
table.tableerror td{
	border-bottom:1px dotted red;
}
table.tableerror th{
	border-bottom:1px dotted red;
	background:#FFD8CC;
}
table.liste{
	font-size:0.8em;
	width:98%;
	border-collapse:collapse;
	border:2px solid black;
	border-left: none;
	border-right:none;
	margin-left:auto;
	margin-right:auto;
}

table.liste th{
	border-top:1px solid black;
	border-bottom:1px solid black;
	background:#C8C665;
	text-align:center;
}

table.liste tr {
	height:20px;
}

table.liste tr.imp td{
	/*background: #EEEEEE;*/
	background: #e2dedf;
}
	
table.liste td{
	/*border-top:1px dotted black;*/
	/*border-bottom:1px dotted black;*/
}

body {
     margin: 0; 
	 background-color: #494825; 
}

/* annonce : ajout*/
#form_Annonce {
	width: 80%;
	margin: auto;
}

#form_Annonce fieldset {
	margin-bottom: 10px;
}

#form_Annonce .label {
	display:-moz-inline-box;
	display: inline-block;
	line-height: 1.8;
	vertical-align: top;
	width: 250px;
	text-align: right;
}
#form_Annonce li {
	list-style-type: none;
	padding: 5px;
	margin: 0;
}
#form_Annonce ol {
	margin: 0;
	padding: 0;
	
}
#form_Annonce fieldset.submit {
	/*border:none;
	text-align:center;*/
	background:#C8C665 none repeat scroll 0% 50%;
	margin-bottom:20px;
	margin-top:-1px;
	padding-bottom:1px;
	padding-top:1px;
	text-align:right;
}
#form_Annonce fieldset.submit li{
	padding-top: 0px;
	padding-bottom: 0px;
}
#form_Annonce .btn_submit{
	width: 100px;
	border: 1px solid black;
	background: LightYellow;
}
.commande{
	text-decoration: none;
	display: block;
	border-top-style: none;
	border-right-style: none;
	border-bottom-style: none;
	border-left-style: none;
	background-color: #FFFFFF;
	height: 32px;
	width: 32px;
	background: url('../images/commande.png') no-repeat;
}
#form_Annonce .inputNormal {
	width: 250px;
}
#form_Annonce .inputDroit {
	width: 250px;
}
.AnnonceAdd {
	font-size: 1.1em;
	padding-left: 160px;
}
#annonceDetail {
	font-size: 1.1em;
}
#annonceDetail table{
	margin: auto;
	width: 500px;
}
#annonceDetail td.cadre{
	border: solid 1px black;
	
}
#annonceDetail ul{
	list-style-type: none;
}
#annonceDetail table .detail{
	vertical-align: top;
	border: 1px solid;
	min-height: 100px;
	height: 100%;
}
h2 {
	text-align: center;
	position:relative;
	font-size: 18px;
	text-decoration:none;
	color:#494825;
	vertical-align:middle;
	text-transform: uppercase;
	border-bottom:1px dotted;
}

h3 {
	text-align: left;
	position:relative;
	font-size: 12px;
	color:#494825;
	vertical-align:middle;
	text-transform: uppercase;
	border-bottom:1px dotted;
	
}
/* utile pour d�caler du bord gauche des block */
.decale {
	margin-left:10px;
}
/* pour que les liens retour soit plus jolie*/
.bouton_retour {
	width: 150px;
	padding-left: 20px;
	position: relative;
	top: 20px;
	left: 110px;
	background: url('../images/arrow_undo.png') no-repeat;
}
/* pour les titres de chaques pages */
.titre {
	position:relative;
	font-size: 24px;
	color:#494825;
	vertical-align:middle;
	text-transform: uppercase;
	border-bottom:1px dotted;
}
.gras {
	font-weight:bold;
}
input.ombre {
	background:#FFFFFF url(../images/form-field-bg.gif) no-repeat scroll left top;
	z-index:1;
	border:medium none;
	color:#999999;
	height:17px;
	padding:3px 7px 2px 5px;
}

input.search {
	background:#FFFFFF url(../images/zoom.png) no-repeat scroll 2px 2px;
	z-index:2;
	padding-left: 20px;
}

/* design des formulaire */
.form {
	width: 80%;
	margin: auto;
}
.form fieldset {
	/*margin-bottom: 10px;*/
	border: 1px solid black;
}
.form label {
  display:-moz-inline-box;
	display: inline-block;
	line-height: 1.8;
	vertical-align: top;
	width: 250px;
	text-align: right;
}
.form li {
	list-style-type: none;
	padding: 5px;
	margin: 0;
}
.form ol {
	margin: 0;
	padding: 0;
}
.form input {
	width: 250px;
}
.form textarea {
	width: 250px;
	height: 100px;
}
.form fieldset.submit {
	/*border:none;
	text-align:center;*/
	background:#C8C665 none repeat scroll 0% 50%;
	margin-bottom:20px;
	margin-top:-1px;
	padding-bottom:1px;
	padding-top:1px;
	text-align:right;
}
.form fieldset.submit li{
	padding-top: 0px;
	padding-bottom: 0px;
}

.form .btn_submit{
	width: 100px;
	border: 1px solid black;
	background: LightYellow;
}
.form2 {
	width: 80%;
	margin: auto;
}
.form2 fieldset {
	/*margin-bottom: 10px;*/
	border: 1px solid black;
}
.form2 label {
  display:-moz-inline-box;
	display: inline-block;
	line-height: 1.8;
	vertical-align: top;
	width: 250px;
	text-align: right;
}
.form2 li {
	list-style-type: none;
	padding: 5px;
	margin: 0;
}
.form2 ol {
	margin: 0;
	padding: 0;
}
.form2 input {
	margin-left:25px;
	margin-right:25px;
}
.form2 img {
	margin-left:125px;
}
.form2 textarea {
	width: 250px;
	height: 100px;
}
.form2 fieldset.submit {
	/*border:none;
	text-align:center;*/
	background:#C8C665 none repeat scroll 0% 50%;
	margin-bottom:20px;
	margin-top:-1px;
	padding-bottom:1px;
	padding-top:1px;
	text-align:right;
}
.form2 fieldset.submit li{
	padding-top: 0px;
	padding-bottom: 0px;
}

.form2 .btn_submit{
	width: 100px;
	border: 1px solid black;
	background: LightYellow;
}
.liens_action {
	width: 150px;
}
.credit {
	font-variant:small-caps;
	width: 400px;
	margin: auto;
	color:#494825;
}
.credit ul {
	list-style-type: none;
}
.credit ul li ul li{
	padding-left: 50px;
}
.navigateur img{
	height: 50px;
}
.iut {
	position: absolute;
	top: 100px;
	left: 700px;
}
.bigTxt {
	width: 600px;
	height: 400px;
}
/* Onglet pour le menu gerer paramètres du site */

#onglet {
	text-align: center;
	padding-bottom: 0;
	margin-bottom: 0;
	border-bottom: 1px solid black;
}
#onglet ul {
	margin: 0;
	padding: 0;
}
#onglet li  {
	display: inline;
	list-style-type: none;

}
#onglet li.active  {
	display: inline;
	list-style-type: none;
}

#onglet a {
	border-top: 1px solid black;
	border-left: 1px solid black;
	border-right: 1px solid black;
	border-bottom: 1px solid black;
	padding-left: 3px;
	padding-right: 3px;
	margin-bottom: 0px;
	padding-bottom: 1px;
	* padding-bottom: 0px;
}

#onglet a:active {
	border-top: 1px solid black;
	border-left: 1px solid black;
	border-right: 1px solid black;
	border-bottom: 1px solid white;
	padding-left: 3px;
	padding-right: 3px;
	padding-bottom: 1px;
	* padding-bottom: 0px;
}
#onglet a:hover {
	border-top: 1px solid black;
	border-left: 1px solid black;
	border-right: 1px solid black;
	border-bottom: 1px solid white;
	padding-left: 3px;
	padding-right: 3px;
	padding-bottom: 1px;
	* padding-bottom: 0px;
}
#onglet a.actif {
	border-top: 1px solid black;
	border-left: 1px solid black;
	border-right: 1px solid black;
	border-bottom: 1px solid white;
	padding-left: 3px;
	padding-right: 3px;
	padding-bottom: 1px;
	* padding-bottom: 0px;
}


.param {
	width: 80%;
	margin: auto;
}
.param fieldset {
	margin-bottom: 10px;
	border: 1px solid transparent;
}
.param label {
  	display:-moz-inline-box;
	display: inline-block;
	line-height: 1.8;
	vertical-align: top;
	width: 250px;
	text-align: right;
}
.param li {
	list-style-type: none;
	padding: 5px;
	margin: 0;
}
.param ol {
	margin: 0;
	padding: 0;
}
.param input {
	width: 250px;
}
.param textarea {
	width: 250px;
	height: 100px;
}
.param fieldset.submit {
	border:none;
	text-align:center;
}
.param .btn_submit{
	width: 100px;
}

/* CSS pour le choix des adresses de livraison et de facturation */
.adresse_ligne{
	width:710px;
	margin-left:auto;
	margin-right:auto;
}
.adresse{
	float:left;
	width: 230px;
	background:white;
	padding:2px;		
}
.adresse .adr{
	border:1px solid black;
	padding-left:3px;
}

.adresse .button{
	background:#C8C665;
	text-align: right;
	border:1px solid black;
	margin-top: -1px;
}

.adresse .button input {
	width: 100px;
	border: 1px solid black;
	background: LightYellow;
	margin-right: 10px;
	margin-top: 2px;
	margin-bottom:2px;
}

fieldset.adresse_recap {
	width: 300px; 
	margin:10px;
}

.recap_panier{
	width:80%;
	margin-left:auto;
	margin-right:auto;
}

.justify {
	width: 80%;
	margin: auto;
}
#scroll ul{
	display: inline;
}
#scroll li {
	list-style-type: none;
	
	display: inline;
}
/* pour les tableaux de la pages liens */
div.block_liens {
	width:47%;
	/*float: left;*/
}
div.block_liens table{
	width:412px;
}
div.float_left {
	float: left;
}
.btn_panier {
	margin: 0;
	padding: 0;
}
.btn_panier span {
	padding: 0px;
	margin: 0px;
	
}
.btn_panier span:hover {

}
.LV_valid {
  	color:#FFFFFF;
	background: url(../images/accept.png) no-repeat;
	height: 16px;
	width: 16px;
	padding-top: 0px;
	padding-right: 8px;
	padding-bottom: 3px;
	padding-left: 8px;
	margin-left: 5px;
	display: inline;
	
}
	
.LV_invalid {
	color:#000000;
	background: url(../images/cross.png) no-repeat;
	height: 16px;
	width: 16px;
	padding-top: 0px;
	padding-right: 8px;
	padding-bottom: 3px;
	padding-left: 8px;
	margin-left: 5px;
	display: inline;
}
a.aide {
	background: #FFFFFF url(../images/aide.png) no-repeat;
	height: 16px;
	width: 16px;
	padding-top: 0px;
	padding-right: 8px;
	padding-bottom: 0px;
	padding-left: 8px;
	margin-left: 5px;
	display: inline;
	color:#FFFFFF;
}
a.aide_menu {
	background: url(../images/aide.png) no-repeat;
	padding-top: 0px;
	padding-bottom: 0px;
	padding-left: 16px;
}
#site #top #menu #panier  table  tbody  tr  td  a.panier {

	background: url(../images/panier.png) no-repeat;
	height: 45px;
	width: 45px;
	margin-top: 3px;
	padding-top: 22px;
	padding-right: 23px;
	padding-bottom: 23px;
	padding-left: 22px;
	display: inline;

}

#site #top #menu #panier  table  tbody  tr  td  a.panier:hover {

	background: url(../images/panier_hover.png) no-repeat;
	height: 45px;
	width: 45px;
	margin-top: 3px;
	padding-top: 22px;
	padding-right: 23px;
	padding-bottom: 23px;
	padding-left: 22px;
	display: inline;

}
#site #middle #form_panier {
	height: 111px;
	width: 410px;
	margin-left: 500px;
	display: block;
	background-color: #c8c665;
	border: 2px solid #000000;
}
#site #middle #form_panier a.recalculer {
	display: block;
	height: 25px;
	width: 95px;
	background-image: url(../images/recalculer_panier.png);
	background-repeat: no-repeat;
	padding-top: 85px;
	text-decoration: none;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	color: #000000;
	font-weight: bold;
	background-position: 15px 10px;
	padding-left: 5px;
	float: left;
}
#site #middle #form_panier a.enregistrer {
	display: block;
	height: 25px;
	width: 95px;
	background-image: url(../images/enregistrer_panier.png);
	background-repeat: no-repeat;
	padding-top: 85px;
	text-decoration: none;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	color: #000000;
	font-weight: bold;
	background-position: 15px 10px;
	padding-left: 5px;
	float: left;
}
#site #middle #form_panier a.panier_vercorps {
	display: block;
	height: 25px;
	width: 95px;
	background-image: url(../images/panier_vercorps.png);
	background-repeat: no-repeat;
	padding-top: 85px;
	text-decoration: none;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	color: #000000;
	font-weight: bold;
	background-position: 15px 10px;
	padding-left: 5px;
	float: left;
}
#site #middle #form_panier a.panier_vercorps:hover {
	background-image: url(../images/panier_vercorps_hover.png);
	background-repeat: no-repeat;
	color: #990000;
}
#site #middle #form_panier a.vider {
	display: block;
	height: 25px;
	width: 73px;
	background-image: url(../images/vider_panier.png);
	background-repeat: no-repeat;
	padding-top: 85px;
	text-decoration: none;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	color: #000000;
	font-weight: bold;
	background-position: 15px 10px;
	padding-left: 27px;
	float: left;
}
#site #middle #form_panier a.valider {
	display: block;
	height: 25px;
	width: 80px;
	background-image: url(../images/passer_commande.png);
	background-repeat: no-repeat;
	padding-top: 85px;
	text-decoration: none;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	color: #000000;
	font-weight: bold;
	background-position: 15px 10px;
	padding-left: 20px;
	float: left;
}
#site #middle #form_panier a.valider:hover {
	background-image: url(../images/passer_commande_hover.png);
	background-repeat: no-repeat;
	color: #990000;
}
#site #middle #form_panier a.vider:hover {
	background-image: url(../images/vider_panier_hover.png);
	background-repeat: no-repeat;
	color: #990000;
}
#site #middle #form_panier a.recalculer:hover {
	background-image: url(../images/recalculer_panier_hover.png);
	background-repeat: no-repeat;
	color: #990000;
}
#site #middle #form_panier a.enregistrer:hover {
	background-image: url(../images/enregistrer_panier_hover.png);
	background-repeat: no-repeat;
	color: #990000;
}

#site #middle a p.bouton_enquete {
	border: 3px solid #CCFF99;
	width: 320px;
	text-align: center;
	background-image: url(../images/enquete_icone.png);
	background-repeat: no-repeat;
	height: 45px;
	padding-left: 35px;
	padding-top: 5px;
	background-color: #669966;
	color: #CCFFFF;
	text-decoration: none;
	margin-left: 320px;
}