<?php
//definition des variable

$idcde = $_GET['idcommande'];
$result7 = $database->prepare("SELECT af_emailclient FROM adresses_facturation INNER JOIN static_commandes ON adresses_facturation.af_emailclient = static_commandes.client_email WHERE static_commandes.id = '$idcde' ORDER BY adresses_facturation.id DESC LIMIT 1") or die ("requete r6 invalid");
$result7->execute();
$id = $result7->fetch();

// POST adresse livraison

$raisonsocial = $_POST['raisonsocial'];
$nomrue = addslashes($_POST['nomrue']);
$codepostal = $_POST['codepostal'];
$ville =  $_POST['ville'];
$pays = $_POST['pays'];
$tel = $_POST['tel'];
$cp_ville = $codepostal." ".$ville;

$adresse_livraison = $raisonsocial."<br />".$nomrue."<br />".$cp_ville."<br />".$pays;

//POST adresse facuration

$af_idadresse = $_POST['af_idadresse'];
$af_raisonsocial = $_POST['af_raisonsocial'];
$af_nomrue = addslashes($_POST['af_nomrue']);
$af_codepostal = $_POST['af_codepostal'];
$af_ville = $_POST['af_ville'];
$af_pays = $_POST['af_pays'];

//Update adresse de livraison

$req1 = $database->query("

UPDATE static_commandes
SET
commande_adresselivraison = '$adresse_livraison'
WHERE id = '$idcde'

") or die ("requete update r1 invalid");

$req2 = $database->query("

UPDATE clients
SET
telclient = '$tel'
WHERE emailclient = '".$id['af_emailclient']."'

") or die ("requete update r2 invalid");

$req4 = $database->query("

UPDATE adresses
SET
raisonsocial = '$raisonsocial',
nomrue = '$nomrue',
codepostal = '$codepostal',
ville = '$ville',
pays = '$pays'
WHERE emailclient = '".$id['af_emailclient']."'

") or die ("requete update r2 invalid");

//Update adresse de facturation

$req3 = $database->query("

UPDATE adresses_facturation
SET
af_raisonsocial = '$af_raisonsocial', 
af_nomrue = '$af_nomrue',
af_codepostal = '$af_codepostal',
af_ville = '$af_ville',
af_pays = '$af_pays'
WHERE id = '$af_idadresse'

") or die ("requete update r3 invalid");

?>



<html>
<body>

<div class='valide messageBox'>Adresses Modifi&eacute;es !</div></br>
<div class="bouton_retour"><a href="<?php echo $basePath ?>index.php?page=admin&action=facture&type=edit&idcommande=<?php echo $idcde; ?>">retour</a></div>

</body>
</html>