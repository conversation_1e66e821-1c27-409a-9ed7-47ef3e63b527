<?php

include("../../../init2.php");

//variable date

$datedebut = $_POST['datedebut'];
$datefin = $_POST['datefin'];

$datedebut2 = explode("/", $datedebut);
$datefin2 = explode("/", $datefin);

$datedebut3 = "".$datedebut2[2]."-".$datedebut2[1]."-".$datedebut2[0]."";
$datefin3 = "".$datefin2[2]."-".$datefin2[1]."-".$datefin2[0]."";


//Requete

$result = mysql_query("SELECT * FROM facture WHERE date_facture BETWEEN '$datedebut3' AND '$datefin3' ORDER BY id_facture DESC") or die ("requete invalid r1");

//init variable

$csv = "";

//En tete champs

//Boucle corps champs

while ($row = mysql_fetch_array($result)){

//Variable
$typecolis = explode(";", $row['info_sup']);
$ttc = $row['ttc'];
$remise = $row['remise'];
$trop_percu = $row['trop_percu'];
$date = explode("-", $row['date_facture']);
$date2 = $date[0].$date[1].$date[2];
$net = $ttc-$remise+$trop_percu;
$net2 = number_format($net, 2, '.', '');
$net3 = -($ttc-$remise+$trop_percu);
$net4 = number_format($net3, 2, '.', '');
$tva = number_format($row['tva'], 2, '.', '');
$port = number_format($row['port'], 2, '.', '');
$totht = number_format($row['totht'], 2, '.', '');
$totpr = number_format($row['totprix_revient'], 2, '.', '');
$marge = number_format(($totht - $totpr), 2, '.', '');
$tva2 = -($tva);
$port2 = -($port);
$totht2 = -($totht);
$trop_percu2 = -($trop_percu);
$totht3 = number_format($totht2, 2, '.', '');
$idcde = $row['id_command'];
$regl = $row['regl'];
$regl2 = explode(";", $regl);
$tp = " - TP - ";

// Condition et variable colis
$nom_colis = $typecolis[0];
$compt_colis = substr($nom_colis, 0, 3);

if ($compt_colis != 626){
$nom_colis = "626POS";
}

// Condition et variable reglement
if ($regl2[1] == "CBi"){
$compt_VT = str_pad("585VTCB", 13);
}
if ($regl2[1] == "CBc"){
$compt_VT = str_pad("585VTCBcp", 13);
}
if ($regl2[1] == "CH"){
$compt_VT = str_pad("585VTCH", 13);
}
if ($regl2[1] == "esp"){
$compt_VT = str_pad("585VTESP", 13);
}
if ($regl2[1] == "pp"){
$compt_VT = str_pad("585VTPAYPAL", 13);
}
if ($regl2[1] == "vir"){
$compt_VT = str_pad("585VTVRT", 13);
}
if ($regl2[1] == "mixte"){
$compt_VT = str_pad("585VTMIX", 13);
}
if ($regl2[1] == "CR" || $regl2[1] == "differe"){
$compt_VT = str_pad("585VTCH", 13);
}

//Export des ventes
	//Condition pour les factures
	if ($ttc>0){

	if ($totpr != 0){
// Calcul de la marge et du prix de revient
	if ((strpos($row['genre'], "JEEP") === false) && (strpos($row['genre'], "DODGE") === false) && (strpos($row['genre'], "GMC") === false) && (strpos($row['genre'], "RENAULT") === false)){
		if ($marge < 0){
$csv .= "      1123VT    ".$date2."                       987".str_pad("AUTRE M", 10)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad(-$marge, 7, " ", STR_PAD_LEFT)."D                                                               O2003";
$csv .= "\r\n";
$csv .= "      1123VT    ".$date2."                       907M".str_pad("", 9)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad(-$marge, 7, " ", STR_PAD_LEFT)."C                                                               O2003";
$csv .= "\r\n";
		} else {
$csv .= "      1123VT    ".$date2."                       987".str_pad("AUTRE M", 10)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad($marge, 7, " ", STR_PAD_LEFT)."D                                                               O2003";
$csv .= "\r\n";
$csv .= "      1123VT    ".$date2."                       907M".str_pad("", 9)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad($marge, 7, " ", STR_PAD_LEFT)."C                                                               O2003";
$csv .= "\r\n";		
		}
$csv .= "      1123VT    ".$date2."                       957".str_pad("AUTRE PR", 10)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad($totpr, 7, " ", STR_PAD_LEFT)."D                                                               O2003";
$csv .= "\r\n";
$csv .= "      1123VT    ".$date2."                       907PR".str_pad("", 8)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad($totpr, 7, " ", STR_PAD_LEFT)."C                                                               O2003";
$csv .= "\r\n";
}else{
		if ($marge < 0){
$csv .= "      1123VT    ".$date2."                       987".str_pad($row['genre']."M", 10)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad(-$marge, 7, " ", STR_PAD_LEFT)."D                                                               O2003";
$csv .= "\r\n";
$csv .= "      1123VT    ".$date2."                       907M".str_pad("", 9)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad(-$marge, 7, " ", STR_PAD_LEFT)."C                                                               O2003";
$csv .= "\r\n";
		} else {
$csv .= "      1123VT    ".$date2."                       987".str_pad($row['genre']."M", 10)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad($marge, 7, " ", STR_PAD_LEFT)."D                                                               O2003";
$csv .= "\r\n";
$csv .= "      1123VT    ".$date2."                       907M".str_pad("", 9)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad($marge, 7, " ", STR_PAD_LEFT)."C                                                               O2003";
$csv .= "\r\n";		
		}
$csv .= "      1123VT    ".$date2."                       957".str_pad($row['genre']."PR", 10)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad($totpr, 7, " ", STR_PAD_LEFT)."D                                                               O2003";
$csv .= "\r\n";
$csv .= "      1123VT    ".$date2."                       907PR".str_pad("", 8)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad($totpr, 7, " ", STR_PAD_LEFT)."C                                                               O2003";
$csv .= "\r\n";
	}
}

}
}

//Export en csv

header("Content-type: application/txt");
header("Content-disposition: attachment; filename=export.txt");
print($csv);
exit;

?>