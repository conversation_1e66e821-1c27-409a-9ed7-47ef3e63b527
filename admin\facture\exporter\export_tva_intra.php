<?php

include("../../../init.php");

//variable date

$datedebut = $_POST['datedebut'];
$datefin = $_POST['datefin'];

$datedebut2 = explode("/", $datedebut);
$datefin2 = explode("/", $datefin);

$datedebut3 = "".$datedebut2[2]."-".$datedebut2[1]."-".$datedebut2[0]."";
$datefin3 = "".$datefin2[2]."-".$datefin2[1]."-".$datefin2[0]."";


//Requete
$result = $database->prepare("SELECT * FROM facture WHERE date_facture BETWEEN '$datedebut3' AND '$datefin3' ORDER BY id_facture DESC") or die ("requete invalid r1");

//init variable

$csv = "";

//En tete champs

//Boucle corps champs
$result->execute();
while ($row = $result->fetch()) {

//Variable
$email_client = $row['emailclient'];
$totht = number_format($row['totht'], 2, '.', '')+number_format($row['port'], 2, '.', '');
$tva = number_format($row['tva'], 2, '.', '');
$date = explode("-", $row['date_facture']);
$date2 = $date[0].$date[1].$date[2];

$result3 = $database->prepare("SELECT * FROM clients WHERE emailclient = '$email_client'") or die ("requete invalid r1");
$result3->execute();
$num_intra_com = $result3->fetch();
$NUM_INTRA_COM = $num_intra_com['tva_intra_com'];

//Export intra communautaire ue
if ($NUM_INTRA_COM != ""){
	if ($tva == 0){
		if ($totht >= 0){
			$csv .= "      1123VT    ".$date2."                       ".str_pad("7070LIC", 13)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad($totht, 7, " ", STR_PAD_LEFT)."C                                                               O2003";
			$csv .= "\r\n";
			$csv .= "      1123VT    ".$date2."                       ".str_pad("7070", 13)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad($totht, 7, " ", STR_PAD_LEFT)."D                                                               O2003";
			$csv .= "\r\n";
		} else {
			$csv .= "      1123VT    ".$date2."                       ".str_pad("7070LIC", 13)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad(-$totht, 7, " ", STR_PAD_LEFT)."D                                                               O2003";
			$csv .= "\r\n";
			$csv .= "      1123VT    ".$date2."                       ".str_pad("7070", 13)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad(-$totht, 7, " ", STR_PAD_LEFT)."C                                                               O2003";
			$csv .= "\r\n";
		}
	}
}

else {
	if ($tva == 0){
		//Export hors ue
		if ($totht >= 0){
			$csv .= "      1123VT    ".$date2."                       ".str_pad("7070EXPHUE", 13)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad($totht, 7, " ", STR_PAD_LEFT)."C                                                               O2003";
			$csv .= "\r\n";
			$csv .= "      1123VT    ".$date2."                       ".str_pad("7070", 13)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad($totht, 7, " ", STR_PAD_LEFT)."D                                                               O2003";
			$csv .= "\r\n";
		} else {
			$csv .= "      1123VT    ".$date2."                       ".str_pad("7070EXPHUE", 13)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad(-$totht, 7, " ", STR_PAD_LEFT)."D                                                               O2003";
			$csv .= "\r\n";
			$csv .= "      1123VT    ".$date2."                       ".str_pad("7070", 13)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad(-$totht, 7, " ", STR_PAD_LEFT)."C                                                               O2003";
			$csv .= "\r\n";
		}
	} else {
		//Export France
			if ($totht >= 0){
			$csv .= "      1123VT    ".$date2."                       ".str_pad("7070FRANCE", 13)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad($totht, 7, " ", STR_PAD_LEFT)."C                                                               O2003";
			$csv .= "\r\n";
			$csv .= "      1123VT    ".$date2."                       ".str_pad("7070", 13)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad($totht, 7, " ", STR_PAD_LEFT)."D                                                               O2003";
			$csv .= "\r\n";
		} else {
			$csv .= "      1123VT    ".$date2."                       ".str_pad("7070FRANCE", 13)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad(-$totht, 7, " ", STR_PAD_LEFT)."D                                                               O2003";
			$csv .= "\r\n";
			$csv .= "      1123VT    ".$date2."                       ".str_pad("7070", 13)."".$row['id_facture']." ".substr(str_pad(html_entity_decode($row['cli_nom_prenom']), 40),0 ,40)."        ".str_pad(-$totht, 7, " ", STR_PAD_LEFT)."C                                                               O2003";
			$csv .= "\r\n";
		}
	}
}

}

//Export en csv

header("Content-type: application/txt");
header("Content-disposition: attachment; filename=export.txt");
print($csv);
exit;

?>