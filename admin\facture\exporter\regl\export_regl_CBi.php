<?php

require("../init.php");

//variable date

$datedebut = $_POST['datedebut'];
$datefin = $_POST['datefin'];

$datedebut2 = explode("/", $datedebut);
$datefin2 = explode("/", $datefin);

$datedebut3 = "".$datedebut2[2]."-".$datedebut2[1]."-".$datedebut2[0]."";
$datefin3 = "".$datefin2[2]."-".$datefin2[1]."-".$datefin2[0]."";


//Requete

$result = mysql_query("SELECT * FROM facture WHERE date_facture BETWEEN '$datedebut3' AND '$datefin3' ORDER BY id_facture DESC") or die ("requete invalid r1");

//init variable

$csv = "";

//En tete champs

//Boucle corps champs

while ($row = mysql_fetch_array($result)){

//Variable
$typecolis = explode(";", $row['info_sup']);
$ttc = $row['ttc'];
$remise = $row['remise'];
$date = explode("-", $row['date_facture']);
$date2 = $date[0].$date[1].$date[2];
$net = $ttc-$remise;
$net2 = number_format($net, 2, '.', '');
$net3 = -($ttc-$remise);
$net4 = number_format($net3, 2, '.', '');
$tva = number_format($row['tva'], 2, '.', '');
$port = number_format($row['port'], 2, '.', '');
$totht = number_format($row['totht'], 2, '.', '');
$tva2 = -($tva);
$port2 = -($port);
$totht2 = -($totht);
$totht3 = number_format($totht2, 2, '.', '');
$nom_colis = html_entity_decode($row['cli_nom_prenom'])." ".$typecolis[0];
$regl2 = explode(";", $row['regl']);
$regl = $regl2[1];
$VTCB = "585VTCB";
$idcde = $row['id_command'];

	if ($regl == "CBi"){

		if ($ttc>0){
		$csv .= "      1264REMCCP".$date2."                       ".str_pad($row['numcompt'], 13)."".$row['id_facture']." ".str_pad(html_entity_decode($row['cli_nom_prenom']), 48)."".str_pad($net2, 7, " ", STR_PAD_LEFT)."C".str_pad($idcde, 6)."                                                         O2003";
		$csv .= "\r\n";
		$csv .= "      1264REMCCP".$date2."                       ".str_pad($VTCB, 13)."".$row['id_facture']." ".str_pad(html_entity_decode($row['cli_nom_prenom']), 48)."".str_pad($net2, 7, " ", STR_PAD_LEFT)."D".str_pad($idcde, 6)."                                                         O2003";
		$csv .= "\r\n";
		}else{
		$csv .= "      1264REMCCP".$date2."                       ".str_pad($row['numcompt'], 13)."".$row['id_facture']." ".str_pad(html_entity_decode($row['cli_nom_prenom']), 48)."".str_pad($net2, 7, " ", STR_PAD_LEFT)."D".str_pad($idcde, 6)."                                                         O2003";
		$csv .= "\r\n";
		$csv .= "      1264REMCCP".$date2."                       ".str_pad($VTCB, 13)."".$row['id_facture']." ".str_pad(html_entity_decode($row['cli_nom_prenom']), 48)."".str_pad($net4, 7, " ", STR_PAD_LEFT)."C".str_pad($idcde, 6)."                                                         O2003";
		$csv .= "\r\n";
		}
	}
}
	

//Export en csv

header("Content-type: application/txt");
header("Content-disposition: attachment; filename=export.txt");
print($csv);
exit;

?>