<div class="form-style-10">	
	<h1><PERSON><PERSON><PERSON> les factures </h1>

		<div style="display:flex; justify-content: space-between;">	  
			<div class="inner-wrap" style="width:40%;">
			<h1>
			<a style="color:white;" href="<?php echo $basePath; ?>index.php?page=admin&action=facture&action2=editer"><img src="<?php echo $basePath; ?>public/images/facture/icone_editer.png" />Editer</a>
			</h1>

							<p>Veuillez &eacute;crire un num&eacute;ro de commande</p>

							<form method="get" action="<?php echo $basePath.'index.php';?>" style="display:flex;">
							
								<input type="hidden" name="page" value="admin" />
								<input type="hidden" name="action" value="facture" />
								<input type="hidden" name="type" value="edit" />
								<input type="text" name="idcommande" style="margin-right: 5px;" /> 
								<input type="submit" value="Ok" /> 
							
							</form>
			</div>	
				
			
			
			<div class="inner-wrap" style="width:40%;">
			<h1>
			<a style="color:white;" href="<?php echo $basePath; ?>index.php?page=admin&action=facture&action2=exporter"><img src="<?php echo $basePath; ?>public/images/facture/icone_export.png" />Exporter</a>
			</h1>
			<?php
			
			  $datedebut = "01/".date("m/Y", strtotime('-1 month'));
			  $datefin = "31/".date("m/Y", strtotime('-1 month'));
			  
					?>
					<div style="display:flex; justify-content: space-between;">	  
			<!--		<a class="export" href="<?php echo $basePath; ?>index.php?page=admin&action=facture&action3=vente"><img src="<?php echo $basePath; ?>public/images/facture/icone_vente_ex.png" /></a>
			-->
					<a class="export" href="<?php echo $basePath; ?>index.php?page=admin&action=facture&action3=venteBFO"><img src="<?php echo $basePath; ?>public/images/facture/icone_vente_ex_bfo.png" /></a>					
					<a class="export" href="<?php echo $basePath; ?>index.php?page=admin&action=facture&action3=marge"><img src="<?php echo $basePath; ?>public/images/facture/icone_marge_ex.png" /></a>
				    <a class="export" href="<?php echo $basePath; ?>index.php?page=admin&action=facture&action3=tva_intra"><img src="<?php echo $basePath; ?>public/images/facture/icone_tva_intra.png" /></a>
					</div>
					<?php
					if (isset($_GET['action3']) && (($_GET['action3'] == "vente") || ($_GET['action3'] == "venteBFO"))){
					
					$suffixe = "";
					if ($_GET['action3'] == "venteBFO") $suffixe = "-BFO-001";
					?>
							<br />
							Choisir une plage de date entre 
							<form style="display:flex; text-align:center;margin-top: 5px;" method="post" target="_blank" action="<?php echo $basePath; ?>admin/facture/exporter/export_fact<?php echo $suffixe; ?>.php">
							<input type="text" name="datedebut" size="10" value="<?php echo $datedebut; ?>" style="text-align: center;margin-right: 5px;" /> 
							<span style="margin-top: 8px;">et</span>
							<input type="text" name="datefin" size="10" value="<?php echo $datefin; ?>" style="text-align: center; margin-right: 5px;margin-left: 5px;" />
							<input type="submit" value="Ok" />
							</form>
					
					<?php
					}
					if (isset($_GET['action3']) && ($_GET['action3'] == "marge")){
					
					?>
					
							<br />
							Choisir une plage de date entre 
							<form style="display:flex; text-align:center;margin-top: 5px;" method="post" target="_blank" action="<?php echo $basePath; ?>admin/facture/exporter/export_marge.php">
							<input type="text" name="datedebut" size="10" value="<?php echo $datedebut; ?>" style="text-align: center;margin-right: 5px;" /> 
							<span style="margin-top: 8px;">et</span>
							<input type="text" name="datefin" size="10" value="<?php echo $datefin; ?>" style="text-align: center; margin-right: 5px;margin-left: 5px;" />
							<input type="submit" value="Ok" />
							</form>
							
					<?php
					}
					if (isset($_GET['action3']) && ($_GET['action3'] == "tva_intra")){
					
					?>
					
							<br />
							Choisir une plage de date entre 
							<form style="display:flex; text-align:center;margin-top: 5px;" method="post" target="_blank" action="<?php echo $basePath; ?>admin/facture/exporter/export_tva_intra.php">
							<input type="text" name="datedebut" size="10" value="<?php echo $datedebut; ?>" style="text-align: center;margin-right: 5px;" /> 
							<span style="margin-top: 8px;">et</span>
							<input type="text" name="datefin" size="10" value="<?php echo $datefin; ?>" style="text-align: center; margin-right: 5px;margin-left: 5px;" />
							<input type="submit" value="Ok" />
							</form>					
							
					<?php
					}
					
					if (isset($_GET['action3']) && ($_GET['action3'] == "regl")){
					
					?>
					
					<table>
					<div class="export"><img src="<?php echo $basePath; ?>public/images/facture/icone_regl_ex.png" /></div>
					<td><a class="export_regl" href="<?php echo $basePath; ?>index.php?page=admin&action=facture&action4=CBi"><img src="<?php echo $basePath; ?>public/images/facture/icone_cbi_ex.png" /></a></td>
					<td><a class="export_regl" href="<?php echo $basePath; ?>index.php?page=admin&action=facture&action4=CH"><img src="<?php echo $basePath; ?>public/images/facture/icone_ch_ex.png" /></a></td>
					<td><a class="export_regl" href="<?php echo $basePath; ?>index.php?page=admin&action=facture&action4=CBc"><img src="<?php echo $basePath; ?>public/images/facture/icone_cbc_ex.png" /></a></td>
					</table>
					<?php 
					}
						if (isset($_GET['action4']) && ($_GET['action4'] == "CBi")){
						
						?>
						<div class="export"><img src="<?php echo $basePath; ?>public/images/facture/icone_regl_ex.png" /></div>
						<div class="export_regl"><img src="<?php echo $basePath; ?>public/images/facture/icone_cbi_ex.png" /></div>
						<li>
								<form method="post" target="_blank" action="<?php echo $basePath; ?>admin/facture/exporter/regl/export_regl_CBi.php">
								Choisir une plage de date entre 
								<input type="text" name="datedebut" size="10" value="<?php echo $datedebut; ?>"/> et
								<input type="text" name="datefin" size="10" value="<?php echo $datedebut; ?>"/> </br></br>
								<input type="submit" value="Exporter" /> </br>
								</form>
						</li>
								
						<?php
						}
						
						if (isset($_GET['action4']) && ($_GET['action4'] == "CH")){
						
						?>
						<div class="export"><img src="<?php echo $basePath; ?>public/images/facture/icone_regl_ex.png" /></div>
						<div class="export_regl"><img src="<?php echo $basePath; ?>public/images/facture/icone_ch_ex.png" /></div>
						<li>
								<form method="post" target="_blank" action="<?php echo $basePath; ?>admin/facture/exporter/regl/export_regl_CH.php">
								Choisir une plage de date entre 
								<input type="text" name="datedebut" size="10" value="<?php echo $datedebut; ?>"/> et
								<input type="text" name="datefin" size="10" value="<?php echo $datedebut; ?>"/> </br></br>
								<input type="submit" value="Exporter" /> </br>
								</form>
						</li>
								
						<?php
						}
						
						if (isset($_GET['action4']) && ($_GET['action4'] == "CBc")){
						
						?>
						<div class="export"><img src="<?php echo $basePath; ?>public/images/facture/icone_regl_ex.png" /></div>
						<div class="export_regl"><img src="<?php echo $basePath; ?>public/images/facture/icone_cbc_ex.png" /></div>
						<li>
								<form method="post" target="_blank" action="<?php echo $basePath; ?>admin/facture/exporter/regl/export_regl_CBc.php">
								Choisir une plage de date entre 
								<input type="text" name="datedebut" size="10" value="<?php echo $datedebut; ?>"/> et
								<input type="text" name="datefin" size="10" value="<?php echo $datedebut; ?>"/> </br></br>
								<input type="submit" value="Exporter" /> </br>
								</form>
						</li>
								
						<?php
						}
			?>
			  
		</div>
		
	</div> 
	<div class="inner-wrap" style="text-align: center;width: 35%;margin-left: auto;margin-right: auto;font-size: 30px;"><a href="<?php echo $basePath; ?>index.php?page=admin&action=facture&type=modiffact"><img src="<?php echo $basePath; ?>public/images/facture/icone_visualiser.png" />Visualiser</a></div>
</div>
