<?php
include("../../../init2.php");

//definition des variable

$id_facture = $_GET['id_facture'];

//requete
$result2 = mysql_query("SELECT id_command FROM facture WHERE id_facture='$id_facture'") or die ("requete r2 invalid");
$idcde = mysql_result($result2, 0);
$result3 = mysql_query("SELECT commande_adresselivraison FROM static_commandes WHERE id ='$idcde'") or die ("requete r3 invalid");
$reglement = mysql_result($result3,0);
//$result5 = mysql_query("SELECT idadresse,emailclient,raisonsocial,nomrue,codepostal,ville,pays FROM adresses INNER JOIN static_commandes ON adresses.emailclient = static_commandes.client_email WHERE id = '$idcde' ORDER BY idadresse DESC LIMIT 1") or die ("requete r5 invalid");
$result6 = mysql_query("SELECT adresses_facturation.id,af_emailclient,af_raisonsocial,af_nomrue,af_codepostal,af_ville,af_pays FROM adresses_facturation INNER JOIN static_commandes ON adresses_facturation.af_emailclient = static_commandes.client_email WHERE static_commandes.id = '$idcde' ORDER BY adresses_facturation.id DESC LIMIT 1") or die ("requete r6 invalid");
$result7 = mysql_query("SELECT af_emailclient FROM adresses_facturation INNER JOIN static_commandes ON adresses_facturation.af_emailclient = static_commandes.client_email WHERE static_commandes.id = '$idcde' ORDER BY adresses_facturation.id DESC LIMIT 1") or die ("requete r6 invalid");
$result9 = mysql_query("SELECT * FROM facture WHERE id_facture='$id_facture'") or die ("requete r9 invalid");
$id = mysql_result($result7, 0);
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title><?php echo $id_facture; ?></title>

<link href="style_impression.css" rel="stylesheet" type="text/css" />

</head>

<body onload="window.print()">

<div id="corps_fact">

<img src="logo_smi_nb.jpg" width="150" height="94" />
<div class="smi">
<p><span style="font-size:30px"><strong>Surplus Militaires et Industriels</strong></span><br />
D1092, La gare - 38840 LA SONE -France <br />
T&eacute;l. +33 (0) 476 644 356 - Fax. +33 (0) 476 644 555 <br />
Du lundi au vendredi de 9h &agrave; 12h30 et de 14h30 &agrave; 18h, le samedi de 9h &agrave; 13h<br />
E-mail : <EMAIL> Web : www.jeep-dodge-gmc.com<br />
N&deg; TVA : FR 88 397 933 763 / 00013 - APE 501 Z - Capital 50 000&euro;</p>
</div>
<HR>

<?php

//affiche adresse livraison client
if ($reglement != ""){
	echo '<div id="adresseL">';
	echo "<strong>Adresse Livraison</strong>";
	echo "<br />";
	echo $reglement;

	$result8 = mysql_query("SELECT telclient FROM clients WHERE emailclient = '$id'") or die ("requete r8 invalid");
	$tel = mysql_result($result8, 0);
	echo 'Tel : '.$tel.'';
	echo "<br />";	

	echo "</div>";

}

//affiche adresse facturation client
echo '<div id="adresseF">';
echo "<strong>Adresse Facturation</strong>";
echo "<br />";
while ($tab3 = mysql_fetch_array($result6)) {
		echo $tab3['af_raisonsocial'];
		echo "<br />";
		echo $tab3['af_nomrue'];
		echo "<br />";
		echo $tab3['af_codepostal'];
		echo " ";
		echo $tab3['af_ville'];
		echo "<br />";
		echo $tab3['af_pays'];
		echo "<br />";
		$result10 = mysql_query("SELECT tva_intra_com FROM clients WHERE emailclient = '$id'") or die ("requete r8 invalid");
		$tva_intra_com = mysql_result($result10, 0);
		if ($tva_intra_com != ""){
		echo 'Numero TVA : '.$tva_intra_com;
		}
}

echo '</div>';


// Imprime le visuel 08/05/2025
echo '<div class="facture-banner">';
echo '<img src="/smi/public/images/facture/banniere_20250508.png" width="600" height="100" alt="Bannière facture" />';
echo '</div>';

while ($tab6 = mysql_fetch_array($result9)) {
if ($tab6['ttc'] > 0 ){
echo '<p><span style="font-size:24px"><strong>FACTURE</strong></span><span style="font-style: italic; margin-left:80px; font-weight: bold; font-size: 24px; color: red;">COPIE</span><br />';
}else{
echo '<p><span style="font-size:24px"><strong>AVOIR</strong></span><span style="font-style: italic; margin-left:80px; font-weight: bold; font-size: 24px; color: red;">COPIE</span><br />';
}
echo '<div id="en_tete_fact">';

// Image 08/05/2025
echo '<img src="/smi/public/images/facture/banniere_20250508.jpg" width="150" height="94" />';

//Affiche le num de cde
echo '<div id="cde">Commande N&deg; '.$idcde.'</div>';
//Affiche l'id client
echo '<div id="id"> Identifiant : '.$id.'</div>';
//Affiche le num fact
echo '<div id="numfact"> Facture N&deg ';
$result10 = mysql_query("SELECT * FROM facture WHERE id_facture='$id_facture'") or die ("requete r10 invalid");
while ($tab4 = mysql_fetch_array($result10)) {
$numfact = $tab4['id_facture'];
$numfact2 = explode(".",$numfact);
echo $numfact2[1];
echo '</div>';
//Affiche la date de fact
echo '<div id="datefact"> Date : ';
$datefac = $tab4['date_facture'];
$datefac2 = explode("-", $datefac);
$datefac3 = "".$datefac2[2]."/".$datefac2[1]."/".$datefac2[0]."";
echo $datefac3;
echo '</div>';
}
?>

</div>

<div id="ligne_tabl">
<table height="218" border="1" cellpadding="0" cellspacing="0" bordercolor="#000000">
  <tr>
    <th width="66" height="31" valign="middle"><div align="center" class="Style3">R&eacute;f&eacute;rence</div></th>
    <th width="110" valign="middle" border="1"><div align="center" class="Style3">Genre / Groupe</div></th>
    <th width="380" valign="middle"><div align="center" class="Style3">D&eacute;signation</div></th>
    <th width="100" valign="middle"><div align="center" class="Style3">Prix HT</div></th>
    <th width="70" valign="middle"><div align="center" class="Style3">Quantit&eacute;</div></th>
    <th width="70" valign="middle"><div align="center" class="Style3">Total</div></th>
  </tr>
  
<?php

// affiche ligne modifi&eacute;
$totht=0;
$result11 = mysql_query("SELECT * FROM ligne_facture WHERE id_facture='$id_facture'") or die ("requete r11 invalid");
while ($tab5 = mysql_fetch_array($result11)) {
		echo "<tr>";
		echo '<td>'.$tab5['reference'].'</td>';
		echo '<td>'.$tab5['genrename'].'</td>';
		echo '<td>'.htmlspecialchars($tab5['designation']).'</td>';
		echo '<td><div align="right">'.number_format($tab5['puhtproduit'],2, '.', '').'&euro;&nbsp;</td>';
		echo '<td><div align="center">'.$tab5['qteproduit'].'</td>';
		echo '<td><div align="right">'.number_format(($htpu = $tab5['qteproduit'] * $tab5['puhtproduit']),2, '.', '').'&euro;&nbsp;</td>';
		echo "</tr>";
		$totht += $htpu;
}
		
	echo "</table>";
echo "</div>";

// affiche totaux
echo '<div id="sous_totaux">';

	echo '<table width="228" border="1" cellpadding="0" cellspacing="0" bordercolor="#000000">';
	
			echo "<tr>";
			echo	'<td width="152" bordercolor="#000000"><div align="right">Montant total HT&nbsp; </div></td>';
			echo	'<td width="70" bordercolor="#000000"><div align="right">'.number_format($tab6['totht'],2, '.', '').'&euro;&nbsp; </div></td>';
			echo "</tr>";
			if ($tab6['port'] != "0"){
			echo "<tr>";
			echo	'<td bordercolor="#000000"><div align="right">Frais de port&nbsp; </div></td>';
			echo	'<td bordercolor="#000000"><div align="right">'.number_format($tab6['port'],2, '.', '').'&euro;&nbsp; </div></td>';
			echo  "</tr>";
			}
			echo "<tr>";
			echo	'<td bordercolor="#000000"><div align="right">TVA '.number_format(((($tab6['ttc']/($tab6['totht']+$tab6['port'])-1))*100),2, '.', '').'%&nbsp;</div></td>';
			echo	'<td bordercolor="#000000"><div align="right">'.number_format($tab6['tva'],2, '.', '').'&euro;&nbsp; </div></td>';
			echo  "</tr>";
			if ($tab6['remise'] != "0"){
			echo "<tr>";
			echo	'<td bordercolor="#000000"><div align="right">Autre (en TTC)&nbsp; </div></td>';
			echo	'<td bordercolor="#000000"><div align="right">'.number_format($tab6['remise'],2, '.', '').'&euro;&nbsp; </div></td>';
			echo  "</tr>";
			}
			echo "<tr>";
			echo	'<td bordercolor="#000000"><div align="right">Montant total TTC&nbsp; </div></td>';
			echo	'<td bordercolor="#000000"><div align="right">'.number_format($tab6['ttc'],2, '.', '').'&euro;&nbsp; </div></td>';
			echo  "</tr>";
			if ($tab6['trop_percu'] != "0"){
			echo "<tr>";
			echo	'<td bordercolor="#000000"><div align="right">Trop per&ccedil;u&nbsp; </div></td>';
			echo	'<td bordercolor="#000000"><div align="right">'.number_format($tab6['trop_percu'],2, '.', '').'&euro;&nbsp; </div></td>';
			echo  "</tr>";
			echo "<tr>";
			echo	'<td bordercolor="#000000"><div align="right">Montant r&eacute;gl&eacute;&nbsp; </div></td>';
			echo	'<td bordercolor="#000000"><div align="right">'.number_format($tab6['trop_percu']+$tab6['ttc'],2, '.', '').'&euro;&nbsp; </div></td>';
			echo  "</tr>";
			}
		}
	echo "</table>";
echo "</div>";

// affiche reglement

$result12 = mysql_query("SELECT regl FROM facture WHERE id_facture='$id_facture'") or die ("requete r12 invalid");
$regl2 = mysql_result($result12, 0);
$regl = explode(";", $regl2);

echo '<div class="date_regl">';
echo '<strong>Mode de r&eacute;glement : </strong>';
if ($regl[1] == "CH"){
echo 'Ch&egrave;que, N&deg; '.$regl[2].', de la banque '.$regl[3].'';
}if ($regl[1] == "CBi"){
echo 'Carte Bancaire Internet, N&deg; autorisation : '.$regl[2].'';
}if ($regl[1] == "CR"){
echo "Contre Remboursement";
}if ($regl[1] =="CBc"){
echo "Carte Bancaire comptoir";
}if ($regl[1] == "pp"){
echo 'Paypal, Num&eacute;ro de transaction Paypal : '.$regl[2].'';
}if ($regl[1] == "mixte"){
echo 'Mixte : '.htmlentities($regl[2]).'';
}if ($regl[1] == "esp"){
echo 'Esp&egrave;ce';
}if ($regl[1] == "vir"){
echo 'Virement, Num&eacute;ro du virment : '.$regl[2].'';
}if ($regl[1] == "differe"){
echo 'Diff&eacute;r&eacute;, date de l\'&eacute;ch&eacute;ance : '.$regl[2].'';
}
if ($regl[1] != "differe"){
echo ' - Date du r&eacute;glement le '.$regl[0].'';
}
echo '</div>';


//affiche information supplementaire

$result13 = mysql_query("SELECT info_sup FROM facture WHERE id_facture='$id_facture'") or die ("requete r13 invalid");
$info_sup2 = mysql_result($result13, 0);
$info_sup = explode(";", $info_sup2);

$result14 = mysql_query("SELECT numcompt FROM facture WHERE id_facture='$id_facture'") or die ("requete r14 invalid");
$numcompt = mysql_result($result14, 0);

echo '<div id="info_sup">';
echo '<strong>Informations suppl&eacute;mentaires</strong><br />';
echo 'N&deg; comptable : ';
echo $numcompt;
if ($reglement != ""){
echo "<br />";

$typecolis = $info_sup[0];

	if ($typecolis == "626POSTCD"){
	echo 'Type de colissimo : CD';
	}
	if ($typecolis == "626POSTS"){
	echo 'Type de colissimo : S';
	}
	if ($typecolis == "626POSTM"){
	echo 'Type de colissimo : M';
	}
	if ($typecolis == "626POSTL"){
	echo 'Type de colissimo : L';
	}
	if ($typecolis == "626POSTXL"){
	echo 'Type de colissimo : XL';
	}
	if ($typecolis == "626POSTP"){
	echo 'Type de colissimo : P';
	}
	if ($typecolis == "626POSTDEI"){
	echo 'Type de colissimo : Hors France M&eacute;tropolitaine';
	}
	if ($typecolis == "626POSTEXPERT"){
	echo 'Type de colissimo : Colissimo expert';
	}
	if ($typecolis == "626PACTIMB"){
	echo 'Colis Autre';
	}
	if ($typecolis == "626CAMION"){
	echo 'Colis Transporteur';
	}

echo "<br />";
echo 'N&deg; de colis : ';
echo $info_sup[1];
echo "<br />";
echo "Commentaire : ";
echo htmlentities($info_sup[2]);
echo '</div>';
}else{
echo "<br />";
echo "Commentaire : ";
echo $info_sup[0];
echo '</div>';
}

?>
</div>
</body>
</html>