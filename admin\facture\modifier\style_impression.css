body {
	padding: 0px;
	margin-top: 0px;
	margin-right: auto;
	margin-bottom: 0px;
	margin-left: auto;
}
.facture-banner {
    margin-bottom: 20px;
}

.facture-banner img {
    display: block;
    max-width: 600px;
    height: auto;
}
#corps_fact {
	width: 850px;
	height: auto;
}
#corps_fact #en_tete_fact {
	height: 30px;
	width: 800px;
	padding-top: 10px;
	padding-left: 10px;
	border: 1px solid #333333;
	margin-bottom: 5px;
	margin-top: 3px;
	display: block;
}
#corps_fact #en_tete_fact #cde {
	float: left;
	width: 150px;
}
#corps_fact #en_tete_fact #id {
	float: left;
	width: auto;
}
#corps_fact #en_tete_fact #numfact {
	float: left;
	width: auto;
	margin-left: 15px;
}
#corps_fact #en_tete_fact #datefact {
	float: left;
	width: auto;
	margin-left: 15px;
}

#corps_fact #sous_totaux {
	height: 150px;
	width: 810px;
	display: block;
}

#corps_fact #adresseF {
	width: 350px;
	padding-top: 10px;
	padding-left: 10px;
	padding-bottom: 10px;
	margin-top: 3px;
	margin-bottom: 3px;
	height: 150px;
	display: block;
	font-size: 18px;
}
#corps_fact #sous_totaux table {
	margin-top: 10px;
	margin-left: 578px;
	border: 1px 1 #000000;
}

#corps_fact #ligne_tabl {
	width: 810px;
	height: auto;
	display: block;
}
#corps_fact #ligne_tabl table {
	width: 800px;
	height: auto;
	margin-right: auto;
	margin-left: auto;
}

#corps_fact #adresseL {
	width: 400px;
	font-size: 20px;
	padding-top: 10px;
	padding-left: 10px;
	padding-bottom: 10px;
	margin-left: 3px;
	border: 1px solid #000000;
	margin-top: 3px;
	margin-bottom: 3px;
	height: 150px;
	display: block;
	float: right;
	margin-right: 50px;
}

.Style3 {color: #000000; }
#corps_fact #regl_left {
	background-color: #CCFFFF;
	float: left;
	width: 424px;
	height: 300px;
	border-top-width: 1px;
	border-bottom-width: 1px;
	border-left-width: 1px;
	border-top-style: solid;
	border-bottom-style: solid;
	border-left-style: solid;
	border-top-color: #000000;
	border-right-color: #000000;
	border-bottom-color: #000000;
	border-left-color: #000000;
}
#corps_fact #regl_right {
	background-color: #CCFFFF;
	float: left;
	width: 424px;
	height: 300px;
	border-top-width: 1px;
	border-right-width: 1px;
	border-bottom-width: 1px;
	border-top-style: solid;
	border-right-style: solid;
	border-bottom-style: solid;
	border-top-color: #000000;
	border-right-color: #000000;
	border-bottom-color: #000000;
}
#corps_fact #regl_center {
	float: Aucune;
	width: 838px;
	height: auto;
	border-left-width: 1px;
	border-top-width: 1px;
	border-right-width: 1px;
	border-bottom-width: 1px;
	border-left-style: solid;
	border-top-style: solid;
	border-right-style: solid;
	border-bottom-style: solid;
	border-top-color: #000000;
	border-right-color: #000000;
	border-bottom-color: #000000;
	border-left-color: #000000;
	padding-top: 10px;
	padding-left: 10px;
	padding-bottom: 10px;
	
	
}
#corps_fact #regl_left li {
	font-style: italic;
	padding: 0px;
	margin-top: 5px;
	margin-right: 0px;
	margin-bottom: 0px;
	margin-left: 0px;
}
#corps_fact #regl_right li {
	font-style: italic;
	padding: 0px;
	margin-top: 5px;
	margin-right: 0px;
	margin-bottom: 0px;
	margin-left: 0px;
}
#corps_fact .date_regl {
	margin-right: 0px;
	margin-bottom: 0px;
	width: 804px;
	height: 60px;
	padding-top: 10px;
	padding-left: 5px;
	display: block;
	border: 1px solid #000000;
}

#corps_fact #regl_left ul {
	margin-top: 10px;
}
#corps_fact #regl_right ul {
	margin-top: 10px;
}
#corps_fact #info_sup {
	float: left;
	width: 280px;
	padding-top: 10px;
	padding-bottom: 10px;
	padding-left: 10px;
	margin-top: 5px;
	border: 1px solid #000000;
	margin-left: 30px;
}
#corps_fact #validation {
	height: auto;
	width: 420px;
	float: left;
	background-color: #FFCC00;
	margin-top: 5px;
	margin-left: 70px;
	padding-top: 5px;
	padding-bottom: 5px;
	padding-left: 5px;
	border: 1px solid #000000;
}
#corps_fact #validation input {
	height: 50px;
	width: 100px;
	text-align: center;
	vertical-align: middle;
	font-size: 20px;
	color: #0033FF;
	padding-top: 5px;
	padding-bottom: 5px;
}
#corps_fact #validation input:hover {
	height: 50px;
	width: 100px;
	text-align: center;
	vertical-align: middle;
	font-size: 20px;
	color: #FF0000;
	padding-top: 5px;
	padding-bottom: 5px;
}
#corps_fact #validation_adresse {
	height: auto;
	width: 250px;
	float: left;
	background-color: #FFCC00;
	margin-top: 5px;
	margin-left: 0px;
	padding-top: 5px;
	padding-bottom: 5px;
	padding-left: 5px;
	border: 1px solid #000000;
}
#corps_fact #validation_adresse input {
	height: 50px;
	width: 100px;
	text-align: center;
	vertical-align: middle;
	font-size: 20px;
	color: #0033FF;
	padding-top: 5px;
	padding-bottom: 5px;
}
#corps_fact #validation_adresse input:hover {
	height: 50px;
	width: 100px;
	text-align: center;
	vertical-align: middle;
	font-size: 20px;
	color: #FF0000;
	padding-top: 5px;
	padding-bottom: 5px;
}
.span {
	color: #FF0000;
}
.smi {
	margin-left: 180px;
}
img {
	float: left;
}
