<?php
/**
 * programme qui ajout un fournisseur de la base de donnée
 */

// ----------- CHAMPS AJOUT DEMANDE FOURNISSEURS ----------------

$result5 = $database->prepare("SELECT * FROM fournisseur ORDER BY id DESC LIMIT 1") or die ("r3 invalid");
$result5->execute();
$dernier_id = $result5->fetch();

?>
<h1><img src="<?php echo $basePath; ?>public/images/agenda.png" alt="panier"/> <span><?php echo $translate->_('Créer une demande fournisseur'); ?></span></h1>
<div class="form" style="width:765px;" >
<form method="POST" action='<?php echo $basePath; ?>index.php?page=admin&action=gererfour&type=modif_dde&ajout_dde_frs=1&id=<?php if(isset($_GET['id'])){ echo $_GET['id']; } else { echo $dernier_id['id']; } ?>' enctype="multipart/form-data">
			<fieldset class="input">
<input type="hidden" name="id" value="" />		
<table style="float:left; margin-left:12px;" width="350" >	
<th align="center">Créer</th>
	<tr>
		<td bgcolor="#CCCCCC">Date de dde : </td>
		<td bgcolor="#CCCCCC"><input type="hidden" name="date_demande_frs" id="date_demande_frs" value="" />
		<div id="calendarMain2" style="margin-left : 67px;"></div>
		<script type="text/javascript">
		//<![CDATA[
		calInit("calendarMain2", "Calendrier", "date_demande_frs", "jsCalendar", "day", "selectedDay");
		//]]>
		</script>
			    <script type="text/javascript">
						var date_demande_frs = new LiveValidation('date_demande_frs');
						date_demande_frs.add( Validate.Presence );
		        </script>
		</td>
	</tr> 
	  	<tr style="height:150px;">
		  <td>Objet / Sujet : </td>
		  <td><textarea id="sujet_demande_frs" name="sujet_demande_frs" cols="40" rows="4"></textarea>
		  			<script type="text/javascript">
							var sujet_demande_frs = new LiveValidation('sujet_demande_frs');
							sujet_demande_frs.add( Validate.Presence );
					</script>
		  </td>
		</tr>
	   <tr>
		<td width="120" bgcolor="#CCCCCC">Par :</td>	  
		<td bgcolor="#CCCCCC">
				<select name="moyen_demande_frs" id="moyen_demande_frs" style="width:200px;">
				<option value="vide" >---</option>
				<option value="email" >E-mail</option>
				<option value="tel" >Tél</option>
				<option value="fax" >Fax</option>
				<option value="courrier" >Courrier</option>
				</select>
				<script type="text/javascript">			
					var moyen_demande_frs = new LiveValidation('moyen_demande_frs');
		            moyen_demande_frs.add(Validate.Exclusion, { within: ['vide'], failureMessage: " "});
		        </script>
		</td>
	  </tr>

</table >
<div id="tab_droite">
<table width="368" >
<th align="center">Relance</th>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Date </td>
		<td bgcolor="#CCCCCC"><input type="hidden" name="date_relance_frs" id="date_relance_frs" value="" />
		<div id="calendarMain1" style="margin-left : 67px;"></div>
		<script type="text/javascript">
		//<![CDATA[
		calInit("calendarMain1", "Calendrier", "date_relance_frs", "jsCalendar", "day", "selectedDay");
		//]]>
		</script>
			    <script type="text/javascript">
						var date_relance_frs = new LiveValidation('date_relance_frs');
						date_relance_frs.add( Validate.Presence );
		        </script>
		</td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Par</td>
		<td bgcolor="#CCCCCC">
				<select name="moyen_relance_frs" id="moyen_relance_frs" style="width:200px;">
				<option value="vide" >---</option>
				<option value="email" >E-mail</option>
				<option value="tel" >Tél</option>
				<option value="fax" >Fax</option>
				<option value="courrier" >Courrier</option>
				</select>
				<script type="text/javascript">			
					var moyen_relance_frs = new LiveValidation('moyen_relance_frs');
		            moyen_relance_frs.add(Validate.Exclusion, { within: ['vide'], failureMessage: " "});
		        </script>
		</td>
	</tr>
	<tr style="height:150px;">
		  <td>Commentaire</td>
		  <td><textarea name="comment_dde_frs" cols="40" rows="4"></textarea></td>
	</tr>
</table>
		</fieldset>
		<fieldset style="width:743px;" class="submit">	
		<strong><input type="submit" value="Ajouter"></strong>
		</fieldset>	
		</form>
		</div>

