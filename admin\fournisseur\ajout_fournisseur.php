<?php
/**
 * programme qui ajout un fournisseur de la base de donnée
 */

require_once(dirname(__FILE__).'/../../class/fournisseur_class.php');

 		function stripAccents($str, $charset='utf-8')
		{
			$str = htmlentities($str, ENT_NOQUOTES, $charset);
			$str = preg_replace('#&([A-za-z])(?:acute|cedil|circ|grave|orn|ring|slash|th|tilde|uml);#', '\1', $str);
			$str = preg_replace('#&([A-za-z]{2})(?:lig);#', '\1', $str); // pour les ligatures e.g. '&oelig;'
			$str = preg_replace('#&[^;]+;#', '', $str); // supprime les autres caractères
			$str = str_replace("'", '', $str); // supprime les autres caractères
			
			return $str;
		}

if(!isset($_POST["ajout"])){



// ----------- CHAMPS AJOUT FOURNISSEURS ----------------

?>
<h1><img src="<?php echo $basePath; ?>public/images/fournisseur.png" alt=""/> Ajouter un fournisseur</h1>

<table width="739" style="margin-left: 90px; margin-bottom: 15px;" >
	<tr>
	<td align="center">
<?php

  		echo '<form action="'.$basePath.'admin/fournisseur/upload.php?ref=ajoutref" class="dropzone"></form>';


?>
	</td>
	</tr>
</table>
<div class="form" style="width:765px;" >
<form method="POST" action="<?php echo $basePath; ?>index.php?page=admin&action=gererfour&type=ajout" enctype="multipart/form-data">
			<fieldset class="input">
<input type="hidden" name="id" value="" />		
<input type="hidden" name="ajout" value="oui" />	
<table style="float:left;" width="390" >	
	<tr>
		<td bgcolor="#CCCCCC">Raison social</td>
		<td bgcolor="#CCCCCC"><input type="text" name="raisonsocial" id="raisonsocial" value="" style="width:245px;" />
			    <script type="text/javascript">
						var raisonsocial = new LiveValidation('raisonsocial');
						raisonsocial.add( Validate.Presence );
		        </script>
		</td>
	</tr>
		<tr>
		<td width="66">Interet pour </td>
	  	<td width="258"> 
				<select name="interet" id="interet" style="width:230px;">
				<option value="vide" >---</option>
					<?php
				$result1 = $database->prepare("SELECT * FROM fournisseur GROUP BY interet") or die ("r10 invalid");
				$result1->execute();
							
				while ($row1 = $result1->fetch()) {

						echo '<option value="',$row1['interet'],'"';
						echo '>'.$row1['interet'];
						echo '</option>';
					}
					?>
				</select>
				<script type="text/javascript">			
					var interet = new LiveValidation('interet');
		            interet.add(Validate.Exclusion, { within: ['vide'], failureMessage: " "});
		        </script>
		</td>
	  </tr>
	   <tr>
		<td width="66" bgcolor="#CCCCCC">Activité </td>
	  
		<td width="258"  bgcolor="#CCCCCC"> 
				<select name="activite" id="activite" style="width:230px;">
				<option value="vide" >---</option>
					<?php
				$result2 = $database->prepare("SELECT * FROM fournisseur GROUP BY activite") or die ("r10 invalid");
				$result2->execute();

				while ($row2 = $result2->fetch()) {

						echo '<option value="',$row2['activite'],'"';
						echo '>'.$row2['activite'];
						echo '</option>';
					}
					?>
				</select>
				<script type="text/javascript">			
					var activite = new LiveValidation('activite');
		            activite.add(Validate.Exclusion, { within: ['vide'], failureMessage: " "});
		        </script>
		</td>
	  </tr>
	  <tr>
		<td>Secteur</td>
		<td><textarea name="secteur" id="secteur" cols="40" rows="1"></textarea>
				<script type="text/javascript">
						var secteur = new LiveValidation('secteur');
						secteur.add( Validate.Presence );
		        </script>
		</td>
	  </tr>
	  <tr style="height:150px;">
		  <td>Commentaire</td>
		  <td><textarea name="commentaire" cols="40" rows="4"></textarea></td>
	  </tr>

</table >
<div id="tab_droite">
<table width="350" >
<th align="center">Coordonnées</th>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Nom contact </td>
	  <td width="258" bgcolor="#CCCCCC"><input type="text" name="nom_contact" id="nom_contact" value="" style="width:200px;" />
	  			<script type="text/javascript">
						var nom_contact = new LiveValidation('nom_contact');
						nom_contact.add( Validate.Presence );
		        </script>
	  </td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Adresse</td>
	  <td width="258" bgcolor="#CCCCCC"><input type="text" name="adresse" id="adresse" value="" style="width:200px;" />
	  	  		<script type="text/javascript">
						var adresse = new LiveValidation('adresse');
						adresse.add( Validate.Presence );
		        </script>
	  </td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">CP</td>
	  <td width="258" bgcolor="#CCCCCC"><input type="text" name="cp" id="cp" value="" style="width:200px;" />
	  	  	  	<script type="text/javascript">
						var cp = new LiveValidation('cp');
						cp.add( Validate.Presence );
		        </script>
	  </td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Ville</td>
	  <td width="258" bgcolor="#CCCCCC"><input type="text" name="ville" id="ville" value="" style="width:200px;" />
	  	  	  	<script type="text/javascript">
						var ville = new LiveValidation('ville');
						ville.add( Validate.Presence );
		        </script>
	  </td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Pays</td>
	  <td width="258" bgcolor="#CCCCCC">
	  			<select name="pays" id="pays" style="width:200px;">
				<option value="vide" >---</option>
				<?php			
				$result4 = $database->prepare("SELECT * FROM pays ORDER BY nom_pays ASC") or die ("r4 invalid");
				$result4->execute();
				
				while ($row4 = $result4->fetch()) {

						echo '<option value="',$row4['alpha2'],'"';
						echo '>'.$row4['nom_pays'];
						echo '</option>';
				}
				?>
				</select>
				<script type="text/javascript">			
					var pays = new LiveValidation('pays');
		            pays.add(Validate.Exclusion, { within: ['vide'], failureMessage: " "});
		        </script>
		</td>
	</tr>
</table>
<table  width="350" >
<th align="center">Contactez</th>
	<tr>
	  <td width="130" bgcolor="#CCCCCC">Telephone</td>
	  <td width="258"  bgcolor="#CCCCCC"><input type="text" name="telephone" id="telephone" value="" style="width:200px;" />
	  	  			<script type="text/javascript">
						var telephone = new LiveValidation('telephone');
						telephone.add( Validate.Presence );
		        </script>
	   </td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Fax</td>
	  <td width="258"  bgcolor="#CCCCCC"><input type="text" name="fax" id="fax" value="" style="width:200px;" /></td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Mobile</td>
	  <td width="258"  bgcolor="#CCCCCC"><input type="text" name="mobile" id="mobile" value="" style="width:200px;" /></td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Email</td>
	  <td width="258"  bgcolor="#CCCCCC"><input type="text" name="email" id="email" value="" style="width:200px;" />
	  			<script type="text/javascript">
						var email = new LiveValidation('email');
						email.add( Validate.Presence );
		        </script>
	  </td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Web</td>
	  <td width="258"  bgcolor="#CCCCCC"><input type="text" name="web" id="web" value="" style="width:230px;" /></td>
	</tr>
</table>
<table  width="350" >
<th align="center">Comptable</th>
	<tr>
	  <td width="130" bgcolor="#CCCCCC">Tva Intra</td>
	  <td width="258"  bgcolor="#CCCCCC"><input type="text" name="tva_intracom" id="tva_intracom" value="" style="width:230px;" /></td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Siret</td>
	  <td width="258"  bgcolor="#CCCCCC"><input type="text" name="siret" id="siret" value="" style="width:230px;" /></td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Ape</td>
	  <td width="258"  bgcolor="#CCCCCC"><input type="text" name="ape" id="ape" value="" style="width:230px;" /></td>
	</tr>
</table>
		</fieldset>
		<fieldset style="width:743px;" class="submit">	
			<strong><input type="submit" value="Ajouter"></strong>
		</fieldset>	
		</form>
		</div>

<?php

// ------------- AJOUT BDD FOURNISSEUR ------------------
}
elseif(isset($_POST["ajout"])) {

 $four = fournisseurDB::getFournisseurById($_POST['id']);

	if($four instanceof Fournisseur) {
		
			$four->setId($_POST['id']);
			$four->setDate_creation(date("Y-m-d"));
			$four->setRaisonsocial(strtoupper(stripAccents($_POST['raisonsocial'])));
			$four->setInteret($_POST['interet']);
			$four->setActivite($_POST['activite']);
			$four->setSecteur(strtolower(stripAccents($_POST['secteur'])));
			$four->setNom_contact(stripAccents($_POST['nom_contact']));
			$four->setCommentaire(strtolower(stripAccents($_POST['commentaire'])));
			$four->setTelephone($_POST['telephone']);
			$four->setMobile($_POST['mobile']);
			$four->setFax($_POST['fax']);
			$four->setEmail($_POST['email']);
			$four->setWeb($_POST['web']);
			$four->setAdresse(strtolower(stripAccents($_POST['adresse'])));
			$four->setCp($_POST['cp']);
			$four->setVille(strtoupper(stripAccents($_POST['ville'])));
			$four->setPays($_POST['pays']);
			$four->setTva_intracom($_POST['tva_intracom']);
			$four->setSiret($_POST['siret']);
			$four->setApe($_POST['ape']);	
			$four->setStatut("1");	
			$four->setDate_modif("0000-00-00");

			
		$res = fournisseurDB::ajoutFournisseur($four);
		

	
	} else {
		$res = false;
	}
	
	if($res == true) {
	
		include("modif_fournisseur.php");

	} else {
			//erreur de modification
			echo '<div class="error messageBox">Erreur lors de ajout du fournisseur.</div>';
	}
}

?>			