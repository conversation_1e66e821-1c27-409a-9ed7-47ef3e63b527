
<?php

//JEEP VILLAGE

if (isset($_GET['valuecompare'])){

?>

http://www.jeepvillage.com/index.php?mmond=1&entry=212&crtsc2=0&crtsc3=0&crttxt=WO811687&crtref=&crtsch=1&crtmeta=48&iview=3

<?php

//JEEPEST

?>

https://jeepest.com/fr/recherche?controller=search&orderby=position&orderway=desc&search_query=WOA1234

<?php

//JEEP SUD EST

?>

http://www.jeepsudest.com/recherche/WOA1234/
<?php

}

//JEEP SUD EST
$lines_jse = file('http://www.jeepsudest.com/recherche/WOA1234/');

foreach ($lines_jse as $line_num => $line) {
	$aff = htmlspecialchars($line);
	$aff2 = strstr($aff, 'euro', true);
	$aff3 = strstr($aff2, 'prix');
	$aff4 = strstr($aff3, ' &amp;', true);
	$aff5 = substr($aff4, 14);
	echo $aff5;
}	
echo "<br />";

//JEEPEST
$lines_jeepest = file('https://jeepest.com/fr/recherche?controller=search&orderby=position&orderway=desc&search_query=WOA1234');

foreach ($lines_jeepest as $line_num2 => $line2) {
	$affj = htmlspecialchars($line2);
	$affj2 = strstr($affj, 'itemprop');
	$affj3 = strstr($affj2, 'price product-price');
	$affj4 = strstr($affj3, '€', true);
	$affj5 = substr($affj4, 29);
	echo $affj5;
}	
echo "<br />";

//JEEP VILLAGE
$lines_gsaa = file('http://www.jeepvillage.com/index.php?mmond=1&entry=212&crtsc2=0&crtsc3=0&crttxt=WO811687&crtref=&crtsch=1&crtmeta=48&iview=3');

foreach ($lines_gsaa as $line_num3 => $line3) {
	$affg = $line3;
	//echo $affg;
	$affg2 = strstr($affg, 'priceReel">');
	//echo $affg2;
	$affg3 = strstr($affg2, '</span>', true);
	//echo $affg3;
	$affg4 = substr($affg3, 11);
	echo $affg4;
}

?>
