<?php

//Class

require_once(dirname(__FILE__).'/../../class/fournisseur_class.php');

//Suppression image

if (isset($_GET['deleteimg'])){

$ref_four = $_GET['deleteimg'];

		$photo = dirname(__FILE__)."/../../public/images/fournisseur/".$ref_four.".jpg";
		if (!file_exists($photo))
			echo "La photo n'existe pas...<br /><br />";
		else
		{
			if (unlink($photo)){

			}else{
				echo "Echec de la suppression de la photo.<br /><br />";
			}
		}

}

//modif action relance fourn

if (isset($_GET['modif_action_relance_frs'])){

$id_demande_frs = $_GET['id_demande_frs'];
$action_relance_frs = '1';
$date_action_relance_frs = date("Y-m-d");

$req = $database->query("

UPDATE demande_fournisseur 
SET action_relance_frs = '$action_relance_frs', date_action_relance_frs = '$date_action_relance_frs'
WHERE id_demande_frs = '$id_demande_frs'

") or die ("requete update modif statut relance");

}

if (isset($_GET['modif_etat_dde_frs'])){

$id_demande_frs = $_GET['id_demande_frs'];
$modif_etat_dde_frs = $_GET['modif_etat_dde_frs'];


$req = $database->query("

UPDATE demande_fournisseur 
SET etat_dde_frs = '$modif_etat_dde_frs'
WHERE id_demande_frs = '$id_demande_frs'

") or die ("requete update modif etat dde frs");

}

//classe dde fournisseur

if (isset($_POST['id_dde_del']) && isset($_POST['statut'])){

$id_demande_frs = $_POST['id_dde_del'];
$statut = $_POST['statut'];

$req = $database->query("

UPDATE demande_fournisseur 
SET statut_dde_frs = '$statut'
WHERE id_demande_frs = '$id_demande_frs'

") or die ("requete update statut_devis");

}

// delete fournisseur

if (isset($_GET['type']) && $_GET['type'] == "delete"){

$id_frs_del = $_GET['id'];

$req = $database->query("

DELETE FROM fournisseur WHERE id = '$id_frs_del'

") or die ("requete delete four");

}

// recherche ++

if (isset($_GET['valuetosearch']) && $_GET['valuetosearch'] != "") {

/**
 * Programme de recherche d'une expression dans la liste de tous les produits
 */
echo '<h1><img src="',$basePath,'public/images/fournisseur.png" /> <span>',$translate->_('Résultats de la recherche fournisseur'),'</span></h1>';
?>

<table style="width: 895px;">
	<tr>
		<td>
			<center>
			<form method="GET" action="<?php echo $basePath; ?>index.php">
						<input type=hidden name="page" value="admin">
						<input type=hidden name="action" value="gererfour">
				Rechercher : <input type="text" name="valuetosearch" value="<?php echo $_GET['valuetosearch']; ?>" />
				dans : 	<select name="in">
							<option value="ALL" <?php echo ($_GET['in']=="ALL")?'selected=true':''; ?>>tout</option>
						</select>
				en utilisant : 	
						<select name="method">
							<option value="AND">tous les mots</option>
							<option value="OR">au moins un des mots</option>
						</select>
			<br /><br />
			<input type="submit" value="Rechercher !" />
			</form>
			<br />
			</center>
		</td>
		<td><a href="index.php?page=admin&action=gererfour&type=ajout" ><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/add_fournisseur.jpg" style="box-shadow: 0px 0px 2px 2px rgba(119, 119, 119, 0.75);" /></a></td>
	</tr>
</table>
	<?php

	$search = $_GET['valuetosearch'];
	$fourlist = fournisseurDB::readFournisseurFromDataBase();
	
	if(isset($_GET['in'])){
		$in=null;
	if($_GET['in'] == "ALL")
			$in = "tout";
	}
	if(isset($_GET['method'])){
		$method=null;
		if($_GET['method'] == "AND")
			$method = "tous les mots";
		elseif($_GET['method'] == "OR")
			$method = "au moins un des mots";
	}

	//découpe tous les mots à recherchés
	$search_array = explode(' ',$search);
	//construction de l'expression régulière
	$regexp = '#'.$search_array[0];
	
	//^.*(mot1|mot2).*(mot1|mot2).*$
	$i=1;
	for($i=1;$i<count($search_array);$i++) {

		if ((strtolower($search_array[$i])) == "neuf"){
		
		$search_array[$i] = "new";
		
		}
		
		if ((strtolower($search_array[$i])) == "occasion"){
		
		$search_array[$i] = "use";
		
		}
		
		if ((strtolower($search_array[$i])) == "renove" ||(strtolower($search_array[$i])) == "renovee"){
		
		$search_array[$i] = "rec";
		
		}

		$regexp.='|'.$search_array[$i];
	}
	$regexp.='#i';
	
	$nbres=0;
	

	foreach($fourlist as $four){
	
	
		//concatène le groupe le genre et le nom, pour ne faire qu'une chaine de caractère
		//plus facile pour rechercher
		//on regarde les options, pour savoir dans quoi on doit rechercher, si rien de définit par défaut on chercher dans tout
		if(isset($_GET['in'])) {
		
			if($_GET['in'] == "ALL") // on recherche dans les genres et les groupes
				$idenfifiant_four = $four->getRaisonsocial().' '.$four->getInteret().' '.$four->getActivite().' '.$four->getSecteur().' '.$four->getNom_contact();

			} else {
				$idenfifiant_four = $four->getRaisonsocial().' '.$four->getInteret().' '.$four->getActivite().' '.$four->getSecteur().' '.$four->getNom_contact();
			}
			
		$trouve = false;
		$i=0;
		
		//on regarde la méthode de recherche, par défaut, tous les mots donc : AND
		if(isset($_GET['method'])) {
			$method = $_GET['method'];
		} else {
			$method = "AND";
		}
		
		if($method == "AND") {
			//on recherche tous les mots, dc on s'arrete dès qu'on a pas trouvé un mot
			while($i<count($search_array) && ($trouve=preg_match('#'.$search_array[$i].'#i', $idenfifiant_four)))
				$i++;
		} elseif($method == "OR" ){
			//on recherche au moins un des mots, dc on s'arrete dès qu'on à trouvé un mot
			while($i<count($search_array) && !preg_match('#'.$search_array[$i].'#i', $idenfifiant_four))
				$i++;
		}
		//on est à la fin, on a donc trouvé tous les mots recherché !
		//ou alors on est pas à la fin
		if(($i==count($search_array) && $method=="AND") || ($i<count($search_array) && $method=="OR"))
		{
		
// ------------- AFFICHE RECHERCHE MODIF PRODUIT ----------------------
	
?>

<link href="../../public/styles/style_vercorps_list.css" rel="stylesheet" type="text/css" />

<div id="vercorps_list" style="width: 895px;">

<?php
//------------ IMAGE ------------				
	if (file_exists("public/images/fournisseur/".$four->getId().".jpg")){

		echo '<div id="left">';
		echo "<a href=\"".Zend_Registry::get('basePath')."public/images/fournisseur/".$four->getId().".jpg\" rel=\"lytebox\" title=\"".$four->getId()."\" >";
		echo "<img src=\"".Zend_Registry::get('basePath')."public/images/fournisseur/".$four->getId().".jpg\" border=\"1\" style='margin-top:3px;' width='160' height='130' /></a>";
?>
	<a href="index.php?page=admin&action=gererfour&id=<?php echo $four->getId();?>&deleteimg=<?php echo $four->getId();?>" 
	style="height: 32px; width: 32px; position: relative; left: 130px; top: -135px; display: block; border: none;" />
	<img style="border : none;"src="<?php echo $basePath; ?>public/images/cross2.png" alt=""/>
	</a>
<?php
	} else {
		echo '<div id="left">';
		echo '<form action="',$basePath,'admin/fournisseur/upload.php?ref='.$four->getId().'" class="dropzone"></form>';
	}
?>
	</div>
	<h3 style="margin-left: 180px; margin-top: 5px;"> 
<?php
	if($four->getStatut() == "1"){
?>
	<img src="<?php echo $basePath; ?>public/images/rond_vert.png" alt="vert"/>
<?php
	}
	if($four->getStatut() == "2"){
?>
	<img src="<?php echo $basePath; ?>public/images/rond_bleu.png" alt="bleu"/>
<?php
	}
	if($four->getStatut() == "3"){
?>
	<img src="<?php echo $basePath; ?>public/images/rond_rouge.png" alt="rouge"/>
<?php
	}
	if($four->getStatut() == "4"){
?>
	<img src="<?php echo $basePath; ?>public/images/rond_noir.png" alt="noir"/>
<?php
	}
?>

	<?php echo$four->getRaisonsocial();?> 
	<img src="<?php echo $basePath; ?>public/images/pays/<?php echo $four->getPays();?>.png"  width='16' height='14' alt="<?php echo $four->getPays();?>"/>
	</h3>
	<div id="mid">
		<p>Creation: <strong><?php echo $four->getDate_creation();?></strong></p>
		<p>Pour: <strong><?php echo $four->getInteret();?></strong></p>
		<p>Activit&eacute;: <strong><?php echo $four->getActivite();?></strong></p>
		<p>Secteur : <?php echo $four->getSecteur();?></p>
		<p>Web: <a href="http://<?php echo $four->getWeb();?>" target="_blank"  style="text-decoration: underline; color: #39F;" ><?php echo $four->getWeb();?></a></p>
	</div>
	<div id="mid">
	
		<p>Nom contact: <strong><?php echo $four->getNom_contact();?></strong></p>
		<p>Ville / Pays: <a href="http://maps.google.com/maps?z=10&q=<?php echo $four->getVille();?>" target="_blank" style="text-decoration: underline; color: #0C0;" ><?php echo $four->getVille();?><br /><?php echo $four->getPays();?></a></p>
		<p>Tel: <?php echo $four->getTelephone();?></p>
		<p>E-mail: <a href="mailto:<?php echo $four->getEmail();?>"  style="text-decoration: underline; color: #39F;" ><?php echo $four->getEmail();?></a></p>
	</div>
	<div id="right">
		<div class="prix" style="margin-top: 0px;" ></div>
		<div class="description" style="margin-top: 0px;" ><a href="<?php echo Zend_Registry::get('basePath'); ?>index.php?page=admin&action=gererfour&type=modif&id=<?php echo $four->getId();?>" ><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/modif.png"  width="16" height="16" /> Modifier</a></div>
		<div class="description" style="margin-top: 0px;" ><a href="<?php echo Zend_Registry::get('basePath'); ?>index.php?page=admin&action=gererfour&type=ajout_dde&id=<?php echo $four->getId();?>" ><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/agenda.png"  width="16" height="16" /> Créer dde</a></div>
		<div class="description"><a onclick="return confirm('Voulez-vous vraiment supprimer ce fournisseur ?');" href="<?php echo Zend_Registry::get('basePath'); ?>index.php?page=admin&action=gererfour&type=delete&id=<?php echo $four->getId(); ?>" ><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/cross.png"  width="16" height="16" /> Supprimer</a></div>
	</div>
</div>

<?php
	
	}	
}

} else {

// LISTE DES DEMANDES EN COURS

		$result6 = $database->prepare("
		SELECT
		*
		FROM demande_fournisseur INNER JOIN
		fournisseur ON id = id_frs
		WHERE statut_dde_frs != 'terminer' AND statut_dde_frs != 'annuler'
		ORDER BY id_frs
		") or die ("requete r5 invalid");
		$result6->execute();

?>		
		
<div class="form-style-10">
<h1 style="padding-top:8px; padding-bottom:22px;" >DEMANDE FOURNISSEURS</h1>
	<div class="inner-wrap">
		<table class="table-fill">
			<tr>
				<th>Date</th>
				<th>Fournisseur</th>
				<th>Référence</th>
				<th>Qté</th>
				<th><span style="font-size:12px;">Prix UHT</span></th>
				<th>Statut</th>
				<th>Commentaire</th>
			</tr>
<?php

		while ($tab6 = $result6->fetch()) {	
		$date = explode("-", $tab6['date_dde_frs']); 
?>	
		<tr>				
			<td style="text-align:center;"><?php echo $date[2]."/".$date[1]."/".$date[0]; ?></td>
			<td><?php echo $tab6['raisonsocial']; ?></td>
			<td><a target="_blank" href="<?php echo $basePath; ?>index.php?page=admin&action=gererprod&valuetosearch=<?php echo $tab6['id_produit']; ?>&in=ALL&method=AND" ><?php echo $tab6['id_produit']; ?></a></td>
			<td style="text-align:center;"><?php echo $tab6['quantite_dde_frs']; ?></td>
			<td style="text-align:right; margin-right:5px;"><?php echo $tab6['prix_dde_frs']; ?>€</td>
			<td style="text-align:center;"><?php echo $tab6['statut_dde_frs']; ?></td>
			<td><?php echo $tab6['comment_dde_frs']; ?></td>
		</tr>	
<?php
		}
?>	
		</table>
	</div>	
</div>	
<?php	


// --------------------   LISTE LES FOURNISSEURS   --------------------------------
?>

<h1><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/fournisseur.png" /> <span>Gérer les fournisseurs <a href="<?php echo Zend_Registry::get('basePath'); ?>index.php?page=admin&action=gererfour&nolimit=1">(Affiche tout)<img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/random.png" /></a></span></h1>
<table style="width: 895px;">
	<tr>
		<td>
			<center>
				<form method="GET" action="<?php echo $basePath; ?>index.php">
							<input type=hidden name="page" value="admin">
							<input type=hidden name="action" value="gererfour">
							<input type=hidden name="in" value="ALL">
							<input type=hidden name="method" value="OR">
					Rechercher : <input type="text" name="valuetosearch" />
					<input type="submit" value="Rechercher !" />
				</form>
			</center>
		</td>
		<td><a href="index.php?page=admin&action=gererfour&type=ajout" ><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/add_fournisseur.jpg" style="box-shadow: 0px 0px 2px 2px rgba(119, 119, 119, 0.75);" /></a></td>
	</tr>
</table>
<link href="../../public/styles/style_vercorps_list.css" rel="stylesheet" type="text/css" />

<?php

$fourlist = fournisseurDB::readFournisseurFromDataBase();

foreach($fourlist as $four) {

?>

<div id="vercorps_list" style="width: 895px;">

<?php
//------------ IMAGE ------------				
	if (file_exists("public/images/fournisseur/".$four->getId().".jpg")){

		echo '<div id="left">';
		echo "<a href=\"".Zend_Registry::get('basePath')."public/images/fournisseur/".$four->getId().".jpg\" rel=\"lytebox\" title=\"".$four->getId()."\" >";
		echo "<img src=\"".Zend_Registry::get('basePath')."public/images/fournisseur/".$four->getId().".jpg\" border=\"1\" style='margin-top:3px;' width='160' height='130' /></a>";
?>
	<a href="index.php?page=admin&action=gererfour&id=<?php echo $four->getId();?>&deleteimg=<?php echo $four->getId();?>" 
	style="height: 32px; width: 32px; position: relative; left: 130px; top: -135px; display: block; border: none;" />
	<img style="border : none;"src="<?php echo $basePath; ?>public/images/cross2.png" alt=""/>
	</a>
<?php
	} else {
		echo '<div id="left">';
		echo '<form action="',$basePath,'admin/fournisseur/upload.php?ref='.$four->getId().'" class="dropzone"></form>';
	}
?>
	</div>
	<h3 style="margin-left: 180px; margin-top: 5px;"> 
<?php
	if($four->getStatut() == "1"){
?>
	<img src="<?php echo $basePath; ?>public/images/rond_vert.png" alt="vert"/>
<?php
	}
	if($four->getStatut() == "2"){
?>
	<img src="<?php echo $basePath; ?>public/images/rond_bleu.png" alt="bleu"/>
<?php
	}
	if($four->getStatut() == "3"){
?>
	<img src="<?php echo $basePath; ?>public/images/rond_rouge.png" alt="rouge"/>
<?php
	}
	if($four->getStatut() == "4"){
?>
	<img src="<?php echo $basePath; ?>public/images/rond_noir.png" alt="noir"/>
<?php
	}
?>

	<?php echo$four->getRaisonsocial();?> 
	<img src="<?php echo $basePath; ?>public/images/pays/<?php echo $four->getPays();?>.png"  width='16' height='14' alt="<?php echo $four->getPays();?>"/>
	</h3>
	<div id="mid">
		<p>Creation: <strong><?php echo $four->getDate_creation();?></strong></p>
		<p>Pour: <strong><?php echo $four->getInteret();?></strong></p>
		<p>Activit&eacute;: <strong><?php echo $four->getActivite();?></strong></p>
		<p>Secteur : <?php echo $four->getSecteur();?></p>
		<p>Web: <a href="http://<?php echo $four->getWeb();?>" target="_blank"  style="text-decoration: underline; color: #39F;" ><?php echo $four->getWeb();?></a></p>
	</div>
	<div id="mid">
	
		<p>Nom contact: <strong><?php echo $four->getNom_contact();?></strong></p>
		<p>Ville / Pays: <a href="http://maps.google.com/maps?z=10&q=<?php echo $four->getVille();?>" target="_blank" style="text-decoration: underline; color: #0C0;" ><?php echo $four->getVille();?><br /><?php echo $four->getPays();?></a></p>
		<p>Tel: <?php echo $four->getTelephone();?></p>
		<p>E-mail: <a href="mailto:<?php echo $four->getEmail();?>" style="text-decoration: underline; color: #39F;" ><?php echo $four->getEmail();?></a></p>

	</div>
	<div id="right">
		<div class="prix" style="margin-top: 0px;" ></div>
		<div class="description" style="margin-top: 0px;" ><a href="<?php echo Zend_Registry::get('basePath'); ?>index.php?page=admin&action=gererfour&type=modif&id=<?php echo $four->getId();?>" ><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/modif.png"  width="16" height="16" /> Modifier</a></div>
		<div class="description" style="margin-top: 0px;" ><a href="<?php echo Zend_Registry::get('basePath'); ?>index.php?page=admin&action=gererfour&type=ajout_dde&id=<?php echo $four->getId();?>" ><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/agenda.png"  width="16" height="16" /> Créer dde</a></div>
		<div class="description"><a onclick="return confirm('Voulez-vous vraiment supprimer ce fournisseur ?');" href="<?php echo Zend_Registry::get('basePath'); ?>index.php?page=admin&action=gererfour&type=delete&id=<?php echo $four->getId(); ?>" ><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/cross.png"  width="16" height="16" /> Supprimer</a></div>
	</div>
</div>

<?php
	}
}
?>