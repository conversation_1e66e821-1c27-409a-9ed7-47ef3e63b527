

<?php
/**
 * programme qui modif une demande fournisseur de la base de donnée
 */
 
$id_dde_frs = $_GET['id'];

// ------------- AJOUT BDD DDE FOURNISSEUR ------------------

if (isset($_GET['ajout_dde_frs'])) {

		function stripAccents($str, $charset='utf-8')
		{
			$str = htmlentities($str, ENT_NOQUOTES, $charset);
			$str = preg_replace('#&([A-za-z])(?:acute|cedil|circ|grave|orn|ring|slash|th|tilde|uml);#', '\1', $str);
			$str = preg_replace('#&([A-za-z]{2})(?:lig);#', '\1', $str); // pour les ligatures e.g. '&oelig;'
			$str = preg_replace('#&[^;]+;#', '', $str); // supprime les autres caractères
			$str = str_replace("'", '', $str); // supprime les autres caractères
			
			return $str;
		}
	
//traitement des données reçues

$id_frs = $_GET['id'];
$sujet_demande_frs = strtolower(stripAccents($_POST['sujet_demande_frs']));
$date_demande_frs2 = explode("/", $_POST['date_demande_frs']);
$date_demande_frs = $date_demande_frs2[2]."-".$date_demande_frs2[1]."-".$date_demande_frs2[0];
$moyen_demande_frs = $_POST['moyen_demande_frs'];
$date_relance_frs2 = explode("/", $_POST['date_relance_frs']);
$date_relance_frs = $date_relance_frs2[2]."-".$date_relance_frs2[1]."-".$date_relance_frs2[0];
$moyen_relance_frs = $_POST['moyen_relance_frs'];
$comment_dde_frs = stripAccents($_POST['comment_dde_frs']);

//insertion des données dans la table BOURSE

	$req2 = $database->query("
	INSERT INTO demande_fournisseur (
	id_frs,
	sujet_demande_frs,
	date_demande_frs,
	moyen_demande_frs,
	date_relance_frs,
	moyen_relance_frs,
	comment_dde_frs
	
	) 
	
	VALUES (
	'".$id_frs."',
	'".$sujet_demande_frs."',
	'".$date_demande_frs."',
	'".$moyen_demande_frs."',
	'".$date_relance_frs."',
	'".$moyen_relance_frs."',
	'".$comment_dde_frs."'
	
	)") or die ("requete insert demande_fournisseur invalid");
	?>
	<div class="valide messageBox">
			La demande fournisseur a bien été ajouté !
	</div>
	<?php
	$result7 = $database->prepare("
	SELECT * FROM demande_fournisseur ORDER BY id_demande_frs DESC LIMIT 1
	") or die ("requete r5 invalid");
	$result7->execute();
	$tab7 = $result7->fetch();	
	$id_dde_frs = $tab7['id_demande_frs'];
}
 
// ------------- MODIF BDD DDE FOURNISSEUR ------------------

if (isset($_GET['valid'])) {

		function stripAccents($str, $charset='utf-8')
		{
			$str = htmlentities($str, ENT_NOQUOTES, $charset);
			$str = preg_replace('#&([A-za-z])(?:acute|cedil|circ|grave|orn|ring|slash|th|tilde|uml);#', '\1', $str);
			$str = preg_replace('#&([A-za-z]{2})(?:lig);#', '\1', $str); // pour les ligatures e.g. '&oelig;'
			$str = preg_replace('#&[^;]+;#', '', $str); // supprime les autres caractères
			$str = str_replace("'", '', $str); // supprime les autres caractères
			
			return $str;
		}
	
//traitement des données reçues

$id_demande_frs = $_GET['id'];
$date_demande_frs2 = explode("/", $_POST['date_demande_frs']);
$date_demande_frs = $date_demande_frs2[2]."-".$date_demande_frs2[1]."-".$date_demande_frs2[0];
$moyen_demande_frs = $_POST['moyen_demande_frs'];
$sujet_demande_frs = strtolower(stripAccents($_POST['sujet_demande_frs']));
$date_relance_frs2 = explode("/", $_POST['date_relance_frs']);
$date_relance_frs = $date_relance_frs2[2]."-".$date_relance_frs2[1]."-".$date_relance_frs2[0];
$moyen_relance_frs = $_POST['moyen_relance_frs'];
$comment_dde_frs = stripAccents($_POST['comment_dde_frs']);
$raz_relance = $_POST['raz_relance'];


//update des données dans la table demande_fournisseur

	$req2 = $database->query("
	UPDATE demande_fournisseur
	SET
	date_demande_frs = '$date_demande_frs',
	moyen_demande_frs = '$moyen_demande_frs',
	sujet_demande_frs = '$sujet_demande_frs',
	date_relance_frs = '$date_relance_frs',
	moyen_relance_frs = '$moyen_relance_frs',
	comment_dde_frs = '$comment_dde_frs'
	WHERE id_demande_frs = '$id_demande_frs'
	
	") or die ("requete insert demande_fournisseur invalid");
	
	if ($_POST['raz_relance'] == "1"){
	
	$req3 = $database->query("
	UPDATE demande_fournisseur
	SET
	action_relance_frs = '0',
	date_action_relance_frs = '0000-00-00'
	WHERE id_demande_frs = '$id_demande_frs'
	
	") or die ("requete insert demande_fournisseur invalid");
	} 
?>
	<div class="valide messageBox">
			La demande fournisseur a bien été modifiée !
	</div>
<?php
	$id_dde_frs = $_GET['id'];
}

// ----------- CHAMPS MODIF DEMANDE FOURNISSEURS ----------------
	
	$result6 = $database->prepare("
	SELECT * FROM demande_fournisseur WHERE id_demande_frs = '$id_dde_frs'
	") or die ("requete r5 invalid");
	$result6->execute();
	$tab6 = $result6->fetch();	

?>
<h1><img style="margin-right:10px;" src="<?php echo $basePath; ?>public/images/fournisseur.png" alt="panier"/> <span><?php echo $translate->_('Modifier une demande fournisseur'); ?></span></h1>
<div class="form" style="width:765px;" >
<form method="POST" action="<?php echo $basePath; ?>index.php?page=admin&action=gererfour&type=modif_dde&valid=1&id=<?php echo $id_dde_frs; ?>" enctype="multipart/form-data">
	<fieldset class="input">	
<table style="float:left; margin-left:12px;" width="350" >	
<th align="center">Créer</th>
	<tr>
		<td bgcolor="#CCCCCC">Date : </td>
		<td bgcolor="#CCCCCC"><input style="width:70px; float:left; margin-left:30px; margin-right:10px;" type="text" name="date_demande_frs" id="date_demande_frs" value="<?php echo date('d/m/Y',strtotime($tab6['date_demande_frs'])); ?>" />
		<div id="calendarMain2" style="margin-left : 67px;"></div>
		<script type="text/javascript">
		//<![CDATA[
		calInit("calendarMain2", "Calendrier", "date_demande_frs", "jsCalendar", "day", "selectedDay");
		//]]>
		</script>
			    <script type="text/javascript">
						var date_demande_frs = new LiveValidation('date_demande_frs');
						date_demande_frs.add( Validate.Presence );
		        </script>
		</td>
	</tr> 
	  	<tr style="height:150px;">
		  <td>Objet / Sujet : </td>
		  <td><textarea id="sujet_demande_frs" name="sujet_demande_frs" cols="40" rows="4"><?php echo $tab6['sujet_demande_frs']; ?></textarea>
		  			<script type="text/javascript">
							var sujet_demande_frs = new LiveValidation('sujet_demande_frs');
							sujet_demande_frs.add( Validate.Presence );
					</script>
		  </td>
		</tr>
	   <tr>
		<td width="120" bgcolor="#CCCCCC">Par :</td>	  
		<td bgcolor="#CCCCCC">
				<select name="moyen_demande_frs" id="moyen_demande_frs" style="width:200px;">
				<option value="<?php echo $tab6['moyen_demande_frs']; ?>" selected="selected" ><?php echo $tab6['moyen_demande_frs']; ?></option>';
				<option value="email" >E-mail</option>
				<option value="tel" >Tél</option>
				<option value="fax" >Fax</option>
				<option value="courrier" >Courrier</option>
				</select>
				<script type="text/javascript">			
					var moyen_demande_frs = new LiveValidation('moyen_demande_frs');
		            moyen_demande_frs.add(Validate.Exclusion, { within: ['vide'], failureMessage: " "});
		        </script>
		</td>
	  </tr>

</table >
<div id="tab_droite">
<table width="368" >
<th align="center">Relance</th>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Date </td>
		<td bgcolor="#CCCCCC"><input style="width:70px; float:left; margin-left:30px; margin-right:10px;" type="type" name="date_relance_frs" id="date_relance_frs" value="<?php echo date('d/m/Y',strtotime($tab6['date_relance_frs'])); ?>" />
		<div id="calendarMain1" style="margin-left : 67px;"></div>
		<script type="text/javascript">
		//<![CDATA[
		calInit("calendarMain1", "Calendrier", "date_relance_frs", "jsCalendar", "day", "selectedDay");
		//]]>
		</script>
			    <script type="text/javascript">
						var date_relance_frs = new LiveValidation('date_relance_frs');
						date_relance_frs.add( Validate.Presence );
		        </script>
		</td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Par</td>
		<td bgcolor="#CCCCCC">
				<select name="moyen_relance_frs" id="moyen_relance_frs" style="width:200px;">
				<option value="<?php echo $tab6['moyen_relance_frs']; ?>" selected="selected" ><?php echo $tab6['moyen_relance_frs']; ?></option>';
				<option value="email" >E-mail</option>
				<option value="tel" >Tél</option>
				<option value="fax" >Fax</option>
				<option value="courrier" >Courrier</option>
				</select>
				<script type="text/javascript">			
					var moyen_relance_frs = new LiveValidation('moyen_relance_frs');
		            moyen_relance_frs.add(Validate.Exclusion, { within: ['vide'], failureMessage: " "});
		        </script>
		</td>
	</tr>
	<tr style="height:150px;">
		  <td>Commentaire</td>
		  <td><textarea name="comment_dde_frs" cols="40" rows="4"><?php echo $tab6['comment_dde_frs']; ?></textarea></td>
	</tr>
	<tr>
		<td bgcolor="#CCCCCC">RAZ statut</td>
		<td align="center">Oui<input style="width:14px; margin-left:10px;" type="radio" name="raz_relance" value= "1"/> Non <input style="width:14px; margin-left:10px;" checked type="radio" name="raz_relance" value= "0"/></td>
	</tr>
</table>
		</fieldset>
		<fieldset style="width:743px;" class="submit">	
		<a style="color:red; margin-right:350px; border: 1px solid #000; background-color: #CCC; padding-left:5px; padding-right:5px;" href="<?php echo $basePath; ?>index.php?page=admin&action=gererfour" ><img style="margin-right:10px;" src="<?php echo $basePath; ?>public/images/arrow_undo.png" /><strong>RETOUR</strong></a>
		<strong><input type="submit" value="Modifier"></strong>
		</fieldset>	
		</form>
		</div>

<?php

?>			