<?php
/**
 * programme qui modifie un fournisseur de la base de donnée
 */
 

 
  if(isset($_GET["valuetosearch"])){

$valuetosearch = "&valuetosearch=".$_GET['valuetosearch'];
 
 } else {
 
$valuetosearch = "";

 }
?>
	
<h1><img src="<?php echo $basePath; ?>public/images/loupe_gm.png" alt=""/>Nouvelle recherche</h1>
    						<div style="margin-left:300px;">
							<form method="get" style="float:left;" action="<?php echo $basePath.'index.php';?>">
							
								<input type="hidden" name="page" value="admin" />
								<input type="hidden" name="action" value="gererfour" />
								<img src="<?php echo $basePath; ?>public/images/loupe.png" alt="loupe"/>
								<input type="text" name="valuetosearch" value="" placeholder="Recherche..." />
								<input type="hidden" name="in" value="ALL" /> 
								<input type="hidden" name="method" value="AND" /> 
								<input type="submit" value="Ok" /> </br>
							
							</form>
							<form method="get" style="float:left; margin-left:50px;" action="<?php echo $basePath.'index.php';?>">
							
								<input type="hidden" name="page" value="admin" />
								<input type="hidden" name="action" value="gererfour" />
								<img src="<?php echo $basePath; ?>public/images/arrow_undo.png" alt="retour"/>
								<input type="hidden" name="valuetosearch" value="<?php $valuetosearch; ?>" />
								<input type="hidden" name="in" value="ALL" /> 
								<input type="hidden" name="method" value="AND" /> 
								<input type="submit" value="Retour" /> </br>
							
							</form>
							</div><br />
<h1><img src="<?php echo $basePath; ?>public/images/produit_modifier.png" alt=""/> Modification du fournisseur</h1>

<?php
require_once(dirname(__FILE__).'/../../class/fournisseur_class.php');

// ------------- MODIF BDD FOURNISSEUR ------------------
if(isset($_GET["id"])){

 $id = $_GET["id"];
 $four = fournisseurDB::getFournisseurById($id);
 
 } else {
 
	$result4 = $database->prepare("SELECT * FROM fournisseur ORDER BY id DESC LIMIT 1") or die ("r4 invalid");
	$result4->execute();
	$dernier_ajout = $result4->fetch();
	$id = $dernier_ajout['id'];
	$four = fournisseurDB::getFournisseurById($id);

	//ajout image
	$ds          = DIRECTORY_SEPARATOR;
	$storeFolder = '../../public'.$ds.'images'.$ds.'fournisseur';
	$targetPath = dirname( __FILE__ ) . $ds. $storeFolder . $ds; 		
	if (file_exists($targetPath."ajoutref.jpg")) {
		rename($targetPath."ajoutref.jpg", $targetPath.$id.".jpg");		
	}
}
 

if(isset($_GET["modifier"]))

{

	if($four instanceof Fournisseur) {
		
 		function stripAccents($str, $charset='utf-8')
		{
			$str = htmlentities($str, ENT_NOQUOTES, $charset);
			$str = preg_replace('#&([A-za-z])(?:acute|cedil|circ|grave|orn|ring|slash|th|tilde|uml);#', '\1', $str);
			$str = preg_replace('#&([A-za-z]{2})(?:lig);#', '\1', $str); // pour les ligatures e.g. '&oelig;'
			$str = preg_replace('#&[^;]+;#', '', $str); // supprime les autres caractères
			$str = str_replace("'", '', $str); // supprime les autres caractères
			
			return $str;
		}
		
			$four->setId($_GET['id']);
			
			$date_creation = explode("/",$_POST['date_creation']);
			$four->setDate_creation($date_creation[2]."-".$date_creation[1]."-".$date_creation[0]);

			$four->setRaisonsocial(strtoupper(stripAccents($_POST['raisonsocial'])));
			$four->setInteret($_POST['interet']);
			$four->setActivite($_POST['activite']);
			$four->setSecteur(strtolower(stripAccents($_POST['secteur'])));
			$four->setNom_contact(stripAccents($_POST['nom_contact']));
			$four->setCommentaire(strtolower(stripAccents($_POST['commentaire'])));
			$four->setTelephone($_POST['telephone']);
			$four->setMobile($_POST['mobile']);
			$four->setFax($_POST['fax']);
			$four->setEmail($_POST['email']);
			$four->setWeb($_POST['web']);
			$four->setAdresse(strtolower(stripAccents($_POST['adresse'])));
			$four->setCp($_POST['cp']);
			$four->setVille(strtoupper(stripAccents($_POST['ville'])));
			$four->setPays($_POST['pays']);
			$four->setTva_intracom($_POST['tva_intracom']);
			$four->setSiret($_POST['siret']);
			$four->setApe($_POST['ape']);	
			$four->setStatut($_POST['statut']);	
			
			$date_modif = explode("/",$_POST['date_modif']);
			$four->setDate_modif($date_modif[2]."-".$date_modif[1]."-".$date_modif[0]);
			
		$res = fournisseurDB::modifFournisseur($four);
		
	
	} else {
		$res = false;
	}
	
	if($res == true) {
		?>
		<div class="valide messageBox">
			Le fournisseur a bien été modifié
		</div>
		<?php
	} else {
		//erreur de modification
		echo '<div class="error messageBox">Erreur lors de la modification du fournisseur.</div>';
	}
}

// ----------- CHAMPS MODIF FOURNISSEURS ----------------

if($four instanceof Fournisseur) {

?>

<table width="739" style="margin-left: 90px; margin-bottom: 15px;" >
	<tr>
	<td align="center">
<?php
  
if (file_exists("public/images/fournisseur/".$four->getId().".jpg")){

		echo "<a href=\"".Zend_Registry::get('basePath')."public/images/fournisseur/".$four->getId().".jpg\" rel=\"lytebox\" title=\"".$four->getId()."\" >";
		echo "<img src=\"".Zend_Registry::get('basePath')."public/images/fournisseur/".$four->getId().".jpg\" border=\"1\" style='margin-top:3px;' width='160' height='130' /></a>";
	} else {
		echo '<form action="',$basePath,'admin/fournisseur/upload.php?ref='.$four->getId().'" class="dropzone"></form>';
	}
?>
	</td>
	</tr>
</table>
<div class="form">
			<form method="POST" action="<?php echo $basePath; ?>index.php?page=admin&action=gererfour&type=modif&modifier=oui&id=<?php echo $four->getId(); ?><?php echo $valuetosearch; ?>" enctype="multipart/form-data">
<fieldset class="input">

<table width="739" style="text-align:center;" >

	<tr>
		<td width="123" rowspan="2" bgcolor="#CCCCCC">Statut</td>
		<?php
			$checked_statut_1 = "";
			$checked_statut_2 = "";
			$checked_statut_3 = "";
			$checked_statut_4 = "";
			
			$couleur_statut_1 = "";
			$couleur_statut_2 = "";
			$couleur_statut_3 = "";
			$couleur_statut_4 = "";
			
			if($four->getStatut() == "1"){
				$checked_statut_1 = 'CHECKED';
				$couleur_statut_1 = 'style="color:green; font-weight:bold;"';
			} 
			if($four->getStatut() == "2"){
				$checked_statut_2 = 'CHECKED';
				$couleur_statut_2 = 'style="color:blue; font-weight:bold;"';
			} 
			if($four->getStatut() == "3"){
				$checked_statut_3 = 'CHECKED';
				$couleur_statut_3 = 'style="color:red; font-weight:bold;"';
			} 
			if($four->getStatut() == "4"){
				$checked_statut_4 = 'CHECKED';
				$couleur_statut_4 = 'style="color:black; font-weight:bold;"';
			} 		
			?>
			
			<td><img src="<?php echo $basePath; ?>public/images/rond_vert.png" alt="vert"/><input style="width:14px; margin-left:10px;" type="radio" <?php echo $checked_statut_1; ?> name="statut" value= "1"/></td>
			<td><img src="<?php echo $basePath; ?>public/images/rond_bleu.png" alt="bleu"/><input style="width:14px; margin-left:10px;" type="radio" <?php echo $checked_statut_2; ?> name="statut" value= "2"/></td>
			<td><img src="<?php echo $basePath; ?>public/images/rond_rouge.png" alt="rouge"/><input style="width:14px; margin-left:10px;" type="radio" <?php echo $checked_statut_3; ?> name="statut" value= "3"/></td>
			<td><img src="<?php echo $basePath; ?>public/images/rond_noir.png" alt="noir"/><input style="width:14px; margin-left:10px;" type="radio" <?php echo $checked_statut_4; ?> name="statut" value= "4"/></td>
	
	</tr>
	<tr>
			<td><span <?php echo $couleur_statut_1; ?> >Actif</span><br /></td>
			<td><span <?php echo $couleur_statut_2; ?> >A vérifier</span><br /></td>
			<td><span <?php echo $couleur_statut_3; ?> >Fermé</span><br /></td>
			<td><span <?php echo $couleur_statut_4; ?> >Introuvable</span><br /></td>
	</tr>

</table>
<table style="float:left;" width="390" >	
	<tr>
		<td bgcolor="#CCCCCC">Raison social</td>
		<td bgcolor="#CCCCCC"><input type="text" name="raisonsocial" id="raisonsocial" value="<?php echo $four->getRaisonsocial(); ?>" style="width:230px;" />
				<script type="text/javascript">
						var raisonsocial = new LiveValidation('raisonsocial');
						raisonsocial.add( Validate.Presence );
		        </script>
		</td>
	</tr>
		<tr>
		<td width="66">Interet pour </td>
	  
		<td width="258"> 
				<select name="interet" style="width:230px;">

					<?php
				$result1 = $database->prepare("SELECT * FROM fournisseur GROUP BY interet") or die ("r10 invalid");
				$result1->execute();
				
				echo '<option value="'.$four->getInteret().'" selected="selected" >'.$four->getInteret().'</option>';
				
				while ($row1 = $result1->fetch()) {

						echo '<option value="',$row1['interet'],'"';
						echo '>'.$row1['interet'];
						echo '</option>';
					}
					?>
				</select>
		</td>
	  </tr>
	   <tr>
		<td width="66" bgcolor="#CCCCCC">Activité </td>
	  
		<td width="258"  bgcolor="#CCCCCC"> 
				<select name="activite" style="width:230px;">

					<?php
				$result2 = $database->prepare("SELECT * FROM fournisseur GROUP BY activite") or die ("r10 invalid");
				$result2->execute();
				
				echo '<option value="'.$four->getActivite().'" selected="selected" >'.$four->getActivite().'</option>';
				
				while ($row2 = $result2->fetch()) {

						echo '<option value="',$row2['activite'],'"';
						echo '>'.$row2['activite'];
						echo '</option>';
					}
					?>
				</select>
		</td>
	  </tr>
	  <tr>
		<td>Secteur</td>
		<td><textarea name="secteur" id="secteur" cols="40" rows="1"><?php echo $four->getSecteur(); ?></textarea>
				<script type="text/javascript">
						var secteur = new LiveValidation('secteur');
						secteur.add( Validate.Presence );
		        </script>
		</td>
	  </tr>
	  <tr style="height:150px;">
		  <td>Commentaire</td>
		  <td><textarea name="commentaire" cols="40" rows="4"><?php echo $four->getCommentaire(); ?></textarea></td>
	  </tr>
	  <tr>
	<?php
		$creation_date2 = explode("-",$four->getDate_creation());
		$creation_date = $creation_date2[2]."/".$creation_date2[1]."/".$creation_date2[0];
	?>
		<td width="139" bgcolor="#CCCCCC">Date création</td>
	    <td width="235" bgcolor="#CCCCCC"><input type="hidden" name="date_creation" id="date_creation" value="<?php echo $creation_date; ?>" style="width:100px;" /><?php echo $creation_date; ?></td>
	  </tr>
	  <tr>
	  	<?php
		$modif_date3 = $four->getDate_modif();
		if (empty($modif_date3) || $modif_date3 == "0000-00-00" || $modif_date3 == ""){
		
		$modif_date = date('d/m/Y');
		
		} else {
		
		$modif_date2 = explode("-",$four->getDate_modif());
		$modif_date = $modif_date2[2]."/".$modif_date2[1]."/".$modif_date2[0];
		
		}
		
		?>
		<td width="139" bgcolor="#CCCCCC">Date modification</td>
	    <td width="235" bgcolor="#CCCCCC"><input type="text" name="date_modif" id="date_modif" value="<?php echo $modif_date; ?>" style="width:100px;" />
			  	<script type="text/javascript">
						var date_modif = new LiveValidation('date_modif');
						date_modif.add( Validate.Presence );
		        </script>
		</td>
	  </tr>
</table >
<div id="tab_droite">
<table>
<th align="center">Coordonnées</th>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Nom contact </td>
	  <td width="258" bgcolor="#CCCCCC"><input type="text" name="nom_contact" id="nom_contact" value="<?php echo $four->getNom_contact(); ?>" style="width:200px;" />
	  	  		<script type="text/javascript">
						var nom_contact = new LiveValidation('nom_contact');
						nom_contact.add( Validate.Presence );
		        </script>
	  </td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Adresse</td>
	  <td width="258" bgcolor="#CCCCCC"><input type="text" name="adresse" id="adresse" value="<?php echo $four->getAdresse(); ?>" style="width:200px;" />
	  	  	  	<script type="text/javascript">
						var adresse = new LiveValidation('adresse');
						adresse.add( Validate.Presence );
		        </script>
	  </td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">CP</td>
	  <td width="258" bgcolor="#CCCCCC"><input type="text" name="cp" id="cp" value="<?php echo $four->getCp(); ?>" style="width:200px;" />
	  	  	  	<script type="text/javascript">
						var cp = new LiveValidation('cp');
						cp.add( Validate.Presence );
		        </script>
	  </td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Ville</td>
	  <td width="258" bgcolor="#CCCCCC"><input type="text" name="ville" id="ville" value="<?php echo $four->getVille(); ?>" style="width:200px;" />  
				<script type="text/javascript">
						var ville = new LiveValidation('ville');
						ville.add( Validate.Presence );
		        </script>
	  </td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Pays <img src="<?php echo $basePath; ?>public/images/pays/<?php echo $four->getPays();?>.png"  width='16' height='14' alt="<?php echo $four->getPays();?>"/></td>
	  <td width="258" bgcolor="#CCCCCC">
	  			<select name="pays" style="width:200px;">

					<?php
				$pays = $four->getPays();

				
				$result3 = $database->prepare("SELECT * FROM pays WHERE alpha2 = '$pays'") or die ("r3 invalid");
				$result3->execute();
				$pays2 = $result3->fetch();

					echo '<option value="'.$pays2['alpha2'].'" selected="selected" >'.$pays2['nom_pays'].'</option>';
				
				$result4 = $database->prepare("SELECT * FROM pays") or die ("r4 invalid");
				$result4->execute();
				
				while ($row4 = $result4->fetch()) {

						echo '<option value="',$row4['alpha2'],'"';
						echo '>'.$row4['nom_pays'];
						echo '</option>';
				}
					?>
				</select>
		</td>
	</tr>
</table>
<table>
<th align="center">Contactez</th>
	<tr>
	  <td width="130" bgcolor="#CCCCCC">Telephone</td>
	  <td width="258"  bgcolor="#CCCCCC"><input type="text" name="telephone" id="telephone" value="<?php echo $four->getTelephone(); ?>" style="width:200px;" />
	  	  	  	<script type="text/javascript">
						var telephone = new LiveValidation('telephone');
						telephone.add( Validate.Presence );
		        </script>
	  </td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Fax</td>
	  <td width="258"  bgcolor="#CCCCCC"><input type="text" name="fax" id="fax" value="<?php echo $four->getFax(); ?>" style="width:200px;" /></td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Mobile</td>
	  <td width="258"  bgcolor="#CCCCCC"><input type="text" name="mobile" id="mobile" value="<?php echo $four->getMobile(); ?>" style="width:200px;" /></td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Email</td>
	  <td width="258"  bgcolor="#CCCCCC"><input type="text" name="email" id="email" value="<?php echo $four->getEmail(); ?>" style="width:200px;" />
	  	  	  	<script type="text/javascript">
						var email = new LiveValidation('email');
						email.add( Validate.Presence );
		        </script>
	  </td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Web</td>
	  <td width="258"  bgcolor="#CCCCCC"><input type="text" name="web" id="web" value="<?php echo $four->getWeb(); ?>" style="width:200px;" /></td>
	</tr>
</table>
<table>
<th align="center">Comptable</th>
	<tr>
	  <td width="130" bgcolor="#CCCCCC">Tva Intra</td>
	  <td width="258"  bgcolor="#CCCCCC"><input type="text" name="tva_intracom" id="tva_intracom" value="<?php echo $four->getTva_intracom(); ?>" style="width:230px;" /></td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Siret</td>
	  <td width="258"  bgcolor="#CCCCCC"><input type="text" name="siret" id="siret" value="<?php echo $four->getSiret(); ?>" style="width:230px;" /></td>
	</tr>
	<tr>
	  <td width="66" bgcolor="#CCCCCC">Ape</td>
	  <td width="258"  bgcolor="#CCCCCC"><input type="text" name="ape" id="ape" value="<?php echo $four->getApe(); ?>" style="width:230px;" /></td>
	</tr>
</table>
		</fieldset>
		<fieldset style="width:740px;" class="submit">	
			<strong><input type="submit" value="Modifier"></strong>
		</fieldset>	
		</form>
		</div>

<?php

}

include ("ajout_dde_frs.php");

// LISTE DE TOUTES LES DEMANDES 

if (isset($_GET['id'])){

?>

<h1><img src="<?php echo $basePath; ?>public/images/agenda.png" alt="panier"/> <span><?php echo $translate->_('Listes de toutes les demandes'); ?></span></h1>
<table class="liste">
	<tr>
	
		<th><?php echo $translate->_('Statut'); ?></th>
		<th><?php echo $translate->_('Date'); ?></th>
		<th><?php echo $translate->_('Par'); ?></th>
		<th><?php echo $translate->_('Sujet'); ?></th>
		<th style="color: #FF0000;"><?php echo $translate->_('A relancer le'); ?></th>
		<th><?php echo $translate->_('Par'); ?></th>
		<th colspan="2"><?php echo $translate->_('Fait ?'); ?></th>
		<th></th>
		<th colspan="2"><?php echo $translate->_('Action'); ?></th>
		
	</tr>
	<?php
	$id_frs = $_GET['id'];
	$result4 = $database->prepare("
	SELECT * FROM demande_fournisseur WHERE id_frs = '$id_frs'
	") or die ("requete r1 invalid");
	$result4->execute();
	while ($tab4 = $result4->fetch()){
			
?>			
					<?php
						if($tab4['statut_dde_frs'] == "0"){
					?>
						<td><img src="<?php echo $basePath; ?>public/images/rond_vert.png" alt="vert" title="En cours"/></td>
					<?php
					} 
					?>	
					<?php
						if($tab4['statut_dde_frs'] == "1"){
					?>
						<td><img src="<?php echo $basePath; ?>public/images/rond_noir.png" alt="vert" title="Terminée"/></td>
					<?php
					} 
					?>	
					<td><?php echo  date('d/m/Y',strtotime($tab4['date_demande_frs'])); ?></td>
					<td><?php echo $tab4['moyen_demande_frs']; ?></td>
					<td><?php echo $tab4['sujet_demande_frs']; ?></td>
					<td style="bold;"><?php echo date('d/m/Y',strtotime($tab4['date_relance_frs'])); ?></td>
					<td><?php echo $tab4['moyen_relance_frs']; ?></td>
<?php	
	if($tab4['action_relance_frs'] == "0"){
?>				
					<td><a style="color: #0000FF; font-weight: bold;" href="<?php echo Zend_Registry::get('basePath'); ?>index.php?page=admin&action=gererfour&modif_action_relance_frs=1&id_demande_frs=<?php echo $tab4['id_demande_frs'];?>">&nbsp;&nbsp;OUI&nbsp;&nbsp;</a></td>
					<td><a onclick="alert('Merci de faire le necessaire !')" style="color: #FF0000; font-weight: bold;" href="">&nbsp;&nbsp;NON&nbsp;&nbsp;</a></td>
					<td></td>
<?php	
	} else {
?>	
					<td colspan="2"><a style="color: green; font-weight: bold;" href="">&nbsp;&nbsp;OUI&nbsp;&nbsp;</a></td>
					<td>le <?php echo date('d/m/Y',strtotime($tab4['date_action_relance_frs'])); ?></td>
<?php	
	}
?>	
					<td><a href="<?php echo Zend_Registry::get('basePath'); ?>index.php?page=admin&action=gererfour&type=modif&id=<?php echo $id_frs;?>" title="modif">&nbsp;<img src="<?php echo $basePath.'public/images/loupe.png'; ?>" alt="MODIF" />&nbsp;</a></td>
					<td><a onclick="return confirm('Supprimer')" href="<?php echo $basePath; ?>index.php?page=admin&action=fournisseur&del_dde_frs=1&id_demande_frs=<?php echo $tab4['id_demande_frs']; ?>" title="supprimer">&nbsp;<img src="<?php echo $basePath.'public/images/cross.png'; ?>" alt="SUPPRIMER" />&nbsp;</a></td>
				</tr>
			<?php 
			
	}
		
?>

	<tr>
		<th colspan=12></th>
	</tr>
</table>
<?php 
			
	}
		
?>
