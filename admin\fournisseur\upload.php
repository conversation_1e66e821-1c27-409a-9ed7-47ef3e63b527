<?php

//insert et modif image
$ds          = DIRECTORY_SEPARATOR;  //1
 
$storeFolder = '../../public'.$ds.'images'.$ds.'fournisseur';   //2
//$storeFolder = 'uploads';   //2
 
if (!empty($_FILES)) {
     
    $tempFile = $_FILES['file']['tmp_name'];          //3             
      
$targetPath = dirname( __FILE__ ) . $ds. $storeFolder . $ds;  //4
     
    $targetFile =  $targetPath. $_FILES['file']['name'];  //5
 
    //move_uploaded_file($tempFile,$targetFile); //6
	
	if (move_uploaded_file($tempFile,$targetFile))
		{
			if (chmod($targetFile,0606) && filesize($targetFile) > 512000){
			//grande image

				// Fichier et nouvelle taille
					$percent = 0.5;

				// Calcul des nouvelles dimensions
					list($width, $height) = getimagesize($targetFile);
					$newwidth = 800;
					$newheight = 600;

				// Chargement
				
					$thumb = imagecreatetruecolor($newwidth, $newheight);
					$source = imagecreatefromjpeg($targetFile);
				
				// Redimensionnement
					imagecopyresized($thumb, $source, 0, 0, 0, 0, $newwidth, $newheight, $width, $height);
				//Sauvegarde
					imagejpeg($thumb, $targetPath. $_FILES['file']['name']);
			}
     
	}
	
rename($targetFile, $targetPath.$_GET['ref'].".jpg");
}
?> 
