<?php
	$db = Zend_Registry::get('database');
	$error = "";	
	if(isset($_POST['add'])){
		// on veut ajouter un nouveau frais de port
		if($_POST['min']< $_POST['max']){
			$req = "INSERT INTO frais_port(`min`,`max`,`taux`,`fixe`) VALUES($_POST[min],$_POST[max],$_POST[taux],$_POST[fixe])";
			if($db->exec($req) == 0){
				$error = "Impossible d'inserer ce frais de port";	
			}
		}
		else{
			$error = "Le Champs min doit être inférieur au champs max";
		}
	}

	$db = Zend_Registry::get('database');
	$req = "SELECT * FROM frais_port ORDER BY min";
	
	$stmt = $db->query($req);
	
	$data = $stmt->fetchAll();
	?>
	<br />
	<br />
	<table class="liste">
	  <tr>
	    <th>Id</th>
	    <th>Min</th>
	    <th>Max</th>
	    <th>Taux</th>
	    <th>Frais fixe</th>
	    <th></th>
	  </tr>
	<?php
		foreach($data as $ligne){
			?>
		<tr style="text-align:center">
			<td><?php echo $ligne['id'] ?></td>
			<td><?php printf('%.2f €', $ligne['min']); ?></td>
			<td><?php printf('%.2f €', $ligne['max']); ?></td>
			<td><?php echo $ligne['taux']; ?></td>
			<td><?php echo $ligne['fixe']; ?></td>
			<td>
				<a href="<?php echo $basePath;?>admin/info/frais_port_delete.php?id=<?php echo $ligne['id']; ?>">
					<img src="<?php echo $basePath; ?>public/images/b_drop.png" />
				</a>
			</td>
		</tr>
			<?php
		}
	?>
	</table>
	<?php 
		if($error != ""){
			echo '<p class="error messageBox">',$error,'</p>';
			$error = "";			
		}
	?>
	<br />
	<br />
	<form action="" method="post" class="form">
		<fieldset class="input">
			<legend>Ajouter une ligne:</legend>
			<ul>
				<li>
					<label>min</label>
					<input type="text" name="min" value="<?php echo @$_POST['min']; ?>" />
				</li>
				<li>
					<label>max</label>
					<input type="text" name="max" value="<?php echo @$_POST['max']; ?>" />
				</li>
				<li>
					<label>taux</label>
					<input type="text" name="taux" value="<?php echo @$_POST['taux']; ?>" />
				</li>
				<li>
					<label>fixe</label>
					<input type="text" name="fixe" value="<?php echo @$_POST['fixe']; ?>" />
				</li>
			</ul>
		</fieldset>
		<fieldset class="submit">
			<ul>
				<li><input type="submit" class="btn_submit" name="add" value="ajouter" /></li>
			</ul>
		</fieldset>
	</form>