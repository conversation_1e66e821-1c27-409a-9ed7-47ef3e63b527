<script type="text/javascript" src="<?php echo $basePath; ?>public/js/tiny_mce/tiny_mce.js"></script>
	<script type="text/javascript">
	<!--
		tinyMCE.init({
			mode : "textareas",
			theme : "advanced",
			theme_advanced_buttons1 : "bold,italic,underline,separator,justifyleft,justifycenter,justifyright,justifyfull,bullist,numlist,undo,redo,link,unlink,forecolor,backcolor,fontselect ",
			theme_advanced_buttons2 : "",
			theme_advanced_buttons3 : "",
			theme_advanced_statusbar_location : "bottom",
			theme_advanced_toolbar_location : "top",
			theme_advanced_toolbar_align : "left",
			language: "fr",
			width : 500,
			height : 200			
		});
	//-->
	</script>
	<div class="param">
		<form action="" method="post">
			<fieldset class="input">
				<ol>
					<li>
						<label>TVA</label>
						<input type="text" name="tva" value="<?php echo $param['tva'] ?>" class="indispensable" /><b class="reditalique">*</b>
					</li>
					<li>
						<label>merchant_id (paiement par carte)</label>
						<input type="text" name="merchant_id" value="<?php echo $param['merchant_id']; ?>" class="indispensable" /><b class="reditalique">*</b>
					</li>
					<li>
						<label>Identifiant Scellius</label>
						<input type="text" name="id_scellius" value="<?php echo $param['id_scellius']; ?>" class="indispensable" /><b class="reditalique">*</b>
					</li>
					<li>
						<label>Mot de passe Scellius</label>
						<input type="text" name="pw_scellius" value="<?php echo $param['pw_scellius']; ?>" class="indispensable" /><b class="reditalique">*</b>
					</li>
					<li>
						<label>Prix du Contre Remboursement</label>
						<input type="text" name="contre_remb" value="<?php echo $param['contre_remb']; ?>" class="indispensable" /><b class="reditalique">*</b>
					</li>
					<li>
						<label>Prix des frais de port KingTony</label>
						<input type="text" name="fdp_kt" value="<?php echo $param['fdp_kt']; ?>" class="indispensable" /><b class="reditalique">*</b>
					</li>
					<li>
						<label>Date de import Produit</label>
						<input type="text" name="bdd_produit" value="<?php echo $param['bdd_produit']; ?>" class="indispensable" /><b class="reditalique">*</b>
					</li>
					 <li>
						<label>Information affiché dans le titre</label>
						<textarea name="ginfo"><?php echo $param['infoEntrepriseAcc'] ?></textarea>
  					</li>
  					<li>
						<label class="reditalique">* Champs obligatoires</label>
					</li>
				 </ol>
			</fieldset>
			<fieldset class="submit">
				<ol>
					<li>
						<input type="submit" value="valider" class="btn_submit" id="valider" name="info"/>
					</li>
				</ol>
			</fieldset>
		</form>
	</div>