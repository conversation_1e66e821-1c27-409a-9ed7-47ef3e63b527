<?php 

/////// ACCES PAR PW /////////
/*
if(isset($_POST['superadmin'])){

$superpw = md5($_POST['superpw']);
$superuser = $_POST['superuser'];

$result1 = $database->prepare("SELECT COUNT(*) AS superadminok FROM clients WHERE emailclient = '$superuser' AND passwdclient = '$superpw' AND isadmin = '2'") or die ("requete r1 invalid");
$result1->execute();
$superadmin = $result1->fetch();
$superadminok = $superadmin['superadminok'];
$_SESSION['superadmin'] = $superadmin['superadminok'];

//////// AFFICHE LE FORMULAIRE DU SUPER ADMIN //////////

}

if(isset($_SESSION['superadmin']) && $_SESSION['superadmin'] != "1"){

?>
<div class="error messageBox">ERREUR MOT DE PASSE OU UTILISATEUR !</div>
<div class="warning messageBox">
Accés restrint - Utilisateur et mot de passe requis !
<br />
<form style="margin-left:250px; " action="<?php echo $basePath; ?>index.php?page=admin&action=gererinfo" method="post">
	<table>
		<tr>
			<td><input class="inputlogin" type="text" name="superuser" value="" placeholder="Utilisateur" /></td>
		</tr>
		<tr>
			<td><input class="inputpassword" type="password" name="superpw" value="" placeholder="Mot de passe" /></td>
		</tr>
		<tr>
			<td><input style="border-radius: 3px; margin-left:20px;" type="submit" name="superadmin" value="<?php echo $translate->_('Ok');?>" /></td>
		</tr>
	</table>
</form>
</div>

<?php

}

if(!isset($_SESSION['superadmin'])){

?>
<div class="warning messageBox">
Accés restrint - Utilisateur et mot de passe requis !
<br />
<form style="margin-left:250px; " action="<?php echo $basePath; ?>index.php?page=admin&action=gererinfo" method="post">
	<table>
		<tr>
			<td><input class="inputlogin" type="text" name="superuser" value="" placeholder="Utilisateur" /></td>
		</tr>
		<tr>
			<td><input class="inputpassword" type="password" name="superpw" value="" placeholder="Mot de passe" /></td>
		</tr>
		<tr>
			<td><input style="border-radius: 3px; margin-left:20px;" type="submit" name="superadmin" value="<?php echo $translate->_('Ok');?>" /></td>
		</tr>
	</table>
</form>
</div>

<?php

}

if(isset($_SESSION['superadmin']) && $_SESSION['superadmin'] == "1"){
*/
////////AFFICHE LES DONNÉES///////
	
echo '<h1><img src="',$basePath,'public/images/param.png" alt=""/><span>',$translate->_('Gérer les paramètres du site'),'</span></h1><br />';
	if(!isset($_GET['action2'])) {
		$actif_default = "class='actif'";
		$actif_contact = "";
		$actif_legal = "";
		$actif_port = "";
	}
	elseif($_GET['action2'] == "contact") {
		$actif_default = "";
		$actif_contact = "class='actif'";
		$actif_legal = "";
		$actif_port = "";
	}
	elseif($_GET['action2'] == "legal") {
		$actif_default = "";
		$actif_contact = "";
		$actif_legal = "class='actif'";
		$actif_port = "";
	}
	elseif($_GET['action2'] == "port"){
		$actif_default = "";
		$actif_contact = "";
		$actif_legal = "";
		$actif_port = "class='actif'";
	}
	else {
		$actif_default = "class='actif'";
		$actif_contact = "";
		$actif_legal = "";
		$actif_port = "";
	}
	
if(isset($_POST['info'])) 
{
	// on update les valeurs 
	$sql = "UPDATE parametres SET valeur = :value WHERE cle = :cle";
	$db = Zend_Registry::get('database');
	
	$stmt = $db->prepare($sql);
	$stmt->bindParam(":value",$cle);
	$stmt->bindParam(":cle",$var);
	
	if(!isset($_GET['action2'])) {
		$cle = $_POST['tva'];
		$var = 'tva';
		$stmt->execute();
	
		$cle = $_POST['ginfo'];
		$var = 'infoEntrepriseAcc';
		$stmt->execute();
		
		$cle = $_POST['merchant_id'];
		$var = 'merchant_id';
		$stmt->execute();
		
		$cle = $_POST['id_scellius'];
		$var = 'id_scellius';
		$stmt->execute();
		
		$cle = $_POST['pw_scellius'];
		$var = 'pw_scellius';
		$stmt->execute();
		
		$cle = $_POST['fdp_kt'];
		$var = 'fdp_kt';
		$stmt->execute();
			
		$cle = $_POST['contre_remb'];
		$var = 'contre_remb';
		$stmt->execute();
		
		$cle = $_POST['bdd_produit'];
		$var = 'bdd_produit';
		$stmt->execute();
		
			// liste des parametres 
		$sql ="SELECT * FROM parametres;";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->execute();
		$data = $stmt->fetchAll();
		$param = array();
		foreach ($data as $ligne) {
			$param[$ligne['cle']] = $ligne['valeur'];
		}
		?>
		<div class="valide messageBox">
			<p>Mise à jour éffectué avec Succés</p>
		</div>
		<div id="onglet">
		<ul>
			<li><a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo" <?php echo $actif_default; ?> >Paramètres généraux</a> </li>
			<li><a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo&action2=contact" <?php echo $actif_contact; ?> >Page Contact</a> </li>
			<li><a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo&action2=legal" <?php echo $actif_legal; ?> >Page Mentions Légales</a></li>
			<li><a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo&action2=port" <?php echo $actif_port; ?> >Page Frais de port</a></li>
		</ul>
		</div>
			<?php require_once(dirname(__FILE__).'/general.php'); 
	}
	elseif($_GET['action2'] == "contact") {
		$cle = $_POST['contact'];
		$var = 'contact';
		$stmt->execute();
			// liste des parametres 
		$sql ="SELECT * FROM parametres;";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->execute();
		$data = $stmt->fetchAll();
		$param = array();
		foreach ($data as $ligne) {
			$param[$ligne['cle']] = $ligne['valeur'];
		}
		?>
		<div class="valide messageBox">
			<p>Mise à jour éffectué avec Succés</p>
		</div>
		<div id="onglet">
		<ul>
			<li><a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo" <?php echo $actif_default; ?> >Paramètres généraux</a> </li>
			<li><a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo&action2=contact" <?php echo $actif_contact; ?> >Page Contact</a> </li>
			<li><a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo&action2=legal" <?php echo $actif_legal; ?> >Page Mentions Légales</a></li>
			<li><a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo&action2=port" <?php echo $actif_port; ?> >Page Frais de port</a></li>
		</ul>
		</div>
			<?php require_once(dirname(__FILE__).'/contact.php'); 
	}
	elseif($_GET['action2'] == "legal") {
		$cle = $_POST['legal'];
		$var = 'mentionLegal';
		$stmt->execute();
			// liste des parametres 
		$sql ="SELECT * FROM parametres;";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->execute();
		$data = $stmt->fetchAll();
		$param = array();
		foreach ($data as $ligne) {
			$param[$ligne['cle']] = $ligne['valeur'];
		}
		?>
		<div class="valide messageBox">
			<p>Mise à jour éffectué avec Succés</p>
		</div>
		<div id="onglet">
		<ul>
			<li><a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo" <?php echo $actif_default; ?> >Paramètres généraux</a> </li>
			<li><a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo&action2=contact" <?php echo $actif_contact; ?> >Page Contact</a> </li>
			<li><a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo&action2=legal" <?php echo $actif_legal; ?> >Page Mentions Légales</a></li>
			<li><a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo&action2=port" <?php echo $actif_port; ?> >Page Frais de port</a></li>
		</ul>
		</div>
			<?php require_once(dirname(__FILE__).'/legal.php');
	}
	else {
		$cle = $_POST['tva'];
		$var = 'tva';
		$stmt->execute();
				
		$cle = $_POST['ginfo'];
		$var = 'infoEntrepriseAcc';
		$stmt->execute();
		
		$cle = $_POST['merchant_id'];
		$var = 'merchant_id';
		$stmt->execute();
		
		$cle = $_POST['id_scellius'];
		$var = 'id_scellius';
		$stmt->execute();
		
		$cle = $_POST['pw_scellius'];
		$var = 'pw_scellius';
		$stmt->execute();
		
		$cle = $_POST['fdp_kt'];
		$var = 'fdp_kt';
		$stmt->execute();

			
		$cle = $_POST['contre_remb'];
		$var = 'contre_remb';
		$stmt->execute();
		
		$cle = $_POST['bdd_produit'];
		$var = 'bdd_produit';
		$stmt->execute();
		
		// liste des parametres 
		$sql ="SELECT * FROM parametres;";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->execute();
		$data = $stmt->fetchAll();
		$param = array();
		foreach ($data as $ligne) {
			$param[$ligne['cle']] = $ligne['valeur'];
		}
		?>
		<div class="valide messageBox">
			<p>Mise à jour éffectué avec Succés</p>
		</div>
		<div id="onglet">
			<a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo&action2=general" <?php echo $actif_default; ?> >Paramètres généraux</a>
			<a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo&action2=save" <?php echo $actif_contact; ?> >Page Sauvegarde</a>
			<a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo&action2=contact" <?php echo $actif_contact; ?> >Page Contact</a>
			<a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo&action2=legal" <?php echo $actif_legal; ?> >Page Mentions Légales</a>
			<a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo&action2=port" <?php echo $actif_port; ?> >Page Frais de port</a>
		</div>
			<?php 
	}
}
else {
	?>
	<div id="onglet">
		<a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo&action2=general" <?php echo $actif_default; ?> >Paramètres généraux</a>
		<a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo&action2=save" <?php echo $actif_contact; ?> >Page Sauvegarde</a>
		<a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo&action2=contact" <?php echo $actif_contact; ?> >Page Contact</a>
		<a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo&action2=legal" <?php echo $actif_legal; ?> >Page Mentions Légales</a>
		<a href="<?php echo $basePath;?>index.php?page=admin&action=gererinfo&action2=port" <?php echo $actif_port; ?> >Page Frais de port</a>
	</div>
<?php
	// liste des parametres 
	$sql ="SELECT * FROM parametres;";
	$db = Zend_Registry::get('database');
	$stmt = $db->prepare($sql);
	$stmt->execute();
	$data = $stmt->fetchAll();
	$param = array();
	foreach ($data as $ligne) {
		$param[$ligne['cle']] = $ligne['valeur'];
	}
	if(isset($_GET['action2']) && $_GET['action2'] == "contact") {
		require_once(dirname(__FILE__).'/contact.php');
	}
	elseif(isset($_GET['action2']) && $_GET['action2'] == "legal") {
		require_once(dirname(__FILE__).'/legal.php');
	}
	elseif(isset($_GET['action2']) && $_GET['action2'] == "general") {
		require_once(dirname(__FILE__).'/general.php');
	}
	elseif(isset($_GET['action2']) && $_GET['action2'] == 'port'){
		require_once dirname(__FILE__).'/frais_port.php';
	}
	elseif(isset($_GET['action2']) && $_GET['action2'] == 'save'){
		$file = 'admin/info/backup.php';
		$newfile = 'init.php';
		copy($file, $newfile);
	}
}
?>
<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>index.php?page=admin&action=administration"><?php echo $translate->_('Retour');?></a>
</p>

<?php

	//} 

?>