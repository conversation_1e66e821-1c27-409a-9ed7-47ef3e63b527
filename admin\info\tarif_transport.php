<div class="form-style-10">	
<div class="section" style="margin-bottom:30px;"><span>¤</span>Tarif des différents transport</div>

<div class="inner-wrap">
<h1 style="text-align:center;">Tarif la <PERSON></h1>
<table class="table-fill" style="font-size:14px; width: 740px;" border="0" cellspacing="0" cellpadding="0">
	<tr>
		<th style="width:75px;"></th>
		<th style="width:75px; text-align:center;">FRANCE</th>
		<th style="width:75px; text-align:center;">OM</th>
		<th style="width:75px; text-align:center;">UE</th>
		<th style="width:75px; text-align:center;">HORS UE</th>
		<th style="width:75px; text-align:center;">MONDE</th>
	</tr>
<?php

$result = $database->prepare("SELECT * FROM tarif_poste ") or die ("requete r1 invalid");
$result->execute();
						
while ($tab = $result->fetch()) {

?>	
	<tr>
		<td style="text-align:center;"><?php echo $tab['poids_poste']; ?> Kg</td>
		<td style="text-align:right;"><?php echo number_format($tab['france_poste'],2, '.', ''); ?> €</td>
		<td style="text-align:right;"><?php echo number_format($tab['om_poste'],2, '.', ''); ?> €</td>
		<td style="text-align:right;"><?php echo number_format($tab['ue_poste'],2, '.', ''); ?> €</td>
		<td style="text-align:right;"><?php echo number_format($tab['hors_ue_poste'],2, '.', ''); ?> €</td>
		<td style="text-align:right;"><?php echo number_format($tab['monde_poste'],2, '.', ''); ?> €</td>
	</tr>
<?php

}

?>	
</table>
</div>
<div class="inner-wrap" style="text-align:center;">
<h1>Tarif Schenker</h1>
<table width="750">
	<tr>
		<td><a href='admin/facture/editer/tarif_schenker_nat.pdf' target='_blank'><strong>Tarif Schenker national</strong></a></td>
		<td><a href='admin/facture/editer/tarif_schenker_inter.pdf' target='_blank'><strong>Tarif Schenker international</strong></a></td>
	</tr>
</table>
</div>
</div>


