<?php

// Fichier créé par <PERSON><PERSON><PERSON>
// Modifié par Benoit Leveque
// Ce fichier PHP permet de gêrer les liens  
require_once dirname(__FILE__).'../../../class/liens_class.php';
?>
<h1><img src="<?php echo $basePath; ?>public/images/liens.png" alt=""/> <span><?php echo $translate->_('Gérer les liens'); ?></span></h1>

<?php
	if ($user->isAuthenticated)
	{
		// Liens pour ajouter un lien
		?>
		<p>
			<a href="<?php echo $basePath ?>index.php?page=admin&action=ajouterliens">
				<img src="<?php echo $basePath ?>public/images/add.png"/> Ajouter un nouveau lien
			</a>
		</p>
		<?php
	
		// Affichage de tous les liens
		$liensDB = new liensDB;
		$lienslist = array();
		$lienslist = $liensDB->getAllLiens();
		// Test si la liste des liens n'est pas vide
		if ($lienslist != null)
		{
			$nbliens = 0;
			foreach ($lienslist as $ligne)
			{
				$lienscourante = $ligne;
				$affId = $lienscourante->getIdLiens();
				$affNom = $lienscourante->getNomLiens();
				$affAdresse = $lienscourante->getAdresseLiens();
				$affTel = $lienscourante->getTelLiens();
				$affFax = $lienscourante->getFaxLiens();
				$affWeb = $lienscourante->getWebLiens();
				$affEmail = $lienscourante->getEmailLiens();
				$affCommentaires = $lienscourante->getCommentaires();
				$admin = true;
				
				// Affichage du lien grâce à une fonction prédéfinie
				afficheLiensUser($affId, $affNom, $affAdresse, $affTel, $affFax, $affWeb, $affEmail, $affCommentaires, $admin);
				// Compteur de liens
				$nbliens = $nbliens + 1;
			}
			if ($nbliens == 1)
			{
				$fin = 'lien enregistré.';
			}
			else
			{
				$fin = 'liens enregistrés.';
			}
		?>
			<div class="info messageBox" style="float:left;">Vous avez exactement <?php echo $nbliens; ?> <?php echo $fin; ?></div>
			<?php
		}
		else
		{
			?>
			<div class="info messageBox"><p>Vous n'avez pas encore de lien enregistré</p></div>
			<?php
		}
	}
?>
<br class="clear" />
<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>index.php?page=admin&action=administration"><?php echo $translate->_('Retour');?></a>
</p>