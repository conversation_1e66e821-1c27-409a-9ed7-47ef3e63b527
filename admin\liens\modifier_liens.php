<?php

// Fichier créé par <PERSON><PERSON><PERSON>

// Ce fichier PHP permet de modifier un lien
require_once dirname(__FILE__).'../../../class/liens_class.php';

echo '<h1>Modifier un lien</h1>';

$afficheform=true;

if(isset($_POST['id'])) // On se trouve sur la page de traitement
{
	// Déclaration des variables :
	// Formulaire :
	$idliens=htmlentities($_POST['id']);
	$nomliens=htmlentities($_POST['nom'], ENT_QUOTES, $charSet='UTF-8');
	$nomliens=stripslashes($nomliens);
	$adresseliens=htmlentities($_POST['adresse'], ENT_QUOTES, $charSet='UTF-8');
	$adresseliens=stripslashes($adresseliens);
	$telliens=htmlentities($_POST['tel'], ENT_QUOTES, $charSet='UTF-8');
	$faxliens=htmlentities($_POST['fax'], ENT_QUOTES, $charSet='UTF-8');
	$webliens=htmlentities($_POST['web']);
	$emailliens=htmlentities($_POST['email']);
	$commentairesliens=htmlentities($_POST['commentaires'], ENT_QUOTES, $charSet='UTF-8');
	$commentairesliens=stripslashes($commentairesliens);
	$toutok = true;
	$error = "";
	// On teste si le champs "nom" ont bien été remplis 
	if (!empty($_POST['nom']))
	{
		// On test si le tel as été posté
		if (!empty($_POST['tel']))
		{
			// On test si le numéro de tel est correct (que des chiffres)
			if(!preg_match('`[0-9]{10}`',$telliens))
			{
				$toutok = false;
				$error = "Le numéro de téléphone est incorrect.";
			}
		}
		// On test si le fax as été posté
		if (!empty($_POST['fax']))
		{
			// On test si le numéro de fax est correct (que des chiffres)
			if(!preg_match('`[0-9]{10}`',$faxliens))
			{
				$toutok = false;
				$error = $error."<br/>Le numéro de fax est incorrect.";
			}
		}
		// On test si l'email as été posté
		if (!empty($_POST['email']))
		{
			// On test si l'adresse mail est correct
			if(!verifierAdresseMail($emailliens))
			{
				$toutok = false;
				$error = $error."<br/>L'adresse mail est incorrect.";
			}
		}

		if ($toutok)
		{
			$afficheform=false;
			//Affichage d'un récapitulatif des données inscrites
			echo '<div class="center"><h2>Récapitulatif de votre lien</h2></div>';
			echo '<p>';
			afficheLiens($idliens, $nomliens, $adresseliens, $telliens, $faxliens, $webliens, $emailliens, $commentairesliens);
			echo '</p>';
			echo '<br/><br/>';
			echo '<div class="center"><h2>Résultat des modifications</h2></div>';
			
			// Enregistrement dans la BDD avec test si la système à réussit à se connecter et à enregistrer les données
			$liens = new liens();
			$liens->setIdLiens($idliens);
			$liens->setNomLiens($nomliens);
			$liens->setAdresseLiens($adresseliens);
			$liens->setTelLiens($telliens);
			$liens->setFaxLiens($faxliens);
			$liens->setWebLiens($webliens);
			$liens->setEmailLiens($emailliens);
			$liens->setCommentaires($commentairesliens);
			$liensDB = new liensDB();
			// On vérifie que tout c'est bien passé dans l'enregistrement
			if ($liensDB->save($liens))
			{
				echo '<div class="valide messageBox"><p>La modification du lien a bien fonctionné.</p></div>';
			}
			else
			{
				echo '<div class="error messageBox"><p>Aucun changement...</p></div>';
			}
		}
		else
		{
			echo '<div class="error messageBox"><p>'.$error.'</p></div>';
		}
	}
	else
	{
		echo '<div class="error messageBox"><p>Il faut impérativement donner un nom au lien.</p></div>';
	}
}

if ($afficheform)
{
	if ($user->isAuthenticated)
	{
		echo '<br/>';
		if (isset($_GET['id']))
		{
			$id = $_GET['id'];
			$liensDB = new liensDB;
			$liensselect = $liensDB->getLiensById($id);
			// Contenu du formulaire d'ajout des coordonnées d'un client avec un tableau pour afficher correctement
			?>
			<div class="form">
				<form action="<?php echo $basePath; ?>index.php?page=admin&action=modifierliens&id=<?php echo $_GET["id"]; ?>" method="POST">
					<fieldset class="input">
						<ol>
							<li>
								<label for="nom">Nom du lien :</label>
								<input type="text" name="nom" value="<?php echo $liensselect->getNomLiens(); ?>" class="indispensable"/><b class="reditalique">* </b>
							</li>
							<li>
								<label for="adressse">Adresse du lien :</label>
								<input type="text" name="adresse" size=50 maxlength=240 value="<?php echo $liensselect->getAdresseLiens(); ?>" />
							</li>
							<li>
								<label for="tel">Téléphone du lien :</label>
								<input type="text" name="tel" size=10 maxlength=10 value="<?php echo $liensselect->getTelLiens(); ?>" />
							</li>
							<li>
								<label for="fax">Fax du lien :</label>
								<input type="text" name="fax" size=10 maxlength=10 value="<?php echo $liensselect->getFaxLiens(); ?>" />
							</li>
							<li>
								<label for="web">Web du lien :</label>
								<input type="text" name="web" size=50 maxlength=100 value="<?php echo $liensselect->getWebLiens(); ?>"/>
							</li>
							<li>
								<label for="email">Email du lien :</label>
								<input type="text" name="email" size=50 maxlength=100 value="<?php echo $liensselect->getEmailLiens(); ?>"/>
							</li>
							<li>
								<label for="commentaire">Commentaires du lien :</label>
								<textarea name="commentaires" cols=37 rows=5><?php echo $liensselect->getCommentaires(); ?></textarea>
							</li> 
							<li>
								<input type="hidden" name="id" value="<?php echo $liensselect->getIdLiens()?>"/>
							</li>
						</ol>
					</fieldset>
					<fieldset class="submit">
						<ol>
							<li>
								<input type="submit" name="envoyer" value="Enregistrer" class="btn_submit" />
							</li>
						</ol>
					</fieldset>
				</form>
			</div>
	<?php
		}
		else
		{
			echo '<div class="error messageBox"><p>Erreur d\'ouverture du lien.</p></div>';
		}
	}
}

?>
<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>index.php?page=admin&action=gererliens">Retour</a>
</p>