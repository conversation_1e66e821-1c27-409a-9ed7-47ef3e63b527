<?php

// Fichier créé par <PERSON><PERSON><PERSON>

// Ce fichier PHP permet de supprimer un lien
require_once dirname(__FILE__).'../../../class/liens_class.php';

echo '<h1>Supprimer un lien</h1>';

if ($user->isAuthenticated)
{
	echo '<br/>';
	if (isset($_GET['id']))
	{
		$id = $_GET['id'];
		// Formulaire de suppression
		
		$liensDB = new liensDB;
		$liensselect = $liensDB->getLiensById($id);
		// Si le client possède déjà une adresse de facturation : On affiche le formulaire pré-remplis
		if ($liensselect instanceof liens)
		{
			$idliens = $liensselect->getIdLiens();
			$nomliens = $liensselect->getNomLiens();
			$adresseliens = $liensselect->getAdresseLiens();
			$telliens = $liensselect->getTelLiens();
			$faxliens = $liensselect->getFaxLiens();
			$webliens = $liensselect->getWebLiens();
			$emailliens = $liensselect->getEmailLiens();
			$commentaires = $liensselect->getCommentaires();
			// Affichage du lien grâce à une fonction prédéfinie
			echo afficheLiens($idliens, $nomliens, $adresseliens, $telliens, $faxliens, $webliens, $emailliens, $commentaires);
			echo '<br/><br/>';
			$liensDB = new liensDB();
			if ($liensDB->deleteLiensById($id))
			{
				echo '<div class="valide messageBox"><p>Suppression effectuée</p></div>';
			}
			else
			{
				echo '<div class="error messageBox"><p>Erreur dans la suppression...</p></div>';
			}
		}
		else
		{
			echo '<div class="error messageBox"><p>Erreur d\'ouverture de liens séléctionné</p></div>';
		}
	}
	else
	{
		echo '<div class="error messageBox"><p>Erreur d\'ouverture du lien.</p></div>';
	}
echo '<br/><p class="bouton_retour"><a href="',$basePath,'index.php?page=admin&action=gererliens">Retour</a></p>';
}

?>