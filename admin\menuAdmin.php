<?php if($user->isAdmin === true): ?>
	<?php
	// affiche le nombre d'annonce en attente de validation
		require_once dirname(__FILE__).'/../class/annonce_class.php';
		$nbAnnonceEnAttenteDeValidation = 0;
		$nbAnnonceEnAttenteDeValidation = annonceDB::getNbAValideAnnonces();
		
		require_once 'commande_static_class.php';
		$nbCommandeEnAttente = commande_staticDB::getNbCommande("Paiement validé");
	
		$result1 = $database->prepare("
		SELECT COUNT(*) AS nb_devis FROM demande_devis WHERE statut_devis = '1'
		") or die ("requete r1 invalid");
		$result1->execute();
		$nb_devis = $result1->fetch();
		
		$result2 = $database->prepare("
		SELECT COUNT(*) AS nb_photo FROM demande_photo WHERE statut_photo = '1'
		") or die ("requete r2 invalid");
		$result2->execute();
		$nb_photo = $result2->fetch();
		
		$result3 = $database->prepare("
		SELECT COUNT(*) AS nb_desc FROM demande_desc WHERE statut_desc = '1'
		") or die ("requete r3 invalid");
		$result3->execute();
		$nb_desc = $result3->fetch();
		
		$result4 = $database->prepare("
		SELECT COUNT(*) AS nb_dde_prod_us FROM demande_produit_usa WHERE statut_dde_prod_us = '1'
		") or die ("requete r4 invalid");
		$result4->execute();
		$nb_dde_prod_us = $result4->fetch();
		
		$result5 = $database->prepare("
		SELECT COUNT(*) AS nb_dde_bourse FROM bourse WHERE statut_bourse = '0'
		") or die ("requete r5 invalid");
		$result5->execute();
		$nb_dde_bourse = $result5->fetch();
		
		$nb_total = $nbAnnonceEnAttenteDeValidation + $nb_photo['nb_photo'] + $nb_devis['nb_devis'] + $nb_desc['nb_desc'] + $nb_dde_prod_us['nb_dde_prod_us'] + $nb_dde_bourse['nb_dde_bourse'];
		
	
	 ?>
	<ul>
		<li><a href="<?php echo $basePath ?>index.php?page=admin&action=commandes">Commandes en attentes : <?php echo $nbCommandeEnAttente; ?></a></li>
		<li><a href="<?php echo $basePath ?>index.php?page=admin&action=annonces" title="<?php echo $nb_total; ?> demande à valider">Demandes en attentes : <?php echo $nb_total; ?></a></li>
		<li><a href="<?php echo $basePath; ?>panier/mes_paniers"><?php echo $translate->_('Mes paniers'); ?></a></li>
	</ul>
	<ul>
		<li><a href="<?php echo $basePath ?>index.php?page=admin&action=administration">Administration</a></li>
    </ul>
	<ul>
		<li><a href="<?php echo $basePath ?>index.php?page=admin&action=scannette">Scan Produits</a></li>
    </ul>
<?php endif; ?>