<?php if($user->isAdmin === true): ?>
	<?php
	// affiche le nombre d'annonce en attente de validation
		require_once dirname(__FILE__).'/../class/annonce_class.php';
		$nbAnnonceEnAttenteDeValidation = 0;
		$nbAnnonceEnAttenteDeValidation = annonceDB::getNbAValideAnnonces();
		
		require_once 'commande_static_class.php';
		$nbCommandeEnAttente = commande_staticDB::getNbCommande("Paiement validé");
		
// identification de l'administrateur :
	 ?>
<script type="text/javascript">
//<![CDATA[	 
function whois(str){
		var xmlhttp = null;
		if (str=="")
		  {
		  document.getElementById("txtHint").innerHTML="";
		  return;
		  }
		if (window.XMLHttpRequest)
		  {// code for IE7+, Firefox, Chrome, Opera, Safari
		  xmlhttp=new XMLHttpRequest();
		  }
		else
		  {// code for IE6, IE5
		  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
		  }
		xmlhttp.onreadystatechange=function()
		 {
		 if (xmlhttp.readyState==4 && xmlhttp.status==200)
		{
			document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
		}
		}
		xmlhttp.open("GET","<?php echo $basePath; ?>client/whois.php?name="+str,true);
		xmlhttp.send();
		document.getElementById('oModal').style.opacity = '0';
		document.getElementById("oModal").style.pointerEvents = "none";
	}	
		
function cachedivpopupmsgdevis() {

	document.getElementById('oModal').style.opacity = '0';
	document.getElementById("oModal").style.pointerEvents = "none";
	
}		
//]]>
</script>

	<table style="margin-left:auto;margin-right:auto">
	<tr>
	<td>
	<h1><a href="<?php echo $basePath ?>index.php?page=admin&action=gererprod"><img src="<?php echo $basePath; ?>public/images/produit.png" alt="produits"/>Gérer les produits</a></h1>
    						<div style="text-align:center;">
							<form method="get" action="<?php echo $basePath.'index.php';?>">
							
								<input type="hidden" name="page" value="admin" />
								<input type="hidden" name="action" value="gererprod" />
								<img src="<?php echo $basePath; ?>public/images/loupe.png" alt="loupe"/>
								<input class="inputnewstyle" style="width:150px;" id="autocomplete2" type="text" name="valuetosearch" value="" placeholder="Editer un produit" class="auto" />
								<input type="hidden" name="in" value="ALL" /> 
								<input type="hidden" name="method" value="AND" /> 
								<input class="submitnewstyle" type="submit" value="Ok" /> </br>
							
							</form>
							

							<script>
							$( "#autocomplete2" ).autocomplete({
							 source: "<?php echo $basePath; ?>client/search.php",
							 minLength: 2,
							 scrollHeight: 220,
							 select: function(event,ui){
							        //assign value back to the form element
								if(ui.item){
									$(event.target).val(ui.item.value);
								}
								//submit the form
								$(event.target.form).submit();
							}
							});
							</script>
							</div>
	</td>
	<td>
	<h1><a href="<?php echo $basePath ?>index.php?page=admin&action=gererclient"><img src="<?php echo $basePath; ?>public/images/clients.png" alt="clients"/>Gérer les clients</a></h1>
							
							<div style="text-align:center;">
							<form method="get" action="<?php echo $basePath.'index.php';?>">
							
								<input type="hidden" name="page" value="admin" />
								<input type="hidden" name="action" value="gererclient" />
								<img src="<?php echo $basePath; ?>public/images/loupe.png" alt="loupe"/>
								<input class="inputnewstyle" style="width:150px;" type="text" name="valuetosearch" value="" placeholder="Editer client" />
								<input type="hidden" name="in" value="ALL" /> 
								<input type="hidden" name="method" value="AND" /> 
								<input class="submitnewstyle" type="submit" value="Ok" /> </br>
							
							</form>
							</div>   

   </td>
	</tr>
	<tr>
	<td>	
	<h1><a href="<?php echo $basePath ?>index.php?page=admin&action=commandes"><img src="<?php echo $basePath; ?>public/images/commande.gif" alt="commandes"/>Gérer les commandes (<?php echo $nbCommandeEnAttente; ?>)</a></h1>
							<div style="text-align:center;">
							<form method="get" action="<?php echo $basePath.'index.php';?>">
							
								<input type="hidden" name="page" value="admin" />
								<input type="hidden" name="action" value="commandes" />
								<img src="<?php echo $basePath; ?>public/images/loupe.png" alt="loupe"/>
								<input class="inputnewstyle" style="width:150px;" type="text" name="id" value="" placeholder="Editer commande" />
								<input class="submitnewstyle" type="submit" value="Ok" /> </br>
							
							</form>
							</div>
	</td>
	<td>	
	<h1><a href="<?php echo $basePath ?>index.php?page=admin&action=facture&action2=editer"> <img src="<?php echo $basePath; ?>public/images/facture/icone_facture.png" alt="factures"/>Gérer les factures</a></h1>
    						
							<div style="text-align:center;">
							<form method="get" action="<?php echo $basePath.'index.php';?>">
								<img src="<?php echo $basePath; ?>public/images/loupe.png" alt="loupe"/>
								<input type="hidden" name="page" value="admin" />
								<input type="hidden" name="action" value="facture" />
								<input type="hidden" name="type" value="edit" />
								<input class="inputnewstyle" style="width:150px;" type="text" name="idcommande" placeholder="Editer facture (N° cde)" />
								<input class="submitnewstyle" type="submit" value="Ok" /> </br>
							</form>
							</div>
	</td>
	</tr>
	<tr>
	<td>
	<?php
	
	$result1 = $database->prepare("
	SELECT COUNT(*) AS nb_devis FROM demande_devis WHERE statut_devis = '1'
	") or die ("requete r1 invalid");
	$result1->execute();
	$nb_devis = $result1->fetch();
	
	$result2 = $database->prepare("
	SELECT COUNT(*) AS nb_photo FROM demande_photo WHERE statut_photo = '1'
	") or die ("requete r2 invalid");
	$result2->execute();
	$nb_photo = $result2->fetch();
	
	$result3 = $database->prepare("
	SELECT COUNT(*) AS nb_desc FROM demande_desc WHERE statut_desc = '1'
	") or die ("requete r3 invalid");
	$result3->execute();
	$nb_desc = $result3->fetch();
	
	$result4 = $database->prepare("
	SELECT COUNT(*) AS nb_dde_prod_us FROM demande_produit_usa WHERE statut_dde_prod_us = '1'
	") or die ("requete r4 invalid");
	$result4->execute();
	$nb_dde_prod_us = $result4->fetch();
	
	$result5 = $database->prepare("
	SELECT COUNT(*) AS nb_dde_bourse FROM bourse WHERE statut_bourse = '0'
	") or die ("requete r5 invalid");
	$result5->execute();
	$nb_dde_bourse = $result5->fetch();
	
	$nb_total = $nbAnnonceEnAttenteDeValidation + $nb_photo['nb_photo'] + $nb_devis['nb_devis'] + $nb_desc['nb_desc'] + $nb_dde_prod_us['nb_dde_prod_us'] + $nb_dde_bourse['nb_dde_bourse'];
	
	?>
	
	<h1><a href="<?php echo $basePath ?>index.php?page=admin&action=annonces" title="<?php echo $nb_total; ?> demande à valider"><img src="<?php echo $basePath; ?>public/images/annonces.png" alt="annonces"/>Gérer les demandes (<?php echo $nb_total; ?>)</a></h1>
    </td>
	<td>	
	<h1><a href="<?php echo $basePath ?>index.php?page=admin&action=gererfour"><img src="<?php echo $basePath; ?>public/images/fournisseur.png" alt="fournisseur"/>Gérer les fournisseurs</a></h1>
	    					<div style="text-align:center;">
							<form method="get" action="<?php echo $basePath.'index.php';?>">
							
								<input type="hidden" name="page" value="admin" />
								<input type="hidden" name="action" value="gererfour" />
								<img src="<?php echo $basePath; ?>public/images/loupe.png" alt="loupe"/>
								<input class="inputnewstyle" style="width:150px;" type="text" name="valuetosearch" value="" placeholder="Recherche fournisseur" />
								<input type="hidden" name="in" value="ALL" /> 
								<input type="hidden" name="method" value="AND" /> 
								<input class="submitnewstyle" type="submit" value="Ok" /> </br>
							
							</form>
							</div>
    </td>
	</tr>
	<tr>
	<td>
	<h1><a href="<?php echo $basePath ?>index.php?page=admin&action=gererinfo"><img src="<?php echo $basePath; ?>public/images/param.png" alt="parametres"/>Gérer les paramètres du site</a></h1>
	</td>
	<td>
	<h1><a target="_blank" href="<?php echo $basePath ?>admothers/"><img src="<?php echo $basePath; ?>public/images/param2.png" alt="Administration 2"/>Admin 2 (photos, etc.)</a></h1>
	</td>
	</tr>
	<tr>
	<td>	
	<h1><a href="<?php echo $basePath ?>index.php?page=admin&action=gererliens"><img src="<?php echo $basePath; ?>public/images/liens.png" alt="liens"/>Gérer les liens</a></h1>
    </td>
	<td>
	<h1><a href="<?php echo $basePath ?>index.php?page=admin&action=enquete"><img src="<?php echo $basePath; ?>public/images/enquete.png" alt="enquete"/>Enquête satisfaction</a></h1>
    </td>
	</tr>
	<tr>
	<td>	
	<h1><a href="<?php echo $basePath ?>index.php?page=admin&action=tariftransport"><img src="<?php echo $basePath; ?>public/images/AVION.png" alt="liens"/>Tarifs Transport</a></h1>
    </td>
	<td>
	<h1><a href="<?php echo $basePath ?>"><img src="<?php echo $basePath; ?>public/images/divers.png" alt="enquete"/>-- -- -- -- -- --</a></h1>
    </td>
	</tr>
	</table>
	<div id="cont_8708718e7b82f7a2016e8ba6bde03b87"><script type="text/javascript" async src="https://www.tameteo.com/wid_loader/8708718e7b82f7a2016e8ba6bde03b87"></script></div>
	<div align="center"><h1><img src="<?php echo $basePath; ?>public/images/agenda.png" alt="enquete"/>  Agenda SMI</h1></div>
	<div align="center"><iframe src="https://calendar.google.com/calendar/embed?src=jeepdodgegmc%40gmail.com&ctz=Europe%2FParis" style=" border-width:0 " width="700" height="400" frameborder="0" scrolling="no"></iframe>
	</div>
<?php 
if (isset($_SESSION['who']) && $_SESSION['who'] != ""){

} else {
/*
?>
	
<div id="popupwho" style="	
		text-align:center;
		background-color:#cec763;
		width:500px;
		height:100;
		border:dashed;
		position:relative;
		position: absolute; left: 220px; top: 150px;">
<table width="100%" border="0" height="21">
	<tr><td align="right">
		<a title="Fermer ce message" onmouseover="document.crpopup.src='<?php echo $basePath;?>public/images/croix_rouge_claire.gif'" onmouseout="document.crpopup.src='<?php echo $basePath;?>public/images/croix_rouge.gif'"><img src="<?php echo $basePath;?>public/images/croix_rouge.gif" name="crpopup" border="0" onclick="cachedivpopuphoto();" /></a>
	</td></tr>
</table>
<strong> Merci de vous identifier ! </strong><br /><br />
<input type="button" style="height: 64px; width: 64px; margin-left: 10px; color: #000099; font-size: 14px; font-weight: bold;" name="who" value="MD" onclick="whois('MD')" />
<input type="button" style="height: 64px; width: 64px; margin-left: 10px; color: #FF6600; font-size: 14px; font-weight: bold;" name="who" value="YB" onclick="whois('YB')" />
<input type="button" style="height: 64px; width: 64px; margin-left: 10px; color: #009900; font-size: 14px; font-weight: bold;" name="who" value="JL" onclick="whois('JL')" />
<input type="button" style="height: 64px; width: 64px; margin-left: 10px; color: #9900FF; font-size: 14px; font-weight: bold;" name="who" value="LT" onclick="whois('LT')" />
<input type="button" style="height: 64px; width: 64px; margin-left: 10px; color: #9900FF; font-size: 14px; font-weight: bold;" name="who" value="BF" onclick="whois('BF')" />
<input type="button" style="height: 64px; width: 64px; margin-left: 10px; color: #000000; font-size: 14px; font-weight: bold;" name="who" value="AU" onclick="whois('AU')" />
<br /><br />
</div>

<?php
*/
?>
				<div id="oModal" class="oModal" style="opacity:1; pointer-events: auto;">
				  <div style=" margin: 10% auto;transition: all 0.4s ease-in-out;-moz-transition: all 0.4s ease-in-out;-webkit-transition: all 0.4s ease-in-out;">
					<header>  
					   Merci de vous identifier !
					   <a href="#fermer" onclick="cachedivpopupmsgdevis();" style="margin-left:325px;" title="Fermer la fenêtre" class="droite"><img src="<?php echo $basePath.'public/images/cross.png'; ?>" alt="SUPPRIMER" /></a>
					 </header>
					 <section>
						<input type="button" style="height: 64px; width: 60px; margin-left: 20px; color: #000099; font-size: 14px; font-weight: bold;" name="who" value="MD" onclick="whois('MD')" />
						<input type="button" style="height: 64px; width: 60px; margin-left: 10px; color: #FF6600; font-size: 14px; font-weight: bold;" name="who" value="YB" onclick="whois('YB')" />
						<input type="button" style="height: 64px; width: 60px; margin-left: 10px; color: #009900; font-size: 14px; font-weight: bold;" name="who" value="JL" onclick="whois('JL')" />
						<input type="button" style="height: 64px; width: 60px; margin-left: 10px; color: #9900FF; font-size: 14px; font-weight: bold;" name="who" value="LT" onclick="whois('LT')" />
						<input type="button" style="height: 64px; width: 60px; margin-left: 10px; color: red; font-size: 14px; font-weight: bold;" name="who" value="BF" onclick="whois('BF')" />
												<input type="button" style="height: 64px; width: 60px; margin-left: 10px; color: #000000; font-size: 14px; font-weight: bold;" name="who" value="AU" onclick="whois('AU')" />
					 <section>
					 <footer class="cf">
						<input onclick="cachedivpopupmsgdevis();" style="padding:6px 0 6px 0; font:bold 13px Arial; background:#478bf9;color:#fff; border-radius:2px; width:100px; border:none;" class="btn droite" type="button" value="Fermée" />
					 </footer>
				  </div>
				</div>

<?php 

}

?>


<?php endif; ?>