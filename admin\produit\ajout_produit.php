<?php
/**
 * programme qui ajoute un produit de la base de donnée
 */
?>


<script>
	function test_reference(str)
	{
		var xmlhttp = null;
		if (str=="")
		  {
		  document.getElementById("txtHint").innerHTML="";
		  return;
		  }
		if (window.XMLHttpRequest)
		  {// code for IE7+, Firefox, Chrome, Opera, Safari
		  xmlhttp=new XMLHttpRequest();
		  }
		else
		  {// code for IE6, IE5
		  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
		  }
		xmlhttp.onreadystatechange=function()
		  {
		  if (xmlhttp.readyState==4 && xmlhttp.status==200)
			{
			document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
			}
		  }
		xmlhttp.open("GET","<?php echo $basePath; ?>client/test_reference.php?ref="+str,true);
		xmlhttp.send();
	} 
	
	function test_reference_add()
	{
		str = document.getElementById("reference").value;
		var xmlhttp = null;
		if (str=="")
		  {
		  document.getElementById("txtHint").innerHTML="";
		  return;
		  }
		if (window.XMLHttpRequest)
		  {// code for IE7+, Firefox, Chrome, Opera, Safari
		  xmlhttp=new XMLHttpRequest();
		  }
		else
		  {// code for IE6, IE5
		  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
		  }
		xmlhttp.onreadystatechange=function()
		  {
		  if (xmlhttp.readyState==4 && xmlhttp.status==200)
			{
			document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
			}
		  }
		xmlhttp.open("GET","<?php echo $basePath; ?>client/test_reference.php?ref="+str,true);
		xmlhttp.send();
	} 
	
</script>


<?php
require_once(dirname(__FILE__).'/../../class/produit_class.php');
require_once(dirname(__FILE__).'/../../class/categorie_class.php');

//on affiche la formulaire pour ajouter les données

if(!isset($_POST["ajout"])){

?>
	
<div class="form-style-10">
    <table>
    	<tr>
        <td>
<script>

Dropzone.options.dzaddprod = {
  paramName: "file", // The name that will be used to transfer the file
  maxFilesize: 0.5, // MB
  acceptedFiles: ".jpg",
};

</script>		
	<form id="dzaddprod" style="margin-right:10px;" action="<?php echo $basePath; ?>admin/produit/upload.php?ref=ajoutref" class="dropzone"></form>
		</td>
		<td>
			<div class="inner-wrap">
			 - Mettre la désignation en MAJUSCULE et les informations secondaire en miniscule.<br />
			 - Dans la référence le caractère " / " est réservé au vêtement.<br />
			 - Les références sont uniques sauf cas excpetionnel.<br />
			 - Ajouter plus de détail dans l'état suivante.<br />
			</div>
		</td>
		</tr>
	</table>
</div>
<div class="form-style-10">
<h1 style="padding-top:8px; padding-bottom:22px;" >Ajouter un produit</h1>
<form method="POST" name="formajout" action="<?php echo $basePath; ?>index.php?page=admin&action=gererprod&ajout=1" enctype="multipart/form-data">
<input type="hidden" name="ajout" value="oui" />	

   <div class="section"><span>¤</span>INFO PRINCIPAL ( * champs obligatoires )</div>
    <table>
    	<tr>
        <td>
    <div class="inner-wrap" style="width:350px; height:450px;">
       	  <label><strong>Référence*</strong> <span id="txtHint" style="color:red; text-decoration: blink; font-size:22px;"></span>
		  <input id="reference" type="text" name="reference" value="<?php if(isset($_GET['reference'])) echo $_GET['reference']; ?>" onchange="test_reference(this.value),addimage()" style="width:200px;" />
	  			<script type="text/javascript">
						var reference = new LiveValidation('reference');
						reference.add( Validate.Presence );
						reference.add(Validate.Exclusion, { within: ["'",'"',' ','à','à','â','ä','ã','ç','é','è','ê','ë','ì','î','ï','ò','ô','ö','õ','µ','ù','û','ü','ñ','&','?','!',':','.',';','#','~','^','¨','@','%','$','£','?','²','¤','§','%','*','(',')','[',']','{','}','-','=','+','<','>','|','`'],partialMatch: true, failureMessage: "Caracteres speciaux interdits"});
						
		        </script>
				
		  </label>
          <label><strong>Désignation*</strong> (courte - max 80 lettres)<textarea onfocus="this.placeholder = ''" maxlength="80" id="designation" name="designation" cols="40" rows="2" placeholder="Mettre seulement les mots essentiels pour la recherche. Eviter les abréviations, caractères spéciaux"><?php if(isset($_GET['designation'])) echo $_GET['designation']; ?></textarea>
			  	<script type="text/javascript">
						var designation = new LiveValidation('designation');
						designation.add( Validate.Presence );
						designation.add(Validate.Exclusion, { within: ['"','â','ä','ã','ç','ë','ì','î','ï','ò','ô','ö','õ','µ','ù','û','ü','ñ','&','?','!',';','#','~','^','¨','@','%','$','£','?','²','¤','§','%','*','{','}','<','>','`'],partialMatch: true, failureMessage: "Caracteres speciaux interdits"});
		        </script>
		  </label>
          <label>Description (Longue - aucune recherche)
		  <textarea onfocus="this.placeholder = ''" id="description_lg" name="description_lg" cols="40" rows="6" placeholder="Faite une description détaillé du produit, ajouter des dimensions, poids, couleurs,..."></textarea>
			<script type="text/javascript">
							var description_lg = new LiveValidation(description_lg);
							description_lg.add(Validate.Exclusion, { within: ["'",'"','â','ä','ã','ç','ë','ì','î','ï','ò','ô','ö','õ','µ','ù','û','ü','ñ','&','#','~','^','¨','@','%','$','£','²','¤','§','%','*','{','}','<','>','`'],partialMatch: true, failureMessage: "Caracteres speciaux interdits"});
			</script>
		   </label>
		  <label> Commentaire (interne)
		  <textarea onfocus="this.placeholder = ''" id="empl_comment" name="empl_comment" cols="40" rows="5" placeholder="Zone réservé pour un commentaire interne, ne sera pas visible au client"></textarea>
			<script type="text/javascript">
							var empl_comment = new LiveValidation('empl_comment');
							empl_comment.add(Validate.Exclusion, { within: ["'",'"','â','ä','ã','ç','ë','ì','î','ï','ò','ô','ö','õ','µ','ù','û','ü','ñ','&','#','~','^','¨','@','%','$','£','²','¤','§','%','*','{','}','<','>','`'],partialMatch: true, failureMessage: "Caracteres speciaux interdits"});
			</script>
		   </label>

    </div>
    	</td>
        <td>
    <div class="inner-wrap" style="width:350px; height:450px;">
	 <table>
        	<tr>
            	<td style="width:200px;"><label><strong>Qualité*</strong>			
				<select id="qualite" name="qualite" onchange="test_reference_add()" >
				
					<option value="vide" >---</option>
					<option value="NEW" >Neuf</option>
					<option value="NOS" >Nos</option>
					<option value="USE" >Occasion</option>
					<option value="REC" >Reconditionné</option>

				</select>
				<script type="text/javascript">			
					var qualite = new LiveValidation('qualite');
		            qualite.add(Validate.Exclusion, { within: ['vide'], failureMessage: " "});
		        </script>
				</label></td>
            	<td style="width:200px;"><label><strong>Stock*</strong>				
				<select id="promo" name="promo">
					
					<option value="vide">---</option>
					<option value="o">Stock non vérifié, jamais cdé ( o )</option>
					<option value="oo">Pas de stock, jamais cdé ( oo )</option>
					<option value="ooo">Introuvable ( ooo )</option>
					<option value="net">Achat ou  faible marge ( net )</option>
					<option value="x">Faible stock ( x )</option>
					<option value="xx">Moyen stock ( xx )</option>
					<option value="xxx">Enorme stock ( xxx )</option>
					<option value="col">Collector JEEP ( col )</option>

				</select>
				<script type="text/javascript">			
					var promo = new LiveValidation('promo');
		            promo.add(Validate.Exclusion, { within: ['vide'], failureMessage: " "});
		        </script>
				</label></td>
            </tr>
          </table>
		  <table>
        	<tr>
            	<td><label style="width:150px;"><strong>Prix HT*</strong><input style="text-align:center;" type="text" id="prix" name="prix" value="" style="width:150px;" />
				  <script type="text/javascript">
					var prix = new LiveValidation('prix');
					prix.add( Validate.Presence );
					prix.add( Validate.Numericality );
				  </script>
				  </label>
				</td>
				<td>
				    <label style="width:175px; margin-left:35px;">Taille (vercorps / outillage)<input type="text" name="taille" value=""  />
		            </label>
				</td>
			</tr>
          </table>			
       	  <label><strong>Genre*</strong><select id="genre" name="genre" >
					<option value="vide" >---</option>
					<?php
				$result10 = $database->prepare("SELECT * FROM produits GROUP BY genre") or die ("r10 invalid");
				$result10->execute();
				
				while ($row10 = $result10->fetch()) {

						echo '<option value="',$row10['genre'],'"';
						list($fr, $en) = explodeCategorieName($row10['genre']);
						echo '>',$fr;
					
						
						echo '</option>';
					}
					?>
				</select>
				<script type="text/javascript">			
					var genre = new LiveValidation('genre');
		            genre.add(Validate.Exclusion, { within: ['vide'], failureMessage: " "});
		        </script>
		  </label>
          <label><strong>Groupe*</strong><?php
				$result11 = $database->prepare("SELECT idgroupe,groupe FROM produits GROUP BY idgroupe ORDER BY idgroupe ASC") or die ("r11 invalid");
				$result11->execute();
				echo '<select id="groupe" name="groupe">';
				echo '<option value="vide" >---</option>';
				
				while ($row11 = $result11->fetch()) {
							echo '<option value="'.$row11['idgroupe'].'-'.$row11['groupe'].'">'.$row11['idgroupe'].'-'.$row11['groupe'].'</option>';
				}
				echo '</select>';
				
?>		
				<script type="text/javascript">			
					var groupe = new LiveValidation('groupe');
		            groupe.add(Validate.Exclusion, { within: ['vide'], failureMessage: " "});
		        </script>
		  </label>
		  <label>Type
		  <input id="type" type="text" name="type" value="" />
		<script type="text/javascript">
						var type = new LiveValidation('type');
						type.add(Validate.Exclusion, { within: ["'",'"','â','ä','ã','ç','ë','ì','î','ï','ò','ô','ö','õ','µ','ù','û','ü','ñ','&','?','!',':','.',';','t','#','~','^','¨','@','%','$','£','?','²','¤','§','%','*','{','}','<','>','`'],partialMatch: true, failureMessage: "Caracteres speciaux interdits"});
		</script>
		  </label>
          <label>
		  <table>
<th colspan="4" align="center">Produits en commun avec</th>
	<tr>
		<td align="center" bgcolor="#CCCCCC">Jeep</td>
		<td align="center" bgcolor="#CCCCCC">Dodge</td>
		<td align="center" bgcolor="#CCCCCC">Gmc</td>
		<td align="center" bgcolor="#CCCCCC">Chevrolet</td>
		<td align="center" bgcolor="#CCCCCC">R2087</td>
	</tr>
	<tr>
		<td>
		<?php
				$result12 = $database->prepare("SELECT idgroupe,groupe FROM produits GROUP BY idgroupe ORDER BY idgroupe ASC") or die ("r11 invalid");
				$result12->execute();
				echo '<select style="width:70px;" id="prod_commun_jeep" name="prod_commun_jeep">';
				echo '<option value="-1/-1" >---</option>';
				
				while ($row12 = $result12->fetch()) {
							$groupename = explode("|",$row12['groupe']);
							echo '<option value="'.$row12['idgroupe'].'/'.$groupename[0].'">'.$row12['idgroupe'].'/'.$groupename[0].'</option>';
				}
				echo '</select>';
				
		?>
		</td>
		<td>
		
		<?php
		
				echo '<select style="width:70px;" id="prod_commun_dodge" name="prod_commun_dodge">';
				echo '<option value="-1/-1" >---</option>';
				$result12->execute();
				while ($row12 = $result12->fetch()) {
							$groupename = explode("|",$row12['groupe']);
							echo '<option value="'.$row12['idgroupe'].'/'.$groupename[0].'">'.$row12['idgroupe'].'/'.$groupename[0].'</option>';
				}
				echo '</select>';
		
		?>
		
		</td>
		<td>
		
		<?php
		
				echo '<select style="width:70px;" id="prod_commun_gmc" name="prod_commun_gmc">';
				echo '<option value="-1/-1" >---</option>';
				$result12->execute();
				while ($row12 = $result12->fetch()) {
							$groupename = explode("|",$row12['groupe']);
							echo '<option value="'.$row12['idgroupe'].'/'.$groupename[0].'">'.$row12['idgroupe'].'/'.$groupename[0].'</option>';
				}
				echo '</select>';
		
		?>
		
		</td>
		<td>
		
		<?php
		
				echo '<select style="width:70px;" id="prod_commun_chevr" name="prod_commun_chevr">';
				echo '<option value="-1/-1" >---</option>';
				$result12->execute();
				while ($row12 = $result12->fetch()) {
							$groupename = explode("|",$row12['groupe']);
							echo '<option value="'.$row12['idgroupe'].'/'.$groupename[0].'">'.$row12['idgroupe'].'/'.$groupename[0].'</option>';
				}
				echo '</select>';
		
		?>
		
		</td>
		<td>
		
		<?php
		
				echo '<select style="width:70px;" id="prod_commun_r2087" name="prod_commun_r2087">';
				echo '<option value="-1/-1" >---</option>';
				$result12->execute();
				while ($row12 = $result12->fetch()) {
							$groupename = explode("|",$row12['groupe']);
							echo '<option value="'.$row12['idgroupe'].'/'.$groupename[0].'">'.$row12['idgroupe'].'/'.$groupename[0].'</option>';
				}
				echo '</select>';
		
		?>
		
		</td>
	</tr>
</table>
		  </label>
		  <table>
			<tr>
				<td>
				  <label>Zone<a href="public/images/adresse_parc_smi.jpg" rel="lytebox" title="Carte emplacement des zones" ><img src="public/images/carte.png" alt=""/></a></label>
				</td>
				<td>
						<select style="width:100px;" name="empl_principal">
						
						<option value="">---</option>
						<option value="H1">H1</option>
						<option value="MV">MV</option>
						<option value="H4">H4</option>
						<option value="B1">B1</option>
						<option value="B2">B2</option>
						<option value="B3">B3</option>
						<option value="H1E">H1E</option>
						<option value="H1O">H1O</option>
						<option value="ATELIER">ATELIER</option>
						<option value="AUVENT">AUVENT</option>
						<option value="MR">M.Ronde</option>
						<?php
						for($i = 1; $i < 6; $i++ ){
							echo '<option value="G'.$i.'">G'.$i.'</option>';
						}
						?>
						<?php
						for($i = 1; $i < 15; $i++ ){
							echo '<option value="P'.$i.'">P'.$i.'</option>';
						}
						?>
						
						</select>
						</td>
						<td>
						<input onfocus="this.placeholder = ''" type="text" style="width:225px;" name="empl_secondaire" id="empl_secondaire" value="" placeholder="Ajouter le modèle de caisse ou l'adresse exact" />
				</td>
			</tr>
		</table>
    </div>
   	    </td>
    	</tr>
    </table>
    <div class="section"><span>¤</span>Gestion produit</div>
        <table>
    	<tr>
        <td>
    <div class="inner-wrap" style="width:350px; height:175px;">
		<table>
		<label>Fournisseurs</label>
			<tr>
				<td align="center" bgcolor="#CCCCCC">Jeepest </td>
				<td align="center" bgcolor="#CCCCCC">Desmet </td>
				<td align="center" bgcolor="#CCCCCC">Gsaa </td>
			</tr>
			<tr>
				<td><input type="text" name="jeepest" value="" style="width:125px;" /></td>
				<td><input type="text" name="desmet" value="" style="width:116px;" /></td>
				<td><input type="text" name="gsaa" value="" style="width:116px;" /></td>
			</tr>
			<tr>
				<td align="center" bgcolor="#CCCCCC" style="width:116px;" >Miltec </td>
				<td align="center" bgcolor="#CCCCCC" colspan="2" style="width:225px;" >Autres </td>
			</tr>
			<tr>
				<td><input type="text" name="miltec" value="" style="width:125px;" /></td>
				<td colspan="2"><input type="text" name="autre_frs" value="" style="width:234px;" /></td>

			</tr>
		</table>

    	</td>
        <td>
		<div class="inner-wrap" style="width:350px; height:175px;">
		<table>
			<label>Achat</label>
			<tr>
				<td align="center" bgcolor="#CCCCCC">Qt&eacute; à cder</td>
				<td align="center" bgcolor="#CCCCCC">Nom Frs</td>
				<td align="center" bgcolor="#CCCCCC">P.P.</td>

			</tr>
			<tr>
				<td><input type="text" name="commande" value="" style="width:116px;" /></td>
				<td><input type="text" name="prix_frs" value="" style="width:116px;" /></td>
				<td><input type="text" name="pp" value="" style="width:116px;" /></td>
			</tr>
		</table>
		<table>
		<label>Stock</label>
			<tr>
			  <td align="center" bgcolor="#CCCCCC" >Qt&eacute; (livr&eacute; / en stock)</td>
			  <td align="center" bgcolor="#CCCCCC">Date (livr&eacute; / cr&eacute;er)</td>
			</tr>
			<tr>
				<td><input type="text" name="qte_livraison" style="width:175px; text-align:center;" value="" /></td>
				<td><input type="text" name="date_livraison" id="date_livraison" style="width:175px; text-align:center;" value="<?php echo date("d/m/Y"); ?>" />
						<script type="text/javascript">
								var date_livraison = new LiveValidation('date_livraison');
								date_livraison.add( Validate.Presence );
						</script>
				</td>
			</tr>
		</table>
		</div>
        </td>
    	</tr>
    </table> 
	<div class="button-section">
      <input type="submit" name="Ajouter" value="Ajouter" />
    </div>
    </div>	
</form>
</div>
<?php

} 
elseif(isset($_POST["ajout"])) {

		$prod = produitDB::getProduitByRef2(strtoupper($_POST['reference']));
		
if($prod instanceof Produit) {
		
		$image = "";
		
		$prod->setReference(strtoupper($_POST['reference']));
		$prod->setDesignation($_POST['designation']);
		$prod->setPrixHT($_POST['prix']);
		$prod->setGenreName($_POST['genre']);
		
		if (isset($_POST['groupe']) && $_POST['groupe'] == "no_change"){
		
		} else {
				
		$groupe = explode("-", $_POST['groupe']);
		$prod->setGroupeName2($groupe[1]);
		$prod->setGroupeName($groupe[1]);
		$prod->setGroupeId($groupe[0]);
		
		}
		
		$prod->setPromotion($_POST['promo']);
		$prod->setImage($image);
		$prod->setTaille($_POST['taille']);
		$prod->setType($_POST['type']);
		$prod->setQualite($_POST['qualite']);
			
		$prod->setJeepest($_POST['jeepest']);
		$prod->setDesmet($_POST['desmet']);
		$prod->setGsaa($_POST['gsaa']);
		$prod->setAutre_frs($_POST['autre_frs']);
		$prod->setMiltec($_POST['miltec']);
			
		$prod->setCommande($_POST['commande']);
		$prod->setPp($_POST['pp']);
		$prod->setPrix_frs($_POST['prix_frs']);
		$prod->setEmpl_comment($_POST['empl_comment']);
		$prod->setEmpl_principal($_POST['empl_principal']);
		$prod->setEmpl_secondaire($_POST['empl_secondaire']);
		
		$groupe_commun_jeep = explode("/",$_POST['prod_commun_jeep']);
		$groupe_commun_dodge = explode("/",$_POST['prod_commun_dodge']);
		$groupe_commun_gmc = explode("/",$_POST['prod_commun_gmc']);
		$groupe_commun_chevr = explode("/",$_POST['prod_commun_chevr']);
		$groupe_commun_r2087 = explode("/",$_POST['prod_commun_r2087']);
		
		$prod->setProd_commun_r2087($groupe_commun_r2087[0]);
		$prod->setProd_commun_jeep($groupe_commun_jeep[0]);
		$prod->setProd_commun_dodge($groupe_commun_dodge[0]);
		$prod->setProd_commun_gmc($groupe_commun_gmc[0]);
		$prod->setProd_commun_chevr($groupe_commun_chevr[0]);
		
		$prod->setProd_commun_r2087_name($groupe_commun_r2087[1]);
		$prod->setProd_commun_jeep_name($groupe_commun_jeep[1]);
		$prod->setProd_commun_dodge_name($groupe_commun_dodge[1]);
		$prod->setProd_commun_gmc_name($groupe_commun_gmc[1]);
		$prod->setProd_commun_chevr_name($groupe_commun_chevr[1]);
		
		if (isset($_SESSION['who'])){
		$prod->setWho($_SESSION['who']);
		} else {
		$prod->setWho("");
		}
		$date = explode("/", $_POST['date_livraison']);
		
		$prod->setDate_livraison($date[2]."-".$date[1]."-".$date[0]);
		$prod->setQte_livraison($_POST['qte_livraison']);
					
		$res = produitDB::ajoutProduit($prod);
		//ajout image
		$ds          = DIRECTORY_SEPARATOR;
		$storeFolder = '../../public'.$ds.'images'.$ds.'produits';
		$targetPath = dirname( __FILE__ ) . $ds. $storeFolder . $ds; 		
		if (file_exists($targetPath."ajoutref.jpg")) {
		rename($targetPath."ajoutref.jpg", $targetPath.$_POST['reference'].".jpg");
		rename($targetPath."mini/ajoutref.jpg", $targetPath."mini/".$_POST['reference'].".jpg");
		}
		
		if (isset($_POST['description_lg'])){
			$description_lg = addslashes($_POST['description_lg']);
			if ($description_lg != ""){
		$reference = $_POST['reference'];
		$result2 = $database->prepare("
		SELECT * FROM description WHERE ref_prod = '$reference'
		") or die ("requete r2 invalid");
		$result2->execute();
		
		$nb_result2 = $result2->rowCount();
				if ($nb_result2 == 0){
			
			// Ajout description longue
				
					$req1 = $database->query("
					INSERT INTO description (
					ref_prod,
					detail_desc,
					image_sup2,
					image_sup3,
					prod_assoc1,
					prod_assoc2,
					prod_assoc3
					) 
								
					VALUES (
					'".strtoupper($_POST['reference'])."',
					'".$description_lg."',
					'',
					'',
					'',
					'',
					''
					)") or die ("requete insert desc sql invalid");
				}	
			}
		}
	} else {
			$res = false;
	}
	
	if($res == true) {

	include("modif_produit.php");

	} else {
			//erreur de modification
			echo '<div class="error messageBox">Erreur lors de ajout du produit.</div>';
	}
}

?>
