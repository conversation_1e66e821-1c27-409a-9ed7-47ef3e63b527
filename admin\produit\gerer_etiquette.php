<h1><img src="<?php echo $basePath; ?>public/images/produit.png" alt=""/> <span>Gerer les produits</span></h1>



<table style="margin-left: 90px;">
<tr>
<td><a class="etiquette" href=""></a></td>
<td>
<form method="POST" action="<?php echo $basePath;?>index.php?page=admin&action=gererprod&etiquette=1" >
Etiquette par date de livraison (jj/mm/aaaa)
<input type="text" value="" id="date" name="date" />
<input type="submit" value="Ok" />
		<div id="calendarMain2" style="margin-left : 67px;"></div>
		<script type="text/javascript">
		//<![CDATA[
		calInit("calendarMain2", "Calendrier", "date", "jsCalendar", "day", "selectedDay");
		//]]>
		</script>


</form>
</td>
</tr>
<tr>
<td align="center"><a class="a_prod" href="">Etiquette (panier)</a></td>
</tr>
</table>

<?php 

if (isset($_POST['date'])){
	
	$user->setPanier(new panier());
	
	$date2 = explode("/",$_POST['date']);
	$date = $date2[2]."-".$date2[1]."-".$date2[0];	
	
	$r2 = $database->prepare("SELECT * FROM produits WHERE date_livraison = '$date'") or die ("requete r1 invalid");
	$r2->execute();
							
	while ($tab2 = $r2->fetch()) {
				
		$p = produitDB::getProduitById($tab2['idproduit']);
		$user->getPanier()->ajouterProduit($p, "1");
							
	}	
?>
<h1>Imprimer une étiquette : choisir le format !</h1>
<table style="margin-left: 250px;">
	<tr>
		<td><input type="button" style="height: 64px; width: 64px; margin-left: 10px; color: #000099; font-size: 14px; font-weight: bold;" name="who" value="Dymo" onclick="location.href='<?php echo $basePath?>admin/produit/p_imprim_etiquette_dymo_pdf.php'" /></td>
		<td><input type="button" style="height: 64px; width: 64px; margin-left: 10px; color: #FF6600; font-size: 14px; font-weight: bold;" name="who" value="A4" onclick="location.href='<?php echo $basePath?>admin/produit/p_imprim_etiquette_A4_pdf.php'" /></td>
		<td><input type="button" style="height: 64px; width: 64px; margin-left: 10px; color: #009900; font-size: 14px; font-weight: bold;" name="who" value="A6" onclick="location.href='<?php echo $basePath?>admin/produit/p_imprim_etiquette_A6_pdf.php'" /></td>
		<td><input type="button" style="height: 64px; width: 80px; margin-left: 10px; color: #9900FF; font-size: 14px; font-weight: bold;" name="who" value="POSTAL" onclick="location.href='<?php echo $basePath?>admin/produit/p_imprim_etiquette_POSTALE_pdf.php'" /></td>
	</tr>
</table>								
<?php
}

