<?php

/*

<form method="get" action="<?php echo $basePath; ?>index.php">
	<input type="hidden" name="page" value="admin">
	<input type="hidden" name="action" value="gererprod">
	<input type="hidden" name="non_dispo" value="1">
	<input type="hidden" name="mailnd" value="oui" />
	<input type="submit" value="Envoyer les mails de ND" />
</form>
*/


if(isset($_GET['mailnd']) && $_GET['mailnd'] == "oui") {
	
include("mail_non_dispo.php");

} else {

// Affiche JEEP

if(isset($_GET['tri'])){

$tri = $_GET['tri'];

} else {

$tri = "dispo DESC, genrename";

}

if(isset($_GET['genre']) && $_GET['genre'] != "AUTRE"){

$genre = $_GET['genre'].'%';
$z = "LIKE";

} elseif (isset($_GET['genre']) && $_GET['genre'] == "AUTRE"){

$genre = "RENAULT%' AND genrename NOT LIKE 'JEEP%' AND genrename NOT LIKE 'DODGE%' AND genrename NOT LIKE 'GMC%";
$z = "NOT LIKE ";

} else {

$genre = "JEEP%";
$z = "LIKE";

}

$result = $database->prepare("
SELECT *,SUM(qteproduit) AS qte, MIN(date_nd) AS date_nd_min FROM non_dispo WHERE genrename $z '$genre' GROUP BY reference ORDER BY $tri
") or die ("requete r1 invalid");
$result->execute();
$nb_nd = $result->rowCount();

echo '<h2 style="text-align:left; font-size: 24px; font-weight: bold; color: #0033FF;"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=1&genre='.$genre.'">'.$genre.' ('.$nb_nd.')</a></h2>';

echo '<table class="liste">';

echo '<th> </th>';
echo '<th><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=1&genre='.$genre.'&tri=reference">Reference</a></th>';
echo '<th> </th>';
echo '<th> </th>';
echo '<th><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=1&genre='.$genre.'&tri=idgroupe">Groupe</a></th>';
echo '<th> </th>';
echo '<th><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=1&genre='.$genre.'&tri=designation">Designation</a></th>';
echo '<th><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=1&genre='.$genre.'&tri=statut DESC">Statut</a></th>';
echo '<th><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=1&genre='.$genre.'&tri=date_nd_min">Date ND</a></th>';
echo '<th> Qté </th>';
echo '<th> Action</th>';

	while ($tab = $result->fetch()) {
		if($tab['dispo'] == "1"){
		echo '<tr>';
		echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&genre='.$genre.'&non_dispo=4&id='.$tab['reference'].'">ND</a></div></td>';
		} else {
		echo '<tr style="color: #FF6600; font-weight: bold;">';
		echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&genre='.$genre.'&non_dispo=5&id='.$tab['reference'].'" style="color: #FF6600;" >Dispo</a></div></td>';
		}
			echo "<td><div align='left'>".$tab['reference']."</div></td>";
			$genrename = explode("/", $tab['genrename']);
			$reference = $tab['reference'];
			
	//STOCK
	
		$result4 = $database->prepare("
		SELECT * FROM produits WHERE referenceproduit = '$reference'
		") or die ("requete r4 invalid");
		$result4->execute();
		$stock = $result4->fetch();

			echo "<td>";
			if ($stock['commande'] == "?" || $stock['promo'] == "ooo" || $stock['prixproduiteuro'] == "-1"){
			echo '<img src="'.$basePath.'public/images/stock_2.png" title="Introuvable" />';
			}
			if ($stock['promo'] == "oo"){
			echo '<img src="'.$basePath.'public/images/stock_2.png" title="Introuvable" />';
			}
			if (!empty($stock['commande']) && $stock['commande'] != "?" && $stock['promo'] != "ooo" && $stock['prixproduiteuro'] != "-1"){
			echo '<img src="'.$basePath.'public/images/stock_3.png" title="En cours de reappro" />';
			}
			
			if (empty($stock['commande'])){
				if ($stock['promo'] == "o" && $stock['prixproduiteuro'] != "-1"){
			echo '<img src="'.$basePath.'public/images/stock_4.png" title="Stock à vérifier" />';
				} else {
					if ($stock['prixproduiteuro'] != "-1"){
				echo '<img src="'.$basePath.'public/images/stock_5.png" title="Normalement en stock" />';
					} else {
					
					}
				}
			}
			echo "<td>";
			
			echo '<td><div align="left">'.$genrename[1].'</a></div></td>';
				if (file_exists("public/images/produits/".$tab['reference'].".jpg"))
					{
							// IMAGE
						echo "<td>";
						echo "<a href=\"".Zend_Registry::get('basePath')."public/images/produits/".$tab['reference'].".jpg\" rel=\"lytebox\" title=\"".$tab['reference']."\" >";
						echo "<img src=\"".Zend_Registry::get('basePath')."public/images/produits/".$tab['reference'].".jpg\" margin-top:3px;\" height=\"40\" width=\"60\" />";
						echo "</a>";
						echo "</td>";
			
					}else{
							echo '<td></td>';
					}	
			echo '<td><div align="left"><a target="_blank" href="'.$basePath.'index.php?page=admin&action=gererprod&type=modif&ref='.$tab['reference'].'">'.$tab['designation'].'</a></div></td>';
			if ($tab['statut'] != "attente"){
			echo "<td><div align='left'>".$tab['statut']."</div></td>";
			}else{
			echo "<td><div align='left'></div></td>";
			}
			$date_nd2 = explode("-", $tab['date_nd_min']);
			if($tab['dispo'] == "1"){
			echo "<td><div align='center'>".$date_nd2[2]."/".$date_nd2[1]."/".$date_nd2[0]."</div></td>";
			} else {
			$date_statut = explode("-", $tab['date_statut']);
				if ($tab['date_statut'] == ""){ 
				
					$date_statut = array(" "," "," ");
				
				} else {
				
					$date_statut = explode("-", $tab['date_statut']);
				
				}
				
			echo "<td><div align='center'>".$date_statut[2]."/".$date_statut[1]."/".$date_statut[0]."</div></td>";
			}
			echo "<td><div align='center'>".$tab['qte']."</div></td>";
			echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&genre='.$genre.'&non_dispo=3&id='.$tab['reference'].'"><img src="'.$basePath.'public/images/zoom.png" alt="Voir" title="Voir"/></a></div></td>';
			echo '</tr>';
	}
echo '</table>';
echo '<br />';

//compte le nb de nd par genre

$div_genre = array("JEEP", "DODGE", "GMC", "RENAULT", "AUTRE");

foreach ($div_genre as $genre_div) {

	if ($genre_div == "AUTRE"){

		$result2 = $database->prepare("
		SELECT * FROM non_dispo WHERE genrename NOT LIKE 'JEEP%' AND genrename NOT LIKE 'DODGE%' AND genrename NOT LIKE 'GMC%' AND genrename NOT LIKE 'RENAULT%' AND dispo != '2' GROUP BY reference") or die ("requete r1 invalid");
		$result2->execute();
		$nb_nd2 = $result2->rowCount();

		$result3 = $database->prepare("
		SELECT * FROM non_dispo WHERE genrename NOT LIKE 'JEEP%' and genrename NOT LIKE 'DODGE%' and genrename NOT LIKE 'GMC%' and genrename NOT LIKE 'RENAULT%' and dispo = '2' GROUP BY reference 
		") or die ("requete r1 invalid");
		$result3->execute();
		$nb_dispo = $result3->rowCount();

	} else {

		$result2 = $database->prepare("
		SELECT * FROM non_dispo WHERE genrename LIKE '$genre_div%' and dispo != '2' GROUP BY reference 
		") or die ("requete r1 invalid");
		$result2->execute();
		$nb_nd2 = $result2->rowCount();

		$result3 = $database->prepare("
		SELECT * FROM non_dispo WHERE genrename LIKE '$genre_div%' and dispo = '2' GROUP BY reference 
		") or die ("requete r1 invalid");
		$result3->execute();
		$nb_dispo = $result3->rowCount();

	}
echo '<h2 style="text-align:left; font-size: 24px; font-weight: bold; color: #0033FF;"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=1&genre='.$genre_div.'">'.$genre_div.' ND('.$nb_nd2.') <span style="color: #FF6600; font-weight: bold;" >Dispo('.$nb_dispo.')</span></a></h2>';
}
}
