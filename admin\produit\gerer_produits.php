<?php

//Class

require_once(dirname(__FILE__).'/../../class/produit_class.php');
require_once(dirname(__FILE__).'/../../class/categorie_class.php');
require_once(dirname(__FILE__).'/../../class/pagination_class.php');

//Suppression image

if (isset($_GET['deleteimg'])){

$ref_prod = $_GET['deleteimg'];

		$photo = dirname(__FILE__)."/../../public/images/produits/".$ref_prod.".jpg";
		$photo_mini = dirname(__FILE__)."/../../public/images/produits/mini/".$ref_prod.".jpg";
		if (!file_exists($photo))
			echo "La photo n'existe pas...<br /><br />";
		else
		{
			if (unlink($photo) && unlink($photo_mini)){

			}else{
				echo "Echec de la suppression de la photo.<br /><br />";
			}
		}

}


?>
<script type="text/javascript">

			Dropzone.options.dzprod = {
			  paramName: "file", // The name that will be used to transfer the file
			  maxFilesize: 0.5, // MB
			  acceptedFiles: ".jpg",
			};
			
			function addtocart(produit,id)
			{				
			qte = 1;
			var xmlhttp = null;
			if (produit=="")
			  {
			  document.getElementById("txtHint").innerHTML="";
			  return;
			  }
			if (window.XMLHttpRequest)
			  {// code for IE7+, Firefox, Chrome, Opera, Safari
			  xmlhttp=new XMLHttpRequest();
			  }
			else
			  {// code for IE6, IE5
			  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
			  }
			xmlhttp.onreadystatechange=function()
			  {
			  if (xmlhttp.readyState==4 && xmlhttp.status==200)
				{
				//document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
				//document.getElementById('articlepanier').innerHTML = "Article ajouté !";
				}
			  }
			xmlhttp.open("GET","<?php echo Zend_registry::get("basePath"); ?>panier/ajouter/"+produit+"/quantite/"+qte,true);
			xmlhttp.send();
			//document.getElementById('articlepanier').innerHTML = '<input type="text" id="nbarticlepanier" value="" size="1" readonly="true" style=" border:#FFFFFF none 0px; text-align:center; font-size:10px"><span style="color:cyan;">Article ajouté !</span>';
			document.getElementById('articlepanier').innerHTML = '<span style="color:cyan;">Article ajouté !</span>';
			document.getElementById('ajoutpanier'+produit).innerHTML = "<span style='color:red;'>Ajouté au panier !</span>";
			//document.getElementById('nbarticlepanier').value=Number(document.getElementById('nbarticlepanier').value)+1; 
			setTimeout(function() { document.getElementById('ajoutpanier'+produit).innerHTML = "Etiquette"; }, 5000);
			}
	
	
	function update_qte(id, val) {
		quantite = document.getElementById('p'+id).value;
		quantite = parseInt(quantite) + parseInt(val);
		if (quantite < 1) { quantite = 1; }
		if (quantite > 99) { quantite = 99; }
		document.getElementById('p'+id).value = quantite;
	}
</script>

	<script>
		function update_commande_frs(prixfour,ref,four)
		{
			var xmlhttp = null;
			if (prixfour=="")
			  {
			  document.getElementById("txtHint").innerHTML="";
			  return;
			  }
			if (window.XMLHttpRequest)
			  {// code for IE7+, Firefox, Chrome, Opera, Safari
			  xmlhttp=new XMLHttpRequest();
			  }
			else
			  {// code for IE6, IE5
			  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
			  }
			xmlhttp.onreadystatechange=function()
			  {
			  if (xmlhttp.readyState==4 && xmlhttp.status==200)
				{
				document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
				}
			  }
			xmlhttp.open("GET","<?php echo $basePath; ?>client/update_commande_frs.php?prixfour="+prixfour+"&ref="+ref+"&four="+four,true);
			xmlhttp.send();
		} 

	</script>
	
		<script>
		function update_achat_frs(achat,ref)
		{
			var xmlhttp = null;
			if (achat=="")
			  {
			  document.getElementById("txtHint").innerHTML="";
			  return;
			  }
			if (window.XMLHttpRequest)
			  {// code for IE7+, Firefox, Chrome, Opera, Safari
			  xmlhttp=new XMLHttpRequest();
			  }
			else
			  {// code for IE6, IE5
			  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
			  }
			xmlhttp.onreadystatechange=function()
			  {
			  if (xmlhttp.readyState==4 && xmlhttp.status==200)
				{
				document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
				}
			  }
			xmlhttp.open("GET","<?php echo $basePath; ?>client/update_achat_frs.php?achat="+achat+"&ref="+ref,true);
			xmlhttp.send();
		} 

	</script>
	
	<script>
		function update_cde_frs(cde,ref)
		{
			var xmlhttp = null;
			if (window.XMLHttpRequest)
			  {// code for IE7+, Firefox, Chrome, Opera, Safari
			  xmlhttp=new XMLHttpRequest();
			  }
			else
			  {// code for IE6, IE5
			  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
			  }
			xmlhttp.onreadystatechange=function()
			  {
			  if (xmlhttp.readyState==4 && xmlhttp.status==200)
				{
				document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
				}
			  }
			xmlhttp.open("GET","<?php echo $basePath; ?>client/update_cde_frs.php?cde="+cde+"&ref="+ref,true);
			xmlhttp.send();
		} 

	</script>
	
	<script>
		function update_prix_frs(prix,ref)
		{
			var xmlhttp = null;
			if (prix=="")
			  {
			  document.getElementById("txtHint").innerHTML="";
			  return;
			  }
			if (window.XMLHttpRequest)
			  {// code for IE7+, Firefox, Chrome, Opera, Safari
			  xmlhttp=new XMLHttpRequest();
			  }
			else
			  {// code for IE6, IE5
			  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
			  }
			xmlhttp.onreadystatechange=function()
			  {
			  if (xmlhttp.readyState==4 && xmlhttp.status==200)
				{
				document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
				}
			  }
			xmlhttp.open("GET","<?php echo $basePath; ?>client/update_prix_frs.php?prix="+prix+"&ref="+ref,true);
			xmlhttp.send();
		} 

	</script>
	<script>
		function update_qte_frs(qte,ref)
		{
			var xmlhttp = null;
			if (qte=="")
			  {
			  document.getElementById("txtHint").innerHTML="";
			  return;
			  }
			if (window.XMLHttpRequest)
			  {// code for IE7+, Firefox, Chrome, Opera, Safari
			  xmlhttp=new XMLHttpRequest();
			  }
			else
			  {// code for IE6, IE5
			  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
			  }
			xmlhttp.onreadystatechange=function()
			  {
			  if (xmlhttp.readyState==4 && xmlhttp.status==200)
				{
				document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
				}
			  }
			xmlhttp.open("GET","<?php echo $basePath; ?>client/update_qte_frs.php?qte="+qte+"&ref="+ref,true);
			xmlhttp.send();
		} 

	</script>
	<script>
		function update_date_frs(date,ref)
		{
			var xmlhttp = null;
			if (date=="")
			  {
			  document.getElementById("txtHint").innerHTML="";
			  return;
			  }
			if (window.XMLHttpRequest)
			  {// code for IE7+, Firefox, Chrome, Opera, Safari
			  xmlhttp=new XMLHttpRequest();
			  }
			else
			  {// code for IE6, IE5
			  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
			  }
			xmlhttp.onreadystatechange=function()
			  {
			  if (xmlhttp.readyState==4 && xmlhttp.status==200)
				{
				document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
				}
			  }
			xmlhttp.open("GET","<?php echo $basePath; ?>client/update_date_frs.php?date="+date+"&ref="+ref,true);
			xmlhttp.send();
		} 

	</script>

<?php
// AFFICHE LES PRODUITS EN COMMANDES
if (isset($_GET['commande_frs'])) {

?>
<h1><img src="<?php echo $basePath; ?>public/images/annonces.png" alt=""/> <span>Requêtes, procédures et modes opératoires</span></h1>

<a style="margin-left:85px; font-size:24px; color:#0080ff; text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.75);" href="<?php echo $basePath;?>admin/produit/rentree_de_stock.pdf" target="_blank" ><strong>1 - FAIRE UNE RENTRÉE DE STOCK </strong></a><br/>
<a style="margin-left:85px; font-size:24px; color:#0080ff; text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.75);" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&arrivage=1"><strong>2 - AFFICHER LES DERNIERS ARRIVAGES </strong></a><br/>

<h1><img src="<?php echo $basePath; ?>public/images/produit.png" alt=""/> <span>Gérer les produits</span></h1>

<table style="margin-left: 190px;">
<tr>
<td><a class="ajout_mini" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&ajout=1"></a></td>
<td><a class="nd_mini" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&non_dispo=1"></a></td>
<td><a class="devis_mini" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&sur_devis=1"></a></td>
<td><a class="recherche_mini" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&search=1"></a></td>
<td><a class="etiquette_mini" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&etiquette=1"></a></td>
</tr>
<tr>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&ajout=1">Ajout</a></td>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&non_dispo=1">ND</a></td>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&sur_devis=1">Devis</a></td>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&search=1">Recherche</a></td>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&etiquette=1">Etiquette</a></td>
</tr>
</table>

<?php

echo '<h1><img src="',$basePath,'public/images/find.png" /> <span>',$translate->_('Résultats de la recherche'),'</span></h1>';

?>
<center>
	<strong>Legende : </strong>
	<img src="<?php echo Zend_registry::get("basePath"); ?>public/images/stock_2.png" />
	Introuvable
	<img src="<?php echo Zend_registry::get("basePath"); ?>public/images/stock_3.png" />
	En cours de réappro
	<img src="<?php echo Zend_registry::get("basePath"); ?>public/images/stock_4.png" />
	Stock à vérifier
	<img src="<?php echo Zend_registry::get("basePath"); ?>public/images/stock_5.png" />
	Théoriquement en stock
	</center><br />
	<?php
	ini_set("memory_limit","300M");
	if(isset($_GET['numpage']))
		$numpage = $_GET['numpage'];
	else
		$numpage = 1;
		
	//création d'un objet pour paginer la page
	$pagination = new Pagination(0, 100, 20, $numpage);
			
	$prodlist = ProduitDB::readProduitsCommandeFrs();
	
	foreach($prodlist as $prod) {
		
// AFFICHE LA REQUETE
	
	?>
	
<link href="../../public/styles/style_vercorps_list.css" rel="stylesheet" type="text/css" />

<div id="vercorps_list" style="width: 895px; height: 175px;">

<?php

		$date_liv = explode("-",$prod->getDate_livraison());
		
		// PRODUIT EN COMMUN
				
		$prodcommunjeep = str_replace(' ','',$prod->getProd_commun_jeep());
		$genreNametr = $prod->getGenreName();
		
		
		if($prodcommunjeep != "-1" && $genreNametr != "JEEP"){
		$affichecommunjeep = " / JEEP";
		} else {
		$affichecommunjeep = "";
		}
		
		$prodcommundodge = str_replace(' ','',$prod->getProd_commun_dodge());
		if($prodcommundodge != "-1" && $genreNametr != "DODGE"){
		$affichecommundodge = " / DODGE";
		} else {
		$affichecommundodge = "";
		}
		
		$prodcommungmc = str_replace(' ','',$prod->getProd_commun_gmc());
		if($prodcommungmc != "-1" && $genreNametr != "GMC"){
		$affichecommungmc = " / GMC";
		} else {
		$affichecommungmc = "";
		}
		
		$prodcommunr2087 = str_replace(' ','',$prod->getProd_commun_r2087());
		if($prodcommunr2087 != "-1" && $genreNametr != "RENAULT"){
		$affichecommunr2087 = " / R2087";
		} else {
		$affichecommunr2087 = "";
		}

				//QUALITE
					
					$image_color = strtolower($prod->getQualite());
					
					if ($image_color == "new"){
					$style_border = "border: 1px solid #3399FF;";
					$qualite = "<strong style='color: #3399FF;'>NEUF</strong>";
					} elseif ($image_color == "nos"){
					$style_border = "border: 1px solid #FF6600;";
					$qualite = "<strong style='color: #FF6600;'>NOS</strong>";
					} elseif ($image_color == "rec"){
					$style_border = "border: 1px solid #00CC33;";
					$qualite = "<strong style='color: #00CC33;'>RENOVÉ</strong>";
					} elseif ($image_color == "use"){
					$style_border = "border: 1px solid #999900;";
					$qualite = "<strong style='color: #999900;'>OCCASION</strong>";
					} else {
					$style_border = "";
					$qualite = "";
					}
					
					if (file_exists("public/images/produits/".$prod->getReference().".jpg")){
							// IMAGE

						echo '<div id="left">';

						echo "<a href=\"".Zend_Registry::get('basePath')."public/images/produits/".$prod->getReference().".jpg\" rel=\"lytebox\" title=\"".$prod->getReference()."\" >";
						echo "<img src=\"".Zend_Registry::get('basePath')."public/images/produits/".$prod->getReference().".jpg\" border=\"1\" style='margin-top:3px;' width='160' height='130' /></a>";
						?>
						<a onclick="return(confirm('Etes-vous sur de vouloir supprimer cette photo?'));" href="index.php?page=admin&action=gererprod&valuetosearch=<?php echo $_GET['valuetosearch'];?>&in=ALL&method=AND&deleteimg=<?php echo $prod->getReference();?>" 
						style="
						  height: 32px;
						  width: 32px;
						  position: relative;
						  left: 130px;
						  top: -135px;
						  display: block;
						  border : none;
						" />
							<img style="border : none;"src="<?php echo $basePath; ?>public/images/cross2.png" alt=""/>
						</a>
						<?php

						echo '</div>';
						echo '<h3 style="margin-left: 180px; margin-top: 5px;">'.$prod->getDesignation().'</h3>';
						echo '<div id="mid">';
					} else {
					
						echo '<div id="left">';
						echo '<form id="dzprod" action="'.$basePath.'admin/produit/upload.php?ref='.$prod->getReference().'" class="dropzone"></form>';

						echo '</div>';
						echo '<h3 style="margin-left: 180px; margin-top: 5px;">'.$prod->getDesignation().'</h3>';
						echo '<div id="mid">';
						
					}
?>
		
		<p>R&eacute;f&eacute;rence : <?php echo $prod->getReference();?></p>
		<?php
		if($qualite == ""){

		} else {

		?>
				<p>Qualit&eacute; : <?php echo $qualite;?></p>
		<?php

		}
		
		?>
		<p>Genre: <strong><?php echo $genreNametr.$affichecommunjeep.$affichecommundodge.$affichecommungmc.$affichecommunr2087;?></strong></p>
		<p>Groupe: <strong><?php echo $prod->getGroupeName();?></strong></p>
		<p>Stock Théo: <strong>
	<?php 
	
		// ------ Calcul stock théorique -------
		$refprod = $prod->getReference();
		$date_livraison = $prod->getDate_livraison();
		$result15 = $database->prepare("SELECT SUM(qteproduit) AS qtevendu FROM ligne_facture INNER JOIN facture ON ligne_facture.id_facture = facture.id_facture WHERE reference = '$refprod ' AND date_facture >= '$date_livraison'") or die ("R15 invalid");
		$result15->execute();
		$qtevendu = $result15->fetch();

		$qteliv = $prod->getQte_livraison();
		$stocktheo = $qteliv - $qtevendu['qtevendu'];

		echo $stocktheo;
	
	?>
	</div>
	<div id="mid">
	<table>
		<tr>
			<td>JEEPEST</td>
			<td>GSAA</td>
			<td>DESMET</td>
		</tr>	
		<tr>
			<td><input style="width:50px;" type="text" name="jeepest" onchange="update_commande_frs(this.value,'<?php echo $prod->getReference();?>','jeepest')" value="<?php echo $prod->getJeepest();?>"><span id="txtHint"></span></td>
			<td><input style="width:50px;" type="text" name="gsaa" onchange="update_commande_frs(this.value,'<?php echo $prod->getReference();?>','gsaa')" value="<?php echo $prod->getGsaa();?>"></td>
			<td><input style="width:50px;" type="text" name="desmet" onchange="update_commande_frs(this.value,'<?php echo $prod->getReference();?>','desmet')" value="<?php echo $prod->getDesmet();?>"></td>
		</tr>	
	</table>
	<table>
		<tr>
			<td>Achat</td>
			<td>Cde</td>
			<td>Prix HT</td>
		</tr>	
		<tr>
			<td><input style="width:50px;" type="text" name="achat" onchange="update_achat_frs(this.value,'<?php echo $prod->getReference();?>')" value="<?php echo $prod->getPrix_frs();?>"></span></td>
			<td><input style="width:50px;" type="text" name="cde" onchange="update_cde_frs(this.value,'<?php echo $prod->getReference();?>')" value="<?php echo $prod->getCommande();?>"></td>
			<td><input style="width:50px;" type="text" name="prix" onchange="update_prix_frs(this.value,'<?php echo $prod->getReference();?>')" value="<?php echo $prod->getPrixHT();?>"></td>
		</tr>	
	</table>
	<table>
		<tr>
			<td>Qté liv</td>
			<td>Date liv</td>
			<td></td>
		</tr>	
		<tr>
			<td><input style="width:50px;" type="text" name="qte" onchange="update_qte_frs(this.value,'<?php echo $prod->getReference();?>')" value="<?php echo $prod->getQte_livraison();?>"></span></td>
			<td><input style="width:100px; text-align:center;" type="text" name="date" onchange="update_date_frs(this.value,'<?php echo $prod->getReference();?>')" value="<?php echo $date_liv[2]."/".$date_liv[1]."/".$date_liv[0]; ?>"></td>
			<td></td>
		</tr>	
	</table>
	</div>
	<div id="right">
		<div class="prix" style="margin-top: 0px;" ><?php 
		
		if ($prod->getPrixHT() > 0){
		
		echo number_format(($prod->getPrixHT()),2, '.', '');
		echo ' &euro; <span style="font-size: 8px; margin:0px;">HT</span>';
		
		} else {
		
		echo "Sur devis";
		
		}
		
		?></div>
		<div class="description" style="margin-top: 0px;" ><a href="<?php echo Zend_Registry::get('basePath'); ?>index.php?page=admin&action=gererprod&type=modif&id=<?php echo $prod->getIdentifiant(); ?>&cat=<?php echo $prod->getGenreName(); ?>&sscat=<?php echo $prod->getGroupeName(); ?>&valuetosearch=<?php echo $_GET['valuetosearch']; ?>" ><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/modif.png"  width="16" height="16" /> Modifier</a></div>
		<div class="description"><a onclick="return confirm('Voulez-vous vraiment supprimer ce produit ?');" href="<?php echo Zend_Registry::get('basePath'); ?>index.php?page=admin&action=gererprod&type=delete&valuetosearch=<?php echo $_GET['valuetosearch']; ?>&id=<?php echo $prod->getIdentifiant(); ?>&cat=<?php echo $prod->getGenreName(); ?>&sscat=<?php echo $prod->getGroupeName(); ?>" ><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/cross.png"  width="16" height="16" /> Supprimer</a></div>
	</div>
</div>

	<?php
	
	}	
} 
// Affiche les derniers produits ajoutés (2mois)
elseif (isset($_GET['arrivage'])) {

?>
<h1><img src="<?php echo $basePath; ?>public/images/annonces.png" alt=""/> <span>Requêtes, procédures et modes opératoires</span></h1>

<a style="margin-left:85px; font-size:24px; color:#0080ff; text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.75);" href="<?php echo $basePath;?>admin/produit/rentree_de_stock.pdf" target="_blank" ><strong>1 - FAIRE UNE RENTRÉE DE STOCK </strong></a><br/>
<a style="margin-left:85px; font-size:24px; color:#0080ff; text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.75);" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&arrivage=1"><strong>2 - AFFICHER LES DERNIERS ARRIVAGES </strong></a>

<h1><img src="<?php echo $basePath; ?>public/images/produit.png" alt=""/> <span>Gérer les produits</span></h1>

<table style="margin-left: 190px;">
<tr>
<td><a class="ajout_mini" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&ajout=1"></a></td>
<td><a class="nd_mini" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&non_dispo=1"></a></td>
<td><a class="devis_mini" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&sur_devis=1"></a></td>
<td><a class="recherche_mini" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&search=1"></a></td>
<td><a class="etiquette_mini" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&etiquette=1"></a></td>
</tr>
<tr>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&ajout=1">Ajout</a></td>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&non_dispo=1">ND</a></td>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&sur_devis=1">Devis</a></td>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&search=1">Recherche</a></td>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&etiquette=1">Etiquette</a></td>
</tr>
</table>

<?php

echo '<h1><img src="',$basePath,'public/images/find.png" /> <span>',$translate->_('Résultats de la recherche'),'</span></h1>';

?>
<center>
	<strong>Legende : </strong>
	<img src="<?php echo Zend_registry::get("basePath"); ?>public/images/stock_2.png" />
	Introuvable
	<img src="<?php echo Zend_registry::get("basePath"); ?>public/images/stock_3.png" />
	En cours de réappro
	<img src="<?php echo Zend_registry::get("basePath"); ?>public/images/stock_4.png" />
	Stock à vérifier
	<img src="<?php echo Zend_registry::get("basePath"); ?>public/images/stock_5.png" />
	Théoriquement en stock
	</center><br />
	<?php
	ini_set("memory_limit","300M");
	if(isset($_GET['numpage']))
		$numpage = $_GET['numpage'];
	else
		$numpage = 1;
		
	//création d'un objet pour paginer la page
	$pagination = new Pagination(0, 100, 20, $numpage);
			
	$prodlist = ProduitDB::readProduitsArrivage();
	
	foreach($prodlist as $prod) {
		
// AFFICHE LA REQUETE
	
	?>
	
<link href="../../public/styles/style_vercorps_list.css" rel="stylesheet" type="text/css" />

<div id="vercorps_list" style="width: 895px;">

<?php
		// PRODUIT EN COMMUN

		$prodcommunjeep = str_replace(' ','',$prod->getProd_commun_jeep());
		$genreNametr = $prod->getGenreName();
		
		
		if($prodcommunjeep != "-1" && $genreNametr != "JEEP"){
		$affichecommunjeep = " / JEEP";
		} else {
		$affichecommunjeep = "";
		}
		
		$prodcommundodge = str_replace(' ','',$prod->getProd_commun_dodge());
		if($prodcommundodge != "-1" && $genreNametr != "DODGE"){
		$affichecommundodge = " / DODGE";
		} else {
		$affichecommundodge = "";
		}
		
		$prodcommungmc = str_replace(' ','',$prod->getProd_commun_gmc());
		if($prodcommungmc != "-1" && $genreNametr != "GMC"){
		$affichecommungmc = " / GMC";
		} else {
		$affichecommungmc = "";
		}
		
		$prodcommunr2087 = str_replace(' ','',$prod->getProd_commun_r2087());
		if($prodcommunr2087 != "-1" && $genreNametr != "RENAULT"){
		$affichecommunr2087 = " / R2087";
		} else {
		$affichecommunr2087 = "";
		}

				//QUALITE
					
					$image_color = strtolower($prod->getQualite());
					
					if ($image_color == "new"){
					$style_border = "border: 1px solid #3399FF;";
					$qualite = "<strong style='color: #3399FF;'>NEUF</strong>";
					} elseif ($image_color == "nos"){
					$style_border = "border: 1px solid #FF6600;";
					$qualite = "<strong style='color: #FF6600;'>NOS</strong>";
					} elseif ($image_color == "rec"){
					$style_border = "border: 1px solid #00CC33;";
					$qualite = "<strong style='color: #00CC33;'>RENOVÉ</strong>";
					} elseif ($image_color == "use"){
					$style_border = "border: 1px solid #999900;";
					$qualite = "<strong style='color: #999900;'>OCCASION</strong>";
					} else {
					$style_border = "";
					$qualite = "";
					}
					
					if (file_exists("public/images/produits/".$prod->getReference().".jpg")){
							// IMAGE

						echo '<div id="left">';

						echo "<a href=\"".Zend_Registry::get('basePath')."public/images/produits/".$prod->getReference().".jpg\" rel=\"lytebox\" title=\"".$prod->getReference()."\" >";
						echo "<img src=\"".Zend_Registry::get('basePath')."public/images/produits/".$prod->getReference().".jpg\" border=\"1\" style='margin-top:3px;' width='160' height='130' /></a>";
						?>
						<a onclick="return(confirm('Etes-vous sur de vouloir supprimer cette photo?'));" href="index.php?page=admin&action=gererprod&valuetosearch=<?php 
						if($_GET['valuetosearch'] != ""){
						echo $_GET['valuetosearch'];
						} else {
						echo $prod->getReference();
						}
						?>
						&in=ALL&method=AND&deleteimg=<?php echo $prod->getReference();?>" 
						style="
						  height: 32px;
						  width: 32px;
						  position: relative;
						  left: 130px;
						  top: -135px;
						  display: block;
						  border : none;
						" />
							<img style="border : none;"src="<?php echo $basePath; ?>public/images/cross2.png" alt=""/>
						</a>
						<?php

						echo '</div>';
						echo '<h3 style="margin-left: 180px; margin-top: 5px;">'.$prod->getDesignation().'</h3>';
						echo '<div id="mid">';
					} else {
					
						echo '<div id="left">';
						echo '<form id="dzprod" action="'.$basePath.'admin/produit/upload.php?ref='.$prod->getReference().'" class="dropzone"></form>';

						echo '</div>';
						echo '<h3 style="margin-left: 180px; margin-top: 5px;">'.$prod->getDesignation().'</h3>';
						echo '<div id="mid">';
						
					}
?>
		
		<p>R&eacute;f&eacute;rence : <?php echo $prod->getReference();?></p>
		<?php
		if($qualite == ""){

		} else {

		?>
				<p>Qualit&eacute; : <?php echo $qualite;?></p>
		<?php

		}
		
		?>
		<p>Genre: <strong><?php echo $genreNametr.$affichecommunjeep.$affichecommundodge.$affichecommungmc.$affichecommunr2087;?></strong></p>
		<p>Groupe: <strong><?php echo $prod->getGroupeName();?></strong></p>
		<p>Livré le: <strong><?php echo $prod->getDate_livraison();?></strong></p>
	</div>
	<div id="mid">
	
	<p>Type: <strong><?php echo $prod->getType();?></strong></p>
	
	<p>Stock:
	<?php
	$stock = $prod->getCommande();
	
	if ($stock == "?" || $prod->getPromotion() == "ooo" || $prod->getPrixHT() == "-1"){
	echo '<img src="'.$basePath.'public/images/stock_2.png" title="Produit Introuvable" alt="Introuvable" />';
	}
	if ($prod->getPromotion() == "oo"){
	echo '<img src="'.$basePath.'public/images/stock_2.png" title="Produit Introuvable" alt="Introuvable" />';
	}
	if (!empty($stock) && $stock != "?" && $prod->getPromotion() != "ooo" && $prod->getPrixHT() != "-1"){
	echo '<img src="'.$basePath.'public/images/stock_3.png" title="En cours de reapprovisonnement" alt="En cours de reapprovisonnement" />';
	}
	
	if (empty($stock)){
		if ($prod->getPromotion() == "o"){
	echo '<img src="'.$basePath.'public/images/stock_4.png" title="Stock à verifier" alt="Stock à verifier"/>';
		} else {
		if ($prod->getPrixHT() != "-1"){
	echo '<img src="'.$basePath.'public/images/stock_5.png" title="Produit théoriquement en stock" alt="Produit théoriquement en stock"/>';
		}
		}
	}
	
	
	?>
	</p>
	<p>Comment: <strong><?php echo $prod->getEmpl_comment();?></strong></p>
	
	</div>
	<div id="right">
		<div class="prix" style="margin-top: 0px;" ><?php 
		
		if ($prod->getPrixHT() > 0){
		
		echo number_format(($prod->getPrixHT()),2, '.', '');
		echo ' &euro; <span style="font-size: 8px; margin:0px;">HT</span>';
		
		} else {
		
		echo "Sur devis";
		
		}
		
		?></div>
		<div class="description" style="margin-top: 0px;" ><a href="<?php echo Zend_Registry::get('basePath'); ?>index.php?page=admin&action=gererprod&type=modif&id=<?php echo $prod->getIdentifiant(); ?>&cat=<?php echo $prod->getGenreName(); ?>&sscat=<?php echo $prod->getGroupeName(); ?>&valuetosearch=<?php echo $_GET['valuetosearch']; ?>" ><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/modif.png"  width="16" height="16" /> Modifier</a></div>
		<div class="description"><a onclick="return confirm('Voulez-vous vraiment supprimer ce produit ?');" href="<?php echo Zend_Registry::get('basePath'); ?>index.php?page=admin&action=gererprod&type=delete&valuetosearch=<?php echo $_GET['valuetosearch']; ?>&id=<?php echo $prod->getIdentifiant(); ?>&cat=<?php echo $prod->getGenreName(); ?>&sscat=<?php echo $prod->getGroupeName(); ?>" ><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/cross.png"  width="16" height="16" /> Supprimer</a></div>
</div>
</div>

	<?php
	
	}	
} else {

if (isset($_GET['valuetosearch']) && $_GET['valuetosearch'] != "") {
	$referenceproduit = $_GET['valuetosearch'];
	$result8 = $database->prepare("
		SELECT * FROM produits WHERE referenceproduit = '$referenceproduit'
		") or die ("requete r2 invalid");
	$result8->execute();
		
	$nb_result8 = $result8->rowCount();
	
	if ($nb_result8 == 1){
		include ("admin/produit/modif_produit.php");
	} else {
		

?>

<table style="margin-left: 190px;">
<tr>
<td><a class="ajout_mini" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&ajout=1"></a></td>
<td><a class="nd_mini" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&non_dispo=1"></a></td>
<td><a class="devis_mini" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&sur_devis=1"></a></td>
<td><a class="recherche_mini" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&search=1"></a></td>
<td><a class="etiquette_mini" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&etiquette=1"></a></td>
</tr>
<tr>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&ajout=1">Ajout</a></td>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&non_dispo=1">ND</a></td>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&sur_devis=1">Devis</a></td>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&search=1">Recherche</a></td>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&etiquette=1">Etiquette</a></td>
</tr>
</table>

<?php

/**
 * Programme de recherche d'une expression dans la liste de tous les produits
 */
echo '<h1><img src="',$basePath,'public/images/find.png" /> <span>',$translate->_('Résultats de la recherche'),'</span></h1>';

?>
	<center>
	<form method="GET" action="<?php echo $basePath; ?>index.php">
				<input type=hidden name="page" value="admin">
				<input type=hidden name="action" value="gererprod">
		Rechercher : <input type="text" name="valuetosearch" value="<?php echo $_GET['valuetosearch']; ?>" />
		dans : 	<select name="in">
					<option value="DESC" <?php echo ($_GET['in']=="DESC")?'selected=true':''; ?>>les désignations des produits</option>
					<option value="REF" <?php echo ($_GET['in']=="REF")?'selected=true':''; ?>>les références des produits</option>
					<option value="GENRE" <?php echo ($_GET['in']=="GENRE")?'selected=true':''; ?>>les noms des genres</option>
					<option value="GRP" <?php echo ($_GET['in']=="GRP")?'selected=true':''; ?>>les noms des groupes</option>
					<option value="GENREGRP" <?php echo ($_GET['in']=="GENREGRP")?'selected=true':''; ?>>les noms des genres et des groupes</option>
					<option value="ALL" <?php echo ($_GET['in']=="ALL")?'selected=true':''; ?>>tout</option>
				</select>
		en utilisant : 	<select name="method">
						<option value="AND">tous les mots</option>
						<option value="OR">au moins un des mots</option>
					</select>
	<br /><br />
	<input type="submit" value="Rechercher !" />
	</form>
	</center><br /><center>
	<strong>Legende : </strong>
	<img src="<?php echo Zend_registry::get("basePath"); ?>public/images/stock_2.png" />
	Introuvable
	<img src="<?php echo Zend_registry::get("basePath"); ?>public/images/stock_3.png" />
	En cours de réappro
	<img src="<?php echo Zend_registry::get("basePath"); ?>public/images/stock_4.png" />
	Stock à vérifier
	<img src="<?php echo Zend_registry::get("basePath"); ?>public/images/stock_5.png" />
	Théoriquement en stock
	</center><br />
	<?php
	ini_set("memory_limit","300M");
	if(isset($_GET['numpage']))
		$numpage = $_GET['numpage'];
	else
		$numpage = 1;
		
	//création d'un objet pour paginer la page
	$pagination = new Pagination(0, 100, 20, $numpage);
			
	$search = $_GET['valuetosearch'];
	$prodlist = ProduitDB::readProduitsFromDataBase2();
	
	if(isset($_GET['in'])){
		$in=null;
		if($_GET['in'] == "DESC")
			$in = "les désignations des produits";
		elseif($_GET['in'] == "REF")
			$in = "les références des produits";
		elseif($_GET['in'] == "GENRE")
			$in = "les noms des genres"; 
		elseif($_GET['in'] == "GRP")
			$in = "les noms des groupes";
		elseif($_GET['in'] == "GENREGRP")
			$in = "les noms des genres et des groupes";
		elseif($_GET['in'] == "ALL")
			$in = "tout";
	}
	if(isset($_GET['method'])){
		$method=null;
		if($_GET['method'] == "AND")
			$method = "tous les mots";
		elseif($_GET['method'] == "OR")
			$method = "au moins un des mots";
	}

	//découpe tous les mots à recherchés
	$search_array = explode(' ',$search);
	//construction de l'expression régulière
	$regexp = '#'.$search_array[0];
	
	//^.*(mot1|mot2).*(mot1|mot2).*$
	$i=1;
	for($i=1;$i<count($search_array);$i++) {

		if ((strtolower($search_array[$i])) == "neuf"){
		
		$search_array[$i] = "new";
		
		}
		
		if ((strtolower($search_array[$i])) == "occasion"){
		
		$search_array[$i] = "use";
		
		}
		
		if ((strtolower($search_array[$i])) == "renove" ||(strtolower($search_array[$i])) == "renovee"){
		
		$search_array[$i] = "rec";
		
		}

		$regexp.='|'.$search_array[$i];
	}
	$regexp.='#i';
	
	$nbres=0;
	

	foreach($prodlist as $prod) {
	
	// PRODUIT EN COMMUN

		$prodcommunjeep = str_replace(' ','',$prod->getProd_commun_jeep());
		$genreNametr = $prod->getGenreName();
		
		
		if($prodcommunjeep != "-1" && $genreNametr != "JEEP"){
		$affichecommunjeep = "JEEP";
		} else {
		$affichecommunjeep = "";
		}
		
		$prodcommundodge = str_replace(' ','',$prod->getProd_commun_dodge());
		if($prodcommundodge != "-1" && $genreNametr != "DODGE"){
		$affichecommundodge = "DODGE";
		} else {
		$affichecommundodge = "";
		}
		
		$prodcommungmc = str_replace(' ','',$prod->getProd_commun_gmc());
		if($prodcommungmc != "-1" && $genreNametr != "GMC"){
		$affichecommungmc = "GMC";
		} else {
		$affichecommungmc = "";
		}
		
		$prodcommunr2087 = str_replace(' ','',$prod->getProd_commun_r2087());
		if($prodcommunr2087 != "-1" && $genreNametr != "RENAULT"){
		$affichecommunr2087 = "R2087";
		} else {
		$affichecommunr2087 = "";
		}
		
		$prodcommunchevr = str_replace(' ','',$prod->getProd_commun_chevr());
		if($prodcommunchevr != "-1" && $genreNametr != "CHEVROLET"){
		$affichecommunchevr = "CHEVROLET";
		} else {
		$affichecommunchevr = "";
		}
		
		//concatène le groupe le genre et le nom, pour ne faire qu'une chaine de caractère
		//plus facile pour rechercher
		//on regarde les options, pour savoir dans quoi on doit rechercher, si rien de définit par défaut on chercher dans tout
		if(isset($_GET['in'])) {
			if($_GET['in'] == "DESC") // on recherche dans les désignations
				$idenfifiant_prod = $prod->getDesignation();
			elseif($_GET['in'] == "REF") // on recherche dans les groupes
				$idenfifiant_prod = $prod->getReference();
			elseif($_GET['in'] == "GRP") // on recherche dans les groupes
				$idenfifiant_prod = $prod->getGroupeName();
			elseif($_GET['in'] == "GENRE") // on recherche dans les genres
				$idenfifiant_prod = $prod->getGenreName();
			elseif($_GET['in'] == "GENREGRP") // on recherche dans les genres et les groupes
				$idenfifiant_prod = $prod->getGenreName().' '.$prod->getGroupeName();
			elseif($_GET['in'] == "ALL") // on recherche dans les genres et les groupes				
				$idenfifiant_prod = $prod->getReference().' '.$prod->getGenreName().' '.$prod->getGroupeId().' '.$prod->getGroupeName().' '.$prod->getDesignation().' '.$prod->getTaille().' '.$prod->getType().' '.$prod->getQualite().' '.$prod->getProd_commun_jeep_name().' '.$prod->getProd_commun_dodge_name().' '.$prod->getProd_commun_gmc_name().' '.$prod->getProd_commun_r2087_name().' '.$affichecommunjeep.' '.$affichecommundodge.' '.$affichecommungmc.' '.$affichecommunr2087.' '.$affichecommunchevr;
		} else {
			$idenfifiant_prod = $prod->getReference().' '.$prod->getGenreName().' '.$prod->getGroupeName().' '.$prod->getDesignation().' '.$prod->getTaille().' '.$prod->getType().' '.$prod->getQualite();
		}
		$trouve = false;
		$i=0;
		
		//on regarde la méthode de recherche, par défaut, tous les mots donc : AND
		if(isset($_GET['method'])) {
			$method = $_GET['method'];
		} else {
			$method = "AND";
		}
		
		if($method == "AND") {
			//on recherche tous les mots, dc on s'arrete dès qu'on a pas trouvé un mot
			while($i<count($search_array) && ($trouve=preg_match('#'.$search_array[$i].'#i', $idenfifiant_prod)))
				$i++;
		} elseif($method == "OR" ){
			//on recherche au moins un des mots, dc on s'arrete dès qu'on à trouvé un mot
			while($i<count($search_array) && !preg_match('#'.$search_array[$i].'#i', $idenfifiant_prod))
				$i++;
		}
		//on est à la fin, on a donc trouvé tous les mots recherché !
		//ou alors on est pas à la fin
		if(($i==count($search_array) && $method=="AND") || ($i<count($search_array) && $method=="OR"))
		{
		
// AFFICHE LA NOUVELLE RECHERCHE MODIF PRODUIT
	
	?>
	
<link href="../../public/styles/style_vercorps_list.css" rel="stylesheet" type="text/css" />

<?php

 if ($prod->getPromotion() == "LOT"){

?>

<div id="vercorps_list" style="width: 895px; background-color: yellow; background-image:none;">

<?php

} else {

?>

<div id="vercorps_list" style="width: 895px;">

<?php

}


				//QUALITE
					
					$image_color = strtolower($prod->getQualite());
					
					if ($image_color == "new"){
					$style_border = "border: 1px solid #3399FF;";
					$qualite = "<strong style='color: #3399FF;'>NEUF</strong>";
					} elseif ($image_color == "nos"){
					$style_border = "border: 1px solid #FF6600;";
					$qualite = "<strong style='color: #FF6600;'>NOS</strong>";
					} elseif ($image_color == "rec"){
					$style_border = "border: 1px solid #00CC33;";
					$qualite = "<strong style='color: #00CC33;'>RENOVÉ</strong>";
					} elseif ($image_color == "use"){
					$style_border = "border: 1px solid #999900;";
					$qualite = "<strong style='color: #999900;'>OCCASION</strong>";
					} else {
					$style_border = "";
					$qualite = "";
					}
					
						if(strpos($prod->getReference(), "/") > 0) {
							$arrayReference = explode("/", $prod->getReference());
							$referenceimg = $arrayReference[0];
						}
						else {
							$referenceimg = $prod->getReference();
						}
						
					if (file_exists("public/images/produits/".$referenceimg.".jpg")){
							// IMAGE

						echo '<div id="left">';

						echo "<a href=\"".Zend_Registry::get('basePath')."public/images/produits/".$referenceimg.".jpg\" rel=\"lytebox\" title=\"".$prod->getReference()."\" >";
						echo "<img src=\"".Zend_Registry::get('basePath')."public/images/produits/".$referenceimg.".jpg\" border=\"1\" style='margin-top:3px;' width='160' height='130' /></a>";
						?>
						<a onclick="return(confirm('Etes-vous sur de vouloir supprimer cette photo?'));" href="index.php?page=admin&action=gererprod&valuetosearch=<?php echo $prod->getReference(); ?>&in=ALL&method=AND&deleteimg=<?php echo $prod->getReference(); ?>" 
						style="height: 32px;width: 32px; position: relative;left: 140px;top: -150px;display: block; border : none;" />
						<img style="border:none;"src="<?php echo $basePath; ?>public/images/cross2.png" alt=""/>
						</a>
						<?php
						echo '</div>';
						echo '<h3 style="margin-left: 180px; margin-top: 5px;">'.$prod->getDesignation().'</h3>';
						echo '<div id="mid">';
					} else {
					
						echo '<div id="left">';
						echo '<form id="dzprod" action="'.$basePath.'admin/produit/upload.php?ref='.$prod->getReference().'" class="dropzone"></form>';

						echo '</div>';
						echo '<h3 style="margin-left: 180px; margin-top: 5px;">'.$prod->getDesignation().'</h3>';
						echo '<div id="mid">';
						
					}
?>
		
		<p>R&eacute;f&eacute;rence : <?php echo $prod->getReference();?></p>
		<?php
		if($qualite == ""){

		} else {

		?>
				<p>Qualit&eacute; : <?php echo $qualite;?></p>
		<?php

		}
		
		?>
		<p>Genre: <strong><?php echo $genreNametr." ".$affichecommunjeep." ".$affichecommundodge." ".$affichecommungmc." ".$affichecommunr2087;?></strong></p>
		<p>Groupe: <strong><?php echo $prod->getGroupeName();?></strong></p>
	</div>
	<div id="mid" style="width: 225px;">
	
	<p>Type: <strong><?php echo $prod->getType();?></strong></p>
	
	<p>Stock:
	<?php
	$stock = $prod->getCommande();
	
	if ($stock == "?" || $prod->getPromotion() == "ooo" || $prod->getPrixHT() == "-1"){
	echo '<img src="'.$basePath.'public/images/stock_2.png" title="Produit Introuvable" alt="Introuvable" />';
	}
	if ($prod->getPromotion() == "oo"){
	echo '<img src="'.$basePath.'public/images/stock_2.png" title="Produit Introuvable" alt="Introuvable" />';
	}
	if (!empty($stock) && $stock != "?" && $prod->getPromotion() != "ooo" && $prod->getPrixHT() != "-1"){
	echo '<img src="'.$basePath.'public/images/stock_3.png" title="En cours de reapprovisonnement" alt="En cours de reapprovisonnement" />';
	}
	
	if (empty($stock)){
		if ($prod->getPromotion() == "o"){
	echo '<img src="'.$basePath.'public/images/stock_4.png" title="Stock à verifier" alt="Stock à verifier"/>';
		} else {
		if ($prod->getPrixHT() != "-1"){
	echo '<img src="'.$basePath.'public/images/stock_5.png" title="Produit théoriquement en stock" alt="Produit théoriquement en stock"/>';
		}
		}
	}
	
	
	?>
	</p>
	<p>Stock Théo: <strong>
	<?php 
	
		// ------ Calcul stock théorique -------
		$refprod = $prod->getReference();
		$date_livraison = $prod->getDate_livraison();
		$result15 = $database->prepare("SELECT SUM(qteproduit) AS qtevendu FROM ligne_facture INNER JOIN facture ON ligne_facture.id_facture = facture.id_facture WHERE reference = '$refprod ' AND date_facture >= '$date_livraison'") or die ("R15 invalid");
		$result15->execute();
		$qtevendu = $result15->fetch();

		$qteliv = $prod->getQte_livraison();
		$stocktheo = $qteliv - $qtevendu['qtevendu'];

		echo $stocktheo;
	
	?>
	
	</strong></p>
	<p>Comment: <strong><?php echo $prod->getEmpl_comment();?></strong></p>
	
	</div>
	<div id="right">
		<div class="prix" style="margin-top: 0px;" ><?php 
		
		if ($prod->getPrixHT() > 0){
		
		echo number_format(($prod->getPrixHT()),2, '.', '');
		echo ' &euro; <span style="font-size: 8px; margin:0px;">HT</span>';
		
		} else {
		
		echo "Sur devis";
		
		}
		
		?></div>
<?php	

/*	
		<table>
			<tr>
				<td><div class="description" style="margin-top: 0px;" ><a style="margin-top:5px;" href="<?php echo Zend_Registry::get('basePath'); ?>index.php?page=admin&action=gererprod&type=modif&id=<?php echo $prod->getIdentifiant(); ?>&cat=<?php echo $prod->getGenreName(); ?>&sscat=<?php echo $prod->getGroupeName(); ?>&valuetosearch=<?php echo $_GET['valuetosearch']; ?>" ><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/modif.png"  width="16" height="16" /> Modifier</a></div></td>
				<td><div class="description"><a style="margin-top:5px;" onclick="return confirm('Voulez-vous vraiment supprimer ce produit ?');" href="<?php echo Zend_Registry::get('basePath'); ?>index.php?page=admin&action=gererprod&type=delete&valuetosearch=<?php echo $_GET['valuetosearch']; ?>&id=<?php echo $prod->getIdentifiant(); ?>&cat=<?php echo $prod->getGenreName(); ?>&sscat=<?php echo $prod->getGroupeName(); ?>" ><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/cross.png"  width="16" height="16" /> Supprimer</a></div></td>
			</tr>
			<tr>
				<td><div class="description" style="margin-top: 0px;" ><a style="margin-top:5px;" href="<?php echo Zend_Registry::get('basePath'); ?>index.php?page=admin&action=gererprod&type=modif&id=<?php echo $prod->getIdentifiant(); ?>&cat=<?php echo $prod->getGenreName(); ?>&sscat=<?php echo $prod->getGroupeName(); ?>&valuetosearch=<?php echo $_GET['valuetosearch']; ?>" ><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/modif.png"  width="16" height="16" /> Modifier</a></div></td>
				<td><div class="description"><a style="margin-top:5px;" onclick="return confirm('Voulez-vous vraiment supprimer ce produit ?');" href="<?php echo Zend_Registry::get('basePath'); ?>index.php?page=admin&action=gererprod&type=delete&valuetosearch=<?php echo $_GET['valuetosearch']; ?>&id=<?php echo $prod->getIdentifiant(); ?>&cat=<?php echo $prod->getGenreName(); ?>&sscat=<?php echo $prod->getGroupeName(); ?>" ><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/cross.png"  width="16" height="16" /> Supprimer</a></div></td>
			</tr>
			<tr>
				<td><div class="description">
				<?php								
				echo '<a style="margin-top:5px;" id="ajoutpanier'.$prod->getIdentifiant().'" onclick="javascript:addtocart(',$prod->getIdentifiant(),',1);" href="#" title="Ajouter au panier"><img src="'.$basePath.'public/images/etiquette.png"  width="16" height="16" /> Etiquette</a>';
				?>			
				</div></td>
				<td><div class="description">
				<?php								
				echo '<a style="margin-top:5px;" id="ajoutpanier'.$prod->getIdentifiant().'" onclick="javascript:addtocart(',$prod->getIdentifiant(),',1);" href="#" title="Ajouter au panier"><img src="'.$basePath.'public/images/etiquette.png"  width="16" height="16" /> Etiquette</a>';
				?>			
				</div></td>
			</tr>	
		</table>
		
*/		
?>

<div class="description" style="margin-top: 0px;" ><a style="margin-top:5px;" href="<?php echo Zend_Registry::get('basePath'); ?>index.php?page=admin&action=gererprod&type=modif&id=<?php echo $prod->getIdentifiant(); ?>&cat=<?php echo $prod->getGenreName(); ?>&sscat=<?php echo $prod->getGroupeName(); ?>&valuetosearch=<?php echo $_GET['valuetosearch']; ?>" ><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/modif.png"  width="16" height="16" /> Modifier</a></div>
<div class="description"><a style="margin-top:5px;" onclick="return confirm('Voulez-vous vraiment supprimer ce produit ?');" href="<?php echo Zend_Registry::get('basePath'); ?>index.php?page=admin&action=gererprod&type=delete&valuetosearch=<?php echo $_GET['valuetosearch']; ?>&id=<?php echo $prod->getIdentifiant(); ?>&cat=<?php echo $prod->getGenreName(); ?>&sscat=<?php echo $prod->getGroupeName(); ?>" ><img src="<?php echo Zend_Registry::get('basePath'); ?>public/images/cross.png"  width="16" height="16" /> Supprimer</a></div>
<div class="description">
	<?php								
	echo '<a style="margin-top:5px;" id="ajoutpanier'.$prod->getIdentifiant().'" onclick="javascript:addtocart(',$prod->getIdentifiant(),',1);" href="#" title="Ajouter au panier"><img src="'.$basePath.'public/images/etiquette.png"  width="16" height="16" /> Etiquette</a>';
	?>			
</div>
	</div>
</div>

	<?php
	
	}	
}
}
} else {

if (isset($_GET['sur_devis']) && $_GET['sur_devis'] == "1") {

include("produit_sur_devis.php");

} else {

if (isset($_GET['etiquette'])) {

include("gerer_etiquette.php");

} else {

if (isset($_GET['ajout']) && $_GET['ajout'] == "1") {

include("ajout_produit.php");

} else {


if (isset($_GET['non_dispo']) && $_GET['non_dispo'] == "3") {

include("modif_non_dispo.php");

} else {


?>
<!-- ajout des fonctions pour charger les listes dynamiquement -->
<script type="text/javascript" src="<?php echo $basePath;?>public/js/fonctions_ajaxcatprod.js"></script>

<?php
if (!isset($_GET['non_dispo'])){
?>
<h1><img src="<?php echo $basePath; ?>public/images/annonces.png" alt=""/> <span>Requêtes, procédures et modes opératoires</span></h1>

<a style="margin-left:85px; font-size:24px; color:#0080ff; text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.75);" href="<?php echo $basePath;?>admin/produit/rentree_de_stock.pdf" target="_blank" ><strong>1 - FAIRE UNE RENTRÉE DE STOCK </strong></a><br/>
<a style="margin-left:85px; font-size:24px; color:#0080ff; text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.75);" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&arrivage=1"><strong>2 - AFFICHER LES DERNIERS ARRIVAGES </strong></a><br/>

<h1><img src="<?php echo $basePath; ?>public/images/produit.png" alt=""/> <span>Gérer les produits</span></h1>

<table style="margin-left: 90px;">
<tr>
<td><a class="ajout" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&ajout=1"></a></td>
<td><a class="nd" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&non_dispo=1"></a></td>
<td><a class="devis" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&sur_devis=1"></a></td>
<td><a class="recherche" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&search=1"></a></td>
<td><a class="etiquette" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&etiquette=1"></a></td>
</tr>
<tr>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&ajout=1">Ajout</a></td>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&non_dispo=1">ND</a></td>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&sur_devis=1">Devis</a></td>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&search=1">Recherche</a></td>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&etiquette=1">Etiquette</a></td>
</tr>
</table>
<?php
/*

// TABLEAU STATIQUES PRODUITS 

<table style="margin-left: 90px; background-color:grey; color: white; font-family:Bitter; padding:0px;">
	<tr>
		<td style="border:10px solid grey; margin:0px; padding:0px; background-color:white;"><img height="96" width="96" style="border:none; padding:0px;" src="<?php echo $basePath; ?>public/images/PICTO/CHIFFRE.png" alt=""/></td>
		<td style="
		border-bottom:10px solid grey; 
		border-top:10px solid grey; 
		border-right:10px solid grey; 
		border-left:5px solid grey;
		margin:0px; padding:0px; 
		background-color:black; ">12735 pièces référencés</td>
	</tr>
	<tr>
		<td style="border-right:10px solid grey; 
		border-left:10px solid grey; 
		border-bottom:10px solid grey; 
		margin:0px; padding:0px;
		"><img src="<?php echo $basePath; ?>public/images/PICTO/LIVRAISON.png" alt=""/></td>
		<td style="
		border-bottom:10px solid grey; 
		border-right:10px solid grey; 
		border-left:5px solid grey;
		margin:0px; padding:0px; 
		background-color:black; ">6% en cours de réapprovisionnement</td>
	</tr>
</table>
*/
?>
<?php
} else {
?>
<h1><img src="<?php echo $basePath; ?>public/images/non_dispo.png" alt=""/> <span>Gérer les produits non disponible</span></h1>
<?php
}

if (isset($_GET['non_dispo']) && $_GET['non_dispo'] == "2") {

$ref = $_GET['id'];

$result2 = $database->prepare("
DELETE FROM non_dispo WHERE reference = '$ref'
") or die ("requete r1 invalid");
$result2->execute();

}
if (isset($_GET['non_dispo']) && $_GET['non_dispo'] == "4") {

$ref = $_GET['id'];

$result2 = $database->prepare("
UPDATE non_dispo SET dispo = '2' WHERE reference = '$ref'
") or die ("requete update r1 invalid");
$result2->execute();

}
if (isset($_GET['non_dispo']) && $_GET['non_dispo'] == "5") {

$ref = $_GET['id'];

$result2 = $database->prepare("
UPDATE non_dispo SET dispo = '1' WHERE reference = '$ref'
") or die ("requete update r1 invalid");
$result2->execute();

}

if (isset($_GET['non_dispo']) && ($_GET['non_dispo'] == "1" || $_GET['non_dispo'] == "2" || $_GET['non_dispo'] == "4" || $_GET['non_dispo'] == "5")) {

include("gerer_non_dispo.php");

} else {

if (isset($_GET['type']) && $_GET['type'] == "requete"){

for ($i = 0; $i < count($_POST['nom_champs']); $i++) {

	$nom_champs = $_POST['nom_champs'][$i];
	$comparaison = $_POST['comparaison'][$i];
	$elem_compar = $_POST['elem_compar'][$i];
	
	if ($i > 1){
	$fin_requete .= " AND ";
	}
	
	$fin_requete .= $nom_champs.' '.$comparaison."'".$elem_compar."'";

}
	
	$requete = "SELECT * FROM produits WHERE ".$fin_requete; 	
	echo $requete;
	
}

if (isset($_GET['search'])){
?>


<br />	

<!-- Formulaire pour recherche ++ -->
				<center>
			<form method="GET" action="<?php echo $basePath; ?>index.php">
				<input type=hidden name="page" value="admin">
				<input type=hidden name="action" value="gererprod">
				Rechercher : <input type="text" name="valuetosearch" value="" />
				dans : 	<select name="in">
							<option value="DESC" >les désignations des produits</option>
							<option value="REF" >les références des produits</option>
							<option value="GENRE" >les noms des genres</option>
							<option value="GRP" >les noms des groupes</option>
							<option value="GENREGRP" >les noms des genres et des groupes</option>
							<option value="ALL" selected="selected" >tout</option>
						</select>
				en utilisant : 	<select name="method">
								<option value="AND">tous les mots</option>
								<option value="OR">au moins un des mots</option>
							</select>
			<br /><br />
			<input type="submit" value="Rechercher !" />
			</form>
			</center><br /><br />
<br />		


<!-- Formulaire de filtre sur les genres et les groupes -->
<form name="filtre" method="GET" action="<?php echo $basePath; ?>index.php">
	<input type=hidden name="page" value="admin">
	<input type=hidden name="action" value="gererprod">
	<table class="table_center">
		<tr>
			<td>Genre :</td>
			<td>Groupe :</td>
		</tr>
		<tr>
			<td> 
				<select name="filtre_cat" size="5" style="max-width:200px;">
					<option value="all">Tous</option>

					<?php
					$namelist = ProduitDB::getGenresNames();
					foreach($namelist as $name)
					{
						echo '<option value="',$name,'"';
						if(isset($_GET['filtre_cat']) && $_GET['filtre_cat']==$name)
							echo ' selected=true';
						
						echo ' onClick="chargeSousCategories(\'';
						echo $name;
						list($fr, $en) = explodeCategorieName($name);
						echo '\');">',$fr;
					
						
						echo '</option>';
					}
					?>
				</select>
			</td>

			<td>
				<div id="progress_sscat" style="display:none;"><img src="public/images/progressbar.gif" alt="Barre de progression"/></div>
				<div id="msg_sscat" style="<?php echo isset($_GET['filtre_cat'])?'display:none;':'display:normal;'; ?>"><b>Sélectionnez un Genre.</b></div>
					<select id="filtre_sscat" name="filtre_sscat" size="5" style="<?php echo isset($_GET['filtre_cat'])?'display:normal;':'display:none;'; ?>">
						<option value="all">Tous</option>
					</select>
			</td>
			<td>
				<input type=submit value="filtrer" />
			</td>
		</tr>
	</table>
</form>
<br />		
				
<?php
//si un filtre est définit on cahrge les sous catégorie en fonction de la catégorie
if(isset($_GET['filtre_cat'])):
?>
	<script type="text/javascript">chargeSousCategories("<?php echo $_GET['filtre_cat']; ?>", "<?php echo $_GET['filtre_sscat']; ?>")</script>
<?php
endif;

if(isset($_GET['numpage']))
	$numpage = $_GET['numpage'];
else
	$numpage = 1;


//par défaut on affiche tout, donc on ne précise aucune catégorie ou sous catégorie et aucun filtres
$genre = null;
$groupe = null;
$tri = null;
if(isset($_GET['filtre_cat']))
{
	$genre = $_GET['filtre_cat'];
	/*if(!in_array($genre, array('jeep', 'dodge', 'gmc')))
		$genre = strtolower($genre);*/
}
if(isset($_GET['filtre_sscat']))
{
	$groupe = $_GET['filtre_sscat'];
}
if(isset($_GET['tri']))
{
	$tri = $_GET['tri'];
}

//création d'un objet pour paginer la page
$pagination = new Pagination(ProduitDB::getProduitsListCount($genre, $groupe), 100, 20, $numpage);

$liste = new ProduitDisplay($genre, $groupe, ProduitDisplay::$PAGE_TOUS);
$liste->showGroupe(true);
$liste->showGenre(true);
$liste->isClientList(false);
$liste->setPagination($pagination);
if(isset($_GET['tri']))
	$liste->setTri($tri);
if(isset($_GET['sens']))
	$liste->setSens($_GET['sens']);
$liste->setInterval($pagination->getStartPos(), $pagination->getNbLignes());
$liste->displayTable();

}
?>

<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>index.php?page=admin&action=administration"><?php echo $translate->_('Retour');?></a>
</p>

<?php

}
}
}
}
}
}
}
?>