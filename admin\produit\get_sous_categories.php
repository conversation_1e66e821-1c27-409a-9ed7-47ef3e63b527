<?php
require_once(dirname(__FILE__).'/../../init.php');
require_once(dirname(__FILE__).'/../../utils/form_utile.php');
require_once(dirname(__FILE__).'/../../class/produit_class.php');
require_once(dirname(__FILE__).'/../../class/categorie_class.php');

/**
 * programme qui renvoit la liste des sous catégories en fonction du nom d'une catégorie
 * utiliser dans 'Gérer produit', pour charger la liste des sous catégories
 * 
 * format de sortie de la liste des sous-catégories :
 * nom_complet1:nom_francais1;nom_complet2:nom_francais2;...
 */
$res = '';


$namelist = ProduitDB::getGroupesNames((string)url_decode($_GET['filtre_cat']));

foreach($namelist as $name)
{
	list($fr,$en)=explodeCategorieName($name);
	$id = ProduitDB::getGoupeIdByName((string)$name);
	
	//$res.=$id.':'.$fr.';';
	if (($_SESSION['lang'] == "fr") || ($en == ""))
		$res.=$id.':'.strtoupper($fr).';';
	else
		$res.=$id.':'.strtoupper($en).';';
}

echo $res;
?>