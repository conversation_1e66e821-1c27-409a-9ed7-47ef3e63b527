<?php
	
$user = "smi001";
$host = "jeep-dodge-gmc.com";
//$user = "user-smi";
//$host = "vps246529.ovh.net";
$password = "smi38160";
$dbname = "smi001_001";

$genre = 'JEEP';

$sql = "SELECT * FROM produits WHERE ";
if (isset($_GET["ref"])){
	$ref = $_GET["ref"];
	$sql .= "referenceproduit LIKE '". $ref . "%'";
}

$sql .= "genre LIKE '%". $genre . "%'";

if (isset($_GET["gr"])){
	$groupe = $_GET["gr"];
	if (isset($_GET["ge"])){ $sql .= " AND ";}
	$sql .= "groupe LIKE '%". $groupe . "%'";
}

$sql .= ' ORDER BY tarif_datemaj';
$sql .= ' LIMIT 3';

echo $sql . '<br>';

// Create connection
$conn = mysqli_connect($host, $user, $password, $dbname);
// Check connection
if (!$conn) {
  die("Connection failed: " . mysqli_connect_error());

}
// Create connection
$connupd = mysqli_connect($host, $user, $password, $dbname);
// Check connection
if (!$connupd) {
  die("Connection failed: " . mysqli_connect_error());
}

$result = mysqli_query($conn, $sql);

// Les URLs pour la recherche
$url_jse = 'https://www.jeepsudest.com/recherche/';	  
$url_jeepest = 'https://jeepest.com/fr/recherche?controller=search&orderby=position&orderway=desc&search_query=';
$url_gsaa = 'https://jeepvillage.com/recherche?controller=search&s=';
//$url_gsaa = 'https://jeepvillage.com/recherche?controller=search&s=WO636297';

// ----
// Traitement 
if (mysqli_num_rows($result) > 0) {
	while($row = mysqli_fetch_assoc($result)) {
		$res = "0<br>";
		$ref_produit = $row["referenceproduit"];
		$ref_produit = str_replace('_NOS', '', $ref_produit);
		
		// ----
		// Traitement JeepVillage
		$lines_gsaa = file($url_gsaa.$ref_produit);
		if(!$lines_gsaa ) {
			echo 'JV|' . $row["referenceproduit"] . '|' . $row["prixproduiteuro"] . '|' .'0<br>';
		}else {
			foreach ($lines_gsaa as $line_num3 => $line3) {
				$aff2 = strstr($line3, '<span class="price" aria-label="Prix">');
				if( $aff2 != false ) {
					$res = preg_replace('/[^0-9,]/', '', $aff2);
					$res = preg_replace('/[,]/', '.', $res);
				}		
			}	
		 echo 'JV|' . $row["referenceproduit"] . '|' . $row["prixproduiteuro"] . '|' .$res .'<br>';
		}
		
		// ----
		// Traitement JEEPEST
		$Trouve = false;
		$DejaTrouve = false;
		$NbreTrouve = 0;
		$res1 = "0<br>";
		$lines_jeepest = file($url_jeepest.$ref_produit);
		if(!$lines_jeepest) {
			echo 'JE|' . $row["referenceproduit"] . '|' . $row["prixproduiteuro"] . '|0<br>';
		}else {
			foreach ($lines_jeepest as $line_num2 => $line2) {
				if($Trouve) {
					$affj = htmlspecialchars($line2);
					$affj1 = strstr($affj, '€', true);
					if( $affj1 != false ) {
						echo 'JE|' . $row["referenceproduit"] . '|' . $row["prixproduiteuro"] . '|' . $affj1."<br>";
						$res1 = $affj1;
						$Trouve = false;
					}
				}
				if(strpos($line2, '<span itemprop="price" class="price product-price">')) {
					if(!$DejaTrouve) $Trouve = true;
						$DejaTrouve = true;
						$NbreTrouve += 1;
					}
				}
				if ($NbreTrouve > 1) {
					echo 'JE|' . $row["referenceproduit"] . '|' . $row["prixproduiteuro"] . '|0<br>';
				}
			}
		
	// ----
	// MàJ de la fiche produits
	$res1 = str_replace(',', '.', $res1);
	$res1 = str_replace('<br>', '', $res1);
	$res1 = str_replace(' ', '', $res1);
	$res  = str_replace('<br>', '', $res );
	$res  = str_replace(' ', '', $res );
	$sqlupd = 'UPDATE produits SET tarif_jeepest = '. $res1 . ', tarif_jeepvillage  = ' . $res . ', tarif_datemaj = NOW() WHERE idproduit = '. $row["idproduit"];
// Create connection
$connupd = mysqli_connect($host, $user, $password, $dbname);
// Check connection
if (!$connupd) {
  die("Connection failed: " . mysqli_connect_error());
}
	echo $sqlupd .'<br>';
	mysqli_query($connupd, $sqlupd);
	mysqli_close($connupd);
	}
}
mysqli_close($conn);