<?php
ini_set("memory_limit" , "48M");  
require_once(dirname(__FILE__).'/../../class/produit_class.php');
require_once(dirname(__FILE__).'/../../class/categorie_class.php');
?>

<!-- Affiche du titre -->
<h1><img src="<?php echo $basePath; ?>public/images/importer.png" height="30"/>Importer des produits</h1>

<?php
if(isset($_FILES['nomfichier']['name'])) // on se trouve sur la page de traitement
{
  //récupère le nom du fichier
  $nomfichier=$_FILES['nomfichier']['name'];

  //test si le nom est vide
  if($nomfichier != '')
  {
  	//une photo a été uploadé, la déplacer dans le répertoire adéquat
	if (!move_uploaded_file($_FILES['nomfichier']['tmp_name'],"temp/".$nomfichier)) {
		$msg = '<p class="messageBox error">Attaque par téléchargement !</p>';
	}

	//récupère l'extension du fichier
    if(($pos = strrpos($nomfichier, '.')))     
    { 
     	$ext = substr($nomfichier,$pos+1);
     	strtolower($ext);
     	
     	if(strcmp($ext, 'csv')==0) //l'extension est la boone
     	{
     		
     		//récupère la liste des produits à partir du fichier
     		$res = array(4);
     		//$res = ProduitDB::readProduitsFromFile($nomfichier, 1, 2, 3, 11, 13, 4);
			$res = ProduitDB::readProduitsFromFile($nomfichier, 1, 2, 3, 4, 5, 6);
     		
     		$produits = $res[0];
     		$list_genre = $res[1];
     		$list_groupe = $res[2];
     		
     		$nbgroupe=count($list_groupe);
     		$nbgenre=count($list_genre);
     		$nbprod=count($produits);
     		

			?>
			<center>
     		<div id="progressbar" style="display:none;"><img src="<?php echo $basePath; ?>public/images/progressbar.gif" alt="Barre de progession"/></div>
     		<h2 id="status" style="display:none;"></h2>
     		</center>
     		<br />
     		<p class="messageBox info"><?php echo $nbgenre; ?> genre(s), <?php echo $nbgroupe; ?> groupe(s) et <?php echo $nbprod; ?> produit(s) trouvés dans le fichier <?php echo $nomfichier; ?>.</p>
     		
			<?php
     		//sauvegarde la liste des produits avant l'appelle ajax
     		$_SESSION['produits'] = serialize($produits);
     		?>
     		<script type="text/javascript">
     			//récupère la balise body, pour ajouter notre fonction lorsque la page est chargée
				body = document.getElementsByTagName('body');
				body[0].setAttribute("onLoad", "startToSave()");
				
				function listSaved()
				{		
					progress = document.getElementById("progressbar");
					status = document.getElementById("status");
					if (xhr.readyState === 1) 
					{
						progress.setAttribute("style", "display:normal;");
						status.setAttribute("style", "display:normal;");
						
						status.innerHTML = "Envoi des données à enregistrer... (peut prendre plusieurs minutes)";  
					}
					if (xhr.readyState === 3) 
					{			
						status.innerHTML = "Enregistrement en cours... (peut prendre plusieurs minutes)";  
					}
					if (xhr.readyState === 4) 
					 { // Données disponibles
					 	
					 	progress.setAttribute("style", "display:none;");
					 	
					 	res = document.getElementById("resultats");
					 	res.innerHTML = xhr.responseText;
					 	
					 	status.innerHTML = "Enregistrement terminé !";  				   
					}
					
				}
			
				//fonction à executer lorsque la page est chargée
				function startToSave() {
					
					//global var xhr;
					xhr = initializeAjax();
					// Ouverture d'une connexion en mode asynchrone
					xhr.onreadystatechange = listSaved;
					xhr.open("GET", "<?php echo $basePath; ?>admin/produit/save_produit_list.php", true);
					xhr.send(null);
					
					body[0].setAttribute("onLoad", "");
				}
			</script>
     		<?php
     		    		
     		echo '<div id="resultats"></div>';

     	}
     	else
     	{
     		echo '<p class="messageBox error">Extension de fichier incorrecte !</p><br>';
     	}
    }
    else
    {
    	echo '<p class="messageBox error">Nom de fichier incorrecte !</p><br>';
    }
	
  }
  else
  {
  	echo '<p class="messageBox error">Veuillez spécifiez un nom de fichier !</p><br>';
  	
  }
} else {
?>
	<!-- Message d'avertissement pour la suppression des produits -->
	<p class="messageBox help">
	<span style="text-decoration:blink;"><img src="<?php echo $basePath; ?>public/images/attention.png" alt="/!\"/> ATTENTION <img src="<?php echo $basePath; ?>public/images/attention.png" alt="/!\"/></span> La base de données de produits sera entièrement vidée ! Ceci peut engendrer des problèmes pour la gestion des commandes des clients. En effet les références des produits associés aux commandes ne seront plus valides si vous supprimez les produits de la base.
	</p>

	<center>
	<form name="form" method="POST" action="<?php echo $basePath; ?>index.php?page=admin&action=importer_produits" enctype="multipart/form-data"><br/>
		<table>
			<tr>
				<td>Fichier à importer : </td><td><input name="nomfichier" type="file"/></td>
			</tr>
		</table><br />
	<input type='submit' name='valider' value='Importer'>
	</form><br/>
	</center>

<?php
}
?>
<p class="bouton_retour"><a href="<?php echo $basePath; ?>index.php?page=admin&action=gererprod">Retour</a></p><br />
<?php


?>