<?php

require_once dirname(__FILE__).'/../../class/produit_class.php';
require_once dirname(__FILE__).'/../../visiteur/panier.php';
require_once dirname(__FILE__).'/../../visiteur/login.php';
require_once dirname(__FILE__).'/../../utils/form_utile.php';

	$lignePs = $user->getPanier()->getLignePanier();

?>

<page> 

<style type="text/css">
body {
	font-family: Verdana, Geneva, sans-serif;
	font-size: 36px;
	padding: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	margin-left: 0px;
}
body table {

}
</style>
</head>

<body>
<table>

<?php
ini_set("display_errors",0);error_reporting(0);
$database = new PDO("mysql:host=jeep-dodge-gmc.com;dbname=smi001_001","smi001","smi38160");
//$database = new PDO("mysql:host=vps246529.ovh.net;dbname=smi001_001","user-smi","smi38160");


if($lignePs != null){

$i=0;

$nb_ligne = count($lignePs);

foreach($lignePs as $k => $ligneP){
		$p = $ligneP->getProduit();
		$ligneP->getQte();
		$genre = $p->getGenreName();
		$reference = $p->getReference();
		$produit_commun = "";
//produit commun	

$result = $database->prepare("SELECT * FROM produits WHERE referenceproduit = '$reference' ORDER BY referenceproduit DESC LIMIT 1 ") or die ("requete r9 invalid");
$result->execute();
while ($tab1 = $result->fetch()) {
			
	if ($tab1['prod_commun_r2087'] != "-1") {				
		$produit_commun .= "R2087 - ".$tab1['prod_commun_r2087_name']." | ";
	}		
	if ($tab1['prod_commun_jeep'] != "-1") { 
		$produit_commun .= "JEEP - ".$tab1['prod_commun_jeep_name']." | ";
	}			
	if ($tab1['prod_commun_dodge'] != "-1") { 
		$produit_commun .= "DODGE - ".$tab1['prod_commun_dodge_name']." | ";
	}		
	if ($tab1['prod_commun_gmc'] != "-1") { 
		$produit_commun .= "GMC - ".$tab1['prod_commun_gmc_name']." | ";
	}
	if ($tab1['prod_commun_chevr'] != "-1") { 
		$produit_commun .= "Chevrolet - ".$tab1['prod_commun_r2087_name']." | ";				
	}
}		
?>
<tr>
    <td style="
	Line-Height: 1.3;
	border-top-width: 1px;
	border-right-width: 1px;
	border-bottom-width: 1px;
	border-left-width: 1px;
	border-top-style: solid;
	border-right-style: solid;
	border-bottom-style: solid;
	border-left-style: solid;
	border-top-color: #000;
	border-right-color: #000;
	border-bottom-color: #000;
	border-left-color: #000;
	margin-top: 0px;
	margin-right: 0px;
	height: 190mm;
	width: 280mm;
	display: block;
	vertical-align: top;" >
	
<?php 
	if (file_exists("../../public/images/produits/".$p->getReference().".jpg")){	
?>
	<div style="text-align:left;" ><img src="http://www.jeep-dodge-gmc.com/smi/public/images/produits/<?php echo $p->getReference(); ?>.jpg" width="300" height="200" />
<?php 
	} else {
?>
	<div style="text-align:center;" >
<?php 
	}	
?>

	<?php	
	if (strlen($genre) > 9){
	echo '<span style="font-size:120px;" ><strong>';	
	echo substr($genre, 0, 15);
	echo "</strong></span>";
	} else {
	echo '<span style="font-size:155px;" ><strong>';	
	echo $genre;
	echo "</strong></span>";
	}
	?>
</div>

	<div style="font-size:40px; text-align:center;" ><strong><?php echo $p->getEmpl_principal(); ?> - <?php echo $p->getEmpl_secondaire(); ?></strong> - <?php echo substr($p->getType(), 0, 20); ?></div>
    <div style="font-size:60px; text-align:center; border-top: 1px solid black;border-bottom: 1px solid black; margin-bottom: 10px;" ><strong><?php echo substr($p->getDesignation(), 0, 50); ?> </strong></div>
	<div style="font-size:60px; text-align:center;" ><strong><?php echo $p->getGroupeId(); ?> </strong><?php echo $p->getGroupeName(); ?></div> 
	<div style="font-size:30px; text-align:center;" ><barcode type="C128A" value="<?php echo $p->getReference(); ?>" style="width:80mm; height:10mm"></barcode></div> 
	<div style="font-size:14px; text-align:center;" >Commun avec : <?php echo $produit_commun; ?></div> 
	<div style="font-size:14px; text-align:center;" ><?php echo $p->getEmpl_comment(); ?> - Date Etiquette : <?php echo date("d/m/y"); ?></div> 
	</td>
</tr>
<?php
}
}
?>
</table>


</body>

 </page> 
