<?php 

require_once dirname(__FILE__).'/../../class/produit_class.php';
require_once dirname(__FILE__).'/../../visiteur/panier.php';
require_once dirname(__FILE__).'/../../init.php';
require_once dirname(__FILE__).'/../../visiteur/login.php';
require_once dirname(__FILE__).'/../../visiteur/panier.php';
require_once dirname(__FILE__).'/../../utils/form_utile.php';

	$lignePs = $user->getPanier()->getLignePanier();

?>

<page> 

<style type="text/css">
body {
	font-family: Verdana, Geneva, sans-serif;
	font-size: 36px;
	padding: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	margin-left: 0px;
}
body table {

}
</style>
</head>

<body>
<table>

<?php

if($lignePs != null){

$i=0;

$nb_ligne = count($lignePs);

foreach($lignePs as $k => $ligneP){
		$p = $ligneP->getProduit();
		$ligneP->getQte();
		$groupe = $p->getGroupeName();
		$designation = $p->getDesignation();
if (($i%2 == 0) && ($i > 0)){
echo "<tr>";
//echo "fin2";
}
if ($i==0){
echo "<tr>";
//echo "debut1";
}
?>

    <td style="
	font-size: 32px;
	border-top-width: 1px;
	border-right-width: 1px;
	border-bottom-width: 1px;
	border-left-width: 1px;
	border-top-style: solid;
	border-right-style: solid;
	border-bottom-style: solid;
	border-left-style: solid;
	border-top-color: #000;
	border-right-color: #000;
	border-bottom-color: #000;
	border-left-color: #000;
	margin-top: 0px;
	margin-right: 0px;
	height: 80mm;
	width: 138mm;
	display: block;
	vertical-align: top;" >
	<div style="font-size:20px; text-align:center;" ><strong><?php echo $p->getGenreName(); ?></strong></div>
	<div style="font-size:20px; text-align:center;" ><?php echo $p->getType(); ?></div>
    <div style="font-size:20px; text-align:center;" ><?php echo substr($designation,0 ,40); ?></div>
	<div style="font-size:70px; text-align:center;" ><strong><?php echo $p->getGroupeId(); ?></strong></div> 
	<?php	
	if (strlen($groupe) < 11){
	echo '<div style="font-size:70px; text-align:center;" ><strong>';	
	echo substr($groupe, 0, 11);
	echo "</strong></div>";
	} else {
	echo '<div style="font-size:30px; text-align:center;" ><strong>';	
	echo $groupe;
	echo "</strong></div>";
	}
	?>
	<div style="font-size:20px; text-align:center;" ><?php echo $p->getEmpl_principal(); ?> - <?php echo $p->getEmpl_secondaire(); ?></div> 
	<div style="font-size:20px; text-align:center;" ><barcode type="C128A" value="<?php echo $p->getReference(); ?>" style="width:50mm; height:10mm"></barcode> <?php echo $p->getEmpl_comment(); ?></div> 
	<div style="font-size:8px; text-align:right;" ><?php echo date("d/m/y"); ?></div> 
	
	</td>

<?php
	if (($i%2 == 1) && ($i != $nb_ligne)  && ($i > 0)){
		echo "</tr>";
		//echo "debut2";
	}
	$i++;
	 //echo $i;
	 if (($i%2 == 1)&&($i == $nb_ligne)  && ($i > 0)){
		echo "</tr>";
		//echo "debut2";
	}
	}
}
?>
</table>


</body>

 </page> 
