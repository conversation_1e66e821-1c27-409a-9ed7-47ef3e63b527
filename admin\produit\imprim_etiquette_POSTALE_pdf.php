<?php

require_once dirname(__FILE__).'/../../class/produit_class.php';
require_once dirname(__FILE__).'/../../visiteur/panier.php';
require_once dirname(__FILE__).'/../../init.php';
require_once dirname(__FILE__).'/../../visiteur/login.php';
require_once dirname(__FILE__).'/../../visiteur/panier.php';
require_once dirname(__FILE__).'/../../utils/form_utile.php';

	$lignePs = $user->getPanier()->getLignePanier();

?>

<page>

<style type="text/css">
body {
	font-family: Verdana, Geneva, sans-serif;
	font-size: 36px;
	padding: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	margin-left: 0px;
}
body table {

}
</style>
</head>

<body>
<table>

<?php

if($lignePs != null){

$i=0;

$nb_ligne = count($lignePs);

foreach($lignePs as $k => $ligneP){
		$p = $ligneP->getProduit();
		$ligneP->getQte();
?>
<tr>
    <td style="
	font-size: 30px;
	Line-Height: 1.3;
	border-top-width: 1px;
	border-right-width: 1px;
	border-bottom-width: 1px;
	border-left-width: 1px;
	border-top-style: solid;
	border-right-style: solid;
	border-bottom-style: solid;
	border-left-style: solid;
	border-top-color: #000;
	border-right-color: #000;
	border-bottom-color: #000;
	border-left-color: #000;
	margin-top: -10px;
	margin-right: 0px;
	height: 80mm;
	width: 195mm;
	display: block;
	vertical-align: top;" >
	<div style="font-size:25px; text-align:center;" ><strong><?php echo $p->getGenreName(); ?></strong></div>
	<div style="font-size:20px; text-align:center;" ><?php echo $p->getType(); ?></div>
    <div style="font-size:50px; text-align:center;" ><strong><?php echo $p->getDesignation(); ?></strong></div>
	<div style="font-size:20px; text-align:center;" ><?php echo $p->getGroupeId(); ?> <?php echo $p->getGroupeName(); ?></div> 
	<div style="font-size:20px; text-align:center;" ><?php echo $p->getEmpl_principal(); ?> - <?php echo $p->getEmpl_secondaire(); ?></div> 
	<div style="font-size:20px; text-align:center;" ><barcode type="C128A" value="<?php echo $p->getReference(); ?>" style="width:50mm; height:10mm"></barcode> <?php echo $p->getEmpl_comment(); ?></div> 
	<div style="font-size:12px; text-align:right;" ><?php echo date("d/m/y"); ?></div> 
	
	</td>
</tr>
<?php
}
}
?>
</table>


</body>

 </page> 
