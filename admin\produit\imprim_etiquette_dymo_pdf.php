<?php

require_once dirname(__FILE__).'/../../class/produit_class.php';
require_once dirname(__FILE__).'/../../visiteur/panier.php';
require_once dirname(__FILE__).'/../../init.php';
require_once dirname(__FILE__).'/../../visiteur/login.php';
require_once dirname(__FILE__).'/../../visiteur/panier.php';
require_once dirname(__FILE__).'/../../utils/form_utile.php';

?>

<page> 

<style type="text/css">
body {
	font-family: Verdana, Geneva, sans-serif;
	font-size: 11px;
	padding: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	margin-left: 0px;
}
</style>
</head>

<body>

<?php

// Pour une seule etiquette

if (isset($_GET['id'])){

$id = $_GET['id'];

$result = $database->prepare("SELECT * FROM produits WHERE idproduit = '$id'") or die ("requete r20 invalid");
$result->execute();
while ($prod = $result->fetch()) {
?>
	<div>
	<div align="center"><barcode type="C128A" value="<?php echo $prod['referenceproduit']; ?>" style="width:35mm; height:8mm"></barcode></div>
    <?php echo substr($prod['descriptionproduit'], 0, 40); ?> <strong>
	<?php
	if ($prod['qualite'] == "NEW"){
		echo "NEUF";
	} elseif ($prod['qualite'] == "NOS"){
		echo "NOS";
	} elseif ($prod['qualite'] == "REC"){
		echo "REC";
	} elseif ($prod['qualite'] == "USE"){
		echo "OCC";
	}
	
	?>
	</strong><br />
	<strong><?php echo $prod['prixproduiteuro']; ?>&euro; HT</strong><br />
	<?php echo substr($prod['groupe'], 0, 15); ?> 
	<strong><?php echo $prod['genre']; ?></strong>
	</div>
	
<?php

}
}

// Par date de réappro

if (isset($_GET['date'])){

$date = explode("/", $_GET['date']);
$id = $date[2]."-".$date[1]."-".$date[0];

$result = $database->prepare("SELECT * FROM produits WHERE date_livraison = '$id'") or die ("requete r20 invalid");
$result->execute();
while ($prod = $result->fetch()) {
?>
	<div>
	<div align="center"><barcode type="C128A" value="<?php echo $prod['referenceproduit']; ?>" style="width:35mm; height:8mm"></barcode></div>
    <?php echo substr($prod['descriptionproduit'], 0, 40); ?> <strong><?php echo $prod['qualite']; ?></strong><br />
	<strong><?php echo $prod['prixproduiteuro']; ?>&euro; HT</strong><br />
	<?php	
	if (strlen($prod['groupe']) < 15){
	echo substr($prod['groupe'], 0, 15);
	} else {
	echo "<span style='font-size: 8px;'>";	
	echo $prod['groupe'];
	echo "</span>";
	}
	?>
	<strong><?php echo substr($prod['genre'], 0, 6); ?></strong>
	</div>
	
<?php

}

// Etiquette du panier (plusieurs)

} else {

$lignePs = $user->getPanier()->getLignePanier();

if($lignePs != null){

$nb_ligne = count($lignePs);

foreach($lignePs as $k => $ligneP){
		$p = $ligneP->getProduit();
		$q = $ligneP->getQte();
		$i=1;
		while ($i <= $q) {
?>
	<div>
	<div align="center"><barcode type="C128A" value="<?php echo $p->getReference(); ?>" style="width:35mm; height:8mm"></barcode></div>
	
	<?php
	$designation = $p->getDesignation();
	if (strlen($designation) > 35){
	echo '<span style="font-size:10px;" >';	
	echo substr($designation, 0, 50);
	echo "</span>";
	} else {
	echo $designation;
	}
	?>
    <strong>
	<?php
	if ($p->getQualite() == "NEW"){
		echo "NEUF";
	} elseif ($p->getQualite() == "NOS"){
		echo "NOS";
	} elseif ($p->getQualite() == "REC"){
		echo "REC";
	} elseif ($p->getQualite() == "USE"){
		echo "OCC";
	}
	
	?></strong><br />
	<div style="text-align:right;" ><strong><?php echo $p->getPrixHT(); ?> &euro; HT</strong></div><br />
	<?php	
	if (strlen($p->getGroupeName()) < 13){
	echo $p->getGroupeId();
	echo " - ";
	echo $p->getGroupeName();
	} else {
	echo "<span style='font-size: 8px;'>";	
	echo $p->getGroupeId();
	echo " - ";
	echo substr($p->getGroupeName(), 0, 25);
	echo "</span>";
	}
	?>
	<strong>
	<?php	
	if (strlen($p->getGenreName()) < 6){
	echo $p->getGenreName();
	} else {
	echo "<span style='font-size: 10px;'>";	
	echo substr($p->getGenreName(), 0, 7);
	echo "</span>";
	}
	?>
	</strong>
	</div>
	
<?php
		$i++;
}
}
}
}
?>

</body>

 </page> 
