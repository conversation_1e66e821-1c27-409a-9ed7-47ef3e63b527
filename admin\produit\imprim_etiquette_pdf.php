<?php

require_once dirname(__FILE__).'/../../class/produit_class.php';
require_once dirname(__FILE__).'/../../visiteur/panier.php';
require_once dirname(__FILE__).'/../../init.php';
require_once dirname(__FILE__).'/../../visiteur/login.php';
require_once dirname(__FILE__).'/../../visiteur/panier.php';
require_once dirname(__FILE__).'/../../utils/form_utile.php';

	$lignePs = $user->getPanier()->getLignePanier();

?>

<page> 

<style type="text/css">
body {
	font-family: Verdana, Geneva, sans-serif;
	font-size: 12px;
	padding: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	margin-left: 0px;
}
body table {

}
</style>
</head>

<body>
<table>

<?php

if($lignePs != null){

$i=0;

$nb_ligne = count($lignePs);

foreach($lignePs as $k => $ligneP){
		$p = $ligneP->getProduit();
		$ligneP->getQte();
if (is_int($i/3) && ($i > 0)){
echo "</tr>";
//echo "fin2";
}
if ($i==0){
echo "<tr>";
//echo "debut1";
}
?>

    <td style="
	border-top-width: 5px;
	border-right-width: 5px;
	border-bottom-width: 5px;
	border-left-width: 5px;
	border-top-style: none;
	border-right-style: solid;
	border-bottom-style: none;
	border-left-style: solid;
	border-top-color: #FFF;
	border-right-color: #FFF;
	border-bottom-color: #FFF;
	border-left-color: #FFF;
	margin-top: 0px;
	margin-right: 5px;
	height: 34.1mm;
	width: 60.5mm;
	display: block;
	vertical-align: top;"  >
	<div align="center"><barcode type="C128A" value="<?php echo $p->getReference(); ?>" style="width:30mm; height:8mm"></barcode></div><br />
    <?php echo $p->getDesignation(); ?> <strong><?php echo $p->getQualite(); ?></strong><br />
	<strong><?php echo $p->getPrixHT(); ?>&euro; HT</strong><br />
	<?php echo $p->getGroupeName(); ?> 
	<strong><?php echo $p->getGenreName(); ?></strong>
	</td>

<?php
	if (is_int($i/3) && $i != $nb_ligne  && ($i > 0)){
	echo "<tr>";
	 //echo "debut2";
	}
	
	$i++;
	 //echo $i;

	}
}
?>
</tr>
</table>


</body>

 </page> 
