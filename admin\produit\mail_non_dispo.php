<?php 

$compteur = 0;

$server_name = 'http://'.$_SERVER['SERVER_NAME'];

$result = $database->prepare("SELECT * FROM non_dispo INNER JOIN static_commandes ON id_command = id WHERE statut = 'attente' AND dispo = '2'
") or die ("requete r1 invalid");
$result->execute();
while ($tab = $result->fetch()) {
	
$mail_client = $tab['client_email'];

// LANGUE
$result2 = $database->prepare("SELECT * FROM adresses_facturation INNER JOIN pays ON af_pays = nom_pays WHERE af_emailclient = '$mail_client'
") or die ("requete r2 invalid");
$result2->execute();
$tab2 = $result2->fetch();
$langue = $tab2['langue_pays'];
 
$content_html_eng = '
<table style="font-family:Arial;" cellspacing="0" cellpadding="0" border="0" bgcolor="#ffffff" width="100%" style="font-size:0; margin: 0px; padding: 0px; border-collapse: collapse; border-spacing: 0;">
  <tr>
    <td valign="top" align=center>
<table cellspacing="0" cellpadding="0" border="0" bgcolor="#cccc66" style="font-size:0; margin: 0px; padding: 0px; border-collapse: collapse; border-spacing: 0;">
  <tr>
    <td valign="top" rowspan="3" ><a href="http://jeep-dodge-gmc.com/smi/" ><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_01.gif" style="display:block;"/></a></td>
    <td colspan="3" valign="top"><a href="mailto:<EMAIL>" ><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_02.gif" style="display:block;"/></a></td>
    <td colspan="1" valign="top"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_03.gif" style="display:block;"/></td>
  </tr>
  <tr>
    <td valign="top"><a href="https://twitter.com/jeep_dodge_gmc"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_04.gif" style="display:block;"/></a></td>
    <td valign="top"><a href="https://plus.google.com/u/0/107839788516299581970/about"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_05.gif" style="display:block;"/></a></td>
    <td valign="top"><a href="https://www.facebook.com/surplus.militaitresetindustriels"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_06.gif" style="display:block;"/></a></td>
    <td valign="top"><a href="http://jeep-dodge-gmc.com/smi/index.php?page=newsletter"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_07.gif" style="display:block;"/></a></td>
  </tr>
  <tr>  
    <td colspan="4" valign="top"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_08.gif" style="display:block;"/></td>
  </tr> 
</table>

<table style="background-color:white; margin-left:auto;margin-right:auto; width:650px; border:solid black;">
	<tr >
        <td colspan=2 style="background-color:black; color:white;">
            <h1 style="margin-top:10px; margin-bottom:10px; text-align:center;">Back in Stock</h1>
        </td>
    </tr>	
	<tr >
        <td>
                <img src="http://jeep-dodge-gmc.com/smi/public/images/produits/'.$tab['reference'].'.jpg" style="display:block; width: 280px;"/>
        </td>
         <td>
                Parts number : '.$tab['reference'].'<br>
				'.$tab['designation'].'<br>
                <strong>Not Available in previous order is back in stock !</strong>
        </td>
    </tr>
    	<tr >
        <td colspan=2 style="padding-left:10px; padding-top:10px;">
                    If you want always this part you need to pass a new order :<br>
					<br>
                	1 - Click on this link : <br>
					<a href="http://www.jeep-dodge-gmc.com/smi/index.php?page=search&in=ALL&method=AND&valuetosearch='.$tab['reference'].'" >http://www.jeep-dodge-gmc.com/smi/index.php?page=search&in=ALL&method=AND&valuetosearch='.$tab['reference'].' </a><br> 
					2 - Add it to your basket <br>
                    3 - If you are in France or EU Area, shipping cost are free -> Click on "Take on the spot" and comment add "Not available in my previous order n&deg;'.$tab['id_command'].'"<br>
                    4 - If you have a credit please add it in comment "I have a credit ...&euro; order n&deg;'.$tab['id_command'].'", we will deduce it on invoice.
        </td>
    </tr>
        	<tr >
        <td colspan=2 style="padding-left:10px; padding-top:20px; padding-bottom:10px;">
        		If you want more informations please contact us by e-mail : <EMAIL><br>
				<br>
                Commercial Services - Jeep-Dodge-Gmc
				<br>
        </td>
    </tr>
</table>
</td>
</tr>
</table>

';

$content_html_fr = '
<table style="font-family:Arial;" cellspacing="0" cellpadding="0" border="0" bgcolor="#ffffff" width="100%" style="font-size:0; margin: 0px; padding: 0px; border-collapse: collapse; border-spacing: 0;">
  <tr>
    <td valign="top" align=center>
<table cellspacing="0" cellpadding="0" border="0" bgcolor="#cccc66" style="font-size:0; margin: 0px; padding: 0px; border-collapse: collapse; border-spacing: 0;">
  <tr>
    <td valign="top" rowspan="3" ><a href="http://jeep-dodge-gmc.com/smi/" ><img src="http://jeep-dodge-gmc.com/smi/newsletter/images/NEWSLETTER-MODELE_01.gif" style="display:block;"/></a></td>
    <td colspan="3" valign="top"><a href="mailto:<EMAIL>" ><img src="http://jeep-dodge-gmc.com/smi/newsletter/images/NEWSLETTER-MODELE_02.gif" style="display:block;"/></a></td>
    <td colspan="1" valign="top"><img src="http://jeep-dodge-gmc.com/smi/newsletter/images/NEWSLETTER-MODELE_03.gif" style="display:block;"/></td>
  </tr>
  <tr>
    <td valign="top"><a href="https://twitter.com/jeep_dodge_gmc"><img src="http://jeep-dodge-gmc.com/smi/newsletter/images/NEWSLETTER-MODELE_04.gif" style="display:block;"/></a></td>
    <td valign="top"><a href="https://plus.google.com/u/0/107839788516299581970/about"><img src="http://jeep-dodge-gmc.com/smi/newsletter/images/NEWSLETTER-MODELE_05.gif" style="display:block;"/></a></td>
    <td valign="top"><a href="https://www.facebook.com/surplus.militaitresetindustriels"><img src="http://jeep-dodge-gmc.com/smi/newsletter/images/NEWSLETTER-MODELE_06.gif" style="display:block;"/></a></td>
    <td valign="top"><a href="http://jeep-dodge-gmc.com/smi/index.php?page=newsletter"><img src="http://jeep-dodge-gmc.com/smi/newsletter/images/NEWSLETTER-MODELE_07.gif" style="display:block;"/></a></td>
  </tr>
  <tr>  
    <td colspan="4" valign="top"><img src="http://jeep-dodge-gmc.com/smi/newsletter/images/NEWSLETTER-MODELE_08.gif" style="display:block;"/></td>
  </tr> 
</table>

<table style="background-color:white; margin-left:auto;margin-right:auto; width:650px; border:solid black;">
	<tr >
        <td colspan=2 style="background-color:black; color:white;">
            <h1 style="margin-top:10px; margin-bottom:10px; text-align:center;">R&eacute;approvisionnement</h1>
        </td>
    </tr>	
	<tr >
        <td>
                <img src="http://jeep-dodge-gmc.com/smi/public/images/produits/'.$tab['reference'].'.jpg" style="display:block; width: 280px;"/>
        </td>
         <td>
                R&eacute;f&eacute;rence : '.$tab['reference'].'<br>
				'.$tab['designation'].'<br>
                <strong>Cette pi&egrave;ce non disponible lors d\'une pr&eacute;c&eacute;dente commande est de nouveau en stock !</strong>
        </td>
    </tr>
    	<tr >
        <td colspan=2 style="padding-left:10px; padding-top:10px;">
                    Si vous souhaitez toujours cette pi&egrave;ce voici comment proc&eacute;der :<br>
					<br>
                	1 - Cliquer sur le lien suivant : <br><a href="http://www.jeep-dodge-gmc.com/smi/index.php?page=search&in=ALL&method=AND&valuetosearch='.$tab['reference'].'">http://www.jeep-dodge-gmc.com/smi/index.php?page=search&in=ALL&method=AND&valuetosearch='.$tab['reference'].'</a><br>
					2 - Ajouter l\'article &agrave; votre panier.<br>
                    3 - Si vous &ecirc;tes livr&eacute; en France ou dans la Zone Europ&eacute;enne les frais de port sont gratuit par un maximum de 20kg.<br>
                    	Pour cela il suffit de cliquer sur "Retrait en magasin" et de mettre en commentaire "Reliquat commande N&deg; '.$tab['id_command'].'"<br>
                    4 - Si vous avez un cr&eacute;dit merci de le notifier en commentaire lors du choix du mode de paiement nous ferons la d&eacute;duction au moment de la facturation.     
        </td>
    </tr>
        	<tr >
        <td colspan=2 style="padding-left:10px; padding-top:20px; padding-bottom:10px;">
        		Si vous avez besoin de plus d\'information merci de nous contacter par e-mail : <EMAIL><br>
				<br>
                Le service commercial - Jeep-Dodge-Gmc
				<br>
        </td>
    </tr>
</table>
</td>
</tr>
</table>

';
		
/*
-- Instruction Module envoi d'un email en php -- 

Les variables :

*/
			
$exp_mail = "<EMAIL>";
$exp_nom = "Jeep-Dodge-Gmc.com";

		
$mail = $mail_client; // Déclaration de l'adresse de destination.
$test_mail = preg_match("#^[a-z0-9._-]+@(hotmail|live|msn).[a-z]{2,4}$#", $mail);
if ($test_mail === "1"){ // On filtre les serveurs qui présentent des bogues.
$passage_ligne = "\r\n";
}else{
$passage_ligne = "\n";
}
 
//=====Définition du sujet.
$sujet_eng = 'Back in Stock ! Part number : '.$tab['reference'];
$sujet_fr = 'De nouveau en stock ! Reference : '.$tab['reference'];

//=====Création du header de l'e-mail.
$header = 'MIME-Version: 1.0' . "".$passage_ligne;
$header.= 'Content-type: text/html; charset=iso-8859-1' . "".$passage_ligne;
$header.= "From: \"".$exp_nom."\"<".$exp_mail.">".$passage_ligne;
$header.= "Reply-To: \"".$exp_nom."\"<".$exp_mail.">".$passage_ligne;
$header.= 'Cc: "contact@jeep-dodge-gmc"'.$passage_ligne;

//=====Envoi de l'e-mail.
if ($langue == "fr"){
mail($mail,$sujet_fr,$content_html_fr,$header);
} else {
mail($mail,$sujet_eng,$content_html_eng,$header);	
}
 
//==========
$date = date("Y-m-d");
$id_nd = $tab['id_nd'];

$database->query("

UPDATE non_dispo 
SET statut = 'informé',
date_statut = '$date'
WHERE id_nd = '$id_nd'
") or die ("requete insert sql3 invalid");

$compteur++;

}

echo "Nombre de clients informé par e-mail : ".$compteur;

?>