
<h1><img src="<?php echo $basePath; ?>public/images/non_dispo.png" alt=""/>Aperçu du produit ND</h1>

<?php
if(isset($_GET['statut'])){

$statut = $_GET['statut'];
$id_nd = $_GET['id_nd'];
$date_statut = date("Y-m-d");

$req = $database->query("

UPDATE non_dispo SET statut = '$statut',date_statut = '$date_statut' , moyen_util ='mail' WHERE id_nd = '$id_nd'

") or die ("requete update statut");

}

if(isset($_GET['nd_del'])){

	$nd_del = $_GET['nd_del'];
	$ref = $_GET['ref'];
	$idcde = $_GET['idcde'];
	$delete_nd = $database->query("DELETE FROM non_dispo WHERE id_nd = '$nd_del'") or die ("delete_nd invalid");
	$sql2 = $database->query("
	UPDATE static_lignes_commandes
	SET 
	produit_nd = '0'
	WHERE commande_id = '$idcde' AND produit_reference = '$ref'
	") or die ("requete insert sql2 invalid");
	
}

if(isset($_GET['moyen_util'])){

$date_statut2 = explode("/", $_GET['date_statut']);
$date_statut = $date_statut2[2]."-".$date_statut2[1]."-".$date_statut2[0];
$moyen_util = $_GET['moyen_util'];
$id_nd = $_GET['id_nd'];

$req = $database->query("

UPDATE non_dispo SET moyen_util = '$moyen_util', date_statut = '$date_statut' WHERE id_nd = '$id_nd'

") or die ("requete update moyen_util");

}

if(isset($_POST['commentaire'])){

$commentaire = htmlentities($_POST['commentaire'], ENT_QUOTES);
$id_nd = $_GET['id_nd'];

$req = $database->query("

UPDATE non_dispo SET commentaire_nd = '$commentaire' WHERE id_nd = '$id_nd'

") or die ("requete update commentaire");

}

if(isset($_GET["id"])){
		
		$ref = $_GET['id'];
		$prix = "<span style='color:red;'><br />Prix non valide (Possible changement de ref) </span>";
		
		$result = $database->prepare("
		SELECT * FROM non_dispo INNER JOIN static_commandes ON non_dispo.id_command = static_commandes.id WHERE non_dispo.reference ='$ref'
		") or die ("requete r1 invalid");
		$result->execute();
			
			$result2 = $database->prepare("
			SELECT * FROM produits WHERE referenceproduit = '$ref'
			") or die ("requete r2 invalid");
			$result2->execute();
			while ($tab2 = $result2->fetch()) {
			$prix = number_format($tab2['prixproduiteuro'],2, '.', '');
			}
		
		$result5 = $database->prepare("
		SELECT * FROM non_dispo WHERE reference ='$ref'
		") or die ("requete r5 invalid");
		$result5->execute();
		$designation = $result5->fetch();
			
		echo "<h2><strong>".$ref." - ".$designation['designation']." ".$prix." € HT</strong></h2>";
			
		echo '<table class="liste">';

		echo '<th>Cde</th>';
		echo '<th>Nom</th>';
		echo '<th>Nb ND</th>';
		echo '<th>Email</th>';
		echo '<th>Tel</th>';
		echo '<th>Date ND</th>';
		echo '<th>Qté</th>';
		echo '<th>Statut</th>';
		echo '<th></th>';
		echo '<th></th>';
		echo '<th></th>';
		echo '<th></th>';
		echo '<th></th>';
	
	while ($tab = $result->fetch()) {
	
	echo '<form name="statut_nd" method="GET" action="'.$basePath.'index.php">';
	echo '<input type="hidden" name="page" value="admin" />';
	echo '<input type="hidden" name="action" value="gererprod" />';
	echo '<input type="hidden" name="non_dispo" value="3" />';
	echo '<input type="hidden" name="id" value="'.$ref.'" />';
	echo '<input type="hidden" name="genre" value="'.$_GET['genre'].'" />';
	echo '<input type="hidden" name="id_nd" value="'.$tab['id_nd'].'" />';

		echo '<tr>';
			echo '<td><div align="center"><a href="index.php?page=admin&action=commandes&id='.$tab['id_command'].'">'.$tab['id_command'].' <img src="'.$basePath.'public/images/zoom.png" alt="Voir" title="Voir"/></a></div></td>';
			echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererclient&type=modif&id='.$tab['client_email'].'">'.$tab['client_nomprenom'].' <img src="'.$basePath.'public/images/zoom.png" alt="Voir" title="Voir"/></a></div></td>';
			$result3 = $database->prepare("
			SELECT COUNT(*) AS nb_nd FROM non_dispo INNER JOIN static_commandes ON non_dispo.id_command = static_commandes.id WHERE static_commandes.client_email ='".$tab['client_email']."'
			") or die ("requete r3 invalid");
			$result3->execute();
			while ($tab3 = $result3->fetch()) {
			echo "<td><div align='center'>".$tab3['nb_nd']."</div></td>";
			}
			echo "<td><div align='center'>".substr($tab['client_email'], 0, 5)."...</div></td>";			
			echo "<td><div align='center'>".substr($tab['client_tel'], 0, 50)."</div></td>";
			
			$statut_date3 = explode("-", $tab['date_nd']);
			$statut_date4 = $statut_date3[2]."/".$statut_date3[1]."/".$statut_date3[0];
			
			echo "<td><div align='center'>".$statut_date4."</div></td>";
			echo "<td><div align='center'>".$tab['qteproduit']."</div></td>";
			echo '<td><div align="center"><select name="statut" >';
				echo '<option value="'.$tab['statut'].'" selected=true >'.$tab['statut'].'</option>';
				echo '<option onClick="this.form.submit();" value="informé">Informé</option>';
				echo '<option onClick="this.form.submit();" value="relancé">Relancé</option>';
				echo '<option onClick="this.form.submit();" value="terminé">Terminé</option>';
				echo '<option onClick="this.form.submit();" value="annulé">Annulé</option>';
				echo '<option onClick="this.form.submit();" value="attente">Attente</option>';
			echo '</select></div></td>';
			if ($tab['statut'] != "attente"){
			
				echo '<td>Le <input type="text" style="width: 70px;" name="date_statut" value="';
				
				if ($tab['date_statut'] == "" || $tab['date_statut'] == "0000-00-00"){
				
					echo date('j/m/Y').'" /></td>';
				
				} else {
				
					$statut_date2 = explode("-", $tab['date_statut']);
					$statut_date = $statut_date2[2]."/".$statut_date2[1]."/".$statut_date2[0];
				
					echo $statut_date.'" /></td>';
				
				}
				
				echo '<td><div align="center">Par <select name="moyen_util" >';
					
					if ($tab['moyen_util'] == ""){
					
						echo '<option selected=true value="">Choix</option>';
					
					} else {
					
						echo '<option selected=true value="'.$tab['moyen_util'].'">'.$tab['moyen_util'].'</option>';
					
					}
					echo '<option onClick="this.form.submit();" value="tel">Tél</option>';
					echo '<option onClick="this.form.submit();" value="mail">E-mail</option>';
					echo '<option onClick="this.form.submit();" value="fax">Fax</option>';
					echo '<option onClick="this.form.submit();" value="sms">SMS</option>';
				echo '</select></div></td>';
			
			}
			echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=3&genre='.$_GET['genre'].'&id='.$ref.'&id_nd='.$tab['id_nd'].'&comment=1">';
			
			if ($tab['commentaire_nd'] == ""){
			echo '<img src="'.$basePath.'public/images/comment.png" alt="Commentaire" title="Commentaire"/>';
			} else {
			echo '<img src="'.$basePath.'public/images/comment_2.png" alt="Contient_Commentaire" title="Contient_Commentaire"/>';
			}
			echo '</a></div></td>';
			
			$exception = array('"', '&quot;', '&', '&amp;', '&apos;', "'", '#039;');
			$designation = str_replace($exception, "", $tab['designation']);
		
			$message = "	
			Bonjour, %0D%0A 
			%0D%0A 
			La référence ".$tab['reference']." ".$designation." non disponible lors d'une precedente commande, est à nouveau en stock au prix de ".$prix." euros HT.%0D%0A 
			De plus, vous profitez des frais de port gratuit sur l'expédition de votre reliquat ainsi que de l'ensemble des articles ajoutés à celui-ci pour un poids maximum de 20kg.%0D%0A 
			Vous pouvez commander directement sur notre site internet à l'aide de cette procedure :	http://jeep-dodge-gmc.com/smi/client/commande/view/reliquat.pdf
			%0D%0A
			Nous restons à votre disposition pour plus de renseignements par mail : <EMAIL>%0D%0A 
			Cordialement, %0D%0A 
			Service commercial.%0D%0A 
			%0D%0A 
			-------------------------------------%0D%0A 
			Surplus Militaires et Industriels %0D%0A 
			D1092, La gare %0D%0A 
			38840 La Sone %0D%0A 
			FRANCE %0D%0A 
			Tel : +33 (0) *********** %0D%0A 
			Fax : +33 (0) *********** %0D%0A 
			@ : <EMAIL> %0D%0A 
			Web : www.jeep-dodge-gmc.com %0D%0A 
			-------------------------------------  %0D%0A ";
			
			$message_eng = "
			Hello, %0D%0A 
			%0D%0A 
			Reference ".$tab['reference']." ".$designation." not available from a previous order, is back in stock in price of ".$prix." euro without tax.%0D%0A 
			If you want always this part, thank you for contacting us by mail : <EMAIL> or by phone : 0033 ***********%0D%0A 
			%0D%0A 
			Best regards, %0D%0A 
			Commercial service.%0D%0A 
			%0D%0A 
			%0D%0A  
			Bonjour, %0D%0A 
			%0D%0A 
			La référence ".$tab['reference']." ".$designation." non disponible lors d'une precedente commande, est à nouveau en stock au prix de ".$prix." euros HT.%0D%0A 
			De plus, vous profitez des frais de port gratuit sur l'expédition de votre reliquat ainsi que de l'ensemble des articles ajoutés à celui-ci pour un poids maximum de 20kg.%0D%0A 
			Vous pouvez commander directement sur notre site internet à l'aide de cette procedure :	http://jeep-dodge-gmc.com/smi/client/commande/view/reliquat.pdf
			%0D%0A
			Nous restons à votre disposition pour plus de renseignements par mail : <EMAIL>%0D%0A 
			Cordialement, %0D%0A 
			Service commercial.%0D%0A 
			%0D%0A  
			-------------------------------------%0D%0A 
			Surplus Militaires et Industriels %0D%0A 
			D1092, La gare %0D%0A 
			38840 La Sone %0D%0A 
			FRANCE %0D%0A 
			Phone : +33 (0) *********** %0D%0A 
			Fax : +33 (0) *********** %0D%0A 
			@ : <EMAIL> %0D%0A 
			Web : www.jeep-dodge-gmc.com %0D%0A 
			-------------------------------------  %0D%0A ";
			
			$ad_liv = explode("<br />", $tab['commande_adressefacturation']);
			
			if ($ad_liv[3] == "France"){
			echo '<td><div align="center"><a href="mailto:'.$tab['client_email'].'?subject=Votre pièce '.$tab['reference'].' est disponible&body='.$message.'"><img src="'.$basePath.'public/images/email.png" alt="mail" title="mail"/></a></div></td>';
			}else{
			echo '<td><div align="center"><a href="mailto:'.$tab['client_email'].'?subject=Votre pièce '.$tab['reference'].' est disponible&body='.$message_eng.'"><img src="'.$basePath.'public/images/email.png" alt="mail" title="mail"/></a></div></td>';
			}
			
			$nd_commentaire = $tab['commentaire_nd'];
			echo '<td><div align="center"><a onclick="return(confirm(\'Etes-vous sur de vouloir supprimer ce non-dispo?\'));" href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=3&genre='.$_GET['genre'].'&nd_del='.$tab['id_nd'].'&id='.$_GET['id'].'&ref='.$tab['reference'].'&idcde='.$tab['id'].'"> <img src="'.$basePath.'public/images/b_drop.png" alt="delete" title="Supprimer"/></a></div></td>';
			
	echo '</form>';
	}
	
echo '</table>';


if (isset($_GET['comment']) && $_GET['comment'] == "1"){

		$id_nd2 = $_GET['id_nd'];
		
		$result3 = $database->prepare("
		SELECT * FROM non_dispo WHERE id_nd ='$id_nd2'
		") or die ("requete r1 invalid");
		$result3->execute();
		
		while ($tab3 = $result3->fetch()) {
		$nd_commentaire = $tab3['commentaire_nd'];
		}
?>
				<script type="text/javascript">
				//<![CDATA[
					function cachedivpopupdevis() {
						document.getElementById("commentaire_nd").style.display = "none"; 
					}
					function limit() {
					    max = 500;
					    commentaire = document.form_commentaire_nd.commentaire;
					    limite = document.form_commentaire_nd.reste;
					    if (commentaire.value.length > max)
							commentaire.value = commentaire.value.substring(0, max);
					    else
							limite.value = max - commentaire.value.length;
				    }
				//]]>
				</script>
				<div id="commentaire_nd">
					<center>
					<table width="100%" border="0" height="11">
						<tr><td align="right">
							<a title="Fermer ce message" onmouseover="document.crpopup.src='<?php echo $basePath;?>public/images/croix_rouge_claire.gif'" onmouseout="document.crpopup.src='<?php echo $basePath;?>public/images/croix_rouge.gif'"><img src="<?php echo $basePath;?>public/images/croix_rouge.gif" name="crpopup" border="0" onclick="cachedivpopupdevis()" /></a>
						</td></tr>
					</table>
					<table width="100%" height="90" border="0" align="center">
						<tr>
							<td align="center">
								Ajouter un commentaire<br /><br />
								<table width="100%" border="0">
									<form action="index.php?page=admin&action=gererprod&non_dispo=3&id=<?php echo $_GET['id']; ?>&id_nd=<?php echo $_GET['id_nd']; ?>" name="form_commentaire_nd" method="POST">
									<tr>
										<td width="150" valign="top" align="left">Commentaire<br />Reste <input readonly="readonly" type="text" name="reste" size="1" maxlength="3" value="500"> caract&egrave;res.</td>
										<td align="left"><textarea name="commentaire" cols="35" rows="5" onKeyDown="limit();" onKeyUp="limit();"><?php echo $nd_commentaire; ?></textarea></td>
									</tr>
									<tr>
										<td colspan="2" align="center"><input type="submit" name="add_com" value="Ajouter" /></td>
									</tr>
									</form>

								</table><br />
							</td>
						</tr>
					</table>
					</center>
				</div>
<?php
	}
}

?>
<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>index.php?page=admin&action=gererprod&genre=<?php echo $_GET['genre']; ?>&non_dispo=1"><?php echo $translate->_('Retour');?></a>
</p>