<?php
/**
 * programme qui modifie un produit de la base de donnée
 */
?>
<h1><img src="<?php echo $basePath; ?>public/images/produit_modifier.png" alt=""/> Modification d'un produit</h1>

<?php
if(!isset($_POST["modifier"])){

$ref = $_GET['ref'];

$result = $database->prepare("SELECT * FROM produits WHERE referenceproduit = '$ref'") or die ("requete r1 invalid");
//Requete et remplissage tableau jpgraph
$result2 = $database->prepare("
SELECT sum(produit_qte) AS produit_qte, YEAR(`commande_date`) AS ANNEE FROM static_lignes_commandes INNER JOIN static_commandes 
ON static_lignes_commandes.commande_id = static_commandes.id
WHERE UPPER(produit_reference) = '$ref'
GROUP BY ANNEE
") or die ("requete r2 invalid");
$result2->execute();
	while ($tab2 = $result2->fetch()) {
$produit_vendu[] = $tab2['produit_qte'];
$annee[] = $tab2['ANNEE'];
	}
//on affiche la formulaire pour modifier les données
$result->execute();
	while ($tab = $result->fetch()) {		
		?>
		<div class="form">
		<form method="POST" action="<?php echo $basePath; ?>index.php?page=admin&action=gererprod&type=modif_par_ref" enctype="multipart/form-data">
			<input type="hidden" name="modifier" value="oui" />
			<input type="hidden" name="idproduit" value="<?php echo $tab['idproduit']; ?>" />
		<fieldset class="input">
			<ol><li>
				<label>Genre : </label>
				<label class="gras"><?php echo $tab['genre']; ?></label>
			</li></ol>
			<ol><li>
				<label>Groupe : </label>
				<label class="gras"><?php echo $tab['groupe']; ?></label>
			</li></ol>
			<ol><li>
				<label>Référence : </label>
				<input type="text" name="referenceproduit" value="<?php echo $tab['referenceproduit']; ?>" />
			</li></ol>
			<ol><li>
				<label>Désignation : </label>
				<input type="text" name="descriptionproduit" value="<?php echo $tab['descriptionproduit']; ?>" />
			</li></ol>
			<ol><li>
				<label>Qualité : </label>

				<select name="qualite">

					<option value="<?php echo $tab['qualite']; ?>" <?php if ($tab['qualite'] == "NEW") echo 'selected="selected"'; ?> >Neuf</option>
					<option value="<?php echo $tab['qualite']; ?>" <?php if ($tab['qualite'] == "NOS")echo 'selected="selected"'; ?> >Nos</option>
					<option value="<?php echo $tab['qualite']; ?>" <?php if ($tab['qualite'] == "USE")echo 'selected="selected"'; ?> >Occasion</option>
					<option value="<?php echo $tab['qualite']; ?>" <?php if ($tab['qualite'] == "REC")echo 'selected="selected"'; ?> >Reconditionné</option>

				</select>
			</li></ol>		
			<ol><li>
				<label>Stock : </label>

				<select name="promo">

					<option value="<?php echo $tab['promo']; ?>" <?php if ($tab['promo'] == "o") echo 'selected="selected"'; ?> >Stock non vérifié, jamais commandé ( o )</option>
					<option value="<?php echo $tab['promo']; ?>" <?php if ($tab['promo'] == "oo")echo 'selected="selected"'; ?> >Pas de stock, jamais commandé ( oo )</option>
					<option value="<?php echo $tab['promo']; ?>" <?php if ($tab['promo'] == "ooo")echo 'selected="selected"'; ?> >Introuvable ( ooo )</option>
					<option value="<?php echo $tab['promo']; ?>" <?php if ($tab['promo'] == "net" || $tab['promo'] == "NET")echo 'selected="selected"'; ?> >Achat ou  faible marge ( net )</option>
					<option value="<?php echo $tab['promo']; ?>" <?php if ($tab['promo'] == "x")echo 'selected="selected"'; ?> >Faible stock ( x )</option>
					<option value="<?php echo $tab['promo']; ?>" <?php if ($tab['promo'] == "xx")echo 'selected="selected"'; ?> >Moyen stock ( xx )</option>
					<option value="<?php echo $tab['promo']; ?>" <?php if ($tab['promo'] == "xxx")echo 'selected="selected"'; ?> >Enorme stock ( xxx )</option>

				</select>
			</li></ol>				
			<ol><li>
				<label>Prix HT € : </label>
				<input type="text" name="prixproduiteuro" value="<?php echo $tab['prixproduiteuro']; ?>" />
			</li></ol>
			<ol><li>
				<label>Plus de détail : </label>
				<?php
				echo '<a href="index.php?page=admin&action=gererprod&type=description&categ='.strtolower($tab['genre']).'&ref='.$tab['referenceproduit'].'">';
				echo "<img src=\"".Zend_Registry::get('basePath')."public/images/zoom.png\" height=\"16\" width=\"16\"/>";
				echo '</a>';
				?>
			</li></ol>
		</fieldset>
		<fieldset class="submit">	
				<input class="btn_submit" type="submit" value="Modifier" />
		</fieldset>	
		</form>
		</div>
		<?php

		include ("lib/graph/src/jpgraph.php");
		include ("lib/graph/src/jpgraph_bar.php");
		/*
		$ydata = $produit_vendu;

		// Creation du graphique
		$graph = new Graph(300,200);
		$graph->SetScale("textlin");

		// Création du système de points
		$lineplot=new BarPlot($ydata);

		// On rajoute les points au graphique
		$graph->Add($lineplot);

		// Affichage
		$graph->Stroke('public/images/produits/graphique_secteur.png');
		
		echo '<img src="public/images/produits/graphique_secteur.png" alt=""/>';
		*/
		// Construction du graphique
		// Spécification largeur et hauteur
		$graph = new Graph(400,250);

		// Réprésentation linéaire
		$graph->SetScale("textlin");

		// Ajouter une ombre au conteneur
		$graph->SetShadow();

		// Fixer les marges
		$graph->img->SetMargin(40,30,25,40);

		// Création du graphique histogramme
		$bplot = new BarPlot($produit_vendu);

		// Spécification des couleurs des barres
		$bplot->SetFillColor(array('red', 'green', 'blue'));
		// Une ombre pour chaque barre
		$bplot->SetShadow();

		// Afficher les valeurs pour chaque barre
		$bplot->value->Show();
		// Modifier le rendu de chaque valeur
		$bplot->value->SetFormat('%d ventes');

		// Ajouter les barres au conteneur
		$graph->Add($bplot);

		// Le titre
		$graph->title->Set("Ventes par annees");
		$graph->title->SetFont(FF_FONT1,FS_BOLD);

		// Titre pour l'axe horizontal(axe x) et vertical (axe y)
		$graph->xaxis->title->Set("Annees");
		$graph->yaxis->title->Set("Nombre de ventes");

		$graph->yaxis->title->SetFont(FF_FONT1,FS_BOLD);
		$graph->xaxis->title->SetFont(FF_FONT1,FS_BOLD);

		// Légende pour l'axe horizontal
		$graph->xaxis->SetTickLabels($annee);

		// Afficher le graphique
		$graph->Stroke('public/images/produits/graphique_secteur.png');
		
		echo '<div align="center"><img src="public/images/produits/graphique_secteur.png" alt=""/></div>';
		}		
		
	} else {
	
$idproduit = $_POST['idproduit'];
$referenceproduit = $_POST['referenceproduit'];
$descriptionproduit = htmlspecialchars($_POST['descriptionproduit'], ENT_QUOTES);
$prixproduiteuro = $_POST['prixproduiteuro'];
$qualite = $_POST['qualite'];
$promo = $_POST['promo'];

$result2 = $database->prepare("
UPDATE produits
SET referenceproduit = '$referenceproduit', descriptionproduit = '$descriptionproduit', prixproduiteuro = '$prixproduiteuro', qualite = '$qualite', promo = '$promo'
WHERE idproduit='$idproduit'
") or die ("r2 invalid");
$result2->execute();
	
		//récuparation des nouvelles données
		$result3 = $database->prepare("SELECT * FROM produits WHERE idproduit = '$idproduit'") or die ("requete r3 invalid");
		//modification réussi réussi
		$result3->execute();
		while ($tab2 = $result3->fetch()) {	
		?>
		<div class="valide messageBox">
			Le produit a bien été modifié : <br /><br />
	
			<b>Désignation : </b><?php echo $tab2['descriptionproduit']; ?><br />
			<b>Qualité : </b><?php echo $tab2['qualite']; ?><br />
			<b>Référence : </b><?php echo $tab2['referenceproduit']; ?><br />
			<b>Prix HT € : </b><?php echo $tab2['prixproduiteuro']; ?><br />
				
		</div>
		<?php
		}
	}


echo '<p class="bouton_retour"><a href="',$basePath,'index.php?page=admin&action=gererprod">Retour</a></p>';

?>
