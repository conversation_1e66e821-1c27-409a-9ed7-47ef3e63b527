<?php
/**
 * programme qui modifie un produit de la base de donnée
 */
?>
		<!-- Generateur de code bar -->
<?php
		/*
		<script type="text/javascript" src="<?php echo $basePath;?>lib/barcode/sample/jquery-1.3.2.min.js"></script>  
		<script type="text/javascript" src="<?php echo $basePath;?>lib/barcode/jquery-ui-1.7.custom.min.js"></script> 	
		*/
?>		
		<script type="text/javascript">
		//<![CDATA[
			function addtocart2(produit) {
				document.location = "<?php echo Zend_registry::get("basePath"); ?>panier/ajouter/"+produit+"/quantite/1";
			}
		//]]>
		</script>
<script type="text/javascript" src="<?php echo $basePath;?>lib/barcode/jquery-barcode-last.min.js"></script>


<table style="margin-left: 190px;">
<tr>
<td><a class="ajout_mini" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&ajout=1"></a></td>
<td><a class="nd_mini" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&non_dispo=1"></a></td>
<td><a class="devis_mini" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&sur_devis=1"></a></td>
<td><a class="recherche_mini" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&search=1"></a></td>

</tr>
<tr>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&ajout=1">Ajout</a></td>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&non_dispo=1">ND</a></td>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&sur_devis=1">Devis</a></td>
<td align="center"><a class="a_prod" href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&search=1">Recherche</a></td>


</tr>
</table>		
<h1><img src="<?php echo $basePath; ?>public/images/loupe_gm.png" alt=""/>Nouvelle recherche</h1>
    						<div style="margin-left:300px;">
							<form method="get" style="float:left;" action="<?php echo $basePath.'index.php';?>">
							
								<input type="hidden" name="page" value="admin" />
								<input type="hidden" name="action" value="gererprod" />
								<img src="<?php echo $basePath; ?>public/images/loupe.png" alt="loupe"/>
								<input type="text" name="valuetosearch" value="" placeholder="Recherche..." />
								<input type="hidden" name="in" value="ALL" /> 
								<input type="hidden" name="method" value="AND" /> 
								<input type="submit" value="Ok" /> </br>
							
							</form>
							<form method="get" style="float:left; margin-left:50px;" action="<?php echo $basePath.'index.php';?>">
							
								<input type="hidden" name="page" value="admin" />
								<input type="hidden" name="action" value="gererprod" />
								<img src="<?php echo $basePath; ?>public/images/arrow_undo.png" alt="loupe"/>
								<input type="hidden" name="valuetosearch" value="<?php if(isset($_GET['valuetosearch'])){ echo $_GET['valuetosearch']; } ?>" />
								<input type="hidden" name="in" value="ALL" /> 
								<input type="hidden" name="method" value="AND" /> 
								<input type="submit" value="Retour" /> </br>
							
							</form>
							</div><br />
<h1><img src="<?php echo $basePath; ?>public/images/produit_modifier.png" alt=""/> Modification du produit</h1>
<div class="info messageBox">
 - Mettre un produit en echange standard ajouter : <strong>ECHSTD</strong> en Commentaire<br />
 - Mettre un produit en commande ajouter : <strong>1</strong> en Qté à cder<br />
 - Mettre un produit sur devis ajouter : <strong>-1</strong> dans Prix HT &euro;<br />
 - Ajouter une taille de vêtements : Renseigner les champs <strong>Taille</strong> et <strong>Désignation</strong> puis <strong>DUPLIQUER</strong><br />
</div>		

<?php
require_once(dirname(__FILE__).'/../../class/produit_class.php');
require_once(dirname(__FILE__).'/../../class/categorie_class.php');

include ("lib/graph/src/jpgraph.php");
include ("lib/graph/src/jpgraph_bar.php");
include ("lib/graph/src/jpgraph_line.php");

//on affiche la formulaire pour modifier les données

if (isset($_GET["ref"])){
 $id = $_GET["ref"];
 $prod = produitDB::getProduitByRef2($id);
}

if (isset($_POST["ref"])){
 $id = $_POST["ref"];
 $prod = produitDB::getProduitByRef2($id);
}

if (isset($_GET["id"]) && $_GET["id"] != ""){
 $id = $_GET["id"];
 $prod = produitDB::getProduitById2($id);
} 

if (isset($_POST["reference"])){
 $id = strtoupper($_POST["reference"]);
 $prod = produitDB::getProduitByRef2($id);
 
}
if(isset($_GET["dupliquer"])) //on a modifier les données du formulaire, on sauvegarde dans la base de données

{

	if($prod instanceof Produit) {
		
		$image = "";
		if ($_POST['taille'] == ""){
		$prod->setReference(strtoupper($_POST['reference']));
		}else{
		$prod->setReference(strtoupper($_POST['reference']).'/'.$_POST['taille']);
		}
		$prod->setDesignation($_POST['designation']);
		$prod->setPrixHT($_POST['prix']);
		$prod->setGenreName($_POST['genre']);
		
		$groupe = explode("-", $_POST['groupe']);
		$prod->setGroupeName2($groupe[1]);
		$prod->setGroupeId($groupe[0]);
		
		$prod->setPromotion($_POST['promo']);
		$prod->setImage($image);
		$prod->setTaille($_POST['taille']);
		$prod->setType($_POST['type']);
		if($_POST['qualite'] != "vide"){
		
		$prod->setQualite($_POST['qualite']);
		
		}
			
		$prod->setJeepest($_POST['jeepest']);
		$prod->setDesmet($_POST['desmet']);
		$prod->setGsaa($_POST['gsaa']);
		$prod->setAutre_frs($_POST['autre_frs']);
		$prod->setMiltec($_POST['miltec']);
			
		$prod->setCommande($_POST['commande']);
		$prod->setPp($_POST['pp']);
		$prod->setPrix_frs($_POST['prix_frs']);
		$prod->setEmpl_comment($_POST['empl_comment']);
		$prod->setEmpl_principal($_POST['empl_principal']);
		$prod->setEmpl_secondaire($_POST['empl_secondaire']);
		
		$date_liv = explode("/",$_POST['date_livraison']);
		
		$prod->setDate_livraison($date_liv[2]."-".$date_liv[1]."-".$date_liv[0]);
		$prod->setQte_livraison($_POST['qte_livraison']);
		
		$groupe_commun_jeep = explode("/",$_POST['prod_commun_jeep']);
		$groupe_commun_dodge = explode("/",$_POST['prod_commun_dodge']);
		$groupe_commun_gmc = explode("/",$_POST['prod_commun_gmc']);
		$groupe_commun_chevr = explode("/",$_POST['prod_commun_chevr']);
		$groupe_commun_r2087 = explode("/",$_POST['prod_commun_r2087']);
		
		$prod->setProd_commun_r2087($groupe_commun_r2087[0]);
		$prod->setProd_commun_jeep($groupe_commun_jeep[0]);
		$prod->setProd_commun_dodge($groupe_commun_dodge[0]);
		$prod->setProd_commun_gmc($groupe_commun_gmc[0]);
		$prod->setProd_commun_chevr($groupe_commun_chevr[0]);
		
		$prod->setProd_commun_r2087_name($groupe_commun_r2087[1]);
		$prod->setProd_commun_jeep_name($groupe_commun_jeep[1]);
		$prod->setProd_commun_dodge_name($groupe_commun_dodge[1]);
		$prod->setProd_commun_gmc_name($groupe_commun_gmc[1]);
		$prod->setProd_commun_chevr_name($groupe_commun_chevr[1]);
		
		if (isset($_SESSION['who'])){
		$prod->setWho($_SESSION['who']);
		} else {
		$prod->setWho("");
		}
			
		$res = produitDB::ajoutProduit($prod);
		
	
	} else {
		$res = false;
	}
	
	if($res == true) {
		?>
		<div class="valide messageBox">
			Le produit a bien été dupliqué
		</div>
		<?php
	} else {
		//erreur de modification
		echo '<div class="error messageBox">Erreur lors de la duplication du produit.</div>';
	}
}

if(isset($_GET["modifier"])) //on a modifier les données du formulaire, on sauvegarde dans la base de données

{

	if($prod instanceof Produit) {
		
		$image = "";
		$prod->setIdentifiant($_GET['id']);
		
		if ($_POST['taille'] == ""){
		$prod->setReference(strtoupper($_POST['reference']));
		}else{
		$prod->setReference(strtoupper($_POST['reference']).'/'.$_POST['taille']);
		}
		$prod->setDesignation($_POST['designation']);
		$prod->setPrixHT($_POST['prix']);
		$prod->setGenreName($_POST['genre']);
		
		$groupe = explode("-", $_POST['groupe']);
		$prod->setGroupeName2($groupe[1]);
		$prod->setGroupeId($groupe[0]);
		
		$prod->setPromotion($_POST['promo']);
		$prod->setImage($image);
		$prod->setTaille($_POST['taille']);
		$prod->setType($_POST['type']);
		$prod->setQualite($_POST['qualite']);
			
		$prod->setJeepest($_POST['jeepest']);
		$prod->setDesmet($_POST['desmet']);
		$prod->setGsaa($_POST['gsaa']);
		$prod->setAutre_frs($_POST['autre_frs']);
		$prod->setMiltec($_POST['miltec']);
			
		$prod->setCommande($_POST['commande']);
		$prod->setPp($_POST['pp']);
		$prod->setPrix_frs($_POST['prix_frs']);
		$prod->setEmpl_comment($_POST['empl_comment']);
		$prod->setEmpl_principal($_POST['empl_principal']);
		$prod->setEmpl_secondaire($_POST['empl_secondaire']);
		
		$date_liv = explode("/",$_POST['date_livraison']);
		
		$prod->setDate_livraison($date_liv[2]."-".$date_liv[1]."-".$date_liv[0]);
		$prod->setQte_livraison($_POST['qte_livraison']);
		
		$groupe_commun_r2087 = explode("/",$_POST['prod_commun_r2087']);
		$groupe_commun_jeep = explode("/",$_POST['prod_commun_jeep']);
		$groupe_commun_dodge = explode("/",$_POST['prod_commun_dodge']);
		$groupe_commun_gmc = explode("/",$_POST['prod_commun_gmc']);
		$groupe_commun_chevr = explode("/",$_POST['prod_commun_chevr']);
		
		$prod->setProd_commun_r2087($groupe_commun_r2087[0]);
		$prod->setProd_commun_jeep($groupe_commun_jeep[0]);
		$prod->setProd_commun_dodge($groupe_commun_dodge[0]);
		$prod->setProd_commun_gmc($groupe_commun_gmc[0]);
		$prod->setProd_commun_chevr($groupe_commun_chevr[0]);
		
		$prod->setProd_commun_r2087_name($groupe_commun_r2087[1]);
		$prod->setProd_commun_jeep_name($groupe_commun_jeep[1]);
		$prod->setProd_commun_dodge_name($groupe_commun_dodge[1]);
		$prod->setProd_commun_gmc_name($groupe_commun_gmc[1]);
		$prod->setProd_commun_chevr_name($groupe_commun_chevr[1]);
		
		if (isset($_SESSION['who'])){
		$prod->setWho($_SESSION['who']);
		} else {
		$prod->setWho("");
		}
		
			
		$res = produitDB::modifProduit($prod);
		
	
	} else {
		$res = false;
	}
	
	if($res == true) {
		?>
		<div class="valide messageBox">
			Le produit a bien été modifié
		</div>
		<?php
	} else {
		//erreur de modification
		echo '<div class="error messageBox">Erreur lors de la modification du produit.</div>';
	}
}


	if($prod instanceof Produit) {
		
		?>
		<script language="Javascript">
			function codebar(){
					
			$("#bcTarget").barcode("<?php echo $prod->getReference(); ?>", "code128");
					
			}
		</script>
	
 <?php
 
 $reference = explode("/",$prod->getReference());
 
$filename1 = "public/images/produits/".$reference[0].".jpg";
 
if (file_exists($filename1)){

?>
<div class="form" onmouseover='codebar();'>
		<form method="POST" action="<?php echo $basePath; ?>index.php?page=admin&action=gererprod&type=modif&modifier=oui&id=<?php echo $prod->getIdentifiant(); ?>&valuetosearch=<?php if(isset($_GET['valuetosearch'])){ echo $_GET['valuetosearch']; } ?>" enctype="multipart/form-data">
			<input type="hidden" name="cat" value="<?php echo $prod->getGenreName(); ?>" />
			<input type="hidden" name="sscat" value="<?php echo $prod->getGroupeId(); ?>" />
	<fieldset class="input" style="width:800px; margin-left:-30px;">
	<table width="739" >
  <tr>
    <td width="374" align="center"><img src="<?php echo $basePath; ?>public/images/produits/<?php echo $reference[0]; ?>.jpg" alt="" width="200"></td>
	<td width="178" align="center"><div id="bcTarget" style="width: 100px; border: 1px solid #C0C0C0; margin: 0 auto; text-align: center;"></div></td>
	<td width="175" align="center">
	<p>Stock:
	<?php
	$stock = $prod->getCommande();
	
	if ($stock == "?" || $prod->getPromotion() == "ooo" || $prod->getPrixHT() == "-1"){
	echo '<img src="'.$basePath.'public/images/stock_2.png" title="Produit Introuvable" alt="Introuvable" />';
	}
	if ($prod->getPromotion() == "oo"){
	echo '<img src="'.$basePath.'public/images/stock_2.png" title="Produit Introuvable" alt="Introuvable" />';
	}
	if (!empty($stock) && $stock != "?" && $prod->getPromotion() != "ooo" && $prod->getPrixHT() != "-1"){
	echo '<img src="'.$basePath.'public/images/stock_3.png" title="En cours de reapprovisonnement" alt="En cours de reapprovisonnement" />';
	}
	
	if (empty($stock)){
		if ($prod->getPromotion() == "o"){
	echo '<img src="'.$basePath.'public/images/stock_4.png" title="Stock à verifier" alt="Stock à verifier"/>';
		} else {
	echo '<img src="'.$basePath.'public/images/stock_5.png" title="Produit théoriquement en stock" alt="Produit théoriquement en stock"/>';
		}
	}
	?>
	</td>
  </tr>
</table>
<?php

}else{

?>
<table width="739" style="margin-left: 90px;" >
	<tr>
		<td width="246" ><form action="<?php echo $basePath; ?>admin/produit/upload.php?ref=<?php echo $reference[0]; ?>" class="dropzone"></form></td>
		<td width="246" onmouseover='codebar();'><div id="bcTarget" style="width: 100px; border: 1px solid #C0C0C0; margin: 0 auto; text-align: center;"></div></td>
		<td width="246" align="center">
	
	<?php
	$stock = $prod->getCommande();
	
	if ($stock == "?" || $prod->getPromotion() == "ooo" || $prod->getPrixHT() == "-1"){
	echo '<img src="'.$basePath.'public/images/stock_2.png" title="Produit Introuvable" alt="Introuvable"  width="60" height="60" />';
	echo '<br />Introuvable';
	}
	if ($prod->getPromotion() == "oo"){
	echo '<img src="'.$basePath.'public/images/stock_2.png" title="Produit Introuvable" alt="Introuvable" />';
	echo '<br />Introuvable';
	}
	if (!empty($stock) && $stock != "?" && $prod->getPromotion() != "ooo" && $prod->getPrixHT() != "-1"){
	echo '<img src="'.$basePath.'public/images/stock_3.png" title="En cours de reapprovisonnement" alt="En cours de reapprovisonnement" />';
	echo '<br />En cours de reapprovisonnement';
	}
	
	if (empty($stock)){
		if ($prod->getPromotion() == "o"){
	echo '<img src="'.$basePath.'public/images/stock_4.png" title="Stock à verifier" alt="Stock à verifier"/>';
	echo '<br />Stock à verifier';
	
		} else {
			if ($prod->getPrixHT() != "-1"){
	echo '<img src="'.$basePath.'public/images/stock_5.png" title="Produit théoriquement en stock" alt="Produit théoriquement en stock"/>';
	echo '<br />Produit théoriquement en stock';
			}
		}
	}
	?>
	
	</td>
	</tr>
</table>
<div class="form">
			<form method="POST" action="<?php echo $basePath; ?>index.php?page=admin&action=gererprod&type=modif&modifier=oui&id=<?php echo $prod->getIdentifiant(); ?>&valuetosearch=<?php if(isset($_GET['valuetosearch'])){ echo $_GET['valuetosearch']; } ?>" enctype="multipart/form-data">
			<input type="hidden" name="cat" value="<?php echo $prod->getGenreName(); ?>" />
			<input type="hidden" name="sscat" value="<?php echo $prod->getGroupeId(); ?>" />
<fieldset class="input" style="width:800px;">
<table width="800" >
  <tr>
	<?php

}

?>

<script language="Javascript">
	function etiquette(){
	
	window.location = "<?php echo Zend_registry::get("basePath"); ?>admin/produit/p_imprim_etiquette_dymo_pdf.php?id="+<?php echo $prod->getIdentifiant(); ?>;
					
	}
</script>

<input type="hidden" name="id" value="<?php echo $prod->getIdentifiant(); ?>" />
<table style="float:left;" width="390" >
	<tr>
		<td width="110" bgcolor="#CCCCCC">Référence</td>
	  <td width="235" bgcolor="#CCCCCC"><input type="text" name="reference" id="reference" value="<?php echo $reference[0]; ?>" style="width:100px;" />
	  	  		<script type="text/javascript">
						var reference = new LiveValidation('reference');
						reference.add( Validate.Presence );
						reference.add(Validate.Exclusion, { within: ["'",'"',' ','-','à','à','â','ä','ã','ç','é','è','ê','ë','ì','î','ï','ò','ô','ö','õ','µ','ù','û','ü','ñ','&','?','!',':','.',';','#','~','^','¨','@','%','$','£','?','²','¤','§','%','*','(',')','[',']','{','}','=','+','<','>','|','`'],partialMatch: true, failureMessage: "Caracteres speciaux interdits"});
						
		        </script></td>
	</tr>
	
	<tr>
		<td>Désignation</td>
		<td><textarea name="designation" id="designation" cols="40" rows="3"><?php echo $prod->getDesignation(); ?></textarea>
			    <script type="text/javascript">
						var designation = new LiveValidation('designation');
						designation.add( Validate.Presence );
						designation.add(Validate.Exclusion, { within: ['"','â','ä','ã','ç','ë','ì','î','ï','ò','ô','ö','õ','µ','ù','û','ü','ñ','&','#','~','^','¨','@','%','$','£','²','¤','§','%','*','{','}','<','>','`'],partialMatch: true, failureMessage: "Caracteres speciaux interdits"});
		        </script></td>
	</tr>
	<tr>
		<td bgcolor="#CCCCCC">Qualité</td>
		<td bgcolor="#CCCCCC">			
		
				<select id="qualite" name="qualite">
					<option value="vide">---</option>
					<option value="NEW" <?php if ($prod->getQualite() == "NEW") echo 'selected="selected"'; ?> >Neuf</option>
					<option value="NOS" <?php if ($prod->getQualite() == "NOS")echo 'selected="selected"'; ?> >Nos</option>
					<option value="USE" <?php if ($prod->getQualite() == "USE")echo 'selected="selected"'; ?> >Occasion</option>
					<option value="REC" <?php if ($prod->getQualite() == "REC")echo 'selected="selected"'; ?> >Reconditionné</option>

				</select>
				<script type="text/javascript">			
					var qualite = new LiveValidation('qualite');
		            qualite.add(Validate.Exclusion, { within: ['vide'], failureMessage: " "});
		        </script>
		</td>
	</tr>
	<tr>
	  <td>Stock</td>
	  <td>			
				
				<select name="promo">

					<option value="o" <?php if ($prod->getPromotion() == "o") echo 'selected="selected"'; ?> >Stock non vérifié, jamais cdé ( o )</option>
					<option value="oo" <?php if ($prod->getPromotion() == "oo")echo 'selected="selected"'; ?> >Pas de stock, jamais cdé ( oo )</option>
					<option value="ooo" <?php if ($prod->getPromotion() == "ooo")echo 'selected="selected"'; ?> >Introuvable ( ooo )</option>
					<option value="net" <?php if ($prod->getPromotion() == "net" || $prod->getPromotion() == "NET")echo 'selected="selected"'; ?> >Achat ou  faible marge ( net )</option>
					<option value="x" <?php if ($prod->getPromotion() == "x")echo 'selected="selected"'; ?> >Faible stock ( x )</option>
					<option value="xx" <?php if ($prod->getPromotion() == "xx")echo 'selected="selected"'; ?> >Moyen stock ( xx )</option>
					<option value="xxx" <?php if ($prod->getPromotion() == "xxx")echo 'selected="selected"'; ?> >Enorme stock ( xxx )</option>

				</select>
	  </td>
  </tr>
  	<tr>
	  <td bgcolor="#CCCCCC">Taille</td>
	  <td bgcolor="#CCCCCC"><input type="text" name="taille" value="<?php echo $prod->getTaille(); ?>" style="width:100px;" /></td>
  </tr>
	<tr>
	  <td bgcolor="#CCCCCC">Prix HT &euro;</td>
	  <td bgcolor="#CCCCCC"><input type="text" name="prix" id="prix" value="<?php echo $prod->getPrixHT(); ?>" style="width:100px;" />
	  	  <script type="text/javascript">
			var prix = new LiveValidation('prix');
			prix.add( Validate.Presence );
			prix.add( Validate.Numericality );
		  </script></td>
  </tr>
  <tr style="height:130px;">
	  <td>Comment.</td>
	  <td><textarea id="empl_comment" name="empl_comment" cols="40" rows="5"><?php echo $prod->getEmpl_comment(); ?></textarea></td>
	  	<script type="text/javascript">
						var empl_comment = new LiveValidation('empl_comment');
						empl_comment.add(Validate.Exclusion, { within: ["'",'"','â','ä','ã','ç','ë','ì','î','ï','ò','ô','ö','õ','µ','ù','û','ü','ñ','&','#','~','^','¨','@','%','$','£','²','¤','§','%','*','{','}','<','>','`'],partialMatch: true, failureMessage: "Caracteres speciaux interdits"});
		</script>
  </tr>
  <tr>
	  <td>
		  <table>
			<tr>
				<td><a href="public/images/adresse_parc_smi.jpg" rel="lytebox" title="Carte emplacement des zones" ><img src="public/images/carte.png" alt=""/></a></td>
				<td>Zone</td>
			</tr>
		  </table>
	  </td>
	  <td>
			<select name="empl_principal">
				
<?php
				if($prod->getEmpl_principal() == ""){
				
				echo '<option value="">---</option>';
				
				} else {
				
				echo '<option value="'.$prod->getEmpl_principal().'" selected="selected" >'.$prod->getEmpl_principal().'</option>';
				echo'<option value="">---</option>';
				
				}
				
?>
				<option value="H1">H1</option>
				<option value="MV">MV</option>
				<option value="H4">H4</option>
				<option value="B1">B1</option>
				<option value="B2">B2</option>
				<option value="B3">B3</option>
				<option value="H1E">H1E</option>
				<option value="H1O">H1O</option>
				<option value="ATELIER">ATELIER</option>
				<option value="AUVENT">AUVENT</option>
				<option value="MR">M.Ronde</option>
				<?php
				for($i = 1; $i < 6; $i++ ){
					echo '<option value="G'.$i.'">G'.$i.'</option>';
				}
				?>
				<?php
				for($i = 1; $i < 15; $i++ ){
					echo '<option value="P'.$i.'">P'.$i.'</option>';
				}
				?>
				
				</select>
				<input type="text" style="width:150px;" name="empl_secondaire" id="empl_secondaire" value="<?php echo $prod->getEmpl_secondaire(); ?>" />
	  </td>
  </tr>
</table >
<div id="tab_droite">
<table>
	<tr>
		<td width="66" bgcolor="#CCCCCC">Genre </td>
	  
	  <td width="258"  bgcolor="#CCCCCC"> 
				<select name="genre" style="width:230px;">

					<?php
				$result10 = $database->prepare("SELECT * FROM produits GROUP BY genre") or die ("r10 invalid");
				$result10->execute();
				
				echo '<option value="'.$prod->getGenreName().'" selected="selected" >'.$prod->getGenreName().'</option>';
				
				while ($row10 = $result10->fetch()) {

						echo '<option value="',$row10['genre'],'"';
						list($fr, $en) = explodeCategorieName($row10['genre']);
						echo '>',$fr;
						echo '</option>';
					}
					?>
				</select>
	  </td>
	</tr>
	<tr>
		<td>Groupe </td>
		<td>
<?php
				$result11 = $database->prepare("SELECT idgroupe,groupe FROM produits GROUP BY idgroupe ORDER BY idgroupe ASC") or die ("r11 invalid");
				$result11->execute();
				echo '<select style="width:230px;" name="groupe">';
				echo '<option value="'.$prod->getGroupeId().'-'.$prod->getGroupeName2().'" selected="selected" >'.$prod->getGroupeName2().'</option>';
				while ($row11 = $result11->fetch()) {
							echo '<option value="'.$row11['idgroupe'].'-'.$row11['groupe'].'">'.$row11['idgroupe'].'-'.$row11['groupe'].'</option>';
				}
				echo '</select>';
?>		</td>
	</tr>
	<tr>
		<td bgcolor="#CCCCCC">Type</td>
		<td bgcolor="#CCCCCC"><input id="type" type="text" style="width:230px;" name="type" value="<?php echo $prod->getType(); ?>" /></td>
		<script type="text/javascript">
						var type = new LiveValidation('type');
						type.add(Validate.Exclusion, { within: ["'",'"','â','ä','ã','ç','ë','ì','î','ï','ò','ô','ö','õ','µ','ù','û','ü','ñ','&','?','!',':','.',';','#','~','^','¨','@','%','$','£','?','²','¤','§','%','*','{','}','<','>','`'],partialMatch: true, failureMessage: "Caracteres speciaux interdits"});
		</script>
	</tr>
</table>
<table>
<th colspan="4" align="center">Produits en commun avec</th>
	<tr>
		<td align="center" bgcolor="#CCCCCC">Jeep</td>
		<td align="center" bgcolor="#CCCCCC">Dodge</td>
		<td align="center" bgcolor="#CCCCCC">Gmc</td>
		<td align="center" bgcolor="#CCCCCC">Chevrolet</td>
		<td align="center" bgcolor="#CCCCCC">R2087</td>
	</tr>
	<tr>
		<td>
		<?php
				$result12 = $database->prepare("SELECT idgroupe,groupe FROM produits GROUP BY idgroupe ORDER BY idgroupe ASC") or die ("r11 invalid");
				$result12->execute();
				echo '<select style="width:75px;" id="prod_commun_jeep" name="prod_commun_jeep">';
				echo '<option value="-1/-1" >---</option>';
				if($prod->getProd_commun_jeep() == "-1"){
				
				echo '<option value="'.$prod->getProd_commun_jeep().'/'.$prod->getProd_commun_jeep_name().'" selected="selected" >---</option>';
				
				} else {
				
				echo '<option value="'.$prod->getProd_commun_jeep().'/'.$prod->getProd_commun_jeep_name().'" selected="selected" >'.$prod->getProd_commun_jeep().'/'.$prod->getProd_commun_jeep_name().'</option>';
				
				}
				
				while ($row12 = $result12->fetch()) {
							$groupename = explode("|",$row12['groupe']);
							echo '<option value="'.$row12['idgroupe'].'/'.$groupename[0].'">'.$row12['idgroupe'].'/'.$groupename[0].'</option>';
				}
				
				echo '</select>';
				
		?>
		</td>
		<td>
		
		<?php
				$result12 = $database->prepare("SELECT idgroupe,groupe FROM produits GROUP BY idgroupe ORDER BY idgroupe ASC") or die ("r11 invalid");
				$result12->execute();
				echo '<select style="width:75px;" id="prod_commun_dodge" name="prod_commun_dodge">';
				echo '<option value="-1/-1" >---</option>';
				if($prod->getProd_commun_dodge() == "-1"){
				
				echo '<option value="'.$prod->getProd_commun_dodge().'/'.$prod->getProd_commun_dodge_name().'" selected="selected" >---</option>';
				
				} else {
				
				echo '<option value="'.$prod->getProd_commun_dodge().'/'.$prod->getProd_commun_dodge_name().'" selected="selected" >'.$prod->getProd_commun_dodge().'/'.$prod->getProd_commun_dodge_name().'</option>';
				
				}
				
				while ($row12 = $result12->fetch()) {
							$groupename = explode("|",$row12['groupe']);
							echo '<option value="'.$row12['idgroupe'].'/'.$groupename[0].'">'.$row12['idgroupe'].'/'.$groupename[0].'</option>';
				}
				
				echo '</select>';
		
		?>
		
		</td>
		<td>
		
		<?php
				$result12 = $database->prepare("SELECT idgroupe,groupe FROM produits GROUP BY idgroupe ORDER BY idgroupe ASC") or die ("r11 invalid");
				$result12->execute();
				echo '<select style="width:75px;" id="prod_commun_gmc" name="prod_commun_gmc">';
				echo '<option value="-1/-1" >---</option>';
				if($prod->getProd_commun_gmc() == "-1"){
				
				echo '<option value="'.$prod->getProd_commun_gmc().'/'.$prod->getProd_commun_gmc_name().'" selected="selected" >---</option>';
				
				} else {
				
				echo '<option value="'.$prod->getProd_commun_gmc().'/'.$prod->getProd_commun_gmc_name().'" selected="selected" >'.$prod->getProd_commun_gmc().'/'.$prod->getProd_commun_gmc_name().'</option>';
				
				}
				
				while ($row12 = $result12->fetch()) {
							$groupename = explode("|",$row12['groupe']);
							echo '<option value="'.$row12['idgroupe'].'/'.$groupename[0].'">'.$row12['idgroupe'].'/'.$groupename[0].'</option>';
				}
				
				echo '</select>';
		
		?>
		
		</td>
		<td>
		
		<?php
				$result12 = $database->prepare("SELECT idgroupe,groupe FROM produits GROUP BY idgroupe ORDER BY idgroupe ASC") or die ("r11 invalid");
				$result12->execute();
				echo '<select style="width:75px;" id="prod_commun_chevr" name="prod_commun_chevr">';
				echo '<option value="-1/-1" >---</option>';
				if($prod->getProd_commun_chevr() == "-1"){
				
				echo '<option value="'.$prod->getProd_commun_chevr().'/'.$prod->getProd_commun_chevr_name().'" selected="selected" >---</option>';
				
				} else {
				
				echo '<option value="'.$prod->getProd_commun_chevr().'/'.$prod->getProd_commun_chevr_name().'" selected="selected" >'.$prod->getProd_commun_chevr().'/'.$prod->getProd_commun_chevr_name().'</option>';
				
				}
				
				while ($row12 = $result12->fetch()) {
							$groupename = explode("|",$row12['groupe']);
							echo '<option value="'.$row12['idgroupe'].'/'.$groupename[0].'">'.$row12['idgroupe'].'/'.$groupename[0].'</option>';
				}
				
				echo '</select>';
		
		?>
		
		</td>
		<td>
		
		<?php
				$result12 = $database->prepare("SELECT idgroupe,groupe FROM produits GROUP BY idgroupe ORDER BY idgroupe ASC") or die ("r11 invalid");
				$result12->execute();
				echo '<select style="width:75px;" id="prod_commun_r2087" name="prod_commun_r2087">';
				echo '<option value="-1/-1" >---</option>';
				if($prod->getProd_commun_r2087() == "-1"){
				
				echo '<option value="'.$prod->getProd_commun_r2087().'/'.$prod->getProd_commun_r2087_name().'" selected="selected" >---</option>';
				
				} else {
				
				echo '<option value="'.$prod->getProd_commun_r2087().'/'.$prod->getProd_commun_r2087_name().'" selected="selected" >'.$prod->getProd_commun_r2087().'/'.$prod->getProd_commun_r2087_name().'</option>';
				
				}
				
				while ($row12 = $result12->fetch()) {
							$groupename = explode("|",$row12['groupe']);
							echo '<option value="'.$row12['idgroupe'].'/'.$groupename[0].'">'.$row12['idgroupe'].'/'.$groupename[0].'</option>';
				}
				
				echo '</select>';
		
		?>
		
		</td>
	</tr>
</table>
<table>
<th colspan="4" align="center">Fournisseurs</th>
	<tr>
		<td align="center" bgcolor="#CCCCCC">Jeepest </td>
		<td align="center" bgcolor="#CCCCCC">Desmet </td>
		<td align="center" bgcolor="#CCCCCC">Gsaa </td>
	</tr>
	<tr>
		<td><input type="text" name="jeepest" value="<?php echo $prod->getJeepest(); ?>" style="width:100px;" /></td>
		<td><input type="text" name="desmet" value="<?php echo $prod->getDesmet(); ?>" style="width:100px;" /></td>
		<td><input type="text" name="gsaa" value="<?php echo $prod->getGsaa(); ?>" style="width:100px;" /></td>
	</tr>
	<tr>
		<td align="center" bgcolor="#CCCCCC">Miltec </td>
		<td align="center" bgcolor="#CCCCCC" colspan="2">Autres </td>
	</tr>
	<tr>
		<td><input type="text" name="miltec" value="<?php echo $prod->getMiltec(); ?>" style="width:100px;" /></td>
		<td colspan="2" ><textarea name="autre_frs" style="width:200px; height:30px;"><?php echo $prod->getAutre_frs(); ?></textarea></td>
	</tr>
</table>

<table>
<th colspan="4" align="center">Achat</th>
	<tr>
		<td align="center" bgcolor="#FC0">Qt&eacute; à cder</td>
        <td align="center" bgcolor="#CCCCCC">Nom_Frs</td>
		<td align="center" bgcolor="#CCCCCC">P.P.</td>

	</tr>
	<tr>
		<td><input type="text" name="commande" value="<?php echo $prod->getCommande(); ?>" style="width:100px;" /></td>
		<td><input type="text" name="prix_frs" value="<?php echo $prod->getPrix_frs(); ?>" style="width:100px;" /></td>
		<td><input type="text" name="pp" value="<?php echo $prod->getPp(); ?>" style="width:100px;" /></td>

	</tr>
  
</table>

<table>
<th colspan="4" align="center">Stock</th>
	<tr>
	  <td align="center" bgcolor="#CCCCCC" >Qt&eacute; (livr&eacute; / en stock)</td>
	  <td align="center" bgcolor="#CCCCCC">Date (livr&eacute; / cr&eacute;er)</td>
	  <td align="center" bgcolor="#CCCCCC">Stock théo</td>
	</tr>
	<tr>
		<td><input type="text" name="qte_livraison" style="width:150px; text-align:center;" value="<?php echo $prod->getQte_livraison(); ?>" /></td>
		
<?php
$date_livraison = $prod->getDate_livraison();
if (empty($date_livraison) || $date_livraison == "0000-00-00" || $date_livraison == ""){
$date = "00/00/0000";
} else {
$date2 = explode("-",$date_livraison);
$date = $date2[2]."/".$date2[1]."/".$date2[0];
}
?>
		
		<td><input type="text" name="date_livraison" style="width:150px; text-align:center;" value="<?php echo $date; ?>" /></td>
<?php

// ------ Calcul stock théorique -------
$refprod = $prod->getReference();
$result15 = $database->prepare("SELECT SUM(qteproduit) AS qtevendu FROM ligne_facture INNER JOIN facture ON ligne_facture.id_facture = facture.id_facture WHERE reference = '$refprod ' AND date_facture >= '$date_livraison'") or die ("R15 invalid");
$result15->execute();
$qtevendu = $result15->fetch();

$qteliv = $prod->getQte_livraison();
$stocktheo = $qteliv - $qtevendu['qtevendu'];

echo '<td style="text-align:center;">'.$stocktheo.'</td>';

?>		

	</tr>
</table>
</div>
		</fieldset>
		<fieldset style="width:800px; margin-left:-30px;" class="submit">	
				  <strong><input type=button value="Dupliquer" onclick="form.action='<?php echo $basePath; ?>index.php?page=admin&action=gererprod&type=modif&dupliquer=oui&id=<?php echo $prod->getIdentifiant(); ?>&valuetosearch=<?php if(isset($_GET['valuetosearch'])){ echo $_GET['valuetosearch']; } ?>';form.submit()"></strong>
				  <strong><input type="submit" value="Modifier"></strong>
		</fieldset>	
		</form>
		</div>

<div class="form">
<fieldset class="input">
<table width="650" >
<?php
$ref =  $reference[0];
$categ = $prod->getGenreName();

include ("admin/produit/modif_description.php");

?>
</table>
</fieldset>
</div>
		
		<?php
		
		$ref = strtoupper($reference[0]);
		//Requete et remplissage tableau jpgraph
		$result2 = $database->prepare("
		SELECT sum(produit_qte) AS produit_qte, YEAR(`commande_date`) AS ANNEE FROM static_lignes_commandes INNER JOIN static_commandes 
		ON static_lignes_commandes.commande_id = static_commandes.id
		WHERE UPPER(produit_reference) = '$ref'
		GROUP BY ANNEE ORDER BY ANNEE DESC LIMIT 4
		") or die ("requete r2 invalid");
		$result2->execute();
		
	$nb_result2 = $result2->rowCount();
	if ($nb_result2 > 0){
		
			while ($tab2 = $result2->fetch()) {
		$produit_vendu[] = $tab2['produit_qte'];
		$annee[] = $tab2['ANNEE'];
		}
		// Construction du graphique
		// Spécification largeur et hauteur
		$graph = new Graph(400,250);

		// Réprésentation linéaire
		$graph->SetScale("textlin");
		//theme
		$theme_class = new SoftyTheme;
		$graph->SetTheme($theme_class);
		// Fixer les marges
		$graph->img->SetMargin(40,30,25,40);

		// Création du graphique histogramme
		$bplot = new BarPlot($produit_vendu);
		// Ajouter les barres au conteneur
		$graph->Add($bplot);
		// Spécification des couleurs des barres
		$bplot->SetFillColor(array('red', 'green', 'blue', 'orange', 'purple'));

		// Afficher les valeurs pour chaque barre
		$bplot->value->Show();
		// Modifier le rendu de chaque valeur
		$bplot->value->SetFormat('%d ventes');

		// Le titre
		$graph->title->Set("Ventes par annees");
		$graph->title->SetFont(FF_FONT1,FS_BOLD);

		// Titre pour l'axe horizontal(axe x) et vertical (axe y)
		$graph->xaxis->title->Set("Annees");
		$graph->yaxis->title->Set("Nombre de ventes");

		$graph->yaxis->title->SetFont(FF_FONT1,FS_BOLD);
		$graph->xaxis->title->SetFont(FF_FONT1,FS_BOLD);

		// Légende pour l'axe horizontal
		$graph->xaxis->SetTickLabels($annee);

		// Afficher le graphique
		$graph->Stroke('public/images/produits/graphique_secteur.png');
		
			
		
//Stat par mois

	// Construction du graphique
		// Spécification largeur et hauteur
		$graph = new Graph(400,250);

		// Réprésentation linéaire
		$graph->SetScale("textlin");

		//$theme_class=new UniversalTheme;

		//$graph->SetTheme($theme_class);
		$graph->img->SetAntiAliasing(false);
		$graph->title->Set('Ventes par mois');
		$graph->SetBox(false);

		$graph->img->SetAntiAliasing();

		$graph->yaxis->HideZeroLabel();
		$graph->yaxis->HideLine(false);
		$graph->yaxis->HideTicks(false,false);

		$graph->xgrid->Show();
		$graph->xgrid->SetLineStyle("solid");
		$graph->xaxis->SetTickLabels(array(1,2,3,4,5,6,7,8,9,10,11,12));
		$graph->xgrid->SetColor('#E3E3E3');

	// requete
		
		$i = 0;
		
		$result4 = $database->prepare("
		SELECT YEAR(`commande_date`) AS annee FROM static_lignes_commandes INNER JOIN static_commandes 
		ON static_lignes_commandes.commande_id = static_commandes.id
		WHERE UPPER(produit_reference) = '$ref' 
		GROUP BY  annee ORDER BY annee DESC LIMIT 5
		") or die ("requete r3 invalid");
		
		$result4->execute();
		
	//couleur
		
		$k = array("#33CC33","#3366CC","#FF6600","#CC6699","#FFFF99","#33CC33","#3366CC");
		
		while ($tab4 = $result4->fetch()) {
		
		$annee = $tab4['annee'];
		
		$result3 = $database->prepare("
		SELECT SUM(produit_qte) AS produit_qte, MONTH(`commande_date`) AS mois FROM static_lignes_commandes INNER JOIN static_commandes 
		ON static_lignes_commandes.commande_id = static_commandes.id
		WHERE UPPER(produit_reference) = '$ref' AND YEAR(`commande_date`) = '$annee'
		GROUP BY mois
		") or die ("requete r3 invalid");
		
		$result3->execute();
		
		$jan = 0;
		$fev = 0;
		$mar = 0;
		$avr = 0;
		$mai = 0;
		$jui = 0;
		$juil = 0;
		$aou = 0;
		$sep = 0;
		$oct = 0;
		$nov = 0;
		$dec = 0;
		
		while ($tab3 = $result3->fetch()) {
		
		if ($tab3['mois'] == 1){$jan = $tab3['produit_qte'];}
		if ($tab3['mois'] == 2){$fev = $tab3['produit_qte'];}
		if ($tab3['mois'] == 3){$mar = $tab3['produit_qte'];}
		if ($tab3['mois'] == 4){$avr = $tab3['produit_qte'];}				
		if ($tab3['mois'] == 5){$mai = $tab3['produit_qte'];}
		if ($tab3['mois'] == 6){$jui = $tab3['produit_qte'];}
		if ($tab3['mois'] == 7){$juil = $tab3['produit_qte'];}
		if ($tab3['mois'] == 8){$aou = $tab3['produit_qte'];}
		if ($tab3['mois'] == 9){$sep = $tab3['produit_qte'];}
		if ($tab3['mois'] == 10){$oct = $tab3['produit_qte'];}	
		if ($tab3['mois'] == 11){$nov = $tab3['produit_qte'];}
		if ($tab3['mois'] == 12){$dec = $tab3['produit_qte'];}
		
		}
		
		$produit_vendu2[$i] = array($jan,$fev,$mar,$avr,$mai,$jui,$juil,$aou,$sep,$oct,$nov,$dec);
		
		// Create the first line
		$p[$i] = new LinePlot($produit_vendu2[$i]);
		$graph->Add($p[$i]);
		$p[$i]->SetColor($k[$i]);
		$p[$i]->SetLegend($annee);
		$i++;
		}
		
		$graph->legend->SetFrameWeight(1);

		// Afficher le graphique
		$graph->Stroke('public/images/produits/graphique_secteur2.png');
		
// AFFICHE LES GRAPHIQUES DANS UN TABLEAU
	echo '<table style="margin-left:50px;">';
		echo '<tr>';
			echo '<td><div align="center"><img src="public/images/produits/graphique_secteur.png" alt=""/></div></td>';	
			echo '<td><div align="center"><img src="public/images/produits/graphique_secteur2.png" alt=""/></div></td>';	
		echo '<tr>';		
	echo '</table>';
	} else {
		//erreur pas de produit à modifier
		echo '<div class="error messageBox">Aucune données statistique sur ce produit !</div>';
	}
	} else {
		echo '<div class="error messageBox">Produit à modifier inconnu.</div>';
	}
?>	
<h2>Affiche les 15 derniers clients qui ont commandé !</h2>

		<table class="liste">
			<tr>
				<th>Numéro</th>
				<th>Date de commande</th>
				<th>Client</th>
				<th>Email</th>
				<th>Tel</th>
				<th>Vue client</th>
				<th>Vue cde</th>
			</tr>
<?php			
		$reference2 = $prod->getReference();
		$result5 = $database->prepare("
		SELECT
		*
		FROM static_lignes_commandes
		INNER JOIN static_commandes ON static_lignes_commandes.commande_id = static_commandes.id
		WHERE produit_reference = '$reference2'
		ORDER BY commande_id DESC
		LIMIT 15 
		") or die ("requete r5 invalid");
		$result5->execute();
		while ($tab5 = $result5->fetch()) {	
		$date = explode("-", $tab5['commande_date']); 
?>	
		<tr>				
			<td><?php echo $tab5['commande_id']; ?></td>
			<td align="center"><?php echo $date[2]."/".$date[1]."/".$date[0]; ?></td>
			<td><?php echo $tab5['client_nomprenom']; ?></td>
			<td><a href="mailto:<?php echo $tab5['client_email']; ?>" ><?php echo $tab5['client_email']; ?></a></td>
			<td><?php echo $tab5['client_tel']; ?></td>
			<td align="center"><a target="_blank" href='<?php echo $basePath; ?>index.php?page=admin&action=gererclient&type=modif&id=<?php echo $tab5['client_email']; ?>'><img src='<?php echo $basePath; ?>public/images/icone_login.png'/></a></td>
			<td align="center"><a target="_blank" href='<?php echo $basePath; ?>index.php?page=admin&action=commandes&id=<?php echo $tab5['commande_id']; ?>'><img src='<?php echo $basePath; ?>public/images/zoom.png'/></a></td>
		</tr>	
<?php
		}
?>	
		</table>
							<form method="get" style="float:left; margin-left:50px;" action="<?php echo $basePath.'index.php';?>">
							
								<input type="hidden" name="page" value="admin" />
								<input type="hidden" name="action" value="gererprod" />
								<img src="<?php echo $basePath; ?>public/images/arrow_undo.png" alt="loupe"/>
								<input type="hidden" name="valuetosearch" value="<?php if(isset($_GET['valuetosearch'])){ echo $_GET['valuetosearch']; } ?>" />
								<input type="hidden" name="in" value="ALL" /> 
								<input type="hidden" name="method" value="AND" /> 
								<input type="submit" value="Retour" /> </br>
							
							</form>
							<br />
					


