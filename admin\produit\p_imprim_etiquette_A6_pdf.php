<?php

	ob_start();
	include('imprim_etiquette_A6_pdf.php');
	$content2 = ob_get_clean();
	
    require_once(dirname(__FILE__).'/html2pdf/html2pdf.class.php');
    $html2pdf = new HTML2PDF('L','A4','fr', false, 'ISO-8859-15', array('5', '10', '5', '5'));
	$html2pdf->setTestTdInOnePage(false);
    $html2pdf->WriteHTML($content2);
    $html2pdf->Output('../../public/images/produits/etiquetteA6.pdf', 'F');
	
		$filename = "../../public/images/produits/etiquetteA6.pdf";

	# Envoi des entêtes HTTP qui permettent de forcer le téléchargement
	header("Content-disposition: attachment; filename=Etiquette_A6_du_".date("d-m-y").".pdf");
	header("Content-Type: application/force-download");
	header("Content-Transfer-Encoding: application/octet-stream");
	header("Content-Length: ".filesize($filename));
	header("Pragma: no-cache");
	header("Cache-Control: must-revalidate, post-check=0, pre-check=0, public");
	header("Expires: 0");

	# Lecture & envoi du fichier au client (navigateur)
	readfile($filename);
	
	
?>