<?php

	ob_start();
	include('imprim_etiquette_POSTALE_pdf.php');
	$content2 = ob_get_clean();
	
    require_once(dirname(__FILE__).'/html2pdf/html2pdf.class.php');
    $html2pdf = new HTML2PDF('L',array('210mm','100mm'),'fr', false, 'ISO-8859-15', array('5', '10', '5', '5'));
	$html2pdf->setTestTdInOnePage(false);
    $html2pdf->WriteHTML($content2);
    $html2pdf->Output('../../public/images/produits/etiquettePOSTALE.pdf', 'F');
	
		$filename = "../../public/images/produits/etiquettePOSTALE.pdf";

	# Envoi des entêtes HTTP qui permettent de forcer le téléchargement
	header("Content-disposition: attachment; filename=Etiquette_POSTALE_du_".date("d-m-y").".pdf");
	header("Content-Type: application/force-download");
	header("Content-Transfer-Encoding: application/octet-stream");
	header("Content-Length: ".filesize($filename));
	header("Pragma: no-cache");
	header("Cache-Control: must-revalidate, post-check=0, pre-check=0, public");
	header("Expires: 0");

	# Lecture & envoi du fichier au client (navigateur)
	readfile($filename);
?>