<?php
if (isset($_GET['id'])){

$id = "?id=".$_GET['id'];

} else {

$id = "";

}
	ob_start();
	include('imprim_etiquette_dymo_pdf.php');
	$content2 = ob_get_clean();
	
    require_once(dirname(__FILE__).'/html2pdf/html2pdf.class.php');
    $html2pdf = new HTML2PDF('L',array('32mm','57mm'),'fr', false, 'ISO-8859-15', array('1', '1', '1', '3'));
	$html2pdf->setTestTdInOnePage(false);
    $html2pdf->WriteHTML($content2);
    $html2pdf->Output('../../public/images/produits/etiquette.pdf', 'F');
	
	$filename = "../../public/images/produits/etiquette.pdf";

	# Envoi des entêtes HTTP qui permettent de forcer le téléchargement
	header("Content-disposition: attachment; filename=Etiquette_Dymo_du_".date("d-m-y").".pdf");
	header("Content-Type: application/force-download");
	header("Content-Transfer-Encoding: application/octet-stream");
	header("Content-Length: ".filesize($filename));
	header("Pragma: no-cache");
	header("Cache-Control: must-revalidate, post-check=0, pre-check=0, public");
	header("Expires: 0");

	# Lecture & envoi du fichier au client (navigateur)
	readfile($filename);
	
	//header('Location: ../../public/images/produits/etiquette.pdf');    
	
	
?>