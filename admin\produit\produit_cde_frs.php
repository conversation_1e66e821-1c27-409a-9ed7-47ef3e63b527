	<script>
		function update_commande_frs(valeur,id_dde_frs,name_value)
		{
			var xmlhttp = null;
			if (valeur=="")
			  {
			  document.getElementById("txtHint").innerHTML="";
			  return;
			  }
			if (window.XMLHttpRequest)
			  {// code for IE7+, Firefox, Chrome, Opera, Safari
			  xmlhttp=new XMLHttpRequest();
			  }
			else
			  {// code for IE6, IE5
			  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
			  }
			xmlhttp.onreadystatechange=function()
			  {
			  if (xmlhttp.readyState==4 && xmlhttp.status==200)
				{
				document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
				}
			  }
			xmlhttp.open("GET","<?php echo $basePath; ?>client/update_cde_frs.php?valeur="+valeur+"&id_dde_frs="+id_dde_frs+"&name_value="+name_value,true);
			xmlhttp.send();
		} 

	</script>

<?php
// --- AJOUTE UNE DEMANDES
$reference2 = $reference[0];
$dateday = date("Y-m-d");

if (isset($_GET['ajout_dde_frs'])){
	
$result2 = $database->prepare("
INSERT INTO demande_fournisseur
(id_dde_frs,
 id_frs, 
 id_produit, 
 date_dde_frs, 
 quantite_dde_frs,
 prix_dde_frs,  
 statut_dde_frs,
 comment_dde_frs)
VALUES (
 '',
 '1',
 '".$reference2."',
 '".$dateday."',
 '1',
 '1',
 'attente',
 ''
 )
") or die ("r2 invalid");
$result2->execute();
	
}
		


// --- AFFICHE LES DEMANDES FOURNISSEURS ---
		$result5 = $database->prepare("
		SELECT
		*
		FROM demande_fournisseur INNER JOIN
		fournisseur ON id = id_frs
		WHERE id_produit = '$reference2' AND statut_dde_frs != 'terminer' AND statut_dde_frs != 'annuler'
		") or die ("requete r5 invalid");
		$result5->execute();
		
		$result6 = $database->prepare("
		SELECT
		*
		FROM demande_fournisseur INNER JOIN
		fournisseur ON id = id_frs
		WHERE id_produit = '$reference2' AND statut_dde_frs = 'terminer'
		") or die ("requete r5 invalid");
		$result6->execute();
		

	
?>
<div class="form-style-10">
<h1 style="padding-top:8px; padding-bottom:22px;" ><a href="<?php echo $basePath; ?>index.php?page=admin&action=gererprod&valuetosearch=<?php echo $reference2; ?>&in=ALL&method=AND" ><img src="public/images/reload.png" alt=""/></a> DEMANDES FOURNISSEURS <a href="<?php echo $basePath ?>index.php?page=admin&action=gererprod&valuetosearch=<?php echo $reference2; ?>&in=ALL&method=AND&ajout_dde_frs=1" >AJOUTER</a></h1>
	<div class="inner-wrap">
		<table class="table-fill">
			<tr>
				<th style="font-size:14px;padding:10px;text-align:center;">Date</th>
				<th style="font-size:14px;padding:0;text-align:center;">Fournisseur</th>
				<th style="font-size:14px;padding:0;text-align:center;">Qté</th>
				<th style="font-size:12px;padding:0;text-align:center;">Prix UHT</th>
				<th style="font-size:14px;padding:0;text-align:center;">Statut</th>
				<th style="font-size:14px;padding:0;text-align:center;">Commentaire</th>
				<th style="font-size:14px;padding:0;text-align:center;"></th>
			</tr>
<?php			
		while ($tab5 = $result5->fetch()) {	
		$date = explode("-", $tab5['date_dde_frs']); 
		
		// MAIL
			//URLENCODE() 
			$mess_dde_frs = "
			Bonjour,%0D%0A
			%0D%0A
			Nous souhaiterions un devis pour l'article suivant :%0D%0A
			%0D%0A
			Référence : ".$reference[0]."%0D%0A
			Désignation : ".$prod->getDesignation()." - ".$prod->getGenreName()."%0D%0A
			Quantité : ".$tab5['quantite_dde_frs']."%0D%0A
			%0D%0A
			Cordialement.%0D%0A
			Villard JB.%0D%0A
			%0D%0A
			-------------------------------------%0D%0A 
			Surplus Militaires et Industriels %0D%0A 
			D1092, La gare %0D%0A 
			38840 La Sone %0D%0A 
			FRANCE %0D%0A 
			Tel : +33 (0) 476 644 356 %0D%0A 
			Fax : +33 (0) 476 644 555 %0D%0A 
			@ : <EMAIL> %0D%0A 
			Web : www.jeep-dodge-gmc.com %0D%0A 
			-------------------------------------  %0D%0A ";
?>	
		<tr>				
			<td><input onchange="update_commande_frs(this.value,'<?php echo $tab5['id_dde_frs'];?>','date_dde_frs')" style="text-align:center; width:80px; height:30px; border-radius: 5px;" id="date_dde_frs" name="date_dde_frs" value="<?php echo $date[2]."/".$date[1]."/".$date[0]; ?>" /></td>
			<td>
			<select onchange="update_commande_frs(this.options[this.selectedIndex].value,'<?php echo $tab5['id_dde_frs'];?>','id_frs')" style="width:150px;" id="id_frs" name="id_frs">
<?php
				$result12 = $database->prepare("SELECT * FROM fournisseur ORDER BY raisonsocial") or die ("r12 invalid");
				$result12->execute();
				
				echo '<option value="'.$tab5['id_frs'].'" >'.$tab5['raisonsocial'].'</option>';
				while ($row12 = $result12->fetch()) {
					echo '<option value="'.$row12['id'].'">'.$row12['raisonsocial'].'</option>';
				}
				
				echo '</select>';				
?>
			</td>
			<td><input onchange="update_commande_frs(this.value,'<?php echo $tab5['id_dde_frs'];?>','quantite_dde_frs')" style="text-align:center; width:50px; height:30px; border-radius: 5px;" id="quantite_dde_frs" name="quantite_dde_frs" value="<?php echo $tab5['quantite_dde_frs']; ?>" /></td>
			<td><input onchange="update_commande_frs(this.value,'<?php echo $tab5['id_dde_frs'];?>','prix_dde_frs')" style="text-align:center; width:60px; height:30px; border-radius: 5px;" id="prix_dde_frs" name="prix_dde_frs" value="<?php echo $tab5['prix_dde_frs']; ?>" /></td>
			<td>
			<select onchange="update_commande_frs(this.options[this.selectedIndex].value,'<?php echo $tab5['id_dde_frs'];?>','statut_dde_frs')" style="width:100px;" id="statut_dde_frs" name="statut_dde_frs">
				<option value="<?php echo $tab5['statut_dde_frs'];?>" ><?php echo $tab5['statut_dde_frs'];?></option>
				<option value="terminer" >terminer</option>
				<option value="attente" >attente</option>
				<option value="valider" >valider</option>
				<option value="livraison" >livraison</option>
				<option value="annuler" >annuler</option>
			</select>
			</td>
			<td><input onchange="update_commande_frs(this.value,'<?php echo $tab5['id_dde_frs'];?>','comment_dde_frs')" style="text-align:center; width:150px; height:30px; border-radius: 5px;" id="comment_dde_frs" name="comment_dde_frs" value="<?php echo $tab5['comment_dde_frs']; ?>" /></td>
			<td><a href="mailto:<?php echo $tab5['email']; ?>?subject=<?php echo $tab5['raisonsocial'];?> - Demande de Devis - Le <?php echo $date[2]."/".$date[1]."/".$date[0]; ?>&body=<?php echo $mess_dde_frs; ?>" ><img src="public/images/email.png" alt=""/></a></td>
		</tr>	
<?php
		}
?>	
		</table>
	</div>	
</div>	

<?php
		$nb_result6 = $result6->rowCount();
		if ($nb_result6 > 0){
?>	

<div class="form-style-10">
<h1 style="padding-top:8px; padding-bottom:22px;" >LIVRAISON FOURNISSEURS</h1>
	<div class="inner-wrap">
		<table class="table-fill">
			<tr>
				<th>Date</th>
				<th>Fournisseur</th>
				<th>Qté</th>
				<th><span style="font-size:12px;">Prix UHT</span></th>
				<th>Statut</th>
				<th>Commentaire</th>
			</tr>
<?php

		while ($tab6 = $result6->fetch()) {	
		$date = explode("-", $tab6['date_dde_frs']); 
?>	
		<tr>				
			<td style="text-align:center;"><?php echo $date[2]."/".$date[1]."/".$date[0]; ?></td>
			<td><?php echo $tab6['raisonsocial']; ?></td>
			<td style="text-align:center;"><?php echo $tab6['quantite_dde_frs']; ?></td>
			<td style="text-align:right; margin-right:5px;"><?php echo $tab6['prix_dde_frs']; ?>€</td>
			<td style="text-align:center;"><?php echo $tab6['statut_dde_frs']; ?></td>
			<td><?php echo $tab6['comment_dde_frs']; ?></td>
		</tr>	
<?php
		}
?>	
		</table>
	</div>	
</div>	
<?php
}	
?>	