
<?php

if(isset($_GET['dde_devis_del'])){

	$dde_devis_del = $_GET['dde_devis_del'];
	$delete_dde_devis = $database->query("DELETE FROM demande_devis WHERE id_devis = '$dde_devis_del'") or die ("delete_dde_devis invalid");
	
}

echo '<h1><img src="<?php echo $basePath; ?>public/images/produit_modifier.png" alt=""/>Aperçu des produits demandés Sur devis</h1>';

$result = $database->prepare("
SELECT * FROM demande_devis
") or die ("requete r1 invalid");
$result->execute();

echo '<table class="liste">';

echo '<th>Date</th>';
echo '<th>Nom</th>';
echo '<th>Prenom</th>';
//echo '<th>Telephone</th>';
//echo '<th>E-mail</th>';
echo '<th>Produit</th>';
echo '<th>Qté</th>';
echo '<th></th>';

	while ($tab = $result->fetch()) {
		echo '<tr>';
			echo "<td><div align='left'>".date("d/m/Y", strtotime($tab['date_devis']))."</div></td>";
			echo '<td><div align="left">'.$tab['nomdevis'].'</a></div></td>';
			echo "<td><div align='left'>".$tab['prenomdevis']."</div></td>";
			//echo "<td><div align='left'>".$tab['telephonedevis']."</div></td>";
			//echo "<td><div align='left'>".$tab['emaildevis']."</div></td>";
			echo "<td><div align='left'>".$tab['produitdevis']."</div></td>";
			echo "<td><div align='left'>".$tab['qtedevis']."</div></td>";
			echo '<td><div align="center"><a onclick="return(confirm(\'Etes-vous sur de vouloir supprimer cette demande?\'));" href="'.$basePath.'index.php?page=admin&action=gererprod&sur_devis=1&dde_devis_del='.$tab['id_devis'].'"> <img src="'.$basePath.'public/images/b_drop.png" alt="delete" title="Supprimer"/></a></div></td>';
		echo '</tr>';
	}
echo '</table>';
echo '<br />';

