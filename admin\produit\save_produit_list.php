<?php 
require_once(dirname(__FILE__).'/../../init.php');
require_once(dirname(__FILE__).'/../../utils/form_utile.php');
require_once(dirname(__FILE__).'/../../class/produit_class.php');
require_once(dirname(__FILE__).'/../../class/categorie_class.php');

/**
 * Programme qui lance la sauvegarde d'une liste de produits
 * dans la base de données.
 * 
 * Renvoi le nombre de catégories ajoutées, de ss catégories et de produits
 */

$res = '';

//récupère les objets et les recrée
$produits = unserialize($_SESSION['produits']);
$nbprod=0;
//vide la table produit
ProduitDB::truncateTable();

set_time_limit(3600);

echo '<div class="nocenter_alignleft error messageBox">';
echo '<table class="liste tableerror" style="width:95%;">';
echo '<tr><th>Ligne</th><th>Genre</th><th>Groupe</th><th>Erreur</th></tr>';
		
for($i=0;$i<count($produits);$i++)
{
	$nbprod += ProduitDB::saveProduit($produits[$i]);
}

set_time_limit(60);
echo '</table>';
echo '</div>';
		
//libère les variables de session
unset($_SESSION['produits']);

//enregistre la date d'importation
$sql = "UPDATE parametres SET valeur = :value WHERE cle = :cle";
$db = Zend_Registry::get('database');
$stmt = $db->prepare($sql);
$cle = date("d/m/Y");
$var = 'bdd_produit';
$stmt->bindParam(":value",$cle);
$stmt->bindParam(":cle",$var);
$stmt->execute();

//retourne le nombre de produits ajoutés.
echo '<div class=\'valide messageBox\'>',$nbprod, ' produits correctements ajoutés dans la BDD.</div>';

?>