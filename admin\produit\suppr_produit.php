<?php
/**
 * programme qui supprime un produit de la base de donnée
 */
?>
<h1><img src="<?php echo $basePath; ?>public/images/produit_suppr.png" alt=""/> Suppression d'un produit</h1>


<?php

require_once(dirname(__FILE__).'/../../class/produit_class.php');
$prod = produitDB::getProduitById($_GET["id"]);

if($prod instanceof Produit) {

	$res = produitDB::removeProduit($prod);
}
else
	$res = false;

if($res == true) {
	//suppression réussi
	
	
	?>
	<div class="valide messageBox">
		Le produit a bien été supprimé : <br /><br />

		<b>Désignation : </b><?php echo $prod->getDesignation()?><br />
		<b>Référence : </b><?php echo $prod->getReference()?><br />
		<b>Prix HT € : </b><?php echo $prod->getPrixHT()?><br />
			
	</div>
	<?php
} else {
	//erreur de suppression
	echo '<div class="error messageBox">Erreur lors de la suppression du produit.</div>';
}

?>

							<form method="get" style="float:left; margin-left:50px;" action="<?php echo $basePath.'index.php';?>">
							
								<input type="hidden" name="page" value="admin" />
								<input type="hidden" name="action" value="gererprod" />
								<img src="<?php echo $basePath; ?>public/images/arrow_undo.png" alt="loupe"/>
								<input type="hidden" name="valuetosearch" value="<?php echo $_GET['valuetosearch']; ?>" />
								<input type="hidden" name="in" value="ALL" /> 
								<input type="hidden" name="method" value="AND" /> 
								<input type="submit" value="Retour" /> </br>
							
							</form>
