<div class="form-style-10">	
<div class="section"><span>¤</span>Commande terminée</div>
<?php
//afficherMenuCommande(6);
require_once 'commande_static_class.php';
if(!isset($cs)){
	$cs = commande_staticDB::getCommandeById("20058");
}
// page de fin de la commande (a ce point là, la commande a été validé)
// affiche le recap de la commande
// si la commande est en retrait en magasin
?>
<div class="inner-wrap">
<?php
if($cs->getRetraitmagasin()){
	
	echo "Pour retirer votre commande en magasin, veuillez venir avec une copie du bon de commande";
	
} else {

	if($cs->getModepaiement() == "Chèque"){
		// si le mode de paiement est Cheque
		echo "Pour recevoir votre commande, vous devez envoyer votre chèque avec une copie du bon de commande. <br /> Nous rappellons que nos jours d'expédition sont les mardis et jeudis matin";		
	}
	if($cs->getModepaiement() == "Paypal"){
		// si le mode de paiement est Paypal
		echo "Votre commande sera bientôt expediée <br /> Nous rappellons que nos jours d'expédition sont les mardis et jeudis matin";		
	}
	if($cs->getModepaiement() == "Mandat cash"){
		// si le mode de paiement est Mandat cash
		echo "Afin de recevoir votre commande, vous devez vous rendre à votre guichet de poste afin de verser la somme indiquée dans le cadre : Total de la commande TTC. Il faut nous faire parvenir la preuve de votre mandat cash par courrier. Attention les frais supplémentaires engendrés sont à votre charge.<br /> Nous rappellons que nos jours d'expédition sont les mardis et jeudis matin";		
	}
	if($cs->getModepaiement() == "Virement bancaire"){
		// si le mode de paiement est Virement bancaire
		echo 'Afin de recevoir votre commande, vous devez effectuer un virement bancaire à l\'aide de <A HREF="http://www.jeep-dodge-gmc.com/smi/client/commande/view/iban_bpa.pdf" TARGET=_BLANK style="color:blue; text-decoration: underline;"><strong>l\'IBAN (télécharger ici!) </strong></a> de la somme indiquée en total TTC sur le bon de commande. Il faut nous faire parvenir la preuve de virement. Attention les frais supplémentaires engendrés sont à votre charge. Ce type de paiement peut prendre quelques jours car nous attendons que le virement soit effectif sur notre compte, pour expédier la commande. <br /> Nous rappellons que nos jours d\'expédition sont les mardis et jeudis matin';		
	}
	if($cs->getModepaiement() == "Contre remboursement" || $cs->getModepaiement() == "Carte de crédit"){
		// 	sinon si le mode de paiement est contre remboursement		
		echo "Votre commande sera bientôt expediée <br /> Nous rappellons que nos jours d'expédition sont les mardis et jeudis matin";
	}
}
?>
</div>
<table>
<tr>
<td>
<div class="inner-wrap" style="width:380px; height:80px; padding:10px;">
<?php
// Afficher un bouton pour imprimer le bon de commande (plutot pour telecharger le fichier pdf)
?>

<a target="_blank" href="<?php echo $basePath; ?>client/commande/impression_commande.php?id=<?php echo $cs->getId(); ?>">

	<div id="div_impression" style="width:370px; height:70px; background-color: black; display:block; margin:auto; border-radius: 7px; padding:5px; text-align:center;">
	
		<table style="text-align:center; width:360px; margin-top:7px; margin-left:12px;">
			<tr>
				<td><img src="<?php echo $basePath; ?>public/images/imprimante-blanc.png" height="50px;" /></td>
				<td><span style="color:white;"> Imprimer le bon de commande</span></td>
			</tr>	
		</table>
		
	</div>

</a>


</div>
</td><td>
<div class="inner-wrap" style="width:380px; height:80px; padding:10px;">

<?php
if (isset($_SESSION['iscommandeadmin']) && $_SESSION['iscommandeadmin'] == 1) {
?>
<a href="<?php echo $basePath; ?>client/commande/view/redirect_admin.php?email=admin&cde=<?php echo $cs->getId(); ?>"><p class="bouton_enquete" >Cliquer Ici pour faire la facture</p></a>
<?php
} else {
	// MODULE AVIS GOOGLE
?>


<a target="_blank" href="https://search.google.com/local/writereview?placeid=ChIJXfAmuuG5ikcRlGC2Mu30KkQ">

	<div id="avis_google" style="width:370px; height:70px; background-color: white; display:block; margin:auto; border-radius: 7px; padding:5px; text-align:center;">
	
		<table style="text-align:center;">
			<tr>
				<td rowspan="2"><img src="<?php echo $basePath; ?>public/images/google-plus.png" height="60px;" /></td>
				<td><strong>Chaque avis compte, donnez le votre !</strong></td>
			</tr>	
			<tr>				
				<td><img src="<?php echo $basePath; ?>public/images/avis-google.png" height="30px;" /></td>
			</tr>
		</table>
		
	</div>

</a>



<?php
}
?>

</div>
</td>
</tr>
</table>
<?php

if(!$cs->getRetraitmagasin()){
	// sinon, on affiche les 2 adresses
?>
	<table>
	<tr>
	<td>
	<div class="inner-wrap" style="width:340px;">
	<h1>Adresse de facturation</h1>
		<?php echo stripslashes($cs->getAdresselivr()); ?>
	</div>				
	</td><td>
	<div class="inner-wrap" style="width:340px;">
	<h1>Adresse de Livraison</h1>
		<?php echo stripslashes($cs->getAdressefact()); ?>
	</div>
	</td>
	</tr>
	</table>

	<?php
} else {
	?>
	
	<table>
	<tr>
	<td>
	<div class="inner-wrap" style="width:340px;">
		<h1>Adresse de facturation</h1>

					<?php echo stripslashes($cs->getAdressefact()); ?>
	</div>
	</td><td>
	</td>
	</tr>
	</table>
<?php
}
// affichage du detail de la commande
$lignePs = $cs->getPanier();
// @todo afficher le detail de la commande
?>
<div class="section"><span>¤</span>Numero de commande : <b><?php echo $cs->getId(); ?></b></div>
<div class="inner-wrap">
<table style="font-size:14px; width: 740px;" border="0" cellspacing="0" cellpadding="0">
	<tr>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000; padding-left:3px; padding-top:7px; padding-bottom:7px;"><?php echo $translate->_('Référence'); ?></th>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000;"><?php echo $translate->_('Genre'); ?></th>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000;text-align:center;"><?php echo $translate->_('Groupe'); ?></th>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000;"><?php echo $translate->_('Désignation'); ?></th>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000; width:65px;"><?php echo $translate->_('Prix HT'); ?></th>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000; width:65px;"><?php echo $translate->_('Quantité'); ?></th>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000; width:65px;"><?php echo $translate->_('Total HT'); ?></th>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000;"></th>
	</tr>
	
	<?php foreach($lignePs->getLignePanier() as $k => $ligneP):	?>
			<tr>
				<td style="padding-left:3px;padding-top:8px; padding-bottom:8px;background-color:white;border-bottom: 2px solid #000000;"><?php echo $ligneP->getReference(); ?></td>
				<?php $tab = explode(" / ",$ligneP->getGenregroupe()); ?>
				<td style="font-size:11px;background-color:white;border-bottom: 2px solid #000000; text-align:center;"><?php echo $tab[0]; ?></td>
				<td style="font-size:11px;background-color:white;border-bottom: 2px solid #000000;"><?php echo $tab[2]; ?></td>
				<td style="padding-left:3px;background-color:white;border-bottom: 2px solid #000000;font-weight: bold;"><?php echo $ligneP->getDesignation(); ?>	
				<td style="background-color:white;border-bottom: 2px solid #000000;"><?php printf("%.2f",$ligneP->getPuHT());?> €</td>						
				<td style="background-color:white;border-bottom: 2px solid #000000;"><?php echo $ligneP->getQte();?></td>
				<td style="background-color:white;border-bottom: 2px solid #000000; padding-right:3px;"><?php printf("%.2f", $ligneP->getSomme());?> €</td>
			 </tr>
	<?php endforeach; ?>
	<tr>
		<td colspan="3" rowspan="5"></td>
		<td align="right" style="border-top:1px dotted black;"><?php echo $translate->_('Montant total HT');?> :</td>
		<td></td>
		<td></td>
		<td align="right" style="padding-right:3px;padding-top:4px; padding-bottom:4px;"><?php printf('%.2f',$cs->getMontanttotalHT()); ?> €</td>
	</tr>
	<?php
	
	if($cs->getRemise_MontanttotalHT() != 0){
	
	?>
	<tr>
		<td align="right" style="border-top:1px dotted black;"><?php echo $translate->_('Remise HT -10% ');?> :</td>
		<td></td>
		<td></td>
		<td align="right" style="padding-right:3px;padding-top:4px; padding-bottom:4px;" >-<?php printf('%.2f',$cs->getRemise_MontanttotalHT()); ?> €</td>
	</tr>
	<?php
	
	}
	
	?>
	<tr>
		<td align="right"><?php echo $translate->_('Frais de port + emballage'); ?> : </td>
		<td></td>
		<td></td>
		<td align="right" style="padding-right:3px;padding-top:4px; padding-bottom:4px;"><?php printf('%.2f',$cs->getFdp()); ?> €</td>
	</tr>
	<tr>
		<td align="right" style="padding-right:3px;padding-top:4px; padding-bottom:4px;">TVA <?php echo $cs->getTauxTVA(); ?>% : </td>
		<td></td>
		<td></td>
		<td align="right" style="padding-right:3px;padding-top:4px; padding-bottom:4px;"><?php printf('%.2f',$cs->getMontantTVA()); ?> €</td>
	</tr>
	<tr>
		<td style="padding-top:7px; padding-bottom:7px;border-top:2px solid black;border-bottom:2px solid black;" align="right"><b><?php echo $translate->_('Total de la commande TTC'); ?> : </b></td>
		<td style="border-top:2px solid black;border-bottom:2px solid black;"> </td>
		<td style="border-top:2px solid black;border-bottom:2px solid black;"></td>
		<td style="color:white;background-color:black;border-top:2px solid black;border-bottom:2px solid black;" align="center"><b><?php printf('%.2f',$cs->getMontanttotalTTC()); ?> €</b></td>
		<td style="background-color:black;border-top:2px solid black;border-bottom:2px solid black;"></td>
	</tr>
</table>
</div>
</div>
