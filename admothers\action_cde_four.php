<?php

// validation commande frs

include ("../init2.php");	

if (isset($_GET['id_produit'])){

$id_produit = $_GET['id_produit'];
$id_frs = $_GET['frs'];
$date_dde_frs = date('Y-m-d');
$quantite_dde_frs = $_GET['quantite'];
$prix_dde_frs = $_GET['prix_dde_frs'];
$statut_dde_frs = "attente";
$comment_dde_frs = "";
	
	$sql3 = mysql_query("
	INSERT INTO `smi001_001`.`demande_fournisseur` (
	  `id_frs`,
	  `id_produit`,
	  `date_dde_frs`,
	  `quantite_dde_frs`,
	  `prix_dde_frs`,
	  `statut_dde_frs`,
	  `comment_dde_frs`
	) 
	VALUES
	  (
		'$id_frs',
		'$id_produit',
		'$date_dde_frs',
		'$quantite_dde_frs',
		'$prix_dde_frs',
		'$statut_dde_frs',
		'$comment_dde_frs'
	  ) ;
	") or die ("requete insert sql3 invalid");
	
	echo "produit ajouté";

}

// Produit dispo

if (isset($_GET['dispo'])){

$idproduit = $_GET['id_produit'];
	
	$sql3 = mysql_query("
	UPDATE produits
	SET 
	commande = ''
	WHERE referenceproduit = '".$idproduit."'
	") or die ("requete insert sql3 maj prod invalid");				
				
}

// Produit cacher

if (isset($_GET['cacher'])){

$idproduit = $_GET['id_produit'];
	
	$sql3 = mysql_query("
	UPDATE produits
	SET 
	promo = 'LOT'
	WHERE referenceproduit = '".$idproduit."'
	") or die ("requete insert sql3 maj prod invalid");				
				
}

?>

