<?php

// validation commande frs

include ("../init2.php");	

if (isset($_GET['quantite'])){

$id_produit = $_GET['id_produit'];
$id_dde_frs = $_GET['id_dde_frs'];
$id_frs = $_GET['frs'];
$date_liv = date('Y-m-d');
$quantite_liv = $_GET['quantite'];
$prix_vente = $_GET['prix_vente'];
$prix_dde_frs = $_GET['prix_dde_frs'];
$statut_dde_frs = "terminer";
	
	$sql3 = mysql_query("
		UPDATE 
		  `smi001_001`.`demande_fournisseur` 
		SET
		  `statut_dde_frs` = '$statut_dde_frs'
		WHERE `id_dde_frs` = '$id_dde_frs' ;
		
	") or die ("requete insert sql3 invalid");
	
	$sql4 = mysql_query("
	UPDATE 
	  `smi001_001`.`produits` 
	SET
	  `prixproduiteuro` = '$prix_vente',
	  `promo` = 'net',
	  `$id_frs` = '$prix_dde_frs',
	  `commande` = '',
	  `date_livraison` = '$date_liv',
	  `qte_livraison` = '$quantite_liv'

	WHERE `referenceproduit` = '$id_produit' ;
	") or die ("requete insert sql3 invalid");
	
	echo "produit ajouté";

}

// Produit non dispo

if (isset($_GET['nd'])){

$idproduit = $_GET['id_produit'];
$id_dde_frs = $_GET['id_dde_frs'];
$id_frs = $_GET['frs'];
	
	$sql3 = mysql_query("
	UPDATE produits
	SET 
	`$id_frs` = 'ND'
	WHERE referenceproduit = '".$idproduit."'
	") or die ("requete insert sql3 maj prod invalid");			

	$sql4 = mysql_query("
		UPDATE 
		  `smi001_001`.`demande_fournisseur` 
		SET
		  `statut_dde_frs` = 'annuler'
		WHERE `id_dde_frs` = '$id_dde_frs' ;
		
	") or die ("requete insert sql3 invalid");	
				
}


?>

