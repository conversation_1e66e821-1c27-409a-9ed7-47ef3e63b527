<html>
<head>
 
 
<script type="text/javascript">
<!--
 
var i = 2
var ligne_supp = ""
     
function nouveauInput(){
    i = i + 1;
	//ligne_supp = ligne_supp + "<tr>";
	ligne_supp = ligne_supp + "<input type='hidden' name='idlign" + i + "' size='5' value='200' />";
	ligne_supp = ligne_supp + "<td><input type='text' name='reference" + i + "' size='10' /></td>";
	ligne_supp = ligne_supp + "<td><input type='text' name='genrename" + i + "' size='15' /></td>";
	ligne_supp = ligne_supp + "<td><input type='text' name='designation" + i + "' size='60' /></td>";
	ligne_supp = ligne_supp + "<td><input type='text' style='text-align:right' name='puhtproduit" + i + "' size='5' /></td>";
	ligne_supp = ligne_supp + "<td><input type='text' style='text-align:center' name='qteproduit" + i + "' size='5' /></td>";
	ligne_supp = ligne_supp + "<td style='text-align:right;'>0.00&nbsp;</td>";
	ligne_supp = ligne_supp + "<td></td>";
	//ligne_supp = ligne_supp + "</tr>";
	document.getElementById('ligne_supp').innerHTML = ligne_supp;
	}

//-->
</script>
</head>
 
<body>
<div id="base">
<label for="proposition1">Proposition 1 <input type="text" name="proposition1" /><br />
<label for="proposition2">Proposition 2 <input type="text" name="proposition2" /><br />
</div>

<table>
<tr id="ligne_supp"></tr>
<tr><input type="button" onclick="nouveauInput()" value="+"/></tr>
</table>

 
</body>
</html>