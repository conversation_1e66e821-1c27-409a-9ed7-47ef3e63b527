<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Photos</title>
<link href="style_admin2.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="global" style="height:1900px; width:800px;" >

  <h1>Page Aspirateur photo </h1>

<?php
include ("../init2.php");

//PHOTO KT

/*
$result = mysql_query("SELECT * FROM produits WHERE genre = 'OUTILLAGE' AND idgroupe = '2300' GROUP BY referenceproduit") or die ("r1 invalid");
				
	while ($row = mysql_fetch_array($result)) {
			
	$ref = substr($row['referenceproduit'], 2);
	
	$dest = "../public/images/kt/KT".$ref.".png";
	$src = "http://www.kingtony.com/upload/product_images/".$ref."_s.png";
	
		if (!copy($src, $dest)) {
    echo "La copie ".$row['referenceproduit']." du fichier a échoué...\n";
	} else {
	echo '<img src="'.$src.'" width="275" height="175" />';
	}
	}
*/

//PHOTO HALF TRACK

for ($i = 1; $i <= 204; $i++) {
	
	$ref = $i;
	
	$dest = "../public/images/halftracktuto/HT3-".$ref.".jpg";
	$src = "http://www.baiv.nl/wp-content/uploads/2014/12/HF_M5_928_".$ref.".jpg";
	
		if (!copy($src, $dest)) {
    echo "La copie ".$ref." du fichier a échoué...\n";
	} else {
	echo '<img src="'.$src.'" width="275" height="175" />';
	}
	
}
				
?>
	<div class="retour"><a href="index.php">Retour</a></div>
	</div>
</body>
</html>