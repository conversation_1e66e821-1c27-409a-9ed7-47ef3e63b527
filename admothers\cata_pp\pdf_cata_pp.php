<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />

<style type="text/css">
<!--
img {
	display: block;
	margin-bottom: 15px;
	margin-top: 10px;
	margin-right: 20px;
	margin-left: 150px;	
	float: left;
}
h1 {
	display: block;
	width: 400px;
	text-align: center;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 20px;
}
h2 {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 100px;
	margin-left: 200px;
	margin-bottom: 5px;
	margin-top: 5px;
	display: block;
	width: 400px;
	text-align: center;
}
p {
	margin-bottom: 3px;
	margin-top: 3px;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 35px;
}
-->
</style>
</head>
<?php
	include('init.php');
	$genre = $_POST['genre'];
	
	$groupe = $_POST['groupe'];
	
	if ($_POST['groupe'] == ""){
	
	$groupe = "all";
	
	}
	
	$tx = $_POST['tx'];
	
	if ($groupe == "all"){
	
	$result = mysql_query('SELECT * FROM produits WHERE genre = "'.$genre.'" and promo = "'.$tx.'"') or die ("r3 invalid");
	
	} else {
	
	$result = mysql_query('SELECT * FROM produits WHERE idgroupe = "'.$groupe.'" and genre = "'.$genre.'" and promo = "'.$tx.'"') or die ("r3 invalid");
	
	}
	
	

echo "<h1>".$genre." - ".$groupe." - ".$tx."</h1>";
	
	while ($tab = mysql_fetch_array($result)) {
?>
<table width="660" border="0" cellspacing="0" cellpadding="0">
  <tr>
<?php
if(file_exists("../../public/images/produits/".$tab['referenceproduit'].".jpg")) {
?>
<td style="padding-left:100px;" ><img src="../../public/images/produits/<?php echo $tab['referenceproduit'];?>.jpg" width="200" /></td>
<?php
}
?>
  </tr>
 </table>
<table width="680" border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td style="width:100px;" ><?php echo $tab['referenceproduit'];?></td>
	
    <td style="width:250px;" ><?php echo htmlspecialchars($tab['descriptionproduit'], ENT_QUOTES);?></td>
    <td style="width:250px;" ><?php echo htmlspecialchars($tab['descriptionproduiten'], ENT_QUOTES);?></td>
    <td style="width:80px;" ><?php echo $tab['prixproduiteuro'];?>&#128; HT</td>
  </tr>
</table>
<br />
<hr />
<?php
}
?>
</body>
</html>