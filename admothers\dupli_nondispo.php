<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Photos</title>
<link href="style_admin2.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="global" style="height:1900px; width:800px;" >

  <h1>Page Test </h1>

<?php
include ("../init2.php");

$result = mysql_query("SELECT * FROM non_dispo") or die ("r1 invalid");
				
	while ($row = mysql_fetch_array($result)) {
			
	$ref = $row['reference'];
	$idcde = $row['id_command'];
		
	$result2 = mysql_query("UPDATE static_lignes_commandes
	SET produit_nd = 1
	WHERE commande_id = '$idcde' AND produit_reference = '$ref'") or die ("r2 invalid");
	
	echo "nd ajouter cde : ".$idcde." reference ".$ref;
	}
				
?>
	<div class="retour"><a href="index.php">Retour</a></div>
	</div>
</body>
</html>