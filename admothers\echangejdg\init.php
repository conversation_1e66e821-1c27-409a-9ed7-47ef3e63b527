<?php
	//test1
	//démarrage de la session
	session_start();

	/**
	 * Code d'initialisation, a executer avant tout autre code
	 * à inclure au début de tout les fichier avec require_once
	 */

	// definition du niveau d'affichage des erreurs
	error_reporting(E_ALL);

	// définition de l'include path
	define('ZF_DIR',dirname(__FILE__).'/lib');
	define('CLASS_DIR',dirname(__FILE__).'/class');
	set_include_path(
		'.'.PATH_SEPARATOR.
		ZF_DIR.PATH_SEPARATOR.
		CLASS_DIR.PATH_SEPARATOR.
		get_include_path()
	);

	//initialisation du traducteur
	require_once 'Zend/Translate.php';
	$translate = new Zend_Translate('Csv',dirname(__FILE__).'/langage/fr.csv','fr');
	// 	Add English language
	$translate->addTranslation(dirname(__FILE__).'/langage/en.csv','en');
	if(!isset($_SESSION['lang'])){
		$_SESSION['lang'] = 'fr';
	}
	else{
		if(isset($_GET['lang'])){
			if($_GET['lang'] == 'en'){
				$_SESSION['lang'] = 'en';
			}
			else{ // le français est la langue par défaut
				$_SESSION['lang'] = 'fr';
			}
		}
	}
	// definition de la langue en cours
	$translate->setLocale($_SESSION['lang']);

	// initialisation de la base de données
	$database = null;
	try{
//		$database = new PDO("mysql:host=bfonet.com;dbname=web9_db1","web9_u1","smi38160");
//		$database = new PDO("mysql:host=*************;dbname=smi_web9_db1","smi_webmaster","smi38160");
//		$database = new PDO("mysql:host=************;dbname=smi_web9_db1","smi_webmaster","smi38160");
//$database = new PDO("mysql:host=localhost;dbname=smi001_001","root","");
//$database = new PDO("mysql:host=jeep-dodge-gmc.com;dbname=smi001_001","smi001","smi38160");
$database = new PDO("mysql:host=mysql.jeep-dodge-gmc.com;dbname=smi001_001","smi001","smi38160");

		$database->setAttribute(PDO::ATTR_ERRMODE,PDO::ERRMODE_EXCEPTION);
		$database->setAttribute(PDO::MYSQL_ATTR_USE_BUFFERED_QUERY, true);
		$database->exec('SET NAMES UTF8');
	}
	catch(PDOException $e){
		//die("Oups!! database connection failed");
		die (header('Location: http://www.jeep-dodge-gmc.com/smi/maintenance.php'));
	}

	require_once 'Zend/Registry.php';
	//On stocke le tout dans le registre
	Zend_Registry::set('translate',$translate);
	Zend_Registry::set('database',$database);

	// definition du basePath
	$basePath = '/smi/';
	Zend_Registry::set('basePath',$basePath);