-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 2.11.5
-- http://www.phpmyadmin.net
--
-- Serveur: localhost
-- G�n�r� le : Jeu 03 Avril 2008 � 10:55
-- Version du serveur: 5.0.51
-- Version de PHP: 5.2.5

SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

--
-- Base de donn�es: `sitesmi`
--

-- --------------------------------------------------------

--
-- Structure de la table `adresses`
--

DROP TABLE IF EXISTS `adresses`;
DROP TABLE IF EXISTS `annonces`;
DROP TABLE IF EXISTS `commande_adresse`;
DROP TABLE IF EXISTS `ligne_commande`;
DROP TABLE IF EXISTS `commandes`;
DROP TABLE IF EXISTS `clients`;
DROP TABLE IF EXISTS `frais_port`;
DROP TABLE IF EXISTS `liens`;
DROP TABLE IF EXISTS `parametres`;
DROP TABLE IF EXISTS `promos`;
DROP TABLE IF EXISTS `produits`;


CREATE TABLE IF NOT EXISTS `adresses` (
  `idadresse` int(11) NOT NULL auto_increment,
  `raisonsocial` varchar(120) collate utf8_bin NOT NULL,
  `emailclient` varchar(100) collate utf8_bin NOT NULL,
  `nomrue` varchar(100) collate utf8_bin NOT NULL,
  `codepostal` varchar(10) collate utf8_bin NOT NULL,
  `ville` varchar(60) collate utf8_bin NOT NULL,
  `pays` varchar(60) collate utf8_bin NOT NULL,
  PRIMARY KEY  (`idadresse`),
  KEY `fk_client` (`emailclient`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


CREATE TABLE IF NOT EXISTS `annonces` (
  `id` int(11) NOT NULL auto_increment,
  `dateannonce` date NOT NULL,
  `telephone` varchar(20) collate utf8_bin default NULL,
  `fax` varchar(20) collate utf8_bin default NULL,
  `operation` varchar(5) collate utf8_bin default NULL,
  `genre` varchar(50) collate utf8_bin default NULL,
  `typeannonce` varchar(50) collate utf8_bin default NULL,
  `sujet` varchar(50) collate utf8_bin NOT NULL,
  `detail` text collate utf8_bin NOT NULL,
  `prix` double NOT NULL,
  `image` varchar(255) collate utf8_bin default NULL,
  `emailclient` varchar(100) collate utf8_bin NOT NULL,
  `status` tinyint(1) NOT NULL default '0',
  PRIMARY KEY  (`id`),
  KEY `emailclient` (`emailclient`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

CREATE TABLE IF NOT EXISTS `clients` (
  `emailclient` varchar(100) collate utf8_bin NOT NULL,
  `nomclient` varchar(60) collate utf8_bin NOT NULL,
  `prenomclient` varchar(60) collate utf8_bin NOT NULL,
  `passwdclient` varchar(32) collate utf8_bin NOT NULL,
  `isadmin` tinyint(1) NOT NULL,
  `uid` varchar(32) collate utf8_bin default NULL,
  `telclient` varchar(10) collate utf8_bin default NULL,
  `faxclient` varchar(10) collate utf8_bin default NULL,
  `infos` text collate utf8_bin NOT NULL,
  PRIMARY KEY  (`emailclient`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

CREATE TABLE IF NOT EXISTS `commandes` (
  `idcommande` int(11) NOT NULL auto_increment,
  `emailclient` varchar(100) collate utf8_bin NOT NULL,
  `prixtotalhtcommande` varchar(20) collate utf8_bin NOT NULL,
  `fraisdeport` double NOT NULL,
  `statutcommande` varchar(50) collate utf8_bin NOT NULL,
  `datecommande` date NOT NULL,
  `tvacommande` float NOT NULL,
  `modepaiement` varchar(30) collate utf8_bin NOT NULL,
  `iscommandeadmin` tinyint(1) NOT NULL,
  `retraitmagasin` tinyint(1) NOT NULL default '0',
  PRIMARY KEY  (`idcommande`),
  KEY `fk_clientcommande` (`emailclient`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_bin;



CREATE TABLE IF NOT EXISTS `commande_adresse` (
  `idcommande` int(11) NOT NULL,
  `typeadresse` tinyint(1) NOT NULL,
  `raisonsocial` varchar(255) collate utf8_bin NOT NULL,
  `nomrue` varchar(100) collate utf8_bin NOT NULL,
  `codepostal` varchar(10) collate utf8_bin NOT NULL,
  `ville` varchar(60) collate utf8_bin NOT NULL,
  `pays` varchar(60) collate utf8_bin NOT NULL,
  PRIMARY KEY  (`idcommande`,`typeadresse`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


CREATE TABLE IF NOT EXISTS `frais_port` (
  `id` int(11) NOT NULL auto_increment,
  `min` float NOT NULL,
  `max` float NOT NULL,
  `taux` float NOT NULL,
  `fixe` float NOT NULL,
  PRIMARY KEY  (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

INSERT INTO `frais_port` (`id`, `min`, `max`, `taux`, `fixe`) VALUES
(1, 0, 50.99, 7, 12),
(2, 51, 300, 8, 11),
(3, 300.01, 1000, 9, 10),
(4, 1000.01, 500000, 10, 10);



CREATE TABLE IF NOT EXISTS `liens` (
  `idliens` int(11) NOT NULL AUTO_INCREMENT,
  `nomliens` varchar(120) collate utf8_bin NOT NULL,
  `adresseliens` varchar(240) collate utf8_bin default NULL,
  `telliens` varchar(10) collate utf8_bin default NULL,
  `faxliens` varchar(10) collate utf8_bin default NULL,
  `webliens` varchar(100) collate utf8_bin default NULL,
  `emailliens` varchar(100) collate utf8_bin default NULL,
  `commentaires` text collate utf8_bin,
  PRIMARY KEY  (`idliens`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;



CREATE TABLE IF NOT EXISTS `ligne_commande` (
  `idligne` int(11) NOT NULL auto_increment,
  `idcommande` int(11) NOT NULL,
  `qteproduit` tinyint(4) NOT NULL,
  `reference` varchar(30) collate utf8_bin NOT NULL,
  `genrename` varchar(150) collate utf8_bin NOT NULL,
  `groupename` varchar(150) collate utf8_bin NOT NULL,
  `designation` varchar(200) collate utf8_bin NOT NULL,
  `puhtproduit` float NOT NULL,
  PRIMARY KEY  (`idligne`),
  KEY `fk_ligne_commande` (`idcommande`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_bin;



CREATE TABLE IF NOT EXISTS `parametres` (
  `cle` varchar(255) collate utf8_bin default NULL,
  `valeur` text collate utf8_bin
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

--
-- Contenu de la table `parametres`
--

INSERT INTO `parametres` (`cle`, `valeur`) VALUES
('contact', 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),
('contre_remb', 0x3130),
('dollar', 0x312e3633),
('infoEntrepriseAcc', 0x3c703e537572706c7573204d696c6974616972657320657420496e647573747269656c733c6272202f3e2b2064652035303020746f6e6e6573206465207069266567726176653b6365733c6272202f3e454d61696c203a20636f6e74616374406a6565702d646f6467652d676d632e636f6d3c6272202f3e54656c203a20282b3333292034373620363434203335363c2f703e),
('mentionLegal', 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),
('merchant_id', 0x303134313431363735393131313131),
('tva', 0x31392e36),
('bdd_produit', 0x30332f30342f32303038);

-- --------------------------------------------------------

--
-- Structure de la table `produits`
--


CREATE TABLE IF NOT EXISTS `produits` (
  `idproduit` int(11) NOT NULL auto_increment,
  `descriptionproduit` varchar(200) collate utf8_bin NOT NULL,
  `referenceproduit` varchar(30) collate utf8_bin NOT NULL,
  `prixproduiteuro` double NOT NULL,
  `promo` tinyint(1) NOT NULL default '0',
  `idgroupe` varchar(6) collate utf8_bin NOT NULL,
  `groupe` varchar(255) collate utf8_bin NOT NULL,
  `genre` varchar(255) collate utf8_bin NOT NULL,
  `image` varchar(255) collate utf8_bin NOT NULL,
  PRIMARY KEY  (`idproduit`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

--
-- Contenu de la table `produits`
--
-- --------------------------------------------------------

--
-- Structure de la table `promos`
--


CREATE TABLE IF NOT EXISTS `promos` (
  `idproduit` int(11) NOT NULL,
  `date` date NOT NULL,
  PRIMARY KEY  (`idproduit`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
