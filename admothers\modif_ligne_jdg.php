<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Modif Ligne JDG</title>
<link href="style_reactu.css" rel="stylesheet" type="text/css" />
</head>

<body>
<div id="global">

<h1> Modification d'une ligne du tarif JDG </h1>

<div id="corps">

<table border="1" cellpadding="0" cellspacing="0" bordercolor="#000000">
  <tr>
	<th width="66" height="31" valign="middle"><div align="center">Idproduit</div></th>
    <th width="66" height="31" valign="middle"><div align="center">R&eacute;f&eacute;rence</div></th>
    <th width="380" valign="middle"><div align="center">D&eacute;signation</div></th>
    <th width="100" valign="middle"><div align="center">Prix HT</div></th>
  </tr>
  
<?php

include("reactu_tarif/init.php");

$modif = $_POST['modif'];

echo '<div align="center"><form action="valid_ligne_jdg.php" method="post">';
echo "<br />";

$result = mysql_query("
SELECT * FROM produits_copy
WHERE UPPER(referenceproduit) LIKE UPPER('%".$modif."%') 
OR UPPER(descriptionproduit) LIKE UPPER('%".$modif."%')") 
or die ("r1 invalid");


while ($tab = mysql_fetch_array($result)) {
		echo "<tr>";
		echo '<td>'.$tab['idproduit'].'</td>';
		echo '<input type="hidden" name="modif" value="'.$tab['idproduit'].'"/>';
		echo '<td><input type="text" name="referenceproduit[]" multiple size="10" value= "'.$tab['referenceproduit'].'"/></td>';
		echo '<td><input type="text" name="descriptionproduit[]" multiple size="70" value= "'.htmlspecialchars($tab['descriptionproduit']).'"/></td>';
		echo '<td><input type="text" name="prixproduiteuro[]" multiple size="5" value= "'.$tab['prixproduiteuro'].'"/></td>';
		echo '<td><input type=button value="Modif" onclick="form.action=\'http://jeep-dodge-gmc.com/smi/admothers/valid_ligne_jdg.php?modif='.$tab['idproduit'].'\';form.submit()"></';

		echo "</tr>";
}
		
?>
</table>
		</form></div>
</div>
</div>
</body>
</html>