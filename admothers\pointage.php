<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>Pointage personnel</title>
<style type="text/css">
.bouton_retour {
	height: 50px;
	width: 300px;
	background-color: #0FC;
	border: 3px solid #000;
	font-size: 24px;
	color: #000;
	margin-right: 10px;
}
.bouton_debut {
	height: 150px;
	width: 300px;
	background-color: #3F3;
	border: 3px solid #000;
	font-size: 24px;
	color: #000;
	margin-right: 10px;
}
.bouton_fin {
	height: 150px;
	width: 300px;
	background-color: #F60;
	border: 3px solid #000;
	font-size: 24px;
	color: #000;
	margin-right: 10px;
}
.bouton_pers {
	height: 150px;
	width: 150px;
	background-color: #0CF;
	border: 3px solid #000;
	font-size: 24px;
	color: #000;
	margin-right: 10px;
}
h1 {
	background-position: center;
	text-align: center;
	font-size: 40px;
}
h3 {
	text-align: center;
}
</style>

<SCRIPT LANGUAGE="JavaScript">

function heure(){
var Today = new Date;
var Heure = Today.getHours();
var Min = Today.getMinutes();
var Sec = Today.getSeconds();
    if (Heure < 10) { Heure = "0" + Heure; } 
    if (Min < 10) { Min = "0" + Min; } 
    if (Sec < 10) { Sec = "0" + Sec; } 
var Message = "il est " + Heure + " H " + Min + " - " + Sec;
document.getElementById('heure').innerHTML = Message;
//jour
var annee = Today.getFullYear();
var moi = Today.getMonth();
var mois = new Array('Janvier', 'F&eacute;vrier', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Ao&ucirc;t', 'Septembre', 'Octobre', 'Novembre', 'D&eacute;cembre');
var j = Today.getDate();
var jour = Today.getDay();
var jours = new Array('Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi');

var resultat = jours[jour]+' '+j+' '+mois[moi]+' '+annee;
document.getElementById("jour").innerHTML = resultat;
setTimeout("heure();",1000);
}

function hiddenh1(){

setTimeout(function(){document.getElementById("h1valid").style.visibility="hidden"},10000);

}

function champsheure(){
var Today = new Date;
//heure
var Heure = Today.getHours();
var Min = Today.getMinutes();
var Sec = Today.getSeconds();
    if (Heure < 10) { Heure = "0" + Heure; } 
    if (Min < 10) { Min = "0" + Min; } 
    if (Sec < 10) { Sec = "0" + Sec; } 
var Message = Heure + ":" + Min + ":" + Sec;
document.getElementById('champsheure').value = Message;
//jour
var annee = Today.getFullYear();
var moi = Today.getMonth()+1;
var j = Today.getDate();
var resultat = annee+'-'+moi+'-'+j;
document.getElementById("champsdate").value = resultat;
setTimeout("heure();",1000);
}

</SCRIPT>

</head>

<?php

if (!isset($_GET['personne'])){
if (!isset($_POST['debut_fin'])){

?>
<body onload="heure()">
<h1 id="heure"></h1>
<h3 id="jour"></h3>
<div align="center"><form  name="choix_nom" method="get" action="pointage.php">
 	<input class="bouton_pers" type="submit" style="background-color:purple; color:white;"  name="personne" value="Angelique" />
    <input class="bouton_pers" type="submit" style="background-color:red; color:white;" name="personne" value="Ender" />
    <input class="bouton_pers" type="submit" name="personne" value="Fabien" />
    <input class="bouton_pers" type="submit" style="background-color:green; color:white;" name="personne" value="JB" />
	
</form></div>
<p align="center"><strong>cliquer sur la personne pour pointer</strong></p>

<?php

}
}

if (isset($_GET['personne'])){

?>
<body onload="heure();champsheure();">
<h1 id="heure"></h1>
<h3 id="jour"></h3>

<div align="center"><form  name="choix_nom" method="post" action="pointage.php">
 	<input type="hidden" name="personne" value="<?php echo $_GET['personne']; ?>" />
	<input type="hidden" id="champsheure" name="heure" value="" />
	<input type="hidden" id="champsdate" name="date" value="" />
 	<input class="bouton_debut" type="submit" name="debut_fin" value="Debut" />
    <input class="bouton_fin" type="submit" name="debut_fin" value="Fin" /><br /><br /><br />
    <input class="bouton_retour" type="submit" name="retour" value="Retour" />
</form></div>

<?php

}

if (isset($_POST['debut_fin'])){

include ("../init2.php");

$date = $_POST['date'];
$personne = $_POST['personne'];
$heure = $_POST['heure'];
$debut_fin =  $_POST['debut_fin'];

	$sql = mysql_query("
	INSERT INTO personnel (
	date,
	personne,
	heure,
	debut_fin
	) 
				
	VALUES (
	'".$date."',
	'".$personne."',
	'".$heure."',
	'".$debut_fin."'
	)") or die ("requete insert sql invalid");



?>
<body onload="heure();hiddenh1();">
<h1 id="heure"></h1>
<h3 id="jour"></h3>

<div align="center"><form  name="choix_nom" method="get" action="pointage.php">
 	<input class="bouton_pers" type="submit" style="background-color:purple; color:white;"  name="personne" value="Angelique" />
    <input class="bouton_pers" type="submit" style="background-color:red; color:white;" name="personne" value="Ender" />
    <input class="bouton_pers" type="submit" name="personne" value="Fabien" />
    <input class="bouton_pers" type="submit" style="background-color:green; color:white;" name="personne" value="JB" />
</form></div>
<p align="center"><strong>cliquer sur la personne pour pointer</strong></p>
<h1 id="h1valid" style="color: #F00;" >Merci d'avoir pointé !</h1>
<?php

}

?>
</body>
</html>
