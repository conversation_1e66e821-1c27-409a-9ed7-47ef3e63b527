<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Publipostage</title>
<link href="style_echange.css" rel="stylesheet" type="text/css" />
</head>

<body style="font-color:white;">

<div id="global" style="height: 900px;">
<h1> Publipostage </h1>

<div align="center" id="corps">

<?php

	include ("../init2.php");
	
?>

<form action="publipostage.php" style="height: 200px;" method="post">
<p>Recherche clients multiples</p>
<textarea style="margin-left: 20px;margin-top: 20px;height: 100px;width: 250px" name="ajout_abonne"></textarea><br />
<input type="submit" name="valider" value="Ok" />

</form>



<?php


if (isset($_POST['ajout_abonne'])) {

?>

			<table id="tab_devis" style="width:1300px;" border="1" cellspacing="0" cellpadding="0">

<?php

$tabTxt = explode("\r\n", $_POST['ajout_abonne']);

	foreach ( $tabTxt as $txt2 ) {
		if ( $txt2 != '' ) {
		
			$txt = strtolower($txt2);
		
			$result2 = mysql_query("
			SELECT * FROM facture WHERE id_facture = '$txt'
			") or die ("requete select");
			
			$nb = mysql_result($result2, 0);
			
			if ($nb != 0){	
				
			$result = mysql_query("
			SELECT * FROM facture 
			INNER JOIN clients ON clients.emailclient = facture.emailclient 
			INNER JOIN adresses_facturation ON clients.emailclient = adresses_facturation.af_emailclient 
			WHERE id_facture = '$txt'
			") or die ("requete select");
			
				while ($row = mysql_fetch_array($result)) {
				echo "<tr>";
				echo "<td>".$row['id_facture']."</td>";
				echo "<td>".$row['date_facture']."</td>";
				echo "<td>".$row['emailclient']."</td>";
				echo "<td>".$row['nomclient']."</td>";
				echo "<td>".$row['prenomclient']."</td>";
				echo "<td>";
								if($row['indiceclient'] == "1"){
								echo '<div align="center"><img src="../public/images/rond_vert.png" alt="vert"/></div>';
								}
								if($row['indiceclient'] == "2"){
								echo '<div align="center"><img src="../public/images/rond_rouge.png" alt="rouge"/></div>';
								}
								if($row['indiceclient'] == "3"){
								echo '<div align="center"><img src="../public/images/rond_noir.png" alt="noir"/></div>';
								}
								if($row['indiceclient'] == "4"){
								echo '<div align="center"><img src="../public/images/medaille.png" alt="medaille"/></div>';
								}
								if($row['indiceclient'] == "5"){
								echo '<div align="center"><img src="../public/images/pro.png" alt="pro"/></div>';
								}
				echo "</td>";
				echo "<td>".$row['infos']."</td>";	
				echo "<td>".$row['af_raisonsocial']."</td>";		
				echo "<td>".$row['af_nomrue']."</td>";	
				echo "<td>".$row['af_codepostal']."</td>";	
				echo "<td>".$row['af_ville']."</td>";	
				echo "<td>".$row['af_pays']."</td>";	
				echo "</tr>";
				}
			} else {
			
					  echo "<tr>";
						echo "<td></td>";
						echo "<td>".$txt."</td>";
						echo "<td></td>";
						echo "<td></td>";
						echo "<td></td>";
						echo "<td></td>";
						echo "<td></td>";
						echo "<td></td>";
						echo "<td></td>";
						echo "<td></td>";
						echo "<td></td>";
						echo "<td></td>";
					  echo "</tr>";
			
			}
		}
	}

echo '</table>';
	
}

?>
</div>
</body>
</html>