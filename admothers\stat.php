<?php

include ("../lib/graph/src/jpgraph.php");
include ("../lib/graph/src/jpgraph_pie.php");
include ("../lib/graph/src/jpgraph_bar.php");
	
		if (isset($_POST['genre']) && $_POST['genre'] != "ALL") {
		
			$genre = "AND genre = '".$_POST['genre']."'";
			$nom_genre = " de ".$_POST['genre'];
			
		}else{
		
		$genre = "";
		$nom_genre = "";
		
		}

		
// Calcul date du mois précédent

$mois_1 = date("m")-1;

if ($mois_1 < 1){

$mois_debut = 12;
$annee_debut = date("Y")-1;

} else {

$mois_debut = $mois_1;
$annee_debut = date("Y");
}
if (isset($_POST['date_debut'])){
$date_debut = $_POST['date_debut'];
} else {
$date_debut = $annee_debut."-".$mois_debut."-01";
}

if (isset($_POST['date_fin'])){
$date_fin = $_POST['date_fin'];
} else {
$date_fin = $annee_debut."-".$mois_debut."-31";
}

		
// Calcul date des 12 derniers mois

		$mois_12 = date("m")-12;
		
		if ($mois_12 > 0){
		
			$mois_start = $mois_12;
			$annee_start = date("Y");
		
		} else {
		
			$mois_start = 12 + $mois_12;
			$annee_start = date("Y") - 1;
		
		}
		
		$date_start = $annee_start."-".$mois_start."-01";

		$date_end = date("Y-m")."-01";
		
		$result = mysql_query("SELECT sum(totht) AS sum_ht, month(date_facture) AS mois FROM facture WHERE date_facture BETWEEN '$date_start' AND '$date_end' $genre GROUP BY MONTH(date_facture) order by year(date_facture), month(date_facture)") or die ("r1 invalid");

		while ($row = mysql_fetch_array($result)) {
		
		
		$sum_q2bis = $row['sum_ht'];
		$nb_q2[] = $sum_q2bis;
		$q2[] = $row['mois'];

		}
?>
<div class="title">		
	<form action="stat.php" method="POST" enctype="multipart/form-data">
	Stat du mois dernier (du <input type="text" style="width:70px; text-align:center;" name="date_debut" value="<?php echo $date_debut;?>" />
	au <input type="text" style="width:70px; text-align:center;" name="date_fin" value="<?php echo $date_fin;?>" />
	<input type="submit" name="submit" value="Ok" /> )
	</form>
</div>	
<?php
		$graph = new Graph(800,300);

		// Réprésentation linéaire
		$graph->SetScale("textlin");

		// Ajouter une ombre au conteneur
		$graph->SetShadow();

		// Fixer les marges
		$graph->img->SetMargin(40,30,25,40);

		// Création du graphique histogramme
		$bplot = new BarPlot($nb_q2);

		// Spécification des couleurs des barres
		$bplot->SetFillColor(array('blue', 'green', 'red', 'yellow'));
		// Une ombre pour chaque barre
		$bplot->SetShadow();

		// Afficher les valeurs pour chaque barre
		$bplot->value->Show();
		// Modifier le rendu de chaque valeur
		$bplot->value->SetFormat('%d');

		// Ajouter les barres au conteneur
		$graph->Add($bplot);

		// Le titre
		$graph->title->SetFont(FF_FONT1,FS_BOLD);
		
		$graph->title->Set("CA 12 derniers mois");

		// Titre pour l'axe horizontal(axe x) et vertical (axe y)
		$graph->xaxis->title->Set("Mois");
		$graph->yaxis->title->Set("CA HT");

		$graph->yaxis->title->SetFont(FF_FONT1,FS_BOLD);
		$graph->xaxis->title->SetFont(FF_FONT1,FS_BOLD);

		// Légende pour l'axe horizontal
		$graph->xaxis->SetTickLabels($q2);

		// Afficher le graphique
		$graph->Stroke('../public/images/produits/stat.png');
		
?>
<div class="title" style="width: 90%;">		
	<img src="../public/images/produits/stat.png" alt=""/>	
</div>	
<?php		

	//requete CA comptoir
		$result2 = mysql_query("SELECT sum(totht) AS ca_comptoir,count(totht) AS nb_comptoir  FROM facture WHERE date_facture BETWEEN '$date_debut' AND '$date_fin' AND port = '0'") or die ("r2 invalid");
	//requete CA exp
		$result3 = mysql_query("SELECT sum(totht) AS ca_exp,count(totht) AS nb_exp FROM facture WHERE date_facture BETWEEN '$date_debut' AND '$date_fin' AND port != '0'") or die ("r3 invalid");
		
		while ($row2 = mysql_fetch_array($result2)) {
		
		$sum_q1bis = $row2['ca_comptoir'];
		$nb_q1[] = $sum_q1bis;
		$q1[] = "CA comptoir";

		$sum_q1bis = $row2['nb_comptoir'];
		$nb_q3[] = $sum_q1bis;
		$q3[] = "Nb client compt";
		
		$panier_moy_comptoir = $row2['ca_comptoir'] / $row2['nb_comptoir'];
		
		$ca_comptoir = $row2['ca_comptoir'];
		$nb_comptoir = $row2['nb_comptoir'];
		
		}
		
		while ($row3 = mysql_fetch_array($result3)) {
		
		$sum_q1bis = $row3['ca_exp'];
		$nb_q1[] = $sum_q1bis;
		$q1[] = "CA exp";
		
		$sum_q1bis = $row3['nb_exp'];
		$nb_q3[] = $sum_q1bis;
		$q3[] = "Nb client exp";
		
		$panier_moy_exp = $row3['ca_exp'] / $row3['nb_exp'];
		
		$ca_exp = $row3['ca_exp'];
		$nb_exp = $row3['nb_exp'];

		}

// Construction du graphique
		$graph = new PieGraph(400,300);

		// Créer un graphique secteur (classe PiePlot)
		$oPie = new PiePlot($nb_q1);

		// Légendes qui accompagnent chaque secteur, ici chaque année
		$oPie->SetLegends($q1);
		
		// Titre du graphique
		$oPie->title->Set("CA comptoir/exp");

		// position du graphique (légèrement à droite)
		$oPie->SetCenter(0.4); 
		
		// Spécification des couleurs des barres
		$oPie->SetSliceColors(array('blue', 'green'));

		$oPie->SetValueType(PIE_VALUE_ABS);

		// Format des valeurs de type entier
		$oPie->value->SetFormat('%d');

		// Ajouter au graphique le graphique secteur
		$graph->Add($oPie);

		// Provoquer l'affichage (renvoie directement l'image au navigateur)
		$graph->Stroke('../public/images/produits/stat_ca_comptoir.png');
?>
<div class="title">		
	<img src="../public/images/produits/stat_nb_comptoir.png" alt=""/>
</div>	
<?php			
		
// Construction du graphique
		$graph = new PieGraph(400,300);

		// Créer un graphique secteur (classe PiePlot)
		$oPie = new PiePlot($nb_q3);

		// Légendes qui accompagnent chaque secteur, ici chaque année
		$oPie->SetLegends($q3);
		
		// Titre du graphique
		$oPie->title->Set("Nbre client comptoir/exp");

		// position du graphique (légèrement à droite)
		$oPie->SetCenter(0.4); 
		
		// Spécification des couleurs des barres
		$oPie->SetSliceColors(array('blue', 'green'));

		$oPie->SetValueType(PIE_VALUE_ABS);

		// Format des valeurs de type entier
		$oPie->value->SetFormat('%d');

		// Ajouter au graphique le graphique secteur
		$graph->Add($oPie);

		// Provoquer l'affichage (renvoie directement l'image au navigateur)
		$graph->Stroke('../public/images/produits/stat_nb_comptoir.png');
		
?>
<div class="title">		
	<img src="../public/images/produits/stat_nb_comptoir.png" alt=""/>
</div>	
<?php	
		
		//requete CA par genre
		$result4 = mysql_query("SELECT sum(totht) AS ca_genre, genre FROM facture WHERE date_facture BETWEEN '$date_debut' AND '$date_fin' GROUP BY genre") or die ("r3 invalid");
		$tot_sum_q4bis = 0;
		while ($row4 = mysql_fetch_array($result4)) {
		
			if ($row4['genre'] == "JEEP " || $row4['genre'] == "DODGE " || $row4['genre'] == "GMC " || $row4['genre'] == "RENAULT "){
			
			$sum_q4bis = $row4['ca_genre'];
			$nb_q4[] = $sum_q4bis;
			$q4[] = $row4['genre'];
			
			} else {
			
			$sum_q4bis = $row4['ca_genre'];
			$tot_sum_q4bis += $sum_q4bis;
			
			}
		
		}
		
		$nb_q4[] = $tot_sum_q4bis;
		$q4[] = "AUTRE";
		
		// Construction du graphique
		$graph = new PieGraph(400,300);

		// Créer un graphique secteur (classe PiePlot)
		$oPie = new PiePlot($nb_q4);

		// Légendes qui accompagnent chaque secteur, ici chaque année
		$oPie->SetLegends($q4);
		
		// Titre du graphique
		$oPie->title->Set("CA par genre");

		// position du graphique (légèrement à droite)
		$oPie->SetCenter(0.4); 
		
		// Spécification des couleurs des barres
		$oPie->SetSliceColors(array('blue', 'green', 'red', 'yellow', 'purple'));

		$oPie->SetValueType(PIE_VALUE_ABS);

		// Format des valeurs de type entier
		$oPie->value->SetFormat('%d');

		// Ajouter au graphique le graphique secteur
		$graph->Add($oPie);

		// Provoquer l'affichage (renvoie directement l'image au navigateur)
		$graph->Stroke('../public/images/produits/stat_ca_genre.png');
		
?>
<div class="title">		
	<img src="../public/images/produits/stat_ca_genre.png" alt=""/>
</div>	
<?php			
		
// calcul des nouveaux clients

		$i = 0;

		$result6 = mysql_query("SELECT COUNT(*) AS nb_fact, client_email FROM facture INNER JOIN static_commandes ON static_commandes.id = facture.id_command GROUP BY client_email HAVING nb_fact = 1") or die ("r6 invalid");
		
		while ($row7 = mysql_fetch_array($result6)) {
		
		$client_email = $row7['client_email'];
		
		$result7 = mysql_query("SELECT client_email FROM facture INNER JOIN static_commandes ON static_commandes.id = facture.id_command WHERE client_email = '$client_email' AND date_facture BETWEEN '$date_debut' AND '$date_fin'") or die ("r7 invalid");
		
		$nb_new_cli = mysql_num_rows($result7);
		
				$nb_new_cli = mysql_num_rows($result7);
		
			if($nb_new_cli == 1){
			
				$i++;
			
			}
		
		}
		
// Calcul des 100 produit les plus vendus en quantité


		$result8 = mysql_query("SELECT *, SUM(produit_qte) AS qte_vendu FROM static_commandes INNER JOIN static_lignes_commandes ON static_commandes.id = static_lignes_commandes.commande_id WHERE commande_date > '$date_debut' AND commande_date < '$date_fin' AND produit_genregroupe LIKE '$genre%' AND produit_designation NOT LIKE '%EURO DE%' GROUP BY produit_reference ORDER BY qte_vendu DESC LIMIT 100") or die ("r7 invalid");
		
		
		?>

		<table class="table-fill" style="width:60%; border: solid 3px gray;" >
		  <tr>
			<td>CA du mois</td>
			<td><?php echo number_format($ca_comptoir + $ca_exp, 2, '.', '')." &euro;";?></td>
		  </tr>
		  <tr>
			<td>Nb cde du mois</td>
			<td><?php echo number_format($nb_comptoir + $nb_exp, 2, '.', '')." &euro;";?></td>
		  </tr>
		  <tr>
			<td>Panier Moyen</td>
			<td>
			<?php
			
			$ca_moyen = ($ca_comptoir + $ca_exp) / ($nb_comptoir + $nb_exp);
			echo number_format($ca_moyen, 2, '.', '')." &euro;";
			
			?>
			</td>
		  </tr>
		  <tr>
			<td>Panier Moyen Exp&eacute;dition</td>
			<td><?php echo number_format($panier_moy_exp, 2, '.', '')." &euro;";?></td>
		  </tr>
		  <tr>
			<td>Panier Moyen Comptoir</td>
			<td><?php echo number_format($panier_moy_comptoir, 2, '.', '')." &euro;";?></td>
		  </tr>
		  <tr>
			<td>Nouveau client</td>
			<td><?php echo $i." / ".($nb_comptoir + $nb_exp)." soit <strong>".number_format((($i/($nb_comptoir + $nb_exp))*100), 0, '.', '');?>%</strong></td>
		  </tr>
		</table>
	</div>
	<div id="middle">
		<table class="table-fill" style="width:90%;" >
		  <tr>
			<th>&nbsp;</th>
			<th>Reference</th>
			<th>Designation</th>
			<th>Genre / Groupe</th>
			<th>Prix</th>
			<th>Qte vendu</th>
		  </tr>
<?php	
		$k = 1;
		while ($row8 = mysql_fetch_array($result8)) {	  
		  echo '<tr>';
			echo '<td>'.$k.'</td>';
			echo '<td>'.$row8['produit_reference'].'</td>';
			echo '<td>'.$row8['produit_designation'].'</td>';
			$genregroupe = $row8['produit_genregroupe'];
			$genregroupe2 = explode("/", $genregroupe);
			echo '<td>'.$genregroupe2[0].'</td>';
			echo '<td>'.$row8['produit_puht'].'</td>';
			echo '<td>'.$row8['qte_vendu'].'</td>';
		  echo '</tr>';
		  $k++;
		  }
?>
		</table>

