body {
	margin-top: 0px;
	margin-bottom: 0px;
	margin-left: 0px;
	margin-right: 0px;
	background: rgba(0, 0, 0, 0) url(icone/background.png) repeat scroll 0% 0%; 
	font-family: <PERSON><PERSON>ana;
}

#banniere {
width: 80%;
height: 240px;
margin-left: auto;
margin-right: auto;
background-color: #8d79af;;
display: flex;
align-items: center;
	
}
#middle {
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
	align-content: flex-start;
	background-color: white;
	height: auto;
	margin-left: auto;
	margin-right: auto;
	width: 80%;
	padding-top: 50px;
	padding-bottom:30px;
}

.title{
	
display: block;
text-align: center;
border: solid 3px gray;
border-radius: 5px;
padding: 25px;
text-decoration: none;
color: #00b1bf;
font-size: 22px;
font-weight: bold;
margin: 15px;
	
}

.title:hover{
	
	border: solid 3px orange;

}	

#principal {
border: solid 1px gray;
width: 90%;
margin: 10px;
display: flex;
padding: 20px;
border-radius: 5px;
margin-bottom:60px;
}

#secondaire {
margin: 20px;
padding: 20px;
border: solid 2px gray;
border-radius: 4px;
display: flex;
width: 500px;
}

#creation {
margin: 20px;
padding: 20px;
border: solid 2px gray;
border-radius: 4px;
width: 500px;
}

#img_sec {
width: 20%;
padding: 10px;
text-align: center;
}

#img_crea {
width: 80%;
padding: 10px;
text-align: center;
margin-left: auto;
margin-right: auto;
margin-bottom: 20px;
}

#title_sec {
font-weight: bold;
}
#paragraphe_sec {
font-size: 14px;
text-align: justify;
}

.image {
	width: 15%;
}

.input_class{
	
	padding: 10px;
	border-radius: 8px;
	width: 250px;
	font-size: 16px;
	border: solid 2px gray;
}	
.input_class::placeholder{
	
	font-style:italic;
	font-family: 'sanchez_regularregular';

}	

.input_class:focus{
	
	border: solid 2px orange;

}	

.form_class {
	
	border: solid 1px gray;
	width: 80%;
	max-width: 800px;
	padding: 20px;
	text-align: center;
	border-radius: 5px;
	margin-bottom: 50px;
}

#bar_nav_top {
	width: auto;
	height: auto;
	display:flex;
	justify-content: center;
	background-color: #DFDFDF;
	height: 40px;
}
#bar_nav_top .bouton_nav {
	width: auto;
	height: auto;
	display: block;
	background-color: #DFDFDF;
	padding-left: 10px;
	padding-right: 10px;
	text-decoration: none;
	padding-top: 10px;
	padding-bottom: 10px;
	color: #373737;
	
}
#bar_nav_top .sperateur_nav {
	padding-top: 10px;
	color: #FFFFFF;
}



#sub_bottom {
	width: 100%;
	height: 50px;
	background-color: #dfdfdf;
}
#bottom {
	width: 100%;
	background-color: #79afab;
	display: flex;
	justify-content: center;
	padding-bottom: 100px;
	color: #373737;
}
#bottom #info_bottom {
	width: 30%;
	max-width: 300px;
	margin-top: 10px;
}
#bottom #info_bottom #info_title {
	text-align: center;
	margin-bottom: 10px;
	font-size: large;
	border-bottom: 1px solid #FFFFFF;
	padding-bottom: 5px;
	margin-right: 10px;
}
#under_bottom {
	width: 100%;
	background-color: #dfdfdf;
	text-align: center;
	padding: 15px;
}
