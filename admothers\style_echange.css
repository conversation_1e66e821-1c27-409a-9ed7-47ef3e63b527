html {
	font-family: Verdana, Arial, Helvetica, sans-serif;
	background-color: #CCCCCC;
	margin: 0px;
	padding: 0px;
}
body {
	font-family: Verdana, Arial, Helvetica, sans-serif;
	background-color: #CCCCCC;
	margin: 0px;
	padding: 0px;
}
#global {
	background-color: #FFFFFF;
	width: 800px;
	display: block;
	margin-top: 0px;
	margin-bottom: 0px;
	margin-right: auto;
	margin-left: auto;
	padding-left: 10px;
	padding-top: 10px;
	height: 600px;
}

#global h1 {
	background-image: url(pandeau.png);
	display: block;
	height: 55px;
	width: 600px;
	color: #FFFFFF;
	margin-top: 0px;
	margin-right: auto;
	margin-bottom: 50px;
	margin-left: auto;
	padding-top: 15px;
	text-align: center;
}
#global #corps h2 {
	font-size: 16px;
}
#global #corps h3 {
	font-size: 14px;
	display: block;
	height: 35px;
	width: 325px;
	border: 2px solid #000000;
	text-align: center;
	vertical-align: middle;
	margin-right: auto;
	margin-left: auto;
	padding-top: 15px;
	background-color: #CCFF33;
}
#global #corps h4 {
	font-size: 14px;
	display: block;
	height: 45px;
	width: 300px;
	border: 2px solid #000000;
	text-align: center;
	vertical-align: middle;
	margin-right: auto;
	margin-left: auto;
	padding-top: 15px;
	background-color: #CCFF33;
}
#global #corps p {
	font-size: 12px;
}


#global ul {
	display: block;
	margin: 0px;
	margin-bottom: 50px;
	padding-bottom: 20px;
}
#global li {
	text-decoration: none;
	list-style-type: none;
	height: auto;
	font-weight: bold;
	width: 300px;
	display: block;
	border-bottom-width: 1px;
	border-bottom-style: dotted;
	border-bottom-color: #666666;
	margin-bottom: 25px;
}
#global li img{
	border-bottom-width: 0px;
	border-top-width: 0px;
	border-right-width: 0px;
	border-left-width: 0px;
}
#global li a{
	color: #000000;
	text-decoration: none;
}
#global .retour {
	text-decoration: none;
	color: #000000;
	display: block;
	height: 25px;
	width: 65px;
	background-image: url(icone/fleche-bas-gauche-retour-icone-8682-48.png);
	background-repeat: no-repeat;
	padding-left: 28px;
	margin-left: 100px;
	margin-top: 50px;
}
#global #corps {
font-size: 10px
}
#global #corps table {
	font-size: 9px;
	list-style-type: none;
	margin-right: auto;
	margin-left: auto;
	width: 700px;
	border: 1px solid #000000;
}
#global #corps .upload {
	display: block;
	height: 40px;
	width: 65px;
	font-size: 14px;
	font-weight: bold;
	color: #0000FF;
}
#global #corps .upload:hover {
	color: #FF0000;
}
#global #corps form {
	height: 120px;
	width: 460px;
	background-color: #FFCC00;
	border: 2px solid #000000;
	margin-bottom: 40px;
}
#global #corps .actualise {
	width: 90px;
	height: 28px;
	display: block;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-size: 16px;
	text-decoration: none;
	color: #000000;
	background-image: url(icone/actualise.png);
	background-repeat: no-repeat;
	padding-left: 37px;
	padding-top: 6px;
	font-weight: bold;
}
.Style1 {
	font-size: 18px;
	font-weight: bold;
	color: #FF0000;
}
.Style2 {color: #000000}
