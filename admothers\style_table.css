div.table-title {
   display: block;
  margin: auto;
  max-width: 600px;
  padding:5px;
  width: 100%;
}

.table-title h3 {
   color: #fafafa;
   font-size: 30px;
   font-weight: 400;
   font-style:normal;
   font-family: "Roboto", helvetica, arial, sans-serif;
   text-shadow: -1px -1px 1px rgba(0, 0, 0, 0.1);
   text-transform:uppercase;
}


/*** Table Styles **/

.table-fill {
  background: white;
  border-radius:3px;
  border-collapse: collapse;

  margin: auto;
  
  padding:5px;
  width: 100%;
  box-shadow: 0 -5px 10px rgba(0, 0, 0, 0.1);
  animation: float 5s infinite;
}
 
.table-fill th {
  color:#D5DDE5;;
  background:#1b1e24;
  border-bottom:4px solid #9ea7af;
  border-right: 1px solid #343a45;
  font-size:18px;
  font-weight: 100;
  padding:18px;
  text-align:left;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  vertical-align:middle;
}

.table-fill th:first-child {
  border-top-left-radius:3px;
}
 
.table-fill th:last-child {
  border-top-right-radius:3px;
  border-right:none;
}
  
.table-fill tr {
  border-top: 1px solid #C1C3D1;
  border-bottom-: 1px solid #C1C3D1;
  color:#666B85;
  font-size:12px;
  font-weight:normal;
  text-shadow: 0 1px 1px rgba(256, 256, 256, 0.1);
}
 
.table-fill tr:hover td {
  background:#4E5066;
  color:#FFFFFF;
  border-top: 1px solid #22262e;
  border-bottom: 1px solid #22262e;
}
.table-fill tr:hover td a {
  background:#4E5066;
  color:#FFFFFF;
}
 
.table-fill tr:first-child {
  border-top:none;
}

.table-fill tr:last-child {
  border-bottom:none;
}
 
.table-fill tr:nth-child(odd) td {
  background:#EBEBEB;
}
 
.table-fill tr:nth-child(odd):hover td {
  background:#4E5066;
}

.table-fill tr:last-child td:first-child {
  border-bottom-left-radius:3px;
}
 
.table-fill tr:last-child td:last-child {
  border-bottom-right-radius:3px;
}
 
.table-fill td {
  background:#FFFFFF;
  padding:8px;
  text-align:left;
  vertical-align:middle;
  font-weight:300;
  font-size:15px;
  text-shadow: -1px -1px 1px rgba(0, 0, 0, 0.1);
  border-right: 1px solid #C1C3D1;
}

.table-fill td:last-child {
  border-right: 0px;
}

.table-fill th.text-left {
  text-align: left;
}

.table-fill th.text-center {
  text-align: center;
}

.table-fill th.text-right {
  text-align: right;
}

.table-fill td.text-left {
  text-align: left;
}

.table-fill td.text-center {
  text-align: center;
}

.table-fill td.text-right {
  text-align: right;
}
