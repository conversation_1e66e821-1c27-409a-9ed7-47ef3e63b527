<?php

include("../init2.php");

//definition des variable

$referenceproduit = $_POST['referenceproduit'];
$descriptionproduit = $_POST['descriptionproduit'];
$prixproduiteuro = $_POST['prixproduiteuro'];
$modif = $_GET['modif'];

for ($i = 0; $i < count($referenceproduit); $i++)
{
echo $referenceproduit[$i];
echo "<br />";
echo $descriptionproduit[$i];
echo "<br />";
echo $prixproduiteuro[$i];
echo "<br />";
echo $modif;
echo "<br />";
}
//Update

$req1 = mysql_query("

UPDATE produits_copy
SET
referenceproduit = '$referenceproduit', 
descriptionproduit = '$descriptionproduit',
prixproduiteuro = '$prixproduiteuro'
WHERE idproduit = '$modif'

") or die ("requete update r1 invalid");
?>



<html>
<body>

Ligne <PERSON>fi&eacute; !</br>

</body>
</html>