<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>Untitled Document</title>
<style type="text/css">

.bouton_pers {
	height: 150px;
	width: 150px;
	background-color: #0CF;
	border: 3px solid #000;
	font-size: 24px;
	color: #000;
	margin-right: 10px;
}
.bouton_retour {
	height: 50px;
	width: 300px;
	background-color: #0FC;
	border: 3px solid #000;
	font-size: 24px;
	color: #000;
	margin-right: 10px;
}
.bouton_debut {
	height: 150px;
	width: 300px;
	background-color: #3F3;
	border: 3px solid #000;
	font-size: 24px;
	color: #000;
	margin-right: 10px;
}
.bouton_fin {
	height: 150px;
	width: 300px;
	background-color: #F60;
	border: 3px solid #000;
	font-size: 24px;
	color: #000;
	margin-right: 10px;
}
h1 {
	background-position: center;
	text-align: center;
	font-size: 40px;
}
h3 {
	text-align: center;
}
</style>

<SCRIPT LANGUAGE="JavaScript">

function heure(){
var Today = new Date;
var Heure = Today.getHours();
var Min = Today.getMinutes();
var Sec = Today.getSeconds();
    if (Heure < 10) { Heure = "0" + Heure; } 
    if (Min < 10) { Min = "0" + Min; } 
    if (Sec < 10) { Sec = "0" + Sec; } 
var Message = "il est " + Heure + " H " + Min + " - " + Sec;
document.getElementById('heure').innerHTML = Message;
//jour
var annee = Today.getFullYear();
var moi = Today.getMonth();
var mois = new Array('Janvier', 'F&eacute;vrier', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Ao&ucirc;t', 'Septembre', 'Octobre', 'Novembre', 'D&eacute;cembre');
var j = Today.getDate();
var jour = Today.getDay();
var jours = new Array('Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi');

var resultat = jours[jour]+' '+j+' '+mois[moi]+' '+annee;
document.getElementById("jour").innerHTML = resultat;
setTimeout("heure();",1000);
}

</SCRIPT>

</head>

<body onload="heure()">
<h1 id="heure"></h1>
<h3 id="jour"></h3>
<div align="center"><form  name="choix_nom" method="post" action="">
 	<input class="bouton_debut" type="submit" name="button" value="Debut" />
    <input class="bouton_fin" type="submit" name="button" value="Fin" /><br /><br /><br />
    <input class="bouton_retour" type="submit" name="button" value="Retour" />
</form></div>
<p align="center"><strong>cliquer sur la personne pour pointer</strong></p>
</body>
</html>
