<?php
/**
 * classe qui représente une adresse...
 *
 */
class adresse {
	/**
	 * id de l'adresse
	 *
	 * @int id
	 */
	private $id;
	/**
	 * raison social...
	 *
	 * @var varchar
	 */
	private $raisonSocial;

	/**
	 * nom de rue...
	 *
	 * @var varchar
	 */
	private $nomrue;
	/**
	 * code postal...
	 *
	 * @var varchar
	 */
	private $codePostal;
	/**
	 * ville...
	 *
	 * @var varchar
	 */
	private $ville;
	/**
	 * pays...
	 *
	 * @var varchar
	 */
	private $pays;
	/**
	 * retourne l'Id de l'adresse
	 *
	 * @return int
	 */
	public function getId() {
		return $this->id;
	}
	
	/**
	 * retourne la raison social...
	 *
	 * @return varchar
	 */
	public function getRaisonSocial() {
		return $this->raisonSocial;
	}
	
	/**
	 * retourne le nom de la rue...
	 *
	 * @return varchar
	 */
	public function getNomRue() {
		return $this->nomrue;
	}
	/**
	 * retourne le code postal
	 *
	 * @return varchar
	 */
	public function getCodePostal() {
		return $this->codePostal;
	}
	/**
	 * retourne la ville...
	 *
	 * @return varchar
	 */
	public function getVille() {
		return $this->ville;
	}
	/**
	 * retourne le pays...
	 *
	 * @return varchar
	 */
	public function getPays() {
		return $this->pays;
	}
	/**
	 * modifie l'id
	 *
	 * @param int $id
	 */
	public function setId($id) {
		$this->id = $id;
	}
	/**
	 * modifie la raison social...
	 *
	 * @param varchar $raisonSocial
	 */
	public function setRaisonSocial($raisonSocial) {
		$this->raisonSocial = $raisonSocial;
	}
	/**
	 * modifie le type d'adresse
	 *
	 * @param boolean $type
	 */
	public function setType($type) {
		$this->type = $type;
	}

	/**
	 * Enter description here...
	 *
	 * @param varchar $nomrue
	 */
	public function setNomRue($nomrue) {
		$this->nomrue = $nomrue;
	}
	/**
	 * Enter description here...
	 *
	 * @param varchar $codePostal
	 */
	public function setCodePostal($codePostal) {
		$this->codePostal = $codePostal;
	}
	/**
	 * modifie la ville...
	 *
	 * @param varchar $ville
	 */
	public function setVille($ville) {
		$this->ville = $ville;
	}
	/**
	 * modifie le pays...
	 *
	 * @param varchar $pays
	 */
	public function setPays($pays) {
		$this->pays = $pays;
	}
	
	// Fonction de construction 
	public function __construct() 
	{
		$this->clear();
	}

	
	// Fonction de netoyage
	public function clear() 
	{
		$this->id = 0;
		$this->raisonsocial = "";
		$this->nomrue = "";
		$this->codepostal = "";
		$this->ville = "";
		$this->pays = "";
	}
}
/**
 * classe d'accés à la base données...
 *
 */
class adresseDB {
	/**
	 * fonction qui retourne un objet adresse à l'aide de l'email du client.
	 *
	 * @param string $email
	 *
	 * @return adresse
	 */
	public static function getAdresseByClient($client) 
	{
		$sql = "SELECT * FROM adresses WHERE emailclient=:email;";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->bindParam(':email', $client->getEmail());
		$stmt->execute();
		$data = $stmt->fetch();
		if($data != null)
		{
			$adresse = new adresse;
			$adresse->setId($data[0]);
			$adresse->setRaisonSocial($data[1]);
			$adresse->setNomRue($data[3]);
			$adresse->setCodePostal($data[4]);
			$adresse->setVille($data[5]);
			$adresse->setPays($data[6]);			
			return $adresse;
		}
		return null;
	}
	
	public static function getAdressesByClient($client){
		$sql = "SELECT * FROM adresses WHERE emailclient=:email;";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->bindParam(':email', $client->getEmail());
		$stmt->execute();
		$data = $stmt->fetchAll();
		$result = array();
		foreach($data as $ligne){
			$adresse = new adresse;
			$adresse->setId($ligne[0]);
			$adresse->setRaisonSocial($ligne[1]);
			$adresse->setNomRue($ligne[3]);
			$adresse->setCodePostal($ligne[4]);
			$adresse->setVille($ligne[5]);
			$adresse->setPays($ligne[6]);			
			$result[] = $adresse;
		}
		return $result;
	}
	
	/**
	* fonction qui enregistre une adresse dans la BDD
	*
	* @param adresse
	* @param email
	* @param type
	*
	* @return boolean
	*/
	public static function save($email, $adresse, $new) 
	{
		// On test si le client utilisateur a déjà une adresse de livraison enregistré, si oui on la modifie, sinon on l'ajoute.
		if (self::haveGotAdresse($email) && $new == false)
		{
			$sql = "UPDATE adresses SET raisonsocial=:raisonsocial, nomrue=:nomrue, codepostal=:cp, ville=:ville, pays=:pays, tele=:tele WHERE idadresse=:id;";
		}
		else
		{
			$sql = "INSERT INTO adresses (raisonsocial, emailclient, nomrue, codepostal, ville, pays, tele) VALUES (:raisonsocial, :email, :nomrue, :cp, :ville, :pays, :tele);";
		}
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->bindParam(':raisonsocial', $adresse->getRaisonSocial());
		if (self::haveGotAdresse($email) && $new == false)
		{
			$stmt->bindParam(':id', $adresse->getId());
		}
		else
		{
			$stmt->bindParam(':email', $email);
		}
		$stmt->bindParam(':nomrue', $adresse->getNomRue());
		$stmt->bindParam(':cp', $adresse->getCodePostal());
		$stmt->bindParam(':ville', $adresse->getVille());
		$stmt->bindParam(':pays', $adresse->getPays());
		$tele = "0";
		$stmt->bindParam(':tele', $tele);
		$stmt->execute();
		if ($stmt->rowCount() == 1)
		{
			$adresse->setId($db->lastInsertId());
			return true;
		}
		else
		{
			return false;
		}
	}
	
	// Test update adresse
	
		public static function update($adresse) 
	{
		// On test si le client utilisateur a déjà une adresse de livraison enregistré, si oui on la modifie, sinon on l'ajoute.
		
		$sql = "UPDATE adresses SET raisonsocial=:raisonsocial, nomrue=:nomrue, codepostal=:cp, ville=:ville, pays=:pays, tele=:tele WHERE idadresse=:id;";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		
		$stmt->bindParam(':id', $adresse->getId());
		$stmt->bindParam(':raisonsocial', $adresse->getRaisonSocial());
		$stmt->bindParam(':nomrue', $adresse->getNomRue());
		$stmt->bindParam(':cp', $adresse->getCodePostal());
		$stmt->bindParam(':ville', $adresse->getVille());
		$stmt->bindParam(':pays', $adresse->getPays());
		$tele = "0";
		$stmt->bindParam(':tele', $tele);
		
		$stmt->execute();
	}
	
	/**
	 * fonction qui retourne vrai ou faux s'il existe des adresse du type donné avec l'email du client.
	 *
	 * @param string $email
	 * @param bool $type
	 *
	 * @return bool
	 */
	public static function haveGotAdresse ($email) 
	{
		$sql = "SELECT count(*) AS nbadresse FROM adresses WHERE emailclient=:email;";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->bindParam(':email', $email);
		$stmt->execute();
		$data = $stmt->fetch();
		// On test si la fonction a bien fonctionné
		if($data != null)
		{
			$nbadresse = $data['nbadresse'];
			// Si le nombre de résultat est différent de zéro, on retourne vrai, sinon on retourne faux
			if ($nbadresse != 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		return null;
	}
	
	/**
	 * fonction qui retourne toutes les adresses d'un client donné (sous forme de tableau)
	 *
	 * @param string $email
	 *
	 * @return tableaux d'adresses
	 */
	public static function SelectionneToutesAdresses ($email) 
	{
		$sql = "SELECT * FROM adresses WHERE emailclient=:email";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->bindParam(':email', $email);
		$stmt->execute();
		$data = $stmt->fetchAll();
		// On test si la fonction a bien fonctionné
		if($data != null)
		{
			$adrlist = array();
			// Création des adresses
			foreach($data as $ligne)
			{
				$adr = new adresse();
				$adr->setId($ligne['idadresse']);
				$adr->setRaisonSocial($ligne['raisonsocial']);
				$adr->setNomRue($ligne['nomrue']);
				$adr->setCodePostal($ligne['codepostal']);
				$adr->setVille($ligne['ville']);
				$adr->setPays($ligne['pays']);
				array_push($adrlist, $adr);
			}
			
			return $adrlist;
		}
		return null;
	}
	public static function deleteAdresseDuClient($client) {
		$sql = "DELETE FROM adresses WHERE emailclient=:email";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->bindParam(':email', $client->getEmail());
		$stmt->execute();
	}
	
	public static function deleteAdresseById($id) {
		$sql = "DELETE FROM adresses WHERE idadresse=:id";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->bindParam(':id', $id);
		$stmt->execute();
		if ($stmt->rowCount() == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
	
	public static function getAdresseById($id) {
		$sql = "SELECT * FROM adresses WHERE idadresse=:id";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->bindParam(':id', $id);
		$stmt->execute();
		$data = $stmt->fetch();
		if($data != null)
		{
			$adresse = new adresse;
			$adresse->setId($data[0]);
			$adresse->setRaisonSocial($data[1]);
			$adresse->setNomRue($data[3]);
			$adresse->setCodePostal($data[4]);
			$adresse->setVille($data[5]);
			$adresse->setPays($data[6]);			
			return $adresse;
		}
		return null;
	}
}