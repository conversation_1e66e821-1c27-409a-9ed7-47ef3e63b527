<?php
/**
 * classe qui représente une adresse...
 *
 */
class af {
	/**
	 * id de l'adresse
	 *
	 * @int id
	 */
	private $id;
	/**
	 * possesseur de l'adresse
	 *
	 * @var varchar
	 */
	private $emailclient;
	/**
	 * raison social...
	 *
	 * @var varchar
	 */
	private $raisonSocial;

	/**
	 * nom de rue...
	 *
	 * @var varchar
	 */
	private $nomrue;
	/**
	 * code postal...
	 *
	 * @var varchar
	 */
	private $codePostal;
	/**
	 * ville...
	 *
	 * @var varchar
	 */
	private $ville;
	/**
	 * pays...
	 *
	 * @var varchar
	 */
	private $pays;
	/**
	 * retourne l'Id de l'adresse
	 *
	 * @return int
	 */
	public function getId() {
		return $this->id;
	}
	
	/**
	 * retourne l'email du possesseur de l'adresse
	 *
	 * @return varchar
	 */
	public function getEmail() {
		return $this->emailclient;
	}
	
	/**
	 * retourne la raison social...
	 *
	 * @return varchar
	 */
	public function getRaisonSocial() {
		return $this->raisonSocial;
	}
	
	/**
	 * retourne le nom de la rue...
	 *
	 * @return varchar
	 */
	public function getNomRue() {
		return $this->nomrue;
	}
	/**
	 * retourne le code postal
	 *
	 * @return varchar
	 */
	public function getCodePostal() {
		return $this->codePostal;
	}
	/**
	 * retourne la ville...
	 *
	 * @return varchar
	 */
	public function getVille() {
		return $this->ville;
	}
	/**
	 * retourne le pays...
	 *
	 * @return varchar
	 */
	public function getPays() {
		return $this->pays;
	}
	/**
	 * modifie l'id
	 *
	 * @param int $id
	 */
	public function setId($id) {
		$this->id = $id;
	}
	/**
	 * modifie l'email
	 *
	 * @param varchar $email
	 */
	public function setEmail($email) {
		$this->emailclient = $email;
	}
	/**
	 * modifie la raison social...
	 *
	 * @param varchar $raisonSocial
	 */
	public function setRaisonSocial($raisonSocial) {
		$this->raisonSocial = $raisonSocial;
	}
	/**
	 * Enter description here...
	 *
	 * @param varchar $nomrue
	 */
	public function setNomRue($nomrue) {
		$this->nomrue = $nomrue;
	}
	/**
	 * Enter description here...
	 *
	 * @param varchar $codePostal
	 */
	public function setCodePostal($codePostal) {
		$this->codePostal = $codePostal;
	}
	/**
	 * modifie la ville...
	 *
	 * @param varchar $ville
	 */
	public function setVille($ville) {
		$this->ville = $ville;
	}
	/**
	 * modifie le pays...
	 *
	 * @param varchar $pays
	 */
	public function setPays($pays) {
		$this->pays = $pays;
	}
	
	// Fonction de construction 
	public function __construct() 
	{
		$this->clear();
	}

	
	// Fonction de netoyage
	public function clear() 
	{
		$this->id = 0;
		$this->emailclient = "";
		$this->raisonSocial = "";
		$this->nomrue = "";
		$this->codePostal = "";
		$this->ville = "";
		$this->pays = "";
	}
}
/**
 * classe d'accés à la base données...
 *
 */
class afDB {
	/**
	 * fonction qui retourne un objet adresse à l'aide de l'email du client.
	 *
	 * @param string $email
	 *
	 * @return adresse
	 */
	public static function getAdresseByClient($emailc) 
	{
		$sql = "SELECT * FROM adresses_facturation WHERE af_emailclient=:email;";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->bindParam(':email', $emailc);
		$stmt->execute();
		$data = $stmt->fetch();
		if($data != null)
		{
			$adressef = new af;
			$adressef->setId($data[0]);
			$adressef->setEmail($data[1]);
			$adressef->setRaisonSocial($data[2]);
			$adressef->setNomRue($data[3]);
			$adressef->setCodePostal($data[4]);
			$adressef->setVille($data[5]);
			$adressef->setPays($data[6]);			
			return $adressef;
		}
		return null;
	}
	
	/**
	* fonction qui enregistre une adresse dans la BDD
	*
	* @param adresse
	* @param email
	*
	* @return boolean
	*/
	public static function save($ad) 
	{
		$sql = "INSERT INTO adresses_facturation (af_emailclient, af_raisonsocial, af_nomrue, af_codepostal, af_ville, af_pays) VALUES (:email, :raisonsocial, :nomrue, :cp, :ville, :pays);";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->bindParam(':email', $ad->getEmail());
		$stmt->bindParam(':raisonsocial', $ad->getRaisonSocial());
		$stmt->bindParam(':nomrue', $ad->getNomRue());
		$stmt->bindParam(':cp', $ad->getCodePostal());
		$stmt->bindParam(':ville', $ad->getVille());
		$stmt->bindParam(':pays', $ad->getPays());
		$stmt->execute();
		if ($stmt->rowCount() == 1)
		{
			$ad->setId($db->lastInsertId());
			return true;
		}
		else
		{
			return false;
		}
	}
}