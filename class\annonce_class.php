<?php
class annonce{
	
	public function __construct(){
		
	}
	
	private $id = 0;
	private $date = "";
	private $client = null;
	private $telephone = "";
	private $fax = "";
	private $operation = "";
	private $genre = "";
	private $type = "";
	private $sujet = "";
	private $detail = "";
	private $prix = 0;
	private $image = "";
	private $emailclient;
	private $visite;
	
	/**
	 * Le status de la commande
	 *
	 * @var int ( 0 => en attente, 1 => valider)
	 */
	private $status;
	
	public function getId(){
		return $this->id;
	}
	public function setId($id){
		$this->id = $id;
	}
	public function getDate(){
		return $this->date;
	}
	public function setDate($date){
		$this->date = $date;
	}
	public function getTelephone(){
		return $this->telephone;
	}
	public function setTelephone($telephone){
		$this->telephone = $telephone;
	}
	public function getFax(){
		return $this->fax;
	}
	public function setFax($fax){
		$this->fax = $fax;
	}
	public function getOperation(){
		return $this->operation;
	}
	public function setOperation($operation){
		$this->operation = $operation;
	}
	public function getGenre(){
		return $this->genre;
	}
	public function setGenre($genre){
		$this->genre = $genre;
	}
	public function getType(){
		return $this->type;
	}
	public function setType($type){
		$this->type = $type;
	}
	public function getSujet(){
		return $this->sujet;
	}
	public function setSujet($sujet){
		$this->sujet = $sujet;
	}
	public function getDetail(){
		return $this->detail;
	}
	public function setDetail($detail){
		$this->detail = $detail;
	}
	public function getPrix(){
		return $this->prix;
	}
	public function setPrix($prix){
		$this->prix = $prix;
	}
	public function getImage(){
		return $this->image;
	}
	public function setImage($image){
		$this->image = $image;
	}
	public function getClient(){
		if($this->client == null){
			$this->client = clientDB::getClientByEmail($this->emailclient);
		}
		return $this->client;
	}
	
	public function setClient(client $c){
		$this->_lient = $c;
		$this->emailclient = $c->getEmail();
	}
	public function setEmailClient($email){
		$this->emailclient = $email;
	}
	public function getEmailClient(){
		return $this->emailclient;
	}
	
	public function setStatus($status){
		$this->status = $status;
	}
	
	public function getStatus(){
		return $this->status;
	}
	

	public function setVisite($visite){
		$this->visite = $visite;
	}
	
	public function getVisite(){
		return $this->visite;
	}
}

class annonceDB{
	
	
	/**
	 * Récupère l'ensemble des annonces d'un client
	 *
	 * @param client $c
	 * @return annonce[]
	 */
	public static function getAnnoncesByClient(client $c){
		$db = Zend_Registry::get('database');
		$req = 'SELECT * FROM annonces WHERE emailclient = :emailclient ORDER BY id DESC';
		$stmt = $db->prepare($req);
		$getEmail = $c->getEmail();
		$stmt->bindParam(':emailclient',$getEmail);
		$stmt->execute();
		$annonces = array();
		$data = $stmt->fetchAll();
		if($data != null){
			foreach($data as $ligne){
				$annonces[] = self::getAnnonceByData($ligne);
			}
		}
		return $annonces;
	}
	public static function getNbAValiderAnnonceByClient($c) {
		$db = Zend_Registry::get('database');
		$req = 'SELECT COUNT(*) FROM annonces WHERE emailclient = :emailclient AND status = 0';
		$stmt = $db->prepare($req);
		$email = $c->getEmail();
		$stmt->bindParam(':emailclient',$email);
		$stmt->execute();		
		$data = $stmt->fetch();
		return $data[0];
		
	}
	public static function getAnnonceByData($data = array()){
		$a = new annonce();
		$a->setId($data['id']);
		$a->setDate($data['dateannonce']);
		$a->setTelephone($data['telephone']);
		$a->setFax($data['fax']);
		$a->setOperation($data['operation']);
		$a->setGenre($data['genre']);
		$a->setType($data['typeannonce']);
		$a->setSujet($data['sujet']);
		$a->setDetail($data['detail']);
		$a->setPrix($data['prix']);
		$a->setImage($data['image']);
		$a->setStatus($data['status']);
		$a->setEmailClient($data['emailclient']);
		$a->setVisite($data['visite']);
		return $a;
	}
	
	/**
	 * Récupère un annonces par rapport a son id
	 *
	 * @param int $id l'identifiant de l'annonce
	 * @return annonce
	 */
	public static function getAnnonceById($id){
		$db = Zend_Registry::get('database');
		$req = 'SELECT * FROM annonces WHERE id = :id';
		$stmt = $db->prepare($req);
		$stmt->bindParam(':id',$id,PDO::PARAM_INT);
		$stmt->execute();
		$data = $stmt->fetch();
		if($data != null){
			return self::getAnnonceByData($data);
		}
		return null;
	}
	
	/**
	 * Récupère l'ensemble des annonces
	 *
	 * @return annonce[]
	 */
	public static function getAnnonces(){
		$db = Zend_Registry::get('database');
		$req = 'SELECT * FROM annonces ORDER BY id DESC';
		$data = $db->query($req);
		$annonces = array();
		if($data != null){
			foreach($data as $ligne){
				$annonces[] = self::getAnnonceByData($ligne);
			}
		}
		return $annonces;
	}
	
	public static function getNbAnnonces($valid = null){
		$req = 'SELECT COUNT(*) FROM annonces ';
		if($valid != null){
			$req .= 'WHERE status = ';
			$req .= $valid;
		}
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($req);
		$stmt->execute();
		$data = $stmt->fetch();
		return $data[0];
	}
	public static function getNbAValideAnnonces(){
		$req = 'SELECT COUNT(*) FROM annonces WHERE status = 0';
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($req);
		$stmt->execute();
		$data = $stmt->fetch();
		return $data[0];
	}
	public static function getValideNbAnnonces(){
		return self::getNbAnnonces(true);
	}
	
	public static function getValideAnnonces(){
		$db = Zend_Registry::get('database');
		$req = 'SELECT * FROM annonces WHERE status = 1 ORDER BY id DESC';
		$data = $db->query($req);
		$annonces = array();
		if($data != null){
			foreach($data as $ligne){
				$annonces[] = self::getAnnonceByData($ligne);
			}
		}
		return $annonces;
	}
	
	public static function getAnnoncesPagination($numPage = 1,$nbParPage = 10){
		$db = Zend_Registry::get('database');
		$req = 'SELECT * FROM annonces LIMIT :numPage, :nbParPage';
		$stmt = $db->prepare($req);
		$numP = ($numPage - 1)*$nbParPage;
		$stmt->bindParam(':numPage',$numP*$nbParPage);
		$stmt->bindParam(':nbParPage',$nbParPage);
		$stmt->execute();
		$data = $stmt->fetchAll();
		$annonces = array();
		if($data != null){
			foreach($data as $ligne){
				$annonces[] = self::getAnnonceByData($ligne);
			}
		}
		return $annonces;
	}
	
	public static function getValideAnnoncesPagination($numPage =1, $nbParPage = 10){
		$db = Zend_Registry::get('database');
		$req = 'SELECT * FROM annonces WHERE status = 1 LIMIT :numpage , :nbparpage';
		$stmt = $db->prepare($req);
		$numP = ($numPage - 1)*$nbParPage;
		$stmt->bindParam(':numpage',$numP,PDO::PARAM_INT);
		$stmt->bindParam(':nbparpage',$nbParPage,PDO::PARAM_INT);
		$stmt->execute();
		$data = $stmt->fetchAll();
		$annonces = array();
		if($data != null){
			foreach($data as $ligne){
				$annonces[] = self::getAnnonceByData($ligne);
			}
		}
		return $annonces;
	}
	
	/**
	 * Insère une annonce dans la base de donnÃ©es
	 *
	 * @param annonce $a
	 */
	public static function insertAnnonce(annonce $a){
		$req = 'INSERT INTO annonces (dateannonce,telephone,fax,operation,genre,typeannonce,sujet,detail,prix,image,emailclient,status) '.
			   'VALUES (:dateannonce,:telephone,:fax,:operation,:genre,:typeannonce,:sujet,:detail,:prix,:image,:emailclient,:status)';
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($req);
		$date = date('Y-m-d');
		$telephone = $a->getTelephone();
		$fax = $a->getFax();
		$operation = $a->getOperation();
		$genre = $a->getGenre();
		$type = $a->getType();
		$sujet = $a->getSujet();
		$detail = $a->getDetail();
		$prix = $a->getPrix();
		$image = $a->getImage();
		$email = $a->getClient()->getEmail();
		$statut = $a->getStatus();
		
		$stmt->bindParam(':dateannonce',$date);
		$stmt->bindParam(':telephone',$telephone);
		$stmt->bindParam(':fax',$fax);
		$stmt->bindParam(':operation',$operation);
		$stmt->bindParam(':genre',$genre);
		$stmt->bindParam(':typeannonce',$type);
		$stmt->bindParam(':sujet',$sujet);
		$stmt->bindParam(':detail',$detail);
		$stmt->bindParam(':prix',$prix);
		$stmt->bindParam(':image',$image);
		$stmt->bindParam(':emailclient',$email);
		$stmt->bindParam(':status',$statut);
		$stmt->execute();
	}
	
	/**
	 * maj d'un annonce dans la base de données
	 *
	 * @param annonce $a
	 */
	public static function updateAnnonce(annonce $a){
		$req = 'UPDATE annonces SET dateannonce = :dateannonce,telephone = :telephone,fax = :fax,operation = :operation,genre = :genre,'.
			   'typeannonce = :typeannonce,sujet = :sujet,detail = :detail,prix = :prix,image = :image,emailclient = :emailclient, status = :status WHERE id = :id ';
		$db = Zend_Registry::get('database');
		
		$stmt = $db->prepare($req);
		
		$date = $a->getDate();
		$telephone = $a->getTelephone();
		$fax = $a->getFax();
		$operation = $a->getOperation();
		$genre = $a->getGenre();
		$type = $a->getType();
		$sujet = $a->getSujet();
		$detail = $a->getDetail();
		$prix = $a->getPrix();
		$image = $a->getImage();
		$email = $a->getClient()->getEmail();
		$statut = $a->getStatus();
		$id = $a->getId();
		
		$stmt->bindParam(':dateannonce',$date);
		$stmt->bindParam(':telephone',$telephone);
		$stmt->bindParam(':fax',$fax);
		$stmt->bindParam(':operation',$operation);
		$stmt->bindParam(':genre',$genre);
		$stmt->bindParam(':typeannonce',$type);
		$stmt->bindParam(':sujet',$sujet);
		$stmt->bindParam(':detail',$detail);
		$stmt->bindParam(':prix',$prix);
		$stmt->bindParam(':image',$image);
		$stmt->bindParam(':emailclient',$email);
		$stmt->bindParam(':status',$statut);
		$stmt->bindParam(':id',$id);
		
		$stmt->execute();
	}
	
	/**
	 * Supprime une annonce de la base de données
	 *
	 * @param annonce $a
	 */
	public static function deleteAnnonce(annonce $a){
		$req = 'DELETE FROM annonces WHERE id = :id';
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($req);
		$id = $a->getId();
		$stmt->bindParam(':id',$id);
		$stmt->execute();
	}
}