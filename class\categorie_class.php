<?php
require_once(dirname(__FILE__).'/../utils/form_utile.php');
/**
 * classe catégorie de produit...
 *
 */
class categorie {
	/**
	 * identifiant (dans la BDD) de la catégorie
	 *
	 * @var int 
	 */
	private $id;
	/**
	 * identifiant (dans la BDD) de la catégorie parente
	 * @var int
	 */
	private $parentid;
	/**
	 * nom français de la catégorie
	 *
	 * @var varchar
	 */
	private $nom_fr;
	/**
	 * nom américain de la catégorie
	 *
	 * @var varchar
	 */
	private $nom_en;
	/**
	 * nom complet de la catégorie.
	 * comprenant le numéro, le nom français, le séparateur ('|') et le nom américain
	 *
	 * @var varchar
	 */
	private $nom_brut;
	/**
	 * nom de la catégorie parente
	 * @var string nom de la catégorie parente
	 */
	private $parent_categorie_name;
	/**
	 * liste des produits de la catégorie si il y a
	 * @var array
	 */
	private $list_produits;
	/**
	 * liste des sous catégories si il y a
	 * @var array
	 */
	private $list_souscat;
	/**
	 * retourne le nom français de la catégorie
	 *
	 * @return varchar
	 */
	public function getNomFR(){
		return $this->nom_fr;
	}
	/**
	 * retourne le nom anglais de la catégorie
	 *
	 * @return varchar
	 */
	public function getNomEN(){
		return $this->nom_en;
	}
/**
	 * retourne le nom complet de la catégorie
	 *
	 * @return varchar
	 */
	public function getNomBrut(){
		return $this->nom_brut;
	}
	/**
	 * retourne le nom de la catégorie parente
	 *
	 * @return string
	 */
	public function getParentCategorieName(){
		return $this->parent_categorie_name;
	}
	/**
	 * retourne l'identifiant de la catégorie parente
	 *
	 * @return int
	 */
	public function getParentCategorieId(){
		return $this->parentid;
	}

	/**
	 * retourne la liste des sous catégories
	 *
	 * @return array
	 */
	public function getSousCategorieList(){
		return $this->list_souscat;
	}
	/**
	 * retourne la liste des produits
	 *
	 * @return array
	 */
	public function getProduitList(){
		return $this->list_produits;
	}
	/**
	 * définit la liste des produits
	 *
	 * param array Liste de produits
	 */
	public function setProduitList($prodlist){
		$this->list_produits = $prodlist;
	}
	/**
	 * retourne l'identifiant dans la base de données
	 *
	 * @return int
	 */
	public function getIdentifiant(){
		return $this->id;
	}
	/**
	 * ajoute un produit dans la catégorie
	 *
	 * @param Produit
	 */
	public function addProduit($produit) {
		array_push($this->list_produits, $produit);
	}
	/**
	 * ajoute une sous-catégorie dans la catégorie
	 *
	 * @param Produit
	 */
	public function addSousCategorie($souscat) {
		array_push($this->list_souscat, $souscat);
	}
	/**
	 * Renvoi la sous-catégorie portant le nom spécifié
	 * @var string Nom de la sous-catégorie à rechercher
	 * @return Categorie Renvoi une Categorie ou null si pas trouvé
	 */
	public function getSousCategorie($nom_souscat) {
		//recherche la catégorie ayant pour nom (fr ou en) nom_souscat
		foreach($this->list_souscat as $cat)
		{
			//vérifie le nom français et américain de la catégorie
			if(strcmp($cat->getNomBrut(),$nom_souscat)==0) 
				return $cat;	
		}
		return null;
	}
	/**
	 * Renvoi le produit portant le nom spécifié (la désignation)
	 * @var string Désignation du produit à rechercher
	 * @return Categorie Renvoi un Produit ou null si pas trouvé
	 */
	public function getProduit($nom_prod) {
		
		foreach($this->list_produits as $prod)
		{
			if(strcmp($prod->getDesignation(),$nom_prod)==0 ) //le nom est le même, on a trouvé le produit
				return $prod;	
		}
		return null;
	}
	/**
	 * définit le nom catégorie parente
	 *
	 * @var string
	 */
	public function setParentCategorieName($cat){
		$this->parent_categorie_name = $cat;
	}
	/**
	 * définit l'identifiant de la catégorie parente
	 *
	 * @var string
	 */
	public function setParentCategorieId($id){
		$this->parentid = $id;
	}
	/**
	 * définit l'identifiant dans la base de données
	 *
	 * @var int $id Identifiant de la catégorie dans la BDD
	 */
	public function setIdentifiant($id){
		$this->id = $id;
	}
	/**
	 * modifie le nom français de la catégorie...
	 *
	 * @param string $nom
	 */
	public function setNomFR($nom){
		$this->nom_fr = $nom;
	}
	/**
	 * modifie le nom complet de la catégorie...
	 *
	 * @param string $nom
	 */
	public function setNomBrut($nom){
		$this->nom_brut = $nom;
	}
	/**
	 * modifie le nom américain de la catégorie...
	 *
	 * @param string $nom
	 */
	public function setNomEN($nom){
		$this->nom_en = $nom;
	}

	/**
	 * Constructeur
	 * @param string $identifiant Identifiant de la catégorie
	 * @param string $nomcat Nom de la catégorie, nouvelle ou pas
	 */
	function __construct($identifiant='', $nomcat='') {
		
		trim($nomcat);
		$this->setNomBrut($nomcat);
		$this->setIdentifiant($identifiant);
		
		$this->setNomFR('');
		$this->setNomEN('');
		
		$this->setParentCategorieName(null);
		$this->setParentCategorieId(null);
		//si on a spécifié un nom, on définit le nom de la catégorie
		if($nomcat != '')
		{
			list($fr,$en) = explodeCategorieName($nomcat);
			$this->setNomFR($fr);
			$this->setNomEN($en);
		}
		
		
		$this->list_produits = array();
		$this->list_souscat = array();
	}
	function read() {
		
	}
	function save() {
		
	}
}
/**
 * classe d'accés à la base de données...
 *
 */
class categorieDB {
	
	/**
	 * Renvoi le groupe donné du genre donné avec la liste des produits
	 * @param string $genre Identifiant du genre du groupe à récupérer
	 * @param string $groupe Identifiant du groupe à récupérer
	 * @return Renvoi une liste de Produit
	 */
	public static function getGroupe($genre, $groupe) {
		
		$cat = new Categorie();
		
		$sql = "SELECT * FROM produits WHERE idgroupe=:idgroupe AND genre Like (:genre);";
		
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		$stmt->bindParam(":idgroupe", $groupe);
		$stmt->bindParam(":genre", $genre);
		$stmt->execute();
		$data = $stmt->fetchAll();
		
		foreach($data as $ligne) {
			
			$prod = new Produit();
			$prod->setDesignation($ligne['descriptionproduit']);
			$prod->setPrixHT($ligne['prixproduiteuro']);
			$prod->setReference($ligne['referenceproduit']);
			$prod->setIdentifiant($ligne['idproduit']);
			
			$prod->setGroupeId($ligne['idgroupe']);
			$prod->setGroupeName($ligne['groupe']);
			$prod->setGenreId($ligne['genre']);
			$prod->setGenreName($ligne['genre']);
			
			$prod->setPromotion($ligne['promo']);
			
			$cat->addProduit($prod);
			
			
		}
		return $cat;	
	}
	
	/**
	 * Renvoi l'identifiant de la catégorie parent
	 * 
	 * @param int $id Identifiant de la catégorie
	 * @return string $catname Identifiant de la catégorie parent 
	 */
	public static function getParentCategorieIdById($id) {
		$catid;
		$sql = "SELECT idparentcategorie FROM categories WHERE idcategorie=:id;";
		
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		$stmt->bindParam(":id", $id);
		$stmt->execute();
		$data = $stmt->fetch();
		
		if(count($data) > 0)
		{
			$catid = $data['idparentcategorie'];
		}
		
		return $catid;	
	}
	/**
	 * Renvoi la liste des noms des catégories parentes
	 * 
	 * @param array Liste de string
	 */
	public static function getParentsCategoriesNames() {
		$namelist  = array(); //liste des noms des catégories parentes
		$sql = "SELECT descriptioncategorie FROM categories WHERE idparentcategorie=0;";
		
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		$stmt->execute();
		$data = $stmt->fetchAll();
		
		//print_r($data);
		foreach($data as $ligne) {
			array_push($namelist, $ligne['descriptioncategorie']);
		}
		return $namelist;	
		
	}
	/**
	 * Renvoi le nom d'une catégorie par rapport à son identifiant
	 * @param string $id Ifentifiant de la catégorie
	 */
	public static function getCategorieNameById($id) {
		$db = Zend_registry::get("database");
		$sql = "SELECT descriptioncategorie FROM categories WHERE idcategorie=:id;";
		$stmt = $db->prepare($sql);
		$stmt->bindParam(":id", $id);
		$stmt->execute();
		$data = $stmt->fetch();
		
		return $data['descriptioncategorie'];
	}
	
	/**
	 * Renvoi une liste de catégories, par rapport à une requête sql
	 * 
	 * @param statement $stmt Requête a executer
	 * @return array Liste de catégorires
	 */
	public static function getCategories($db, $stmt) {
		$catlist = array();
		
		$stmt->execute();
		$data = $stmt->fetchAll();
		
		//print_r($data);
		foreach($data as $ligne) {
			//crée la catégorie avec les paramètres récupérés
			$cat = new Categorie($ligne['descriptioncategorie']);
			$cat->setEclate($ligne['eclatecategorie']);
			$cat->setImage($ligne['imagecategorie']);
			$cat->setIdentifiant($ligne['idcategorie']);
			$cat->setParentCategorieId($ligne['idparentcategorie']);
			
			if($ligne['idparentcategorie'] == 0) {
			//echo 'c une cat parent';
				//echo 'id egal a zero, id parent=',$catparent;
				//on récupère aussi les sous catégories
				$stmt = $db->prepare("select * from categories where idparentcategorie=:parent;");
				$stmt->bindParam(':parent',$cat->getIdentifiant());			
				//récupère les sous catégorie de la catégorie courrante
				$list_sscat = CategorieDB::getCategories($db, $stmt);
				//parcours les sous catégorie pour les ajouter à la catégorie courrante
				foreach($list_sscat as $sscat) {
					
					/*print_r($sscat);
					echo 'on lui ajoute une sous cat';*/
					$cat->addSousCategorie($sscat);
				}
				$cat->setParentCategorieName(null);
				
			} elseif($ligne['idparentcategorie'] > 0) {
				
				
				$name = CategorieDB::getCategorieNameById($ligne['idparentcategorie']);
				$cat->setParentCategorieName($name);
				
				//echo 'id cat parent du prod : ',$cat->getIdentifiant();
				//on récupère aussi les produits
				$stmt = $db->prepare("select * from produits where idcategorie=:parent;");
				$stmt->bindParam(':parent',$cat->getIdentifiant());			
				$stmt->execute();
				$data = $stmt->fetchAll();
				
				//print_r($data);
				foreach($data as $ligne) {
					
					$prod = new Produit();
					$prod->setDesignation($ligne['descriptionproduit']);
					$prod->setPrixHT($ligne['prixproduiteuro']);
					$prod->setReference($ligne['referenceproduit']);
					$prod->setIdentifiant($ligne['idproduit']);
					$prod->setGroupeName($cat->getNomBrut());
					$prod->setGroupeId($cat->getIdentifiant());
					$prod->setGenreName($cat->getParentCategorieName());
					$prod->setGenreId($cat->getParentCategorieId());
				
					$cat->addProduit($prod);
				}
				
			}
			
			array_push($catlist, $cat);
		}
				
		return $catlist;
	}
	/**
	 * Renvoi la liste des catégories parentes (niveau 0)
	 * 
	 * @return array Renvoi une liste de catégories
	 */
	public static function getParentsCategoriesList() {
		$sql = "SELECT * FROM categories WHERE idparentcategorie=:parent;";
		
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		$catparent = 0;
		$stmt->bindParam(':parent',$catparent);
		
		return CategorieDB::getCategories($db, $stmt);
	}
	/**
	 * Renvoi une catégorie, avec la liste de ses sous-cat ou de ses produits à partir de son identifiant
	 * 
	 * @param string $id Identifiant de la catégorie
	 */
	public static function getCategorieById($id) {
		
		$sql = "SELECT * FROM categories WHERE idcategorie=:id;";
		
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		
		$stmt->bindParam(':id',$id);
		
		$res = CategorieDB::getCategories($db, $stmt);
		
		if(count($res)>0)
			return $res[0];
		else
			return null;
		
	}
	/**
	 * Renvoi une catégorie, avec la liste de ses sous-cat ou de ses produits à partir du nom
	 * 
	 * @param string $nom Nom de la catégorie à charger
	 * @param Categorie $catparent Categorie parent ou null si pas de parent
	 */
	public static function getCategorieByName($nom, $catparent = 0) {
		
		$sql = "SELECT * FROM categories WHERE descriptioncategorie=:desc AND idparentcategorie=:parent;";
		
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		
		$stmt->bindParam(':desc',$nom);
		$stmt->bindParam(':parent',$catparent);
		
		$res = CategorieDB::getCategories($db, $stmt);
		
		if(count($res)>0)
			return $res[0];
		else
			return null;
		
	}
	/**
	 * sauvegarde une liste de catégories dans la base de données
	 * sauvegarde aussi ses sous catégorie et ses produits
	 * 
	 * @var array Liste de catégories
	 */
	public static function saveCategorieList($cat_list) {
		
		$iserror = false; //boolean pour savoir si une erreur est apparue
		echo '<div class="nocenter_alignleft error messageBox">';
		echo '<table class="liste tableerror" style="width:95%;">';
		echo '<tr><th>Ligne</th><th>Genre</th><th>Groupe</th><th>Erreur</th></tr>';
		
		$nbtotalcat=0;
		$nbtotalsscat=0;
		$nbtotalprod=0;
		set_time_limit(300);
		
		//parcours toutes les catégories
		foreach($cat_list as $cat) 
		{
			//sauvegarde la catégorie et retourne l'identifiant dans la base de donnée
			list($nbcat, $nbsscat, $nbprod) = self::saveCategorie($cat);
			$nbtotalcat+=$nbcat;
			$nbtotalsscat+=$nbsscat;
			$nbtotalprod+=$nbprod;
		}
		set_time_limit(60);
		echo '</table>';
		echo '</div>';
		return array($nbtotalcat, $nbtotalsscat, $nbtotalprod);
	}
	/**
	 * sauvegarde une catégorie dans la base de données
	 * sauvegarde aussi ses sous-catégories et aussi ses produits
	 * 
	 * @var Categorie $cat
	 * @return list Renvoi une liste (le nombre de catégories ajoutées, le nombre de sous-catégories ajoutées, le nombre de produits ajoutés)
	 */
	public static function saveCategorie($cat) {
		
		// Connexion à la BDD
		$db = Zend_registry::get("database");

		//SAUVEGARDE DE LA CATEGORIE
		
		// Préparation de la requête pour vérifier si le produit existe déjà
		$stmt = $db->prepare("insert into categories (descriptioncategorie, imagecategorie, eclatecategorie, idparentcategorie) VALUES (:desc, :img, :eclat, :parentcat)");
		
		$stmt->bindParam(':desc', $cat->getNomBrut());
		$stmt->bindParam(':img', $cat->getImage());
		$stmt->bindParam(':eclat', $cat->getEclate());
		
		//vérifie si la catégorie courante est une catégorie parente
		if($cat->getParentCategorieId()!=0)
		{
			//non, donc on sauvegarde l'id de la catégorie parente
			//echo 'il y a une cat parent !<br>';
			$stmt->bindParam(':parentcat', $cat->getParentCategorieId());
		}
		else
		{
			//oui, donc on enregistre rien
			//echo 'pas de cat parent !<br>';
			$temp = 0;
			$stmt->bindParam(':parentcat', $temp);
		}
			
		$stmt->execute();
		//on regarde le résultat
		$nb_cat_ajoutes = 0;
		$nb_sscat_ajoutes = 0;
		$nb_prod_ajoutes = 0;
		
		$newcatid = null;
		if ($stmt->rowCount() == 1)
		{
			//echo $cat->getNomBrut(),', identifiant : ',$db->lastInsertId('idcategorie'),'<br>';
			//sauvegarde l'identiant dans le produit
			$cat->setIdentifiant($db->lastInsertId('idcategorie'));
			$newcatid = $db->lastInsertId('idcategorie');
			$nb_cat_ajoutes++;
		}
		
		//SAUVEGARDE DES SOUS-CATEGORIES OU DES PRODUITS
		if(count($cat->getProduitList()) == 0)
		{
			//on s'occupe des sous-catégories, la catégorie courante est une catégorie principale
			$sscatlist = $cat->getSousCategorieList();
			foreach($sscatlist as $sscat)
			{
				//on enregistre l'id de la catégorie parent avant d'enregistrer la catégorie
				$sscat->setParentCategorieId($newcatid);
				list($c, $ssc, $p) = self::saveCategorie($sscat);
				//modifie nombre de sous catégories ajoutées ainsi que le nombre de produits
				//égal au nombre de catégories ajoutées par l'appel de la méthode précédente
				$nb_sscat_ajoutes += $c;
				$nb_prod_ajoutes += $p;
			}
		}
		else
		{
			//on s'occupe des produits, la catégorie courante est une sous-catégorie
			//on s'occupe des sous-catégories, la catégorie courante est une catégorie principale
			$prodlist = $cat->getProduitList();
			foreach($prodlist as $prod)
			{
				//sauvegarde les données avant l'enregistrement dans la BDD
				/*$_SESSION['catparentproduittosave'] = serialize($cat->getParentCategorie());
				$_SESSION['catproduittosave'] = serialize($cat);
				$_SESSION['produittosave'] = serialize($prod);*/
			
				$prod->setGroupeId($newcatid); //définit l'identifiant du groupe auquel appartient le produit avant de l'enregistrer	
				$nb_prod_ajoutes += ProduitDB::saveProduit($prod);
			}
			
		
			
		}
		
		return array($nb_cat_ajoutes,$nb_sscat_ajoutes,$nb_prod_ajoutes);
	}
	/**
	 * Vide la table catégorie
	 */
	public static function truncateTable() {
		$sql ="TRUNCATE TABLE categories;";
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		$stmt->execute();	
	}
}
/**
 * Gère l'affichage d'une catégorie
 */
class categorieDisplay {
	

}
?>