<?php

/**
 * classe client qui herite de internaute
 *
 */
class client extends internaute {

	private $email;
	private $password;
	private $nom;
	private $prenom;
	private $tel;
	private $fax;
	private $infos;
	private $datecreate;
	private $vehiculeprefer;
	private $uid;
	private $indice;
	private $email_second;
	private $date_anni;
	//******** ADRESSE LIVRAISON
	private $livreAdresse;
	private $raisonsocial;
	private $nomrue;
	private $codepostal;
	private $ville;
	private $pays;
	//******** ADRESSE FACTURATION
	private $factAdresse;
	private $af_raisonsocial;
	private $af_nomrue;
	private $af_codepostal;
	private $af_ville;
	private $af_pays;
	
//--------------- GET -------------------	
	public function getEmail() { return $this->email; }
	public function getPassword() { return $this->password; }
	public function getNom() { return $this->nom; }
	public function getPrenom() { return $this->prenom; }
	public function getUid() { return $this->uid; }
	public function getTel() { return $this->tel; }
	public function getFax() { return $this->fax; }
	public function getInfos(){ return $this->infos; }
	public function getDatecreate() { return $this->datecreate; }	
	public function getVehiculeprefer() { return $this->vehiculeprefer; }	
	public function getIndice() { return $this->indice; }	
	public function getEmail_second() { return $this->email_second; }	
	public function getDate_anni() { return $this->date_anni; }	
	//******** ADRESSE LIVRAISON
	public function getLivreAdresse() { return $this->livreAdresse; }
	public function getRaisonsocial() { return $this->raisonsocial; }	
	public function getNomrue() { return $this->nomrue; }	
	public function getCodepostal() { return $this->codepostal; }	
	public function getVille() { return $this->ville; }	
	public function getPays() { return $this->pays; }	
	//******** ADRESSE FACTURATION
	public function getFactAdresse() { return $this->factAdresse; }
	public function getAf_Raisonsocial() { return $this->af_raisonsocial; }	
	public function getAf_Nomrue() { return $this->af_nomrue; }	
	public function getAf_Codepostal() { return $this->af_codepostal; }	
	public function getAf_Ville() { return $this->af_ville; }	
	public function getAf_Pays() { return $this->af_pays; }	


//--------------- SET --------------------
	public function setNom($nom) { $this->nom = $nom; }	
	public function setPrenom($prenom) { $this->prenom = $prenom; }
	public function setEmail($email) { $this->email = $email; }	
	public function setPassword($password) { $this->password = $password; }
	public function setUid($uid) {$this->uid = $uid;}
	public function setTel($tel) {$this->tel = $tel;}
	public function setFax($fax) {$this->fax = $fax;}	
	public function setInfos($infos){$this->infos = $infos;}
	public function setIndice($indice) {$this->indice = $indice;}
	public function setDatecreate($datecreate) {$this->datecreate = $datecreate;}	
	public function setVehiculeprefer($vehiculeprefer) {$this->vehiculeprefer = $vehiculeprefer;}	
	public function setEmail_second($email_second) {$this->email_second = $email_second;}	
	public function setDate_anni($date_anni) {$this->date_anni = $date_anni;}
	//******** ADRESSE LIVRAISON	
	public function setLivreAdresse($livreAdresse) {$this->livreAdresse = $livreAdresse;}
	public function setRaisonsocial($raisonsocial) {$this->raisonsocial = $raisonsocial;}	
	public function setNomrue($nomrue) {$this->nomrue = $nomrue;}	
	public function setCodepostal($codepostal) {$this->codepostal = $codepostal;}	
	public function setVille($ville) {$this->ville = $ville;}	
	public function setPays($pays) {$this->pays = $pays;}	
	//******** ADRESSE FACTURATION	
	public function setFactAdresse($factAdresse) {$this->factAdresse = $factAdresse;}
	public function setAf_Raisonsocial($af_raisonsocial) {$this->af_raisonsocial = $af_raisonsocial;}
	public function setAf_Nomrue($af_nomrue) {$this->af_nomrue = $af_nomrue;}	
	public function setAf_Codepostal($af_codepostal) {$this->af_codepostal = $af_codepostal;}
	public function setAf_Ville($af_ville) {$this->af_ville = $af_ville;}	
	public function setAf_Pays($af_pays) {$this->af_pays = $af_pays;}
	
	public function __construct($email = '') {
		$this->clear();
		if($email !== ''){
			$temp = clientDB::getClientByEmail($email);
			if($temp != null){
				$this->setEmail($temp->getEmail());
				$this->setNom($temp->getNom(),$temp->getPrenom());
				$this->setPassword($temp->getPassword());
				$this->isAdmin = $temp->isAdmin;
				$this->isAuthenticated = $temp->isAuthenticated;
				$this->setTel($temp->getTel());
				$this->setFax($temp->getFax());
				$this->setInfos($temp->getInfos());
			}
		}
	}
	
	public function clear() {
		$this->email = "";
		$this->nom = "";
		$this->prenom = "";
		$this->password = "";
		$this->factAdresse = "";
		$this->livreAdresse = "";
		$this->isAdmin = false;
		$this->isAuthenticated = false;;
		$this->tel = "";
		$this->fax = "";
		$this->infos = "";
	}
	public function checkPassword($pass) {
		//if($pass == $this->password){
		if(md5($pass) == $this->password){
			$this->isAuthenticated = true;
			return true;
		}
		return false;
	}

}
/**
 * classe d'accés à la base de données...
 *
 */
class clientDB {
	
	public static function getIndicePro($emailclient)
	{
		$db = Zend_registry::get("database");
		$sql = "SELECT indiceclient FROM clients WHERE emailclient=:emailclient";
		$stmt = $db->prepare($sql);
		$stmt->bindParam(":emailclient", $emailclient);
		$stmt->execute();
		$indiceclient = $stmt->fetch();
		return $indiceclient['indiceclient'];	
	}
	
	public static function getClientByData2($ligne = array()){
		$client = new client;
		$client->setEmail($ligne[0]);
		$client->setNom($ligne[1], $ligne[2]);
		$client->setPassword($ligne[3]);
		$client->isAdmin = $ligne[4]==0?false:true;
		$client->isAuthenticated = true;
		$client->setTel($ligne[6]);
		$client->setFax($ligne[7]);
		$client->setInfos($ligne[8]);
		$client->setIndice($ligne[9]);
		$client->setAf_Raisonsocial($ligne[10]);
		$client->setAf_Nomrue($ligne[11]);
		$client->setAf_Codepostal($ligne[12]);
		$client->setAf_Ville($ligne[13]);
		$client->setAf_Pays($ligne[14]);
		return $client;
	}
	
	public static function getClientByData($ligne = array()){
		$client = new client;
		$client->setEmail($ligne[0]);
		$client->setNom($ligne[1], $ligne[2]);
		$client->setPassword($ligne[3]);
		$client->isAdmin = $ligne[4]==0?false:true;
		$client->isAuthenticated = true;
		$client->setTel($ligne[6]);
		$client->setFax($ligne[7]);
		$client->setInfos($ligne[8]);
		$client->setIndice($ligne[9]);
		return $client;
	}
	
	/**
	 * Récupère la liste des clients
	 * 
	 * @return array Un tableau de Client
	 */
	public static function getClientsList() {
		$tab = array();
		$sql = "SELECT  clients.emailclient,nomclient,prenomclient,passwdclient,isadmin,uid,telclient,faxclient,infos,indiceclient,af_raisonsocial,af_nomrue,af_codepostal,af_ville,af_pays
		FROM clients 
		INNER JOIN adresses_facturation 
		ON clients.emailclient=adresses_facturation.af_emailclient 
		ORDER BY indiceclient, nomclient, prenomclient";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->execute();
		$data = $stmt->fetchAll();
		foreach($data as $ligne){
			array_push($tab, self::getClientByData2($ligne));
		}
	
		return $tab;
	}
	/**
	 * fonction qui retourne un objet client à l'aide de son identifiant...
	 *
	 * @param string $id
	 * @return client
	 */
	 
	public static function getClientByEmail($id) {
		$sql = "SELECT * FROM clients WHERE emailclient=?;";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->bindParam(1,$id);
		$stmt->execute();
		$data = $stmt->fetch();
		if($data != null){
			return self::getClientByData($data);
		}
		return null;
	}
	
	public static function  getClientByEmail2($id) {
		
		$sql = "SELECT * FROM clients 
		INNER JOIN adresses
			ON clients.emailclient = adresses.emailclient
		INNER JOIN adresses_facturation
			ON clients.emailclient = adresses_facturation.af_emailclient
		WHERE clients.emailclient=:id;";
		
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		$stmt->bindParam(':id',$id);
		$stmt->execute();
		$data = $stmt->fetch();

		if(count($data) != 0){
			
			$client = new client;
			$client->setEmail($data['emailclient']);
			$client->setNom($data['nomclient'], $data['prenomclient']);
			$client->setPrenom($data['prenomclient']);
			$client->setTel($data['telclient']);
			$client->setFax($data['faxclient']);
			$client->setInfos($data['infos']);
			$client->setIndice($data['indiceclient']);
			$client->setVehiculeprefer($data['vehiculeprefer']);
			$client->setEmail_second($data['email_secondaire']);
			$client->setDate_anni($data['date_anni']);
			//******** ADRESSE LIVRAISON
			$client->setRaisonsocial($data['raisonsocial']);
			$client->setNomrue($data['nomrue']);
			$client->setCodepostal($data['codepostal']);
			$client->setVille($data['ville']);
			$client->setPays($data['pays']);	
			//******** ADRESSE FACTURATION	
			$client->setAf_Raisonsocial($data['af_raisonsocial']);
			$client->setAf_Nomrue($data['af_nomrue']);
			$client->setAf_Codepostal($data['af_codepostal']);
			$client->setAf_Ville($data['af_ville']);
			$client->setAf_Pays($data['af_pays']);
			return $client;
			
		}
		return null;
	}	
	
	/* fonction qui retourne un objet client à l'aide de son identifiant...
	 *
	 * @param string $id
	 * @return client
	 */
	public static function getClientByuid($uid) {
		$sql = "SELECT * FROM clients WHERE uid=?;";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->bindParam(1,$uid);
		$stmt->execute();
		$data = $stmt->fetch();
		if($data != null){
			return self::getClientByData($data);
		}
		return null;
	}
	/**
	* fonction qui enregistre un nouveau client dans la BDD
	*
	* @param client
	*
	* @return boolean
	*/
	public function saveNew($client) {
		$sql = "INSERT INTO clients (nomclient, prenomclient, emailclient, passwdclient, isadmin, telclient, faxclient, infos, datecreate, vehiculeprefer) VALUES (:nom, :prenom, :email, :passwd, :isadmin, :tel, :fax, :infos, :datecreate, :vehiculeprefer);";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->bindParam(':nom', $client->getNom());
		$stmt->bindParam(':prenom', $client->getPrenom());
		$stmt->bindParam(':email', $client->getEmail());
		$stmt->bindParam(':passwd', $client->getPassword());
		$stmt->bindParam(':tel', $client->getTel());
		$stmt->bindParam(':fax', $client->getFax());
		$stmt->bindParam(':infos', $client->getInfos());
		$stmt->bindParam(':datecreate', $client->getDatecreate());
		$stmt->bindParam(':vehiculeprefer', $client->getVehiculeprefer());
		$isadmin = "0";
		$stmt->bindParam(':isadmin', $isadmin);
		$stmt->execute();
		
		$sql = "INSERT INTO adresses (emailclient, raisonsocial, nomrue, codepostal, ville, pays) VALUES (:emailclient, :raisonsocial, :nomrue, :codepostal, :ville, :pays);";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->bindParam(':emailclient', $client->getEmail());
		$stmt->bindParam(':raisonsocial', $client->getRaisonsocial());
		$stmt->bindParam(':nomrue', $client->getNomrue());
		$stmt->bindParam(':codepostal', $client->getCodepostal());
		$stmt->bindParam(':ville', $client->getVille());
		$stmt->bindParam(':pays', $client->getPays());
		$stmt->execute();
		
		$sql = "INSERT INTO adresses_facturation (af_emailclient, af_raisonsocial, af_nomrue, af_codepostal, af_ville, af_pays) VALUES (:af_emailclient, :af_raisonsocial, :af_nomrue, :af_codepostal, :af_ville, :af_pays);";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->bindParam(':af_emailclient', $client->getEmail());
		$stmt->bindParam(':af_raisonsocial', $client->getAf_Raisonsocial());
		$stmt->bindParam(':af_nomrue', $client->getAf_Nomrue());
		$stmt->bindParam(':af_codepostal', $client->getAf_Codepostal());
		$stmt->bindParam(':af_ville', $client->getAf_Ville());
		$stmt->bindParam(':af_pays', $client->getAf_Pays());
		$stmt->execute();
		
		if ($stmt->rowCount() == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
	/**
	* fonction qui enregistre les données d'un client dans la BDD
	*
	* @param client
	*
	* @return boolean
	*/
	public static function save($client) {
		$sql = "UPDATE clients SET nomclient=:nom, prenomclient=:prenom, passwdclient=:passwd, isadmin=:isadmin, uid=:uid, telclient=:tel, faxclient=:fax, infos=:infos WHERE emailclient=:idclient;";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$nom = $client->getNom();
		$prenom = $client->getPrenom();
		$password = $client->getPassword();
		$email = $client->getEmail();
		$iud = $client->getUid();
		$tel = $client->getTel();
		$fax = $client->getFax();
		$infos = $client->getInfos();
		$stmt->bindParam(':nom', $nom);
		$stmt->bindParam(':prenom', $prenom);
		$stmt->bindParam(':passwd', $password);
		$stmt->bindParam(':idclient', $email);
		$stmt->bindParam(':uid', $iud);
		$stmt->bindParam(':tel', $tel);
		$stmt->bindParam(':fax', $fax);
		$stmt->bindParam(':infos', $infos);
		$isadmin = 0;
		$stmt->bindParam(':isadmin', $isadmin);
		$stmt->execute();
		
		if ($stmt->rowCount() == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
	//
	public function save2($client) {
		$sql = "UPDATE clients SET nomclient=:nom, prenomclient=:prenom, passwdclient=:passwd, telclient=:tel, faxclient=:fax, vehiculeprefer=:vehiculeprefer, email_secondaire=:email_secondaire, date_anni=:date_anni  WHERE emailclient=:idclient;";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$nom = $client->getNom();
		$prenom = $client->getPrenom();
		$passwd = $client->getPassword();
		$idclient = $client->getEmail();
		$tel = $client->getTel();
		$fax = $client->getFax();
		$vehiculeprefer = $client->getVehiculeprefer();
		$email_secondaire = $client->getEmail_second();
		$date_anni = $client->getDate_anni();
		$stmt->bindParam(':nom', $nom);
		$stmt->bindParam(':prenom', $prenom);
		$stmt->bindParam(':passwd', $passwd);
		$stmt->bindParam(':idclient', $idclient);
		$stmt->bindParam(':tel', $tel);
		$stmt->bindParam(':fax', $fax);
		$stmt->bindParam(':vehiculeprefer', $vehiculeprefer);
		$stmt->bindParam(':email_secondaire', $email_secondaire);
		$stmt->bindParam(':date_anni', $date_anni);
		$stmt->execute();
		
		$sql = "UPDATE adresses SET raisonsocial=:raisonsocial, nomrue=:nomrue, codepostal=:codepostal, ville=:ville, pays=:pays WHERE emailclient=:idclient;";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		
		$raisonsocial = $client->getRaisonsocial();
		$nomrue = $client->getNomrue();
		$codepostal = $client->getCodepostal();
		$ville = $client->getVille();
		$pays = $client->getPays();
		
		$stmt->bindParam(':idclient', $idclient);
		$stmt->bindParam(':raisonsocial', $raisonsocial);
		$stmt->bindParam(':nomrue', $nomrue);
		$stmt->bindParam(':codepostal',$codepostal);
		$stmt->bindParam(':ville', $ville);
		$stmt->bindParam(':pays', $pays);
		$stmt->execute();
		
		$sql = "UPDATE adresses_facturation SET af_raisonsocial=:af_raisonsocial, af_nomrue=:af_nomrue, af_codepostal=:af_codepostal, af_ville=:af_ville, af_pays=:af_pays WHERE af_emailclient=:idclient;";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		
		$af_raisonsocial = $client->getAf_Raisonsocial();
		$af_nomrue = $client->getAf_Nomrue();
		$af_codepostal = $client->getAf_Codepostal();
		$af_ville = $client->getAf_Ville();
		$af_pays = $client->getAf_Pays();
		
		$stmt->bindParam(':idclient', $idclient);
		$stmt->bindParam(':af_raisonsocial', $af_raisonsocial);
		$stmt->bindParam(':af_nomrue', $af_nomrue);
		$stmt->bindParam(':af_codepostal', $af_codepostal);
		$stmt->bindParam(':af_ville', $af_ville);
		$stmt->bindParam(':af_pays', $af_pays);
		$stmt->execute();
		
		if ($stmt->rowCount() == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
	
	/**
	 * fonction qui supprimer un client de la base de donnée
	 *
	 * @param client $client
	 */
	public static function delete($client) {
		// pour supprimer un client il faut tout d'abord supprimer ses adresses
		adresseDB::deleteAdresseDuClient($client);
		$sql ="DELETE FROM clients WHERE emailclient=:idclient;";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$idclient = $client->getEmail();
		$stmt->bindParam(':idclient',$idclient);
		$stmt->execute();	
		if ($stmt->rowCount() == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
}