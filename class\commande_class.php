<?php
require_once 'adresse_class.php';
require_once 'panier_class.php';
/**
 * classe qui gère une commande
 *
 */
class commande {
	/**
	 * total HT de la commande...
	 *
	 * @var decimal
	 */
	private $totalHT;
	/**
	 * identifiant de la commande...
	 *
	 * @var int
	 */
	private $id;
	/**
	 * Taux de TVA...
	 *
	 * @var decimal
	 */
	private $tauxTVA;
	/**
	 * Panier qui correspond à la commande...
	 *
	 * @var Panier
	 */
	private $panier;
	/**
	 * date de le commande...
	 *
	 * @var date
	 */
	private $dateCommande;
	/**
	 * statut de la commande...
	 *
	 * @var int
	 */
	private $statut;
	/**
	 * mode de paiement...
	 *
	 * @var int
	 */
	private $modePaiement;
	/**
	 * identifiant de l'adresse de facturation...
	 *
	 * @var int
	 */
	private $factAdresse;
	/**
	 * identifiant de l'adresse de livraison...
	 *
	 * @var int
	 */
	private $livreAdresse;
	/**
	 * retourne le total HT de la commande...
	 *
	 * @return decimal
	 */
	public function getTotalHT(){
		return $this->totalHT;
	}
	/**
	 * retourne l'identifiant de la commande...
	 *
	 * @return int
	 */
	public function getId(){
		return $this->id;
	}
	/**
	 * retourne le taux de TVA de la commande...
	 *
	 * @return decimal
	 */
	public function getTauxTVA(){
		return  $this->tauxTVA;
	}
	/**
	 * retourne le panier associé à la commande..
	 *
	 * @return Panier
	 */
	public function getPanier(){
		if($this->panier == null){
			$this->panier = commandeDB::getPanierByCommande($this);
		}
		return $this->panier;
	}
	/**
	 * retourne la date de la commande ...
	 *
	 * @return date
	 */
	public function getDateCommande(){
		return $this->dateCommande;
	}
	/**
	 * retourne le statut de la commande...
	 *
	 * @return int
	 */
	public function getStatut(){
		return $this->statut;
	}
	/**
	 * retourne le mode de paiement...
	 *
	 * @return int
	 */
	public function getModePaiement(){
		return $this->modePaiement;
	}
	/**
	 * retourne l'adresse de facturation..
	 *
	 * @return int
	 */
	public function getFactAdresse(){
		if($this->factAdresse == null){
			$this->factAdresse = commandeDB::getAdresseByCommandeAndType($this,false);
		}
		return $this->factAdresse;
	}
	/**
	 * retourne l'adresse de livraison...
	 *
	 * @return int
	 */
	public function getLivreAdresse(){
		if($this->livreAdresse == null){
			$this->livreAdresse = commandeDB::getAdresseByCommandeAndType($this,true);
		}
		return $this->livreAdresse;
	}
	
	/**
	 * modifie le prix total HT de la commande...
	 *
	 * @param decimal $totaltHT
	 */
	public function setTotalHT($totalHT){
		$this->totalHT = $totalHT;
	}
	/**
	 * modifie l'identifiant de la commande...
	 *
	 * @param int $id
	 */
	public function setId($id){
		$this->id = $id;
	}
	/**
	 * modifie le taux de TVA de la commande...
	 *
	 * @param decimal $tauxTVA
	 */
	public function setTauxTVA($tauxTVA){
		$this->tauxTVA = $tauxTVA;
	}
	/**
	 * modifie le panier associé à la commande...
	 *
	 * @param Panier $panier
	 */
	public function setPanier($panier){
		$this->panier = $panier;
	}
	/**
	 * modifie la date de commande...
	 *
	 * @param date $dateCommande
	 */
	public function setDateCommande($dateCommande){
		$this->dateCommande = $dateCommande;
	}
	/**
	 *modifie le statut de la commande...
	 *
	 * @param int $statut
	 */
	public function setStatut($statut){
		$this->statut = $statut;
	}
	/**
	 * modifie le mode de paiement de la commande...
	 *
	 * @param int $modePaiement
	 */
	public function setModePaiement($modePaiement){
		$this->modePaiement = $modePaiement;
	}
	/**
	 * modifie l'adresse de facturation...
	 *
	 * @param int $factAdresse
	 */
	public function setFactAdresse($factAdresse){
		$this->factAdresse = $factAdresse;
	}
	/**
	 * modifie l'adresse de livraison...
	 *
	 * @param int $livreAdresse
	 */
	public function setLivreAdresse($livreAdresse){
		$this->livreAdresse = $livreAdresse;
	}
	
	private $emailClient;
	private $client;
	
	public function getClient(){
		if($this->client == null){
			$this->client = clientDB::getClientByEmail($this->emailClient);
		}
		return $this->client;
	}
	
	public function setClient($client){
		$this->client = $client;
	}
	
	public function setEmailClient($email){
		$this->emailClient = $email;
	}
	
	public function getEmailClient(){
		return $this->emailClient;
	}
	
	public function getTotalTTC(){
		//return ($this->getTotalHT()*($this->getTauxTVA()/100.0))+$this->getTotalHT();
		return (($this->getTotalHT()*($this->getTauxTVA()/100.0))+$this->getTotalHT()) + ($this->getFraisDePort()+($this->getFraisDePort()*$this->getTauxTVA()/100.0));
	}
	
	private $retraitMagasin;
	
	public function setRetraitMagasin($retraitmagasin){
		$this->retraitMagasin = $retraitmagasin;
	}
	public function getRetraitMagasin(){
		return $this->retraitMagasin;
	}
	
	public function __construct(){
		$this->totaltHT = 0.0;
		$this->id = 0;
		$this->tauxTVA = 0.0;
		$this->panier = null;
		$this->dateCommande = null;
		$this->statut = 0;
		$this->modePaiement = 0;
		$this->factAdresse = null;
		$this->livreAdresse = null;
		$this->retraitMagasin = false;
		$this->emailClient = "";
		$this->client = null;
	}
	
	
	private $fraisPort;
	
	public function setFraisDePort($frais){
		$this->fraisPort = $frais;
	}
	
	public function getFraisDePort(){
		return $this->fraisPort;
	}
	// constante etat commande
	const ST_CANCEL = -1;
	const ST_ATTENTE_PAY = 1;
	const ST_ATTENTE = 2;
	const ST_PREPA = 3;
	const ST_SEND = 4;
	
	//constante mode de paiement
	const MD_CB = 1;
	const MD_CH = 2;
	const MD_CR = 3;
	const MD_PP = 4;
	const MD_MC = 5;
	const MD_VI = 6;
}
/**
 * classe d'accés à la base de donnée...
 *
 */
class commandeDB {
	
/**
	 * Fonction qui enregistre une commande dans la base de données
	 * si commande->livraisonAdresse et facturation adresse sont nulles, 
	 * on suppose que l'on retire les produits en magasin
	 * @param string $idclient
	 * @param commande $c
	 * @param boolean $new
	 * @throw Exception si il y a une erreur lors de l'enregistrement
	 */	
	public static function saveCommande($idclient, commande $c, $new = true){
		global $user;
		$db = Zend_Registry::get('database');
		if($new == true){
			// on veut enregistrer une nouvelle commande
			$req1 = "INSERT INTO commandes (`emailclient`,
											`prixtotalhtcommande`,
											`fraisdeport`,
											`statutcommande`,
											`datecommande`,
											`tvacommande`,
											`modepaiement`,
											`iscommandeadmin`,
											`retraitmagasin`
											) VALUES (?,?,?,?,?,?,?,?,?)";
			$db = Zend_Registry::get('database');
			$stmt = $db->prepare($req1);
			$stmt->bindParam(1,$idclient);
			$stmt->bindParam(2,$c->getTotalHT());
			$stmt->bindParam(3,$c->getPanier()->getFraisDePort());
			$c->setFraisDePort($c->getPanier()->getFraisDePort());
			$stmt->bindParam(4,$c->getStatut());
			$stmt->bindParam(5,$c->getDateCommande());
			$stmt->bindParam(6,$c->getPanier()->getTauxTVA());
			$stmt->bindParam(7,$c->getModePaiement());
			$stmt->bindParam(8,$user->isAdmin);
			$stmt->bindParam(9,$c->getRetraitMagasin());
			$c->setEmailClient($idclient);
			$stmt->execute();
			if($stmt->rowCount() == 1){
				// l'enregistrement de la première partie de la commande a réussi
				$c->setId($db->lastInsertId());
				// etape 2, on enregistre les adresses
				if(!$c->getRetraitMagasin()){
					// seulement dans le cas ou elle ne sont pas nulle
					self::saveAdresseCommande($c,$c->getLivreAdresse(),true);
					self::saveAdresseCommande($c,$c->getFactAdresse(),false);
				} else {
					self::saveAdresseCommande($c,$c->getFactAdresse(),false);
				}
				
				// etape 3, on enregistre les lignes de la commande (il y en a au moins 1)
				$lignPs = $c->getPanier()->getLignePanier();
				foreach($lignPs as $lp){
					self::saveLigneCommande($c,$lp);
				}
				
				// l'enregistrement de la commande est terminée
				return true;
			}
			else{
				// echec de l'enregistrement
				throw new Exception("Impossible de créer la commande !!");
			}
		}
		else{
			// mise a jour de la commande (pour l'instant, juste l'etat
			$req = "UPDATE commandes SET statutcommande = :st WHERE idcommande = :id";
			$stmt = $db->prepare($req);
			$stmt->bindParam(':st',$c->getStatut());
			$stmt->bindParam(':id',$c->getId());
			$stmt->execute();
		}
	}
	
	public static function getCommandeByData($data = array()){
		
		
		$commande = new commande();
		$commande->setFraisDePort($data['fraisdeport']);
		$commande->setRetraitMagasin($data['retraitmagasin']);
		$commande->setDateCommande($data['datecommande']);
		$commande->setId($data['idcommande']);
		$commande->setModePaiement($data['modepaiement']);
		$commande->setStatut($data['statutcommande']);
		$commande->setTauxTVA($data['tvacommande']);
		$commande->setTotalHT($data['prixtotalhtcommande']);
		$commande->setEmailClient($data['emailclient']);
		return $commande;
	}
	
	/**
	 * Enter description here...
	 *
	 * @param unknown_type $id
	 * @return commande
	 */
	public static function getCommandeById($id = ''){
		$db = Zend_Registry::get('database');
		$req = "SELECT * FROM commandes WHERE idcommande = :id";
		$stmt = $db->prepare($req);
		$stmt->bindParam(':id',$id);
		$stmt->execute();
		$data = $stmt->fetch();
		return self::getCommandeByData($data);
	}
	
	public static function getCommandes(){
		$db = Zend_Registry::get('database');
		//$req = "SELECT * FROM commandes ORDER BY datecommande DESC";
		$req = "SELECT * FROM commandes ORDER BY idcommande DESC";
		$stmt = $db->prepare($req);
		$stmt->execute();
		$data = $stmt->fetchAll();
		$commandes = array();
		foreach($data as $ligne){
			$commandes[] = self::getCommandeByData($ligne);	
		}		
		return $commandes;
	}
	
	public static function getCommandesByTri($tri1,$sens1){
		$db = Zend_Registry::get('database');
		//$req = "SELECT * FROM commandes ORDER BY datecommande DESC";
		if ($tri1 == "prixtotalhtcommande")
			$req = "SELECT * FROM commandes ORDER BY (prixtotalhtcommande + tvacommande + fraisdeport) ".$sens1;
		else
			$req = "SELECT * FROM commandes ORDER BY ".$tri1." ".$sens1;
		$stmt = $db->prepare($req);
		$stmt->execute();
		$data = $stmt->fetchAll();
		$commandes = array();
		foreach($data as $ligne){
			$commandes[] = self::getCommandeByData($ligne);	
		}		
		return $commandes;
	}
	
	public static function getCommandesByFiltre($filtre1){
		$db = Zend_Registry::get('database');
		$req = "SELECT * FROM commandes WHERE statutcommande = ".$filtre1." ORDER BY idcommande DESC";
		$stmt = $db->prepare($req);
		$stmt->execute();
		$data = $stmt->fetchAll();
		$commandes = array();
		foreach($data as $ligne){
			$commandes[] = self::getCommandeByData($ligne);	
		}		
		return $commandes;
	}
	
	public static function deleteCommande($id){
		/*$db = Zend_Registry::get('database');
		$req = "DELETE FROM commande_adresse WHERE idcommande = :id; ";
		$req .= "DELETE FROM ligne_commande WHERE idcommande = :id; ";
		$req .= "DELETE FROM commandes WHERE idcommande = :id; ";
		$stmt = $db->prepare($req);
		$stmt->bindParam(':id',$id);
		$stmt->execute();*/
		$numCde = $id;
		
		$db = Zend_Registry::get('database');
		$req = "DELETE FROM commande_adresse WHERE idcommande = :id; ";
		$stmt = $db->prepare($req);
		$stmt->bindParam(':id',$numCde);
		$stmt->execute();
		
		$db = Zend_Registry::get('database');
		$req = "DELETE FROM ligne_commande WHERE idcommande = :id; ";
		$stmt = $db->prepare($req);
		$stmt->bindParam(':id',$numCde);
		$stmt->execute();
		
		$db = Zend_Registry::get('database');
		$req = "DELETE FROM commandes WHERE idcommande = :id; ";
		$stmt = $db->prepare($req);
		$stmt->bindParam(':id',$numCde);
		$stmt->execute();
	}
	
	/**
	 * Enregistre les adresses correspondant a une commande
	 *
	 * @param commande $commande
	 * @param adresse $adresse
	 * @param unknown_type $type true = adresse de livraison et false = adresse de facturation
	 * @throw Exception si l'adresse ne peut être enregistré
	 */
	public static function saveAdresseCommande(commande $commande,adresse $adresse,$type){
		$db = Zend_Registry::get('database');
		$req = "INSERT INTO commande_adresse (`idcommande`,`typeadresse`,`raisonsocial`,`nomrue`,`codepostal`,`ville`,`pays`)
				VALUES (?,?,?,?,?,?,?)";
		$stmt = $db->prepare($req);
		$stmt->bindParam(1,$commande->getId());
		$stmt->bindParam(2,$type);
		$stmt->bindParam(3,$adresse->getRaisonSocial());
		$stmt->bindParam(4,$adresse->getNomRue());
		$stmt->bindParam(5,$adresse->getCodePostal());
		$stmt->bindParam(6,$adresse->getVille());
		$stmt->bindParam(7,$adresse->getPays());
		
		$stmt->execute();
		
		if($stmt->rowCount() != 1){
			throw new Exception("Impossible d'enregistrer l'adresse");
		}
		return true;
	}
	
	/**
	 * Sauvegarde une ligne de la commande en base de données
	 *
	 * @param commande $commande
	 * @param LignePanier $lignePanier
	 */
	public static function saveLigneCommande($commande, $lignePanier){
		$db = Zend_Registry::get('database');
		$req = "INSERT INTO `ligne_commande` (`idcommande`,`qteproduit`,`reference`,`genrename`,`groupename`,`designation`,`puhtproduit`)
				VALUES(?,?,?,?,?,?,?);";
		$stmt = $db->prepare($req);
		$stmt->bindParam(1,$commande->getId());
		$stmt->bindParam(2,$lignePanier->getQte());
		$stmt->bindParam(3,$lignePanier->getProduit()->getReference());
		$stmt->bindParam(4,$lignePanier->getProduit()->getGenreName());
		$stmt->bindParam(5,$lignePanier->getProduit()->getGroupeName());
		$stmt->bindParam(6,$lignePanier->getProduit()->getDesignation());
		$stmt->bindParam(7,$lignePanier->getProduit()->getPrixHT());
		
		$stmt->execute();
		if($stmt->rowCount() != 1){
			throw new Exception("Impossible d'enregistrer le contenu de la commande");
		}
		return true;
	}

	public static function getCommandesByClient(client $client){
		$db = Zend_Registry::get('database');
		//$req = "SELECT * FROM commandes WHERE emailclient = :email ORDER BY datecommande DESC";
		$req = "SELECT * FROM commandes WHERE emailclient = :email ORDER BY idcommande DESC";
		$stmt = $db->prepare($req);
		$stmt->bindParam(':email',$client->getEmail());
		$stmt->execute();
		$data = $stmt->fetchAll();
		$commandes = array();
		foreach($data as $ligne){
			$commandes[] = self::getCommandeByData($ligne);	
		}		
		return $commandes;
	}
	
	/**
	 * Récupère une adresse en fonction de la commande et du type d'adresse(true = livrason, false=facturation)
	 * @param commande $c
	 * @param boolean $type
	 * @return adresse
	 */
	public static function getAdresseByCommandeAndType(commande $c,$type = true){
		$req = "SELECT * FROM commande_adresse WHERE idcommande = :id AND typeadresse = :type";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($req);
		$stmt->bindParam(':id',$c->getId());
		$stmt->bindParam(':type',$type);
		$stmt->execute();
		$data = $stmt->fetch();
		$adresse = new adresse();
		$adresse->setRaisonSocial($data['raisonsocial']);
		$adresse->setNomRue($data['nomrue']);
		$adresse->setCodePostal($data['codepostal']);
		$adresse->setVille($data['ville']);
		$adresse->setPays($data['pays']);
		return $adresse;
	}

	/**
	 * Récupère le contenu d'une commande
	 *
	 * @param commande $c
	 * @return panier
	 */
	public static function getPanierByCommande(commande $c){
		$p = new panier();
		$req = "SELECT * FROM ligne_commande WHERE idcommande = :id";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($req);
		$stmt->bindParam(':id',$c->getId());
		$stmt->execute();
		$data = $stmt->fetchAll();
		foreach($data as $ligne){
			$produit = new produit();
			$produit->setReference($ligne['reference']);
			$produit->setGenreName($ligne['genrename']);
			$produit->setGroupeName($ligne['groupename']);
			$produit->setDesignation($ligne['designation']);
			$produit->setPrixHT($ligne['puhtproduit']);
			$p->ajouterProduit($produit,$ligne['qteproduit']);
		}
		return $p;
	}

	public static function getNbCommande($etat){
		$req = "SELECT Count(*) FROM commandes WHERE statutcommande = ?";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($req);
		$stmt->bindParam(1,$etat);
		$stmt->execute();
		return $stmt->fetchColumn(0);	
	}
}

/**
 * Cette classe genère un fichier pdf à partir d'un objet commande
 *
 */
class commandePDF{
	private $_commande;
	public function __construct(commande $c){
		
	}
}