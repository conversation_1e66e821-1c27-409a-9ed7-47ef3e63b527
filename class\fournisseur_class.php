<?php

// ------- CLASS de gestion fournisseur -------

class fournisseur {

// ---------------------- Declaration -------------------------

	private $id;
	private $date_creation;
	private $raisonsocial;
	private $interet;
	private $activite;
	private $secteur;
	private $nom_contact;
	private $commentaire;
	private $telephone;
	private $mobile;
	private $fax;
	private $email;	
	private $web;
	private $adresse; 
	private $cp;
	private $ville;
	private $pays;
	private $tva_intracom;
	private $siret;
	private $ape;
	private $statut;
	private $date_modif;
	
// ---------------------- GET -------------------------

	public function getId() {
		return $this->id;
	}
	public function getDate_creation() {
		return $this->date_creation;
	}
	public function getRaisonsocial() {
		return $this->raisonsocial;
	}
	public function getInteret() {
		return $this->interet;
	}
	public function getActivite() {
		return $this->activite;
	}
	public function getSecteur() {
		return $this->secteur;
	}
	public function getNom_contact() {
		return $this->nom_contact;
	}
	public function getCommentaire() {
		return $this->commentaire;
	}
	public function getTelephone() {
		return $this->telephone;
	}
	public function getMobile() {
		return $this->mobile;
	}
	public function getFax() {
		return $this->fax;
	}
	public function getEmail() {
		return $this->email;
	}
	public function getWeb() {
		return $this->web;
	}
	public function getAdresse() {
		return $this->adresse;
	}
	public function getCp(){
		return $this->cp;
	}
	public function getVille() {
		return $this->ville;
	}
	public function getPays() {
		return $this->pays;
	}
	public function getTva_intracom() {
		return $this->tva_intracom;
	}
	public function getSiret() {
		return $this->siret;
	}
	public function getApe() {
		return $this->ape;
	}
	public function getStatut() {
		return $this->statut;
	}
	public function getDate_modif() {
		return $this->date_modif;
	}
// ----------------------- SET ----------------------

	public function setId($id) {
		$this->id = $id;
	}
	public function setDate_creation($date_creation) {
		$this->date_creation = $date_creation;
	}
	public function setRaisonsocial($raisonsocial) {
		$this->raisonsocial = $raisonsocial;
	}
	public function setInteret($interet) {
		$this->interet = $interet;
	}
	public function setActivite($activite) {
		$this->activite = $activite;
	}
	public function setSecteur($secteur) {
		$this->secteur = $secteur;
	}
	public function setNom_contact($nom_contact) {
		$this->nom_contact = $nom_contact;
	}
	public function setCommentaire($commentaire) {
		$this->commentaire = $commentaire;
	}
	public function setTelephone($telephone) {
		$this->telephone = $telephone;
	}
	public function setMobile($mobile) {
		$this->mobile = $mobile;
	}
	public function setFax($fax) {
		$this->fax = $fax;
	}
	public function setEmail($email) {
		$this->email = $email;
	}
	public function setWeb($web) {
		$this->web = $web;
	}
	public function setAdresse($adresse) {
		$this->adresse = $adresse;
	}
	public function setCp($cp) {
		$this->cp = $cp;
	}
	public function setVille($ville) {
		$this->ville = $ville;
	}
	public function setPays($pays) {
		$this->pays = $pays;
	}
	public function setTva_intracom($tva_intracom) {
		$this->tva_intracom = $tva_intracom;
	}
	public function setSiret($siret) {
		$this->siret = $siret;
	}
	public function setApe($ape) {
		$this->ape = $ape;
	}
	public function setStatut($statut) {
		$this->statut = $statut;
	}
	public function setDate_modif($date_modif) {
		$this->date_modif = $date_modif;
	} 
	
	public function __construct($id = 0) {
		if($id>0)
		{
			//on a un identifiant, donc on charge le produit à partir de la bdd
			$temp = fournisseurDB::getFournisseurById($id);
			$this->setId($temp->getId());
			$this->setDate_creation($temp->getDate_creation());
			$this->setRaisonsocial($temp->getReference());
			$this->setInteret($temp->getInteret());
			$this->setActivite($temp->getActivite());
			$this->setSecteur($temp->getSecteur());
			$this->setNom_contact($temp->getNom_contact());
			$this->setCommentaire($temp->getCommentaire());
			$this->setTelephone($temp->getTelephone());
			$this->setMobile($temp->getMobile());
			$this->setFax($temp->getFax());
			$this->setEmail($temp->getEmail());
			$this->setWeb($temp->getWeb());
			$this->setAdresse($temp->getAdresse());
			$this->setCp($temp->getCp());
			$this->setVille($temp->getVille());
			$this->setPays($temp->getPays());
			$this->setTva_intracom($temp->getTva_intracom());
			$this->setSiret($temp->getSiret());
			$this->setApe($temp->getApe());
			$this->setStatut($temp->getStatut());
			$this->setDate_modif($temp->getDate_modif());


		}
			
	}

}
/**
 * classe d'accés à la base de données...
 *
 */
class fournisseurDB {
	/**
	 * renvoi un fournisseur à partir de son identifiant
	 * 
	 * @param int $id Identifiant du produit dans la bdd
	 */
	static function getFournisseurById($id) {
		
		$sql = "SELECT * FROM fournisseur WHERE id=:id;";

		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		
		$stmt->bindParam(':id',$id);
		
		$stmt->execute();
		$data = $stmt->fetch();

		if(count($data) != 0){
			
			$four = new Fournisseur();

			$four->setId($data['id']);
			$four->setDate_creation($data['date_creation']);
			$four->setRaisonsocial($data['raisonsocial']);
			$four->setInteret($data['interet']);
			$four->setActivite($data['activite']);
			$four->setSecteur($data['secteur']);
			$four->setNom_contact($data['nom_contact']);
			$four->setCommentaire($data['commentaire']);
			$four->setTelephone($data['telephone']);
			$four->setMobile($data['mobile']);
			$four->setFax($data['fax']);
			$four->setEmail($data['email']);
			$four->setWeb($data['web']);
			$four->setAdresse($data['adresse']);
			$four->setCp($data['cp']);
			$four->setVille($data['ville']);
			$four->setPays($data['pays']);
			$four->setTva_intracom($data['tva_intracom']);
			$four->setSiret($data['siret']);
			$four->setApe($data['ape']);
			$four->setStatut($data['statut']);
			$four->setDate_modif($data['date_modif']);
			
			return $four;
		}
		
		return null;
	}

	/**
	 * Lit toute la base de données des fournisseurs
	 * @return array Renvoi une liste de catégories contenant des sous-catégorires, contenant des fournisseurs
	 */
	public static function readFournisseurFromDataBase() {
		$fours = array();
				
		$sql = "SELECT * FROM fournisseur ORDER BY date_creation DESC LIMIT 20";
		
		if(isset($_GET['sort'])){
			$sql .= "SELECT * FROM fournisseur ORDER BY `".$_GET['sort']."`";
			if(isset($_GET['sens'])){
				$sql .= " ".$_GET['sens'];
			}
		}
		if(isset($_GET['nolimit'])){
			$sql = "SELECT * FROM fournisseur ORDER BY date_creation DESC";
		
		}
		if(isset($_GET['valuetosearch'])){
			$sql = "SELECT * FROM fournisseur ORDER BY raisonsocial, interet, activite";
			
		} 
		
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		

		$stmt->execute();
		$data = $stmt->fetchAll();
		
		foreach($data as $ligne) {
			
			$four = new Fournisseur();

			$four->setId($ligne['id']);
			$four->setDate_creation($ligne['date_creation']);
			$four->setRaisonsocial($ligne['raisonsocial']);
			$four->setInteret($ligne['interet']);
			$four->setActivite($ligne['activite']);
			$four->setSecteur($ligne['secteur']);
			$four->setNom_contact($ligne['nom_contact']);
			$four->setCommentaire($ligne['commentaire']);
			$four->setTelephone($ligne['telephone']);
			$four->setMobile($ligne['mobile']);
			$four->setFax($ligne['fax']);
			$four->setEmail($ligne['email']);
			$four->setWeb($ligne['web']);
			$four->setAdresse($ligne['adresse']);
			$four->setCp($ligne['cp']);
			$four->setVille($ligne['ville']);
			$four->setPays($ligne['pays']);
			$four->setTva_intracom($ligne['tva_intracom']);
			$four->setSiret($ligne['siret']);
			$four->setApe($ligne['ape']);
			$four->setStatut($ligne['statut']);
			$four->setDate_modif($ligne['date_modif']);
			
			array_push($fours, $four);
			
		}
		
		return $fours;

	}
	
	/**
	 * supprime un produit de la base de donnée
	 * @param Produit $prod Produit à supprimer
	 * @return Renvoi vrai si suppression réussi, sinon faux
	 */
	function removeFournisseur(Fournisseur $four) {
		
		$sql ="DELETE FROM fournisseur WHERE id=:id;";
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		$stmt->bindParam(':id',$four->getId());
		$stmt->execute();	
		if ($stmt->rowCount() == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
/**
	 * modifie un produit de la base de donnée
	 * @param Produit $prod Produit à modifier
	 * @return Renvoi vrai si modification réussi, sinon faux
	 */
	static function modifFournisseur(Fournisseur $four) {
		
		$sql ="UPDATE fournisseur 
		
		SET date_creation=:date_creation, 
		raisonsocial=:raisonsocial, 
		interet=:interet, 
		activite = :activite,
		secteur=:secteur, 
		nom_contact=:nom_contact, 
		commentaire=:commentaire, 
		telephone=:telephone, 
		mobile=:mobile, 
		fax=:fax, 
		email = :email,
		web=:web, 
		adresse=:adresse, 
		cp=:cp, 
		ville = :ville,
		pays=:pays, 
		tva_intracom=:tva_intracom, 
		siret=:siret, 
		ape = :ape,
		statut = :statut,
		date_modif = :date_modif
		
		WHERE id=:id;";
		
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		
		$date_creation = $four->getDate_creation();
		$raisonsocial = $four->getRaisonsocial();
		$interet = $four->getInteret();
		$activite = $four->getActivite();
		$secteur = $four->getSecteur();
		$nom_contact = $four->getNom_contact();
		$commentaire = $four->getCommentaire();
		$telephone = $four->getTelephone();
		$mobile = $four->getMobile();
		$fax = $four->getFax();
		$email = $four->getEmail();
		$web = $four->getWeb();
		$adresse = $four->getAdresse();
		$cp = $four->getCp();
		$ville = $four->getVille();
		$pays = $four->getPays();
		$tva_intracom = $four->getTva_intracom();
		$siret = $four->getSiret();
		$ape = $four->getApe();
		$statut = $four->getStatut();
		$date_modif = $four->getDate_modif();
	
		$stmt->bindParam(':date_creation', $date_creation);
		$stmt->bindParam(':raisonsocial', $raisonsocial);
		$stmt->bindParam(':interet', $interet);
		$stmt->bindParam(':activite', $activite);
		$stmt->bindParam(':secteur', $secteur);
		$stmt->bindParam(':nom_contact', $nom_contact);
		$stmt->bindParam(':commentaire', $commentaire);
		$stmt->bindParam(':telephone', $telephone);
		$stmt->bindParam(':mobile', $mobile);
		$stmt->bindParam(':fax', $fax);
		$stmt->bindParam(':email', $email);
		$stmt->bindParam(':web', $web);
		$stmt->bindParam(':adresse', $adresse);
		$stmt->bindParam(':cp', $cp);
		$stmt->bindParam(':ville', $ville);
		$stmt->bindParam(':pays', $pays);
		$stmt->bindParam(':tva_intracom', $tva_intracom);
		$stmt->bindParam(':siret', $siret);
		$stmt->bindParam(':ape', $ape);
		$stmt->bindParam(':statut', $statut);
		$stmt->bindParam(':date_modif', $date_modif);
		
		$id = $four->getId();
		$stmt->bindParam(':id',$id);

		$stmt->execute();	
		
		if ($stmt->rowCount() == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
	
/**
	 * ajout un Fournisseur de la base de donnée
	 * @param Fournisseur $four Fournisseur à ajouter
	 * @return Renvoi vrai si modification réussi, sinon faux
	 */
	static function ajoutFournisseur(Fournisseur $four) {
		
		$sql ="INSERT INTO fournisseur
		(date_creation, 
		raisonsocial, 
		interet, 
		activite,
		secteur, 
		nom_contact,
		commentaire,
		telephone,
		mobile,
		fax, 
		email, 
		web,
		adresse, 
		cp, 
		ville, 
		pays,
		tva_intracom, 
		siret, 
		ape,
		statut,
		date_modif)

		VALUES(:date_creation,
		:raisonsocial,
		:interet,
		:activite,
		:secteur,
		:nom_contact,
		:commentaire,
		:telephone,
		:mobile,
		:fax,
		:email,
		:web,
		:adresse,
		:cp,
		:ville,
		:pays,
		:tva_intracom,
		:siret,
		:ape,
		:statut,
		:date_modif)";

		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		
		$date_creation = $four->getDate_creation();
		$raisonsocial = $four->getRaisonsocial();
		$interet = $four->getInteret();
		$activite = $four->getActivite();
		$secteur = $four->getSecteur();
		$nom_contact = $four->getNom_contact();
		$commentaire = $four->getCommentaire();
		$telephone = $four->getTelephone();
		$mobile = $four->getMobile();
		$fax = $four->getFax();
		$email = $four->getEmail();
		$web = $four->getWeb();
		$adresse = $four->getAdresse();
		$cp = $four->getCp();
		$ville = $four->getVille();
		$pays = $four->getPays();
		$tva_intracom = $four->getTva_intracom();
		$siret = $four->getSiret();
		$ape = $four->getApe();
		$statut = $four->getStatut();
		$date_modif = $four->getDate_modif();
	
		$stmt->bindParam(':date_creation', $date_creation);
		$stmt->bindParam(':raisonsocial', $raisonsocial);
		$stmt->bindParam(':interet', $interet);
		$stmt->bindParam(':activite', $activite);
		$stmt->bindParam(':secteur', $secteur);
		$stmt->bindParam(':nom_contact', $nom_contact);
		$stmt->bindParam(':commentaire', $commentaire);
		$stmt->bindParam(':telephone', $telephone);
		$stmt->bindParam(':mobile', $mobile);
		$stmt->bindParam(':fax', $fax);
		$stmt->bindParam(':email', $email);
		$stmt->bindParam(':web', $web);
		$stmt->bindParam(':adresse', $adresse);
		$stmt->bindParam(':cp', $cp);
		$stmt->bindParam(':ville', $ville);
		$stmt->bindParam(':pays', $pays);
		$stmt->bindParam(':tva_intracom', $tva_intracom);
		$stmt->bindParam(':siret', $siret);
		$stmt->bindParam(':ape', $ape);
		$stmt->bindParam(':statut', $statut);
		$stmt->bindParam(':date_modif', $date_modif);

		$stmt->execute();	
		
		if ($stmt->rowCount() == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}

}
?>