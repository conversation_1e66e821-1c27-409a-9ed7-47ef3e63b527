<?php
	require_once dirname(__FILE__).'/panier_class.php';
/**
 * classe qui représente un internaute
 *
 */
class internaute {
	/**
	 * attribut qui représente le panier de l'internaute
	 *
	 * @var Panier
	 */
	private $panier;
	/**
	 * indique si l'internaute est un admin...
	 *
	 * @var booleen
	 */
	public $isAdmin;
	/**
	 * indique si l'internaute est un client authentifié...
	 *
	 * @var booleen
	 */
	public $isAuthenticated;
	/**
	 * retourne le panier de l'internaute...
	 *
	 * @return Panier
	 */
	public function getPanier() {
		return $this->panier;
	}

	/**
	 * modifie le panier de l'internaute...
	 *
	 * @param Panier $panier
	 */
	public function setPanier($panier) {
		$this->panier = $panier;
	}
		
	public function __construct() {
		$this->panier = new panier();
		$this->isAdmin = 0;
		$this->isAuthenticated = false;
	}
}