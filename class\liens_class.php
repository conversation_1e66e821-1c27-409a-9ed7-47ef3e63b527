<?php

/**
 * classe liens qui permet de gérer les liens
 *
 */
 class liens 
{
	/**
	* id liens...
	*
	*@var int
	*/
	private $idliens;
	/**
	* nomliens...
	*
	* @var varchar
	*/
	private $nomliens;
	/**
	* adresseliens...
	*
	* @var varchar
	*/
	private $adresseliens;
	/**
	* telliens...
	*
	* @var varchar
	*/
	private $telliens;
	/**
	* faxliens...
	*
	* @var varchar
	*/
	private $faxliens;
	/**
	* webliens...
	*
	* @var varchar
	*/
	private $webliens;
	/**
	* emailliens...
	*
	* @var varchar
	*/
	private $emailliens;
	/**
	* commentaires...
	*
	* @var text
	*/
	private $commentaires;
	
	/** retourne l'identifiant du lien
	*
	*
	* @return int
	*/
	public function getIdLiens () {
		return $this->idliens;
	}
	/**
	 * retourne le nom du lien...
	 *
	 * @return varchar
	 */
	public function getNomLiens() {
		return $this->nomliens;
	}
	/**
	 * retourne l'adresse du lien...
	 *
	 * @return varchar
	 */
	public function getAdresseLiens() {
		return $this->adresseliens;
	}
	/**
	 * retourne le téléphone du lien...
	 *
	 * @return varchar
	 */
	public function getTelLiens() {
		return $this->telliens;
	}
	/**
	 * retourne le fax du lien...
	 *
	 * @return varchar
	 */
	public function getFaxLiens() {
		return $this->faxliens;
	}
	/**
	 * retourne l'adresse du site du lien...
	 *
	 * @return varchar
	 */
	public function getWebLiens() {
		return $this->webliens;
	}
	/**
	 * retourne l'email du liens...
	 *
	 * @return varchar
	 */
	public function getEmailLiens() {
		return $this->emailliens;
	}
	/**
	 * retourne les commentaires sur le liens...
	 *
	 * @return varchar
	 */
	public function getCommentaires() {
		return $this->commentaires;
	}
	/**
	 * modifie l'identifiant  du liens...
	 *
	 * @param int $idliens
	 */
	public function setIdLiens($idliens) {
		$this->idliens = $idliens;
	}
	/**
	 * modifie le nom du liens...
	 *
	 * @param varchar $nomliens
	 */
	public function setNomLiens($nomliens) {
		$this->nomliens = $nomliens;
	}
	/**
	 * modifie l'adresse du liens...
	 *
	 * @param varchar $adresseliens
	 */
	public function setAdresseLiens($adresseliens) {
		$this->adresseliens = $adresseliens;
	}
	/**
	 * modifie le téléphone du liens...
	 *
	 * @param varchar $telliens
	 */
	public function setTelLiens($telliens) {
		$this->telliens = $telliens;
	}
	/**
	 * modifie le fax du liens...
	 *
	 * @param varchar $faxliens
	 */
	public function setFaxLiens($faxliens) {
		$this->faxliens = $faxliens;
	}
	/**
	 * modifie l'adresse du site web du liens...
	 *
	 * @param varchar $webliens
	 */
	public function setWebLiens($webliens) {
		$this->webliens = $webliens;
	}
	/**
	 * modifie l'email du liens...
	 *
	 * @param varchar $emailliens
	 */
	public function setEmailLiens($emailliens) {
		$this->emailliens = $emailliens;
	}
	/**
	 * modifie les commentaires du liens...
	 *
	 * @param varchar $commentaires
	 */
	public function setCommentaires($commentaires) {
		$this->commentaires = $commentaires;
	}
	/**
	 * efface l'instance de liens...
	 */
	public function clear() 
	{
		$this->idliens = "";
		$this->nomliens = "";
		$this->adresseliens = "";
		$this->telliens = "";
		$this->faxliens = "";
		$this->webliens = "";
		$this->emailliens = "";
		$this->commentaires = "";
	}
	/**
	 * construit une instance de liens...
	 */
	public function __construct($nomliens = '') 
	{
		$this->clear();
		if($nomliens !== '')
		{
			$temp = liensDB::getLiensByNom($nomliens);
			if($temp != null)
			{
				$this->setIdLiens($temp->getIdLiens());
				$this->setNomLiens($temp->getNomLiens());
				$this->setAdresseLiens($temp->getAdresseLiens());
				$this->setTelLiens($temp->getTelLiens());
				$this->setFaxLiens($temp->getFaxLiens);
				$this->setWebLiens($temp->getWebLiens());
				$this->setEmailLiens($temp->getEmailLiens());
				$this->setCommentaires($temp->getCommentaires());
			}
		}
	}
}
/**
 * classe d'accés à la base de données...
 *
 */
class liensDB 
{
	/**
	 * fonction qui retourne un objet liens à l'aide de son identifiant : le nom...
	 *
	 * @param string $nomliens
	 * @return liens
	 */
	public static function getLiensById($idliens) 
	{
		$sql = "SELECT * FROM liens WHERE idliens=?;";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->bindParam(1,$idliens);
		$stmt->execute();
		$data = $stmt->fetch();
		if($data != null){
			$liens = new liens;
			$liens->setIdLiens($data[0]);
			$liens->setNomLiens($data[1]);
			$liens->setAdresseLiens($data[2]);
			$liens->setTelLiens($data[3]);
			$liens->setFaxLiens($data[4]);
			$liens->setWebLiens($data[5]);
			$liens->setEmailLiens($data[6]);
			$liens->setCommentaires($data[7]);
			return $liens;
		}
		return null;
	}
	
	/**
	 * fonction qui retourne tous les liens (sous forme de tableau)
	 *
	 * @return tableaux de liens
	 */
	public static function getAllLiens () 
	{
		$sql = "SELECT * FROM liens";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->execute();
		$data = $stmt->fetchAll();
		// On test si la fonction a bien fonctionné
		if($data != null)
		{
			$lienslist = array();
			// Création des adresses
			foreach($data as $ligne)
			{
				$liens = new liens();
				$liens->setIdLiens($ligne['idliens']);
				$liens->setNomLiens($ligne['nomliens']);
				$liens->setAdresseLiens($ligne['adresseliens']);
				$liens->setTelLiens($ligne['telliens']);
				$liens->setFaxLiens($ligne['faxliens']);
				$liens->setWebLiens($ligne['webliens']);
				$liens->setEmailLiens($ligne['emailliens']);
				$liens->setCommentaires($ligne['commentaires']);
				array_push($lienslist, $liens);
			}
			
			return $lienslist;
		}
		return null;
	}
	
	/**
	 * fonction qui enregistre dans la base de données un nouveau lien
	 *
	 * @return bool
	 */
	public function saveNew($liens) 
	{
		$sql = "INSERT INTO liens (nomliens, adresseliens, telliens, faxliens, webliens, emailliens, commentaires) VALUES (:nom, :adresse, :tel, :fax, :web, :email, :com);";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->bindParam(':nom', $liens->getNomLiens());
		$stmt->bindParam(':adresse', $liens->getAdresseLiens());
		$stmt->bindParam(':tel', $liens->getTelLiens());
		$stmt->bindParam(':fax', $liens->getFaxLiens());
		$stmt->bindParam(':web', $liens->getWebLiens());
		$stmt->bindParam(':email', $liens->getEmailLiens());
		$stmt->bindParam(':com', $liens->getCommentaires());
		$stmt->execute();
		if ($stmt->rowCount() == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
	
	/**
	 * fonction qui enregistre dans la base de données un lien
	 *
	 * @return bool
	 */
	public function save($liens) 
	{
		$sql = "UPDATE liens SET nomliens=:nom, adresseliens=:adresse, telliens=:tel, faxliens=:fax, webliens=:web, emailliens=:email, commentaires=:com WHERE idliens=:id;";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->bindParam(':id', $liens->getIdLiens());
		$stmt->bindParam(':nom', $liens->getNomLiens());
		$stmt->bindParam(':adresse', $liens->getAdresseLiens());
		$stmt->bindParam(':tel', $liens->getTelLiens());
		$stmt->bindParam(':fax', $liens->getFaxLiens());
		$stmt->bindParam(':web', $liens->getWebLiens());
		$stmt->bindParam(':email', $liens->getEmailLiens());
		$stmt->bindParam(':com', $liens->getCommentaires());
		$stmt->execute();
		if ($stmt->rowCount() == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}

	
	/**
	 * fonction qui supprime un lien en fonction de son id
	 *
	 * @return bool
	 */
	public function deleteLiensById($id) 
	{
		$sql = "DELETE FROM liens WHERE idliens=:id";
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->bindParam(':id', $id);
		$stmt->execute();
		if ($stmt->rowCount() == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
}

?>