<?php
/**
 * classe qui représente une ligne du produit
 *
 */
class LignePanier {
	/**
	 * identifiant du produit se trouvant dans la lignePanier...
	 *
	 * @var Produit
	 */
	private $produit;
	/**
	 * prix unitaire HT du produit...
	 *
	 * @var decimal
	 */
	private $prixUHT;
	/**
	 * quantité du produit...
	 *
	 * @var unknown_type
	 */
	private $qte;
	
	/**
	 * retourne l'identifiant du produit...
	 *
	 * @return Produit
	 */
	public function getProduit() {
		return $this->produit;
	}
	/**
	 * retourne le prix ht du produit...
	 *
	 * @return decimal
	 */
	public function getPrixUHT() {
		return $this->prixUHT;
	}
	/**
	 * retourne la quantité produit ...
	 *
	 * @return int
	 */
	public function getQte() {
		return $this->qte;
	}
	/**
	 * modifie l'attribut produit
	 *
	 * @param int $produit
	 */
	public function setProduit(Produit $produit) {
		$this->produit = $produit;
	}
	/**
	 * modifie l'attribut prixUHT
	 *
	 * @param decimal $prixUHT
	 */
	public function setPrixUHT($prixUHT) {
		$this->prixUHT = $prixUHT;
	}
	/**
	 * modifier la quantité commandé du produit...
	 *
	 * @param int $qte
	 */
	public function setQte($qte) {
		$this->qte = $qte;
	}
}