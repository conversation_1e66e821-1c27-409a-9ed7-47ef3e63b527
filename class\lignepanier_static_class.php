<?php

class lignepanier_static {

	private $reference;
	private $genregroupe;
	private $designation;
	private $qte;
	private $puht;
	private $somme;
	
	public function getReference() {
		return $this->reference;
	}
	public function getGenregroupe() {
		return $this->genregroupe;
	}
	public function getDesignation() {
		return $this->designation;
	}
	public function getQte() {
		return $this->qte;
	}
	public function getPuHT() {
		return $this->puht;
	}
	public function getSomme() {
		return $this->somme;
	}
	
	public function setReference($reference) {
		$this->reference = $reference;
	}
	public function setGenregroupe($genregroupe) {
		$this->genregroupe = $genregroupe;
	}
	public function setDesignation($designation) {
		$this->designation = $designation;
	}
	public function setQte($qte) {
		$this->qte = $qte;
	}
	public function setPuHT($puht) {
		$this->puht = $puht;
	}
	public function setSomme($somme) {
		$this->somme = $somme;
	}
}

?>