<?php
class Pagination {
	
	private $nb_lignes_par_page;
	private $nb_liens_par_page;
	private $count;
	private $numpage;

	
	function __construct($total, $nb_lignes_par_page=30, $nb_liens_par_page=20, $numpage=1) {
		
		//$nb_lignes_par_page = 50;  // BFO
		
		$this->nb_lignes_par_page = $nb_lignes_par_page;
		$this->nb_liens_par_page = $nb_liens_par_page;
		$this->count=$total;
		$this->numpage = $numpage;

	}
	
	
	public function getStartPos() {
		return ($this->numpage-1)*$this->nb_lignes_par_page;
	}
	public function getNbLignes() {
		return $this->nb_lignes_par_page;
	}
	public function getNumPage() {
		return $this->numpage;
	}
	public function getNbPages()
	{
		return $nbpage = (int)($this->count / $this->nb_lignes_par_page) + 1;
	}
	
public function incremente() {
		$this->count++;
	}
	
	public function getCount() {
		return $this->count;
	}
	
	public function isTimeToRender() {
		return ($this->count<= $this->numpage*$this->nb_lignes_par_page && $this->count>($this->numpage-1)*$this->nb_lignes_par_page);
	}
	public function setCount($total) {
		$this->count = $total;
	}
	
	public function printPagesLinks($lien, $fullurl) {
		
		//si on doit afficher l'url complète (avec les nom des variables)
		if($fullurl == true)
			$separator = '&numpage=';
		else
			$separator = '/';
			
		$translate = Zend_Registry::get('translate');
		$nbpage = (int)($this->count / $this->nb_lignes_par_page) + 1;
	
	
		if($this->numpage > 1 ){
						
			echo '<li><a href="',$lien,$separator,($this->numpage-1),'" class="prev fa fa-arrow-left"></a></li>';
			
		} else {
			
			echo '<li><a href="',$lien,$separator,'1','" class="prev fa fa-arrow-left"></a></li>';
			
		}		
		
		if($nbpage > $this->nb_liens_par_page)
		{
			//définition des bornes min et max pour l'affichage des liens avec les numéros de pages
			if($this->numpage<=$this->nb_liens_par_page) {
				$min=1;
				$max=$this->nb_liens_par_page;
			}
			if($this->numpage>$this->nb_liens_par_page && $this->numpage<$nbpage-9) {
				$min=$this->numpage-($this->nb_liens_par_page/2);
				$max=$this->numpage+($this->nb_liens_par_page/2)-1;
			}
			if($this->numpage>=$nbpage-$this->nb_liens_par_page) {
				$min=$nbpage-$this->nb_liens_par_page;
				$max=$nbpage;
			}
				
			for($i=$min;$i<=$max;$i++) {
					echo '<a style="color:white; text-align:center;"  href="',$lien,$separator,$i,'"';
					if($i==$this->numpage){
						echo ' class="active" >'.$i;
					}else{
						echo '>'.$i;
					}	
					echo '</a>';
				}
				
		} else {
			for($i=1;$i<=$nbpage;$i++) {
				echo '<a style="color:white; text-align:center;" href="',$lien,$separator,($i),'"';
				if($i==$this->numpage){
					echo ' class="active" >'.$i;
				}else{
					echo '>'.$i;
				}	
				echo '</a> ';
			}
		}
		
		$pagetotalnb = $this->getNbPages();

		if($this->numpage < $pagetotalnb ){
						
			echo '<li><a href="',$lien,$separator,($this->numpage+1),'" class="next fa fa-arrow-right"></a></li>';
			
		} else {
			
			echo '<li><a href="',$lien,$separator,($this->numpage),'" class="next fa fa-arrow-right"></a></li>';
			
		}	
	
	}

	
	//separator : le séprateur entre le lien et le numéro de page ex : &numpage=
	/*
	public function printPagesLinks($lien, $fullurl) {
		
		//si on doit afficher l'url complète (avec les nom des variables)
		if($fullurl == true)
			$separator = '&numpage=';
		else
			$separator = '/';
			
		$translate = Zend_Registry::get('translate');
		$nbpage = (int)($this->count / $this->nb_lignes_par_page) + 1;
		
		if($nbpage > $this->nb_liens_par_page)
		{
			if($this->numpage > $this->nb_liens_par_page)
			{
				echo '<a href="',$lien,$separator,'1','">< ',$translate->_('Première'),'</a>&nbsp;';
				echo '<a href="',$lien,$separator,($this->numpage-1),'">< ',$translate->_('Précédente'),'</a>&nbsp;';
			}
			//définition des bornes min et max pour l'affichage des liens avec les numéros de pages
			if($this->numpage<=$this->nb_liens_par_page) {
				$min=1;
				$max=$this->nb_liens_par_page;
			}
			if($this->numpage>$this->nb_liens_par_page && $this->numpage<$nbpage-9) {
				$min=$this->numpage-($this->nb_liens_par_page/2);
				$max=$this->numpage+($this->nb_liens_par_page/2)-1;
			}
			if($this->numpage>=$nbpage-$this->nb_liens_par_page) {
				$min=$nbpage-$this->nb_liens_par_page;
				$max=$nbpage;
			}
				
			for($i=$min;$i<=$max;$i++) {
					echo '<a href="',$lien,$separator,$i,'"';
					if($i==$this->numpage)
						//echo '<font size=4 color="red"> ',$i,'</font>';
						echo ' class="active" >';
					else
						echo $i;
					echo '</a>';
				}
				
			
			if($this->numpage < $nbpage)
				echo '&nbsp;<a href="',$lien,$separator,($this->numpage+1),'">',$translate->_('Suivante'),' ></a>';
				echo '&nbsp;<a href="',$lien,$separator,round($this->count/$this->nb_lignes_par_page),'">',$translate->_('Dernière'),' ></a>';
		}
		else
		{
			for($i=1;$i<=$nbpage;$i++) {
				echo '<a href="',$lien,$separator,($i),'">';
				if($i==$this->numpage)
					echo '<font size=4 color="red">',$i,'</font>';
				else
					echo $i;
				echo '</a> ';
			}
		}
	}
	
	*/
	
	
}
?>