<?php
require_once dirname(__FILE__).'/lignePanier_class.php';
require_once dirname(__FILE__).'/produit_class.php';
class panier {
	/**
	 * tableau de ligne du panier
	 *
	 * @var lignePanier
	 */
	private $lignePanier;
	/**
	 * total HT du panier
	 *
	 * @var decimal
	 */
	private $totalHT;
	/**
	 * Taux de TVA lors de la commande du panier
	 * Le taux est fixé dans le panier, car même si le taux change, le panier ne doit pas changer
	 *
	 * @var decimal
	 */
	private $tauxTVA;
	/**
	 * frais de ports associés au panier
	 *@var decimal
	 */
	private $fraisdeport;
	
	public function __construct(){
		$this->lignePanier = array();
		$this->totalHT = 0.0;
		$this->fraisdeport = 0.0;
		
		$sql = "SELECT * FROM parametres WHERE cle = 'tva';";
	
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($sql);
		$stmt->execute();
		$data = $stmt->fetch();
		
		if(count($data) != 0){
			$this->tauxTVA = $data['valeur'];
		} else {
			$this->tauxTVA = 0.0;
		}
	
	}
	
	/**
	 * Calcule la montant de la TVA pour le prix HT donné
	 * 
	 * @return double Renvoi le résultat du calcul
	 */
	public function getMontantTVA() 
	{	
		return ($this->tauxTVA*$this->getTotalHT()/100.0);
		//return ($this->tauxTVA*($this->getTotalHT()+$this->getFraisDePort())/100.0);			
	}

	/**
	 * retourne la ligne du panier
	 *
	 * @return array(int => lignePanier)
	 */
	public function getLignePanier() {
		return $this->lignePanier;
	}
	/**
	 * retourne le totaltHT du panier
	 *
	 * @return decimal
	 */
	public function getTotalHT() {
		return $this->totalHT;
	}
/**
	 * retourne les frais de port du panier
	 *
	 * @return decimal
	 */
	public function getFraisDePort() {
		return $this->fraisdeport;
	}
/**
	 * définit les frais de port du panier
	 *
	 * @var decimal
	 */
	public function setFraisDePort($port) {
		$this->fraisdeport = $port;
	}

/**
	 * retourne le total TTC du panier
	 *
	 * @return decimal
	 */
	public function getTotalTTC() {
		return $this->getTotalHT()+$this->getMontantTVA();//+$this->getFraisDePort();
	}
	
	/**
	 * rempli l'attribut lignePanier
	 *
	 * @param LigneProduit[] $lignePanier
	 */
	public function setLignePanier($lignePanier) {
		$this->lignePanier = $lignePanier;
	}
	/**
	 * rempli l'attribut totalHT du panier
	 *
	 * @param decimal $totalHT
	 */
	public function setTotalHT($totalHT) {
		$this->totalHT = $totalHT;
	}
/**
	 * function qui ajoute un produit
	 *
	 * @param $p Produit le produit à ajouter
	 * @param $nb integer Nombre de produits à ajouter
	 */
	public function ajouterProduit(Produit $p, $nb) {
		$add = false;
		if(count($this->lignePanier) > 0){
			foreach($this->lignePanier as $ligneP){
				if($ligneP->getProduit() == $p){
					// Le produit est déja dans le panier, on augmente la quantité
					$ligneP->setQte($ligneP->getQte()+$nb);
					$add = true;
					break;
				}
			}
		}
		if($add == false){
			// le produit n'a pas été ajouter dans le panier
			$ligneP = new LignePanier();
			$ligneP->setQte($nb);
			$ligneP->setProduit($p);
			$this->lignePanier[] = $ligneP;
		}
		if($p->getPrixHT()>0)
			$this->totalHT += $p->getPrixHT()*$nb;
	}
	
	public function ajouterProduitPro(Produit $p, $nb, $r) {
		$add = false;
		if(count($this->lignePanier) > 0){
			foreach($this->lignePanier as $ligneP){
				if($ligneP->getProduit() == $p){
					// Le produit est déja dans le panier, on augmente la quantité
					$ligneP->setQte($ligneP->getQte()+$nb);
					$add = true;
					break;
				}
			}
		}
		if($add == false){
			// le produit n'a pas été ajouter dans le panier
			$ligneP = new LignePanier();
			$ligneP->setQte($nb);
			$ligneP->setProduit($p);
			$this->lignePanier[] = $ligneP;
		}
		if($p->getPrixHT()>0)
			$this->totalHT += (($p->getPrixHT()-($p->getPrixHT()*($r/100)))*$nb);
			//$this->totalHT += $p->getPrixHT()*$nb;
	}
	/**
	 * function qui supprimer un produit
	 *
	 */
	public function supprimerProduit(Produit $p) {
		//On parcourt l'ensemble des lignes produit, jusqu'a que l'on trouve le produit, puis on le supprime
		foreach($this->lignePanier as $key => $lignP){
			if($lignP->getProduit() == $p){
				/*
				if($p->getPrixHT()>0) {
					$this->totalHT -= $p->getPrixHT()*$lignP->getQte();
				}
				*/
				unset($this->lignePanier[$key]);
				break;
			}
		}
	}
	
	public function recalculer(){
		$this->totalHT = 0.0;
		foreach($this->lignePanier as $lignP){
			if($lignP->getProduit()->getPrixHT()>0)
				$this->totalHT += $lignP->getProduit()->getPrixHT()*$lignP->getQte();
		}
	}
	
	public function recalculerPro($emailclient){
		$this->totalHT = 0.0;
		foreach($this->lignePanier as $lignP){
		$lignP->getProduit();
		$q = $lignP->getProduit();
		$PrixProHT = ProduitDB::getPrixPro(strtolower($q->getPromotion()), strtolower($q->getQualite()), $q->getPrixHT(), $emailclient);
			if($PrixProHT>0){
				if($q->getGenreName() == "JEEP" || $q->getGenreName() == "DODGE" || $q->getGenreName() == "GMC" || $q->getGenreName() == "RENAULT"){
					$this->totalHT += ($q->getPrixHT()-$PrixProHT)*$lignP->getQte() ;
				} else {
					$this->totalHT += $q->getPrixHT()*$lignP->getQte() ;
				}
			} else {
				$this->totalHT += $q->getPrixHT()*$lignP->getQte() ;
			}
		}
	}

	public function setTauxTVA($txTVA){
		$this->tauxTVA = $txTVA;
	}
	
	public function getTauxTVA(){
		return $this->tauxTVA;
	}

	
	/**
	 * Renvoie si le contenu du panier est sur devis
	 * @return unknown
	 */
	public function isSurDevis(){
		$count = count($this->lignePanier);
		foreach($this->lignePanier as $ligne)
			if($ligne->getProduit()->getPrixHT() < 0)
				return true;
		return false;
	}
}