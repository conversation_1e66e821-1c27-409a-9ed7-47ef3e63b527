<?php

require_once 'lignepanier_static_class.php';

class panier_static {

	private $lignePanier;
	
	/**
	 * retourne la ligne du panier
	 *
	 * @return array(int => lignePanier)
	 */
	public function getLignePanier() {
		return $this->lignePanier;
	}
	
	/**
	 * rempli l'attribut lignePanier
	 *
	 * @param LigneProduit[] $lignePanier
	 */
	public function setLignePanier($lignePanier) {
		$this->lignePanier = $lignePanier;
	}

	/**
	 * function qui ajoute un produit
	 *
	 * @param $p Produit le produit Ó ajouter
	 * @param $nb integer Nombre de produits Ó ajouter
	 */
	public function ajouterProduit(lignepanier_static $ls) {
		$add = false;
		/*if(count($this->lignePanier) > 0){
			foreach($this->lignePanier as $ligneP){
				if($ligneP->getProduit() == $p){
					// Le produit est dÚja dans le panier, on augmente la quantitÚ
					$ligneP->setQte($ligneP->getQte()+$nb);
					$add = true;
					break;
				}
			}
		}*/
		if($add == false){
			// le produit n'a pas ÚtÚ ajouter dans le panier
			$this->lignePanier[] = $ls;
		}
	}
	
	public function supprimerProduit(Produit $p) {
		//On parcourt l'ensemble des lignes produit, jusqu'a que l'on trouve le produit, puis on le supprime
		/*foreach($this->lignePanier as $key => $lignP){
			if($lignP->getProduit() == $p){
				
				if($p->getPrixHT()>0) {
					$this->totalHT -= $p->getPrixHT()*$lignP->getQte();
				}
				unset($this->lignePanier[$key]);
				break;
			}
		}*/
	}
	
	public function __construct(){
		$this->lignePanier = array();
	}
}

?>