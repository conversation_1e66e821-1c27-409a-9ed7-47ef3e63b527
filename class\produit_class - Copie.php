<?php

// ------- CLASS de gestion produit -------

class produit {

// ---------------------- Declaration -------------------------

	private $reference;
	private $designation;
	private $prixHT;
	private $promotion;
	private $genre;
	private $groupe;
	private $groupe2;
	private $genreid;
	private $groupeid;
	private $identifiant;
	private $image;
	private $taille;	
	private $type;
	private $qualite; 
	private $jeepest;
	private $desmet;
	private $gsaa;
	private $autre_frs;
	private $miltec;
	private $commande;
	private $pp;
	private $prix_frs;
	private $empl_comment;
	private $date_livraison;
	private $qte_livraison;
	private $prod_commun_r2087;
	private $prod_commun_jeep;
	private $prod_commun_dodge;
	private $prod_commun_gmc;
	private $who;
	
// ---------------------- GET -------------------------

	public function getReference() {
		return $this->reference;
	}
	public function getDesignation() {
		return $this->designation;
	}
	public function getGroupeId() {
		return $this->groupeid;
	}
	public function getPrixHT() {
		return $this->prixHT;
	}
	public function getGroupeName2() {
		return $this->groupe2;
	}
	public function getGenreId() {
		return $this->genreid;
	}
	public function isPromotion() {
		return $this->promotion;
	}
	public function getGroupeName($langue = "fr") {
		$arrayGroupeName = explodeCategorieName($this->groupe);
		if($langue == "fr") {
			return $arrayGroupeName[0];
		}
		else {
			return strtoupper($arrayGroupeName[1]);
		}
		return $this->groupe;
	}
	public function getGenreName() {
		return $this->genre;
	}
	public function getIdentifiant() {
		return $this->identifiant;
	}
	public function getTaille() {
		return $this->taille;
	}
	public function getType() {
		return $this->type;
	}
	public function getQualite() {
		return $this->qualite;
	}
	public function getPromotion() {
		return $this->promotion;
	}
	public function getImage(){
		return $this->image;
	}
	public function getProd_commun_r2087() {
		return $this->prod_commun_r2087;
	}
	public function getProd_commun_jeep() {
		return $this->prod_commun_jeep;
	}
	public function getProd_commun_dodge() {
		return $this->prod_commun_dodge;
	}
	public function getProd_commun_gmc() {
		return $this->prod_commun_gmc;
	}
	public function getJeepest() {
		return $this->jeepest;
	}
	public function getDesmet() {
		return $this->desmet;
	}
	public function getGsaa() {
		return $this->gsaa;
	}
	public function getAutre_frs() {
		return $this->autre_frs;
	}
	public function getMiltec() {
		return $this->miltec;
	}
	public function getCommande() {
		return $this->commande;
	}
	public function getPp() {
		return $this->pp;
	}
	public function getPrix_frs() {
		return $this->prix_frs;
	}
	public function getEmpl_comment() {
		return $this->empl_comment;
	}
	public function getDate_livraison() {
		return $this->date_livraison;
	}
	public function getQte_livraison() {
		return $this->qte_livraison;
	}
	public function getWho() {
		return $this->who;
	}
// ----------------------- SET ----------------------

	public function setGroupeId($id) {
		$this->groupeid = $id;
	}
	public function setGenreId($id) {
		$this->genreid = $id;
	}
	public function setGroupeName($groupe) {
		$this->groupe = $groupe;
	}
	public function setGroupeName2($groupe2) {
		$this->groupe2 = $groupe2;
	}
	public function setProd_commun_r2087($prod_commun_r2087) {
		$this->prod_commun_r2087 = $prod_commun_r2087;
	}
	public function setProd_commun_jeep($prod_commun_jeep) {
		$this->prod_commun_jeep = $prod_commun_jeep;
	}
	public function setProd_commun_dodge($prod_commun_dodge) {
		$this->prod_commun_dodge = $prod_commun_dodge;
	}
	public function setProd_commun_gmc($prod_commun_gmc) {
		$this->prod_commun_gmc = $prod_commun_gmc;
	}
	public function setJeepest($jeepest) {
		$this->jeepest = $jeepest;
	}
	public function setDesmet($desmet) {
		$this->desmet = $desmet;
	}
	public function setGsaa($gsaa) {
		$this->gsaa = $gsaa;
	}
	public function setAutre_frs($autre_frs) {
		$this->autre_frs = $autre_frs;
	}
	public function setMiltec($miltec) {
		$this->miltec = $miltec;
	}
	public function setCommande($commande) {
		$this->commande = $commande;
	}
	public function setPp($pp) {
		$this->pp = $pp;
	}
	public function setPrix_frs($prix_frs) {
		$this->prix_frs = $prix_frs;
	}
	public function setEmpl_comment($empl_comment) {
		$this->empl_comment = $empl_comment;
	}
	public function setDate_livraison($date_livraison) {
		$this->date_livraison = $date_livraison;
	}
	public function setQte_livraison($qte_livraison) {
		$this->qte_livraison = $qte_livraison;
	}
	public function setGenreName($genre) {
		$this->genre = $genre;
	}
	public function setIdentifiant($id) {
		$this->identifiant = $id;
	}
	public function setReference($reference) {
		$this->reference = $reference;
	}
	public function setDesignation($designation) {
		$this->designation = $designation;
	}
	public function setPrixHT($prixHT) {
		$this->prixHT = $prixHT;
	}
	public function setPromotion($promotion) {
		$this->promotion = $promotion;
	}
	public function setGenre($genre) {
		$this->genre = $genre;
	}
	public function setGroupe($groupe) {
		$this->groupe = $groupe;
	}
	public function setImage($image){
		$this->image = $image;
	}
	public function setTaille($taille) {
		$this->taille = $taille;
	}
	public function setWho($who) {
		$this->who = $who;
	}
	public function setType($type) {
		$this->type = $type;
	}
	public function setQualite($qualite) {
		$this->qualite = $qualite;
	}
	
	public function __construct($id = 0) {
		$this->setGroupe(null);
		$this->setGenre(null);
		if($id>0)
		{
			//on a un identifiant, donc on charge le produit à partir de la bdd
			$temp = produitDB::getProduitById($id);
			$this->setIdentifiant($temp->getIdentifiant());
			$this->setDesignation($temp->getDesignation());
			$this->setReference($temp->getReference());
			$this->setPrixHT($temp->getPrixHT());
			$this->setImage($temp->getImage());
			$this->setTaille($temp->getTaille());
			$this->setType($temp->getType());
			$this->setQualite($temp->getQualite());
			$this->setCommande($temp->getCommande());

		}
			
	}

}
/**
 * classe d'accés à la base de données...
 *
 */
class produitDB {
	
	public static function getQualite($Qualite)
	{
				$image_color = strtolower($Qualite);
			if ($image_color == "new"){
				$qualite = "<strong style='color: #3399FF;'>NEUF</strong>";
			} elseif ($image_color == "nos"){
				$qualite = "<strong style='color: #FF6600;'>NOS</strong>";
			} elseif ($image_color == "rec"){
				$qualite = "<strong style='color: #00CC33; font-size: 12px;'>RENOVÉ</strong>";
			} elseif ($image_color == "use"){
				$qualite = "<strong style='color: #999900; font-size: 12px;'>OCCASION</strong>";
			} else {
				$qualite = "";
			}
			
		return $qualite;	
			
	}
	public static function getQualiteNoStyle($Qualite)
	{
				$image_color = strtolower($Qualite);
			if ($image_color == "new"){
				$qualite = "NEUF";
			} elseif ($image_color == "nos"){
				$qualite = "NOS";
			} elseif ($image_color == "rec"){
				$qualite = "RENOVÉ";
			} elseif ($image_color == "use"){
				$qualite = "OCCASION";
			} else {
				$qualite = "";
			}
			
		return $qualite;	
			
	}
	public static function getStyleQualite($Qualite)
	{
				$image_color = strtolower($Qualite);
			if ($image_color == "new"){
				$style_border = "border: 1px solid #3399FF;";
			} elseif ($image_color == "nos"){
				$style_border = "border: 1px solid #FF6600;";
			} elseif ($image_color == "rec"){
				$style_border = "border: 1px solid #00CC33;";
			} elseif ($image_color == "use"){
				$style_border = "border: 1px solid #999900;";
			} else {
				$style_border = "";
			}
			
		return $style_border;	
			
	}
	/**
	 * Renvoi la remise pro
	 */
	public static function getRemiseProRevendeur($stock, $qualite)
	{
		$db = Zend_registry::get("database");

		$sql = "SELECT remise_revendeur FROM remise_pro WHERE stock=:stock AND qualite=:qualite;";
			
		$stmt = $db->prepare($sql);
		$stmt->bindParam(":stock", $stock);
		$stmt->bindParam(":qualite", $qualite);
		$stmt->execute();
		$data = $stmt->fetch();

		return $data['remise_revendeur'];	
	}
	public static function getRemiseProGarage($stock, $qualite)
	{
		$db = Zend_registry::get("database");

		$sql = "SELECT remise_garagiste FROM remise_pro WHERE stock=:stock AND qualite=:qualite;";
			
		$stmt = $db->prepare($sql);
		$stmt->bindParam(":stock", $stock);
		$stmt->bindParam(":qualite", $qualite);
		$stmt->execute();
		$data = $stmt->fetch();

		return $data['remise_garagiste'];	
	}
	public static function getPrixPro($stock, $qualite, $prixHT, $emailclient)
	{
		$db = Zend_registry::get("database");

		$sql = "SELECT indiceclient FROM clients WHERE emailclient=:emailclient";
			
		$stmt = $db->prepare($sql);
		$stmt->bindParam(":emailclient", $emailclient);
		$stmt->execute();
		$indiceclient = $stmt->fetch();
				if($indiceclient['indiceclient'] == "5"){
					$remisepro2 = ProduitDB::getRemiseProRevendeur($stock, $qualite);
					$remisepro = $prixHT*($remisepro2/100);
				} elseif($indiceclient['indiceclient'] == "6"){
					$remisepro2 = ProduitDB::getRemiseProGarage($stock, $qualite);
					$remisepro = $prixHT*($remisepro2/100);
				} else {
					$remisepro = 0;
				}
		return $remisepro;	
	}
	public static function getProduitAssocie1($genre, $idgroupe)
	{
		$db = Zend_registry::get("database");

		$sql = "SELECT ref_prod_assoc FROM produits_associes WHERE idgroupe_prod_assoc=:idgroupe_prod_assoc AND genre_prod_assoc=:genre_prod_assoc LIMIT 1";
			
		$stmt = $db->prepare($sql);
		$stmt->bindParam(":idgroupe_prod_assoc", $idgroupe);
		$stmt->bindParam(":genre_prod_assoc", $genre);
		$stmt->execute();
		$ref_prod_assoc = $stmt->fetch();
		return $ref_prod_assoc['ref_prod_assoc'];	
	}
	/**
	 * Renvoi le nom d'un groupe par rapport à son id
	 */
	public static function getRemisePro($stock, $qualite, $emailclient)
	{
		$db = Zend_registry::get("database");

		$sql = "SELECT indiceclient FROM clients WHERE emailclient=:emailclient";
			
		$stmt = $db->prepare($sql);
		$stmt->bindParam(":emailclient", $emailclient);
		$stmt->execute();
		$indiceclient = $stmt->fetch();
				if($indiceclient['indiceclient'] == "5"){
					$remisepro2 = ProduitDB::getRemiseProRevendeur($stock, $qualite);
					
				} elseif($indiceclient['indiceclient'] == "6"){
					$remisepro2 = ProduitDB::getRemiseProGarage($stock, $qualite);
					
				} else {
					$remisepro2 = 0;
				}
		return $remisepro2;	
	}
	/**
	 * Renvoi le nom d'un groupe par rapport à son id
	 */
	public static function getGroupeNameById($id)
	{
		$db = Zend_registry::get("database");

		$sql = "SELECT groupe FROM produits WHERE idgroupe=:id LIMIT 1;";
			
		$stmt = $db->prepare($sql);
		$stmt->bindParam(":id", $id);
		$stmt->execute();
		$data = $stmt->fetch();

		return $data['groupe'];	
	}
	/**
	 * Renvoi l'identifiant d'un groupe par rapport à son nom
	 * @param string $nom Nom du groupe
	 */
	public static function getGoupeIdByName($nom) {
		$db = Zend_registry::get("database");
		

		$sql = "SELECT idgroupe FROM produits WHERE groupe like (:nom) LIMIT 1;";
			
		$stmt = $db->prepare($sql);
		$stmt->bindParam(":nom", $nom);
		$stmt->execute();
		$data = $stmt->fetch();

		return $data['idgroupe'];	
	}
/**
	 * Renvoi la liste des noms de tous les genres
	 * 
	 * @param 
	 * @return array Liste de string
	 */
	public static function getGenresNames() {
		
		$namelist  = array(); //liste des noms des catégories parentes
		$db = Zend_registry::get("database");
		

		$sql = "SELECT DISTINCT genre FROM produits;";
			
		$stmt = $db->prepare($sql);
		$stmt->execute();
		$data = $stmt->fetchAll();

		foreach($data as $ligne) {
			array_push($namelist, $ligne['genre']);
		}

		return $namelist;	
	}
	/**
	 * Renvoi la liste des noms de tous les groupes d'un genre précis
	 * 
	 * @param string $id L'identifiant du genre
	 * @return array Liste de string
	 */
	public static function getGroupesNames($genre_id) {
		
		$namelist  = array(); //liste des groupes
		$db = Zend_registry::get("database");
		

		$sql = "SELECT DISTINCT groupe FROM produits WHERE genre Like (:genre);";
			
		$stmt = $db->prepare($sql);
		$stmt->bindParam(":genre", $genre_id);
		$stmt->execute();
		$data = $stmt->fetchAll();

		foreach($data as $ligne) {
			array_push($namelist, $ligne['groupe']);
		}

		return $namelist;	
	}
	/**
	 * Renvoi le nombre de résultats pour une requete
	 * 
	 */
	public static function getProduitsListCount($genre, $groupe)
	{
		$sql = "SELECT count(*) FROM produits ";
		
		if(!is_null($genre))
		{
			$sql .= "WHERE genre LIKE ('".$genre."') ";
		}
		if(!is_null($groupe))
		{
			if(!is_null($genre) && ($genre == "JEEP")){
			$sql .= "AND idgroupe = '".$groupe."' OR prod_commun_jeep = '".$groupe."' ";
			}
			if(!is_null($genre) && ($genre == "DODGE")){
			$sql .= "AND idgroupe = '".$groupe."' OR prod_commun_dodge = '".$groupe."' ";
			}
			if(!is_null($genre) && ($genre == "GMC")){
			$sql .= "AND idgroupe = '".$groupe."' OR prod_commun_gmc = '".$groupe."' ";
			}
			if(!is_null($genre) && ($genre == "RENAULT")){
			$sql .= "AND idgroupe = '".$groupe."' OR prod_commun_r2087 = '".$groupe."' ";
			}			
			
			if(!is_null($genre) && ($genre != "RENAULT" && $genre != "JEEP" && $genre != "DODGE" && $genre != "GMC")) {
			$sql .= "AND idgroupe = '".$groupe."' ";
			}
			
		}
		
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		
		$stmt->execute();
		$data = $stmt->fetchColumn();
		
		return $data;
	}
	/**
	 * Renvoi une liste de produits
	 * @param string $genre Identifiant du genre des produits
	 * @param string $groupe Identifiant du groupe des produits
	 * @param int $start Début de l'interval de résultats de la requete
	 * @param int $len Taille de l'interval de résultats de la requete
	 * @param string $sort Colonne sur laquelle trier les résultats
	 * @param string $sens Sens du tri ASC ou DESC
	 */
	 
	public static function getProduitsList($genre, $groupe, $start, $len, $sort, $sens)
	
	{
		$prods = array();
		
		$sql = "SELECT * FROM produits ";
		if(!is_null($genre))
		{
			$sql .= "WHERE genre = '".$genre."' ";
		}
		if(!is_null($groupe))
		{
			if(!is_null($genre) && ($genre == "JEEP")){
			$sql .= "AND idgroupe = '".$groupe."' OR prod_commun_jeep = '".$groupe."' ";
			} 
			if(!is_null($genre) && ($genre == "DODGE")){
			$sql .= "AND idgroupe = '".$groupe."' OR prod_commun_dodge = '".$groupe."' ";
			} 
			if(!is_null($genre) && ($genre == "GMC")){
			$sql .= "AND idgroupe = '".$groupe."' OR prod_commun_gmc = '".$groupe."' ";
			} 
			if(!is_null($genre) && ($genre == "RENAULT")){
			$sql .= "AND idgroupe = '".$groupe."' OR prod_commun_r2087 = '".$groupe."' ";
			} 
			if(!is_null($genre) && ($genre != "RENAULT" && $genre != "JEEP" && $genre != "DODGE" && $genre != "GMC")) {
			$sql .= "AND idgroupe = '".$groupe."' ";
			}
			
		}
		if(!is_null($sort)){
			$sql .= "ORDER BY `".$sort."` ".$sens." ";
		} else {
			if(!is_null($groupe)){
			$sql .= "ORDER BY descriptionproduit ASC,qualite DESC ";
			}else{
			$sql .= "ORDER BY idgroupe ASC,descriptionproduit ASC,qualite DESC ";
			}
		}
		$sql .= "LIMIT ".$start.", ".$len;

		
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);		

		$stmt->execute();
		$data = $stmt->fetchAll();
		
		foreach($data as $ligne) {
			$prod = new Produit();
			if (($_SESSION['lang'] == "fr") || ($ligne['descriptionproduiten'] == ""))
				$prod->setDesignation($ligne['descriptionproduit']);
			else
				$prod->setDesignation($ligne['descriptionproduiten']);
			//$prod->setDesignation($ligne['descriptionproduit']);
			$prod->setPrixHT($ligne['prixproduiteuro']);
			$prod->setReference($ligne['referenceproduit']);
			$prod->setIdentifiant($ligne['idproduit']);
			
			$prod->setGroupeId($ligne['idgroupe']);
			$prod->setGroupeName($ligne['groupe']);
			$prod->setGenreId($ligne['genre']);
			$prod->setGenreName($ligne['genre']);
			
			$prod->setPromotion($ligne['promo']);
			$prod->setImage($ligne['image']);
			$prod->setTaille($ligne['taille']);
			$prod->setType($ligne['type']);
			$prod->setQualite($ligne['qualite']);
			$prod->setCommande($ligne['commande']);
			$prod->setEmpl_comment($ligne['empl_comment']);
			
				
			array_push($prods, $prod);
			
		}
		
		return $prods;
	}
	/**
	 * renvoi un produit à partir de son identifiant
	 * 
	 * @param int $id Identifiant du produit dans la bdd
	 */
	function getProduitById($id) {
		
		$sql = "SELECT * FROM produits WHERE idproduit=:id;";

		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		
		$stmt->bindParam(':id',$id);
		
		$stmt->execute();
		$data = $stmt->fetch();
		
		//print_r($data);
		if(count($data) != 0){
			
			$prod = new Produit();
			if (($_SESSION['lang'] == "fr") || ($data['descriptionproduiten'] == ""))
				$prod->setDesignation($data['descriptionproduit']);
			else
				$prod->setDesignation($data['descriptionproduiten']);
				
			$prod->setPrixHT($data['prixproduiteuro']);
			$prod->setReference($data['referenceproduit']);
			$prod->setIdentifiant($data['idproduit']);
			
			$prod->setGroupeId($data['idgroupe']);
			$prod->setGroupeName($data['groupe']);
			$prod->setGenreId($data['genre']);
			$prod->setGenreName($data['genre']);
			
			$prod->setPromotion($data['promo']);
			$prod->setImage($data['image']);
			
			$prod->setTaille($data['taille']);
			$prod->setType($data['type']);
			$prod->setQualite($data['qualite']);
			
			$prod->setCommande($data['commande']);
			
			$prod->setEmpl_comment($data['empl_comment']);
			
			return $prod;
		}
		
		return null;
	}
	
	function getProduitById2($id) {
		
		$sql = "SELECT * FROM produits WHERE idproduit=:id;";

		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		
		$stmt->bindParam(':id',$id);
		
		$stmt->execute();
		$data = $stmt->fetch();
		
		//print_r($data);
		if(count($data) != 0){
			
			$prod = new Produit();
			
			$prod->setDesignation($data['descriptionproduit']);
			$prod->setPrixHT($data['prixproduiteuro']);
			$prod->setReference($data['referenceproduit']);
			$prod->setIdentifiant($data['idproduit']);
			
			$prod->setGroupeId($data['idgroupe']);
			$prod->setGroupeName2($data['groupe']);
			$prod->setGenreId($data['genre']);
			$prod->setGenreName($data['genre']);
			
			$prod->setPromotion($data['promo']);
			$prod->setImage($data['image']);
			
			$prod->setTaille($data['taille']);
			$prod->setType($data['type']);
			$prod->setQualite($data['qualite']);
			
			$prod->setEmpl_comment($data['empl_comment']);
			
			$prod->setJeepest($data['jeepest']);
			$prod->setDesmet($data['desmet']);
			$prod->setGsaa($data['gsaa']);
			$prod->setAutre_frs($data['autre_frs']);
			$prod->setMiltec($data['miltec']);
			
			$prod->setCommande($data['commande']);
			$prod->setPp($data['pp']);
			$prod->setPrix_frs($data['prix_frs']);
			
			$prod->setDate_livraison($data['date_livraison']);
			$prod->setQte_livraison($data['qte_livraison']);
			
			$prod->setProd_commun_r2087($data['prod_commun_r2087']);
			$prod->setProd_commun_jeep($data['prod_commun_jeep']);
			$prod->setProd_commun_dodge($data['prod_commun_dodge']);
			$prod->setProd_commun_gmc($data['prod_commun_gmc']);
			
			return $prod;
		}
		
		return null;
	}
	/**
	* Sauvegarde un produit dans la base de données
	*
	* @param Produit $produit Produit a sauvegarder
	* @param boolean $ajax Si on appelle cette méthode depuis un script AJAX ou pas
	* @return Renvoi le nombres de produits ajoutés dans la BDD
	**/
	function getProduitByRef2($id) {
		
		$sql = "SELECT * FROM produits WHERE referenceproduit=:id;";

		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		
		$stmt->bindParam(':id',$id);
		
		$stmt->execute();
		$data = $stmt->fetch();
		
		//print_r($data);
		if(count($data) != 0){
			
			$prod = new Produit();
			
			$prod->setDesignation($data['descriptionproduit']);
			$prod->setPrixHT($data['prixproduiteuro']);
			$prod->setReference($data['referenceproduit']);
			$prod->setIdentifiant($data['idproduit']);
			
			$prod->setGroupeId($data['idgroupe']);
			$prod->setGroupeName($data['groupe']);
			$prod->setGroupeName2($data['groupe']);
			$prod->setGenreId($data['genre']);
			$prod->setGenreName($data['genre']);
			
			$prod->setPromotion($data['promo']);
			$prod->setImage($data['image']);
			
			$prod->setTaille($data['taille']);
			$prod->setType($data['type']);
			$prod->setQualite($data['qualite']);
			
			$prod->setEmpl_comment($data['empl_comment']);
			
			$prod->setJeepest($data['jeepest']);
			$prod->setDesmet($data['desmet']);
			$prod->setGsaa($data['gsaa']);
			$prod->setAutre_frs($data['autre_frs']);
			$prod->setMiltec($data['miltec']);
			
			$prod->setCommande($data['commande']);
			$prod->setPp($data['pp']);
			$prod->setPrix_frs($data['prix_frs']);
			
			$prod->setDate_livraison($data['date_livraison']);
			$prod->setQte_livraison($data['qte_livraison']);
			
			$prod->setProd_commun_r2087($data['prod_commun_r2087']);
			$prod->setProd_commun_jeep($data['prod_commun_jeep']);
			$prod->setProd_commun_dodge($data['prod_commun_dodge']);
			$prod->setProd_commun_gmc($data['prod_commun_gmc']);
			
			return $prod;
		}
		
		return null;
	}
	/**
	* Sauvegarde un produit dans la base de données
	*
	* @param Produit $produit Produit a sauvegarder
	* @param boolean $ajax Si on appelle cette méthode depuis un script AJAX ou pas
	* @return Renvoi le nombres de produits ajoutés dans la BDD
	**/
	function saveProduit($produit) {
	
		// Enregistrement dans la BDD avec test si la système à réussit à se connecter et à enregistrer les données
		// Connexion à la BDD
		$db = Zend_registry::get("database");
		
		// Préparation de la requête pour vérifier si le produit existe déjà
		$stmt = $db->prepare("SELECT * FROM produits WHERE descriptionproduit Like (:desc) AND referenceproduit Like (:ref) AND idgroupe Like (:idgroupe) AND genre Like (:genre);");
		
		$stmt->bindParam(':desc', $produit->getDesignation());
		$stmt->bindParam(':ref', $produit->getReference());
		$stmt->bindParam(':idgroupe', $produit->getGroupeId());
		$stmt->bindParam(':genre', $produit->getGenreId()); //l'identifiant du genre = le nom du genre (pas de diff)

		$stmt->execute();
		//on regarde le résultat
		if ($stmt->rowCount() == 0)
		{
			
			//le produit n'a pas été trouvé, réparation de la requête
			$stmt = $db->prepare("insert into produits (descriptionproduit, referenceproduit, prixproduiteuro, promo, idgroupe, groupe, genre, image) VALUES (:desc, :ref, :prix, :promo, :idgroupe, :groupe, :genre, :image)");
			
			$desc = utf8_encode($produit->getDesignation());
			$ref = utf8_encode($produit->getReference());
			$prix = $produit->getPrixHT();
			$idgroupe = $produit->getGroupeId();
			$groupe = utf8_encode($produit->getGroupeName());
			$genre = utf8_encode($produit->getGenreName());
			$image = utf8_encode($produit->getImage());
			$stmt->bindParam(':desc',$desc);
			$stmt->bindParam(':ref', $ref);
			$stmt->bindParam(':prix', $prix);	
			$stmt->bindParam(':idgroupe', $idgroupe);
			$stmt->bindParam(':groupe', $groupe);
			$stmt->bindParam(':genre', $genre);
			$stmt->bindParam(':image',$image);
			
			$bool = $produit->getPromotion();
			if($bool):
				$promo = '1';
			else:
				$promo = '0';
			endif;			
			$stmt->bindParam(':promo', $promo);			

			$stmt->execute();
			// On vérifie si on a bien une ligne de résultat : donc que tout c'est bien passé.
			// Si oui, on affiche un message de confirmation d'inscription
			if ($stmt->rowCount() == 1)
			{
				return 1;
			}
			else
			{
				
				echo '<tr><td></td><td></td><td></td><td>Impossible d\'enregistrer le produit : ',$produit->getDesignation(),'.</td></tr>';
			}
		}
		else
		{
			list($genrefr, $en) = explodeCategorieName($produit->getGenreName());
			list($groupefr, $en) = explodeCategorieName($produit->getGroupeName());
			//ici l'identifiant reprsente le numéro de la ligne où se trouve le produit dans le fichier
			echo '<tr>',$produit->getIdentifiant(),'<td>',$genrefr,'</td>';
			echo '<td>',$groupefr,'</td>'; 
			echo '<td>Le produit : ',$produit->getDesignation(),' est déjà présent dans la base de données.</td></tr>';
		}
		return 0;
	}
	/**
	* Lit chaque ligne d'un fichier et crée la liste des produits
	* Renvoi la liste des produits, et la liste des produits en double dans le fichier
	*
	* @param file $file
	* @param int $numcol_reference Numéro de la colonne contenant la référence
	* @param int $numcol_designation Numéro de la colonne contenant la désignation
	* @param int $numcol_prixht Numéro de la colonne contenant le prix HT
	* @param int $numcol_souscategorie Numéro de la colonne contenant la sous catégorie du produit
	* @param int $numcol_categorie Numéro de la colonne contenant la catégorie du produit
	* @param int $numcol_promo Numéro de la colonne contenant le nombres "d'étoiles", qui spécifie si le produit est en promo ou pas
	* @return Renvoi un tableau de produits, et tableau des genre, et un talbeau des groupes
	**/
	function readProduitsFromFile($file, $numcol_reference, $numcol_designation, $numcol_prixht, $numcol_souscategorie, $numcol_categorie, $numcol_promo) {
		
		$tabproduits = array(); //liste des produits
		$tabproduits_error = array(); //liste des produits présent plus d'une fois dans la base 
	
		$tabgenre = array();	//tableau des identifiants des genres
		$tabgroupe = array();	//tableau des identifiants des groupes
		
		$handle = fopen('./temp/'.$file, "r");
		//calcul le plus grand des numéros de colonnes 
		$maxcol = max($numcol_reference, $numcol_designation, $numcol_prixht, $numcol_souscategorie, $numcol_categorie, $numcol_promo);
		$row=0;
		while (($data = fgetcsv($handle, 1000, ";")) !== FALSE) {
		    $num = count($data); //nombre de champs sur la ligne
		    $row++;
		    
			//si le nombre de champs sur la ligne est inférieur au plus grand des numéros de colonnes donnés en paramètres, la ligne est invalide
			if($num < $maxcol)
			{
				echo '<tr><td>',$row,'</td><td></td><td></td><td>Impossible d\'importer le produit !</td></tr>';
			}
			else
			{
				$promo = $data[$numcol_promo-1];				//récupère si le produit est en promo ou pas
				//si c'est un produit en promotion
				
				if($promo == "xxx")
					$promo = true;
				else
					$promo = false;
				
				$groupe = $data[$numcol_souscategorie-1]; //nom de la sous catégorie du produit
				$genre = $data[$numcol_categorie-1]; //nom de la catégorie du produit;
				//enlève les espaces avant et après
				$genre = trim($genre);
				
				//l'identifiant du genre correspond à son nom (on a pas plus d'info !)
				$genreid = $genre;
				$genrename = $genre;
				
				if($groupe!="")
				{
					//on découpe l'identifiant du groupe ( ex : '01 00' )
					$groupeid = '';
					$c=0;

					while($c<strlen($groupe) && (is_numeric($groupe[$c]) || $groupe[$c]==' ')) {
						
						$groupeid .= $groupe[$c];
						$c++;
					}
					//enregistre le nom du groupe
					$groupename = trim(substr($groupe, $c));
					//enlève les espaces dans l'identifiant du groupe
					//str_replace("\xA0", "", $groupeid);
					$groupeid = str_replace(" ", "", $groupeid);
				}
				else
				{
					$groupeid = "";
					$groupename = "";
				}
				
				/////////////PARTIE COMPTAGE DES GENRES ///////////////
				$t = 0;
				while($t<count($tabgenre) && $tabgenre[$t]!=$genreid)
					$t++;
					
				//on a pas trouvé le genre, donc on l'ajoute à la liste
				if($t==count($tabgenre))
					array_push($tabgenre, $genreid);
					
				//////////////////////////////////////////////////////
				////////////PARTIE COMPTAGE DES GROUPES///////////////
				$t = 0;
				while($t<count($tabgroupe) && $tabgroupe[$t]!=$groupeid)
					$t++;
					
				//on a pas trouvé le genre, donc on l'ajoute à la liste
				if($t==count($tabgroupe))
					array_push($tabgroupe, $groupeid);
				////////////////////////////////////////////////////////
					

				
				//on récupère le prix tel qu'il est dans le fichier
				$prix_base = $data[$numcol_prixht-1];
				//on remplace les virgules par des . pour que ce soit un type numérique
				$prix_base = str_replace(',', '.', $prix_base);	
				//on vérifie que c'est un type numérique
				if(is_numeric($prix_base))
				{
					//c'est le cas, on connait le prix
					//elève les espaces dans le prix du produits
					//ex : 3 000,00 remplacé par 3000,00
					$prix = str_replace("\xA0", '', $prix_base);
				} 
				else 
				{
					//on ne connait pas le prix, 'sur devis'
					//on ne connait pas le prix, on met -1 pour dire que le produit est 'sur devis'
					$prix = -1.0;				
				}
				$designation =$data[$numcol_designation-1];
				$reference =$data[$numcol_reference-1];
				
				
				$produit = new Produit();									//crée un nouveau produit
				$produit->setIdentifiant($row);								//identifiant provisoir, ici le numéro de la ligne
				$produit->setReference($reference); 		//définit la référence produit
				$produit->setDesignation($designation); 	//définit la désignation
				$produit->setPrixHT($prix); 								//définit le prix
				$produit->setGroupeName($groupename);
				$produit->setGenreName($genrename);
				$produit->setGroupeId($groupeid);
				$produit->setGenreId($genreid);
				$produit->setPromotion($promo);		
				$produit->setImage('');	
				
				//ajoute le produit à la liste des produits
				array_push($tabproduits, $produit);
					

			}
		    
		}
		fclose($handle);

		return array($tabproduits,$tabgenre,$tabgroupe);
	}
	/**
	 * Lit toute la base de données des produits
	 * @return array Renvoi une liste de catégories contenant des sous-catégorires, contenant des produits
	 */
	function readProduitsFromDataBase() {
		$prods = array();
				
		$sql = "SELECT * FROM produits ";
		
		if(isset($_GET['sort'])){
			$sql .= "ORDER BY `".$_GET['sort']."`";
			if(isset($_GET['sens'])){
				$sql .= " ".$_GET['sens'];
			}
		} else {
			$sql .= "ORDER BY genre ASC,idgroupe ASC,descriptionproduit ASC,qualite DESC ";
		}
		
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		

		$stmt->execute();
		$data = $stmt->fetchAll();
		
		foreach($data as $ligne) {
			
			$prod = new Produit();
		
			$prod->setDesignation($ligne['descriptionproduit']);
			$prod->setPrixHT($ligne['prixproduiteuro']);
			$prod->setReference($ligne['referenceproduit']);
			$prod->setIdentifiant($ligne['idproduit']);
			
			$prod->setGroupeId($ligne['idgroupe']);
			$prod->setGroupeName($ligne['groupe']);
			$prod->setGenreId($ligne['genre']);
			$prod->setGenreName($ligne['genre']);
			
			$prod->setPromotion($ligne['promo']);
			$prod->setImage($ligne['image']);
			$prod->setTaille($ligne['taille']);
			$prod->setType($ligne['type']);
			$prod->setQualite($ligne['qualite']);
			
			$prod->setEmpl_comment($ligne['empl_comment']);
			$prod->setCommande($ligne['commande']);
			
			$prod->setProd_commun_r2087($ligne['prod_commun_r2087']);
			$prod->setProd_commun_jeep($ligne['prod_commun_jeep']);
			$prod->setProd_commun_dodge($ligne['prod_commun_dodge']);
			$prod->setProd_commun_gmc($ligne['prod_commun_gmc']);
			
			array_push($prods, $prod);
			
		}
		
		return $prods;

	}
		function readProduitsCommandeFrs() {
		
		$mois_today = strtotime("-2 Months");
		$date_moins_deux = date("Y-m-d", $mois_today);

		$prods = array();
		
		$sql = "SELECT * FROM produits WHERE commande != '' ORDER BY genre, idgroupe, descriptionproduit";
		
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		

		$stmt->execute();
		$data = $stmt->fetchAll();
		
		foreach($data as $ligne) {
			
			$prod = new Produit();
		
			$prod->setDesignation($ligne['descriptionproduit']);
			$prod->setPrixHT($ligne['prixproduiteuro']);
			$prod->setReference($ligne['referenceproduit']);
			$prod->setIdentifiant($ligne['idproduit']);
			
			$prod->setGroupeId($ligne['idgroupe']);
			$prod->setGroupeName($ligne['groupe']);
			$prod->setGenreId($ligne['genre']);
			$prod->setGenreName($ligne['genre']);
			
			$prod->setPromotion($ligne['promo']);
			$prod->setImage($ligne['image']);
			$prod->setTaille($ligne['taille']);
			$prod->setType($ligne['type']);
			$prod->setQualite($ligne['qualite']);
			
			$prod->setJeepest($ligne['jeepest']);
			$prod->setDesmet($ligne['desmet']);
			$prod->setGsaa($ligne['gsaa']);
			$prod->setAutre_frs($ligne['autre_frs']);
			$prod->setMiltec($ligne['miltec']);
			
			$prod->setCommande($ligne['commande']);
			$prod->setPp($ligne['pp']);
			$prod->setPrix_frs($ligne['prix_frs']);
			$prod->setEmpl_comment($ligne['empl_comment']);
			
			$prod->setDate_livraison($ligne['date_livraison']);
			$prod->setQte_livraison($ligne['qte_livraison']);
			
			$prod->setProd_commun_r2087($ligne['prod_commun_r2087']);
			$prod->setProd_commun_jeep($ligne['prod_commun_jeep']);
			$prod->setProd_commun_dodge($ligne['prod_commun_dodge']);
			$prod->setProd_commun_gmc($ligne['prod_commun_gmc']);
			
			array_push($prods, $prod);
			
		}
		
		return $prods;

	}
	function readProduitsArrivage() {
		
		$mois_today = strtotime("-2 Months");
		$date_moins_deux = date("Y-m-d", $mois_today);

		$prods = array();
		
		$sql = "SELECT * FROM produits WHERE date_livraison > '$date_moins_deux' AND taille = '' ORDER BY date_livraison DESC, genre ASC, descriptionproduit ASC";
		
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		

		$stmt->execute();
		$data = $stmt->fetchAll();
		
		foreach($data as $ligne) {
			
			$prod = new Produit();
		
			$prod->setDesignation($ligne['descriptionproduit']);
			$prod->setPrixHT($ligne['prixproduiteuro']);
			$prod->setReference($ligne['referenceproduit']);
			$prod->setIdentifiant($ligne['idproduit']);
			
			$prod->setGroupeId($ligne['idgroupe']);
			$prod->setGroupeName($ligne['groupe']);
			$prod->setGenreId($ligne['genre']);
			$prod->setGenreName($ligne['genre']);
			
			$prod->setPromotion($ligne['promo']);
			$prod->setImage($ligne['image']);
			$prod->setTaille($ligne['taille']);
			$prod->setType($ligne['type']);
			$prod->setQualite($ligne['qualite']);
			
			$prod->setJeepest($ligne['jeepest']);
			$prod->setDesmet($ligne['desmet']);
			$prod->setGsaa($ligne['gsaa']);
			$prod->setAutre_frs($ligne['autre_frs']);
			$prod->setMiltec($ligne['miltec']);
			
			$prod->setCommande($ligne['commande']);
			$prod->setPp($ligne['pp']);
			$prod->setPrix_frs($ligne['prix_frs']);
			$prod->setEmpl_comment($ligne['empl_comment']);
			
			$prod->setDate_livraison($ligne['date_livraison']);
			$prod->setQte_livraison($ligne['qte_livraison']);
			
			$prod->setProd_commun_r2087($ligne['prod_commun_r2087']);
			$prod->setProd_commun_jeep($ligne['prod_commun_jeep']);
			$prod->setProd_commun_dodge($ligne['prod_commun_dodge']);
			$prod->setProd_commun_gmc($ligne['prod_commun_gmc']);
			
			array_push($prods, $prod);
			
		}
		
		return $prods;

	}
	
	function readProduitsFromDataBase2() {
		$prods = array();
		
		$sql = "SELECT * FROM produits ORDER BY genre,idgroupe,descriptionproduit";
		
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		

		$stmt->execute();
		$data = $stmt->fetchAll();
		
		foreach($data as $ligne) {
			
			$prod = new Produit();
		
			$prod->setDesignation($ligne['descriptionproduit']);
			$prod->setPrixHT($ligne['prixproduiteuro']);
			$prod->setReference($ligne['referenceproduit']);
			$prod->setIdentifiant($ligne['idproduit']);
			
			$prod->setGroupeId($ligne['idgroupe']);
			$prod->setGroupeName($ligne['groupe']);
			$prod->setGenreId($ligne['genre']);
			$prod->setGenreName($ligne['genre']);
			
			$prod->setPromotion($ligne['promo']);
			$prod->setImage($ligne['image']);
			$prod->setTaille($ligne['taille']);
			$prod->setType($ligne['type']);
			$prod->setQualite($ligne['qualite']);
			
			$prod->setJeepest($ligne['jeepest']);
			$prod->setDesmet($ligne['desmet']);
			$prod->setGsaa($ligne['gsaa']);
			$prod->setAutre_frs($ligne['autre_frs']);
			$prod->setMiltec($ligne['miltec']);
			
			$prod->setCommande($ligne['commande']);
			$prod->setPp($ligne['pp']);
			$prod->setPrix_frs($ligne['prix_frs']);
			$prod->setEmpl_comment($ligne['empl_comment']);
			
			$prod->setDate_livraison($ligne['date_livraison']);
			$prod->setQte_livraison($ligne['qte_livraison']);
			
			$prod->setProd_commun_r2087($ligne['prod_commun_r2087']);
			$prod->setProd_commun_jeep($ligne['prod_commun_jeep']);
			$prod->setProd_commun_dodge($ligne['prod_commun_dodge']);
			$prod->setProd_commun_gmc($ligne['prod_commun_gmc']);
			
			array_push($prods, $prod);
			
		}
		
		return $prods;

	}
	
	function readProduitsFromDataBase3() {
		$prods = array();
		
		$sql = "SELECT * FROM produits ORDER BY genre,idgroupe,descriptionproduit LIMIT 100";
		
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		

		$stmt->execute();
		$data = $stmt->fetchAll();
		
		foreach($data as $ligne) {
			
			$prod = new Produit();
			
			$prod->setReference($ligne['referenceproduit']);
			$prod->setDesignation($ligne['descriptionproduit']);
			$prod->setGroupeName($ligne['groupe']);
			$prod->setGenreName($ligne['genre']);
			
			array_push($prods, $prod);
			
		}
		
		return $prods;

	}
	/**
	 * supprime un produit de la base de donnée
	 * @param Produit $prod Produit à supprimer
	 * @return Renvoi vrai si suppression réussi, sinon faux
	 */
	function removeProduit(Produit $prod) {
		
		$sql ="DELETE FROM produits WHERE idproduit=:id;";
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		$stmt->bindParam(':id',$prod->getIdentifiant());
		$stmt->execute();	
		if ($stmt->rowCount() == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
/**
	 * modifie un produit de la base de donnée
	 * @param Produit $prod Produit à modifier
	 * @return Renvoi vrai si modification réussi, sinon faux
	 */
	function modifProduit(Produit $prod) {
		
		$sql ="UPDATE produits SET descriptionproduit=:desc, referenceproduit=:ref, prixproduiteuro=:prix, image = :image,
		promo=:promo, idgroupe=:idgroupe, groupe=:groupe, genre=:genre, taille=:taille, type=:type, qualite = :qualite,
		jeepest=:jeepest, desmet=:desmet, gsaa=:gsaa, autre_frs = :autre_frs,
		miltec=:miltec, commande=:commande, pp=:pp, prix_frs = :prix_frs, empl_comment = :empl_comment,
		date_livraison = :date_livraison, qte_livraison = :qte_livraison,
		prod_commun_r2087 = :prod_commun_r2087, prod_commun_jeep = :prod_commun_jeep, prod_commun_dodge = :prod_commun_dodge, prod_commun_gmc = :prod_commun_gmc
		
		WHERE idproduit=:id;";
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		
		$desc = $prod->getDesignation();
		$ref = $prod->getReference();
		$prix = $prod->getPrixHT();
		
		$image = $prod->getImage();
		
		$promo = $prod->getPromotion();
		$taille = $prod->getTaille();
		
		$groupe = $prod->getGroupeName2();
		$idgroupe = $prod->getGroupeId();
		$genre = $prod->getGenreName();
		
		$type = $prod->getType();
		$qualite = $prod->getQualite();
			
		$jeepest = $prod->getJeepest();
		$desmet = $prod->getDesmet();
		$gsaa = $prod->getGsaa();
		$autre_frs = $prod->getAutre_frs();
		$miltec = $prod->getMiltec();
			
		$commande = $prod->getCommande();
		$pp = $prod->getPp();
		$prix_frs = $prod->getPrix_frs();
		$empl_comment = $prod->getEmpl_comment();
		
		$date_livraison = $prod->getDate_livraison();
		$qte_livraison = $prod->getQte_livraison();
		
		$prod_commun_r2087 = $prod->getProd_commun_r2087();
		$prod_commun_jeep = $prod->getProd_commun_jeep();
		$prod_commun_dodge = $prod->getProd_commun_dodge();
		$prod_commun_gmc = $prod->getProd_commun_gmc();
	
		
		$stmt->bindParam(':desc', $desc);
		$stmt->bindParam(':ref', $ref);
		$stmt->bindParam(':prix', $prix);	
		$stmt->bindParam(':image', $image);
		$stmt->bindParam(':promo', $promo);
		$stmt->bindParam(':taille', $taille);
		
		$stmt->bindParam(':idgroupe', $idgroupe);
		$stmt->bindParam(':groupe', $groupe);
		$stmt->bindParam(':genre', $genre);
		
		$stmt->bindParam(':type', $type);
		$stmt->bindParam(':qualite', $qualite);
			
		$stmt->bindParam(':jeepest', $jeepest);
		$stmt->bindParam(':desmet', $desmet);
		$stmt->bindParam(':gsaa', $gsaa);
		$stmt->bindParam(':autre_frs', $autre_frs);
		$stmt->bindParam(':miltec', $miltec);
			
		$stmt->bindParam(':commande', $commande);
		$stmt->bindParam(':pp', $pp);
		$stmt->bindParam(':prix_frs', $prix_frs);
		$stmt->bindParam(':empl_comment', $empl_comment);
		
		$stmt->bindParam(':date_livraison', $date_livraison);
		$stmt->bindParam(':qte_livraison', $qte_livraison);
		
		$stmt->bindParam(':prod_commun_r2087', $prod_commun_r2087);
		$stmt->bindParam(':prod_commun_jeep', $prod_commun_jeep);
		$stmt->bindParam(':prod_commun_dodge', $prod_commun_dodge);
		$stmt->bindParam(':prod_commun_gmc', $prod_commun_gmc);
		
		$stmt->bindParam(':id',$prod->getIdentifiant());

		$stmt->execute();	
		
		if ($stmt->rowCount() == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
	
/**
	 * ajout un produit de la base de donnée
	 * @param Produit $prod Produit à ajouter
	 * @return Renvoi vrai si modification réussi, sinon faux
	 */
	function ajoutProduit(Produit $prod) {
		
$sql ="INSERT INTO produits
(descriptionproduit, 
referenceproduit, 
prixproduiteuro, 
image,
promo, 
idgroupe,
groupe,
genre,
prix_revient,
taille, 
type, 
qualite,
jeepest, 
desmet, 
gsaa, 
autre_frs,
miltec, 
commande, 
pp, 
prix_frs, 
empl_comment,
date_livraison, 
qte_livraison,
prod_commun_r2087,
prod_commun_jeep,
prod_commun_dodge,
prod_commun_gmc,
who)

VALUES(:desc,
:ref,
:prix,
:image,
:promo,
:idgroupe,
:groupe,
:genre,
:prix_revient,
:taille,
:type,
:qualite,
:jeepest,
:desmet,
:gsaa,
:autre_frs,
:miltec,
:commande,
:pp,
:prix_frs,
:empl_comment,
:date_livraison,
:qte_livraison,
:prod_commun_r2087,
:prod_commun_jeep,
:prod_commun_dodge,
:prod_commun_gmc,
:who)";

		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		
		$desc = $prod->getDesignation();
		$ref = $prod->getReference();
		$prix = $prod->getPrixHT();
		
		$idgroupe = $prod->getGroupeId();
		$groupe = $prod->getGroupeName2();
		$genre = $prod->getGenreName();
		
		$prix_revient = (($prod->getPrixHT())/2);
		$image = $prod->getImage();
		
		$promo = $prod->getPromotion();
		$taille = $prod->getTaille();
		$type = $prod->getType();
		$qualite = $prod->getQualite();
			
		$jeepest = $prod->getJeepest();
		$desmet = $prod->getDesmet();
		$gsaa = $prod->getGsaa();
		$autre_frs = $prod->getAutre_frs();
		$miltec = $prod->getMiltec();
			
		$commande = $prod->getCommande();
		$pp = $prod->getPp();
		$prix_frs = $prod->getPrix_frs();
		$empl_comment = $prod->getEmpl_comment();
		
		$date_livraison = $prod->getDate_livraison();
		$qte_livraison = $prod->getQte_livraison();
		
		$prod_commun_r2087 = $prod->getProd_commun_r2087();
		$prod_commun_jeep = $prod->getProd_commun_jeep();
		$prod_commun_dodge = $prod->getProd_commun_dodge();
		$prod_commun_gmc = $prod->getProd_commun_gmc();
		
		$who = $prod->getWho();
	
		
		$stmt->bindParam(':desc', $desc);
		$stmt->bindParam(':ref', $ref);
		$stmt->bindParam(':idgroupe', $idgroupe);	
		$stmt->bindParam(':groupe', $groupe);
		$stmt->bindParam(':genre', $genre);
		$stmt->bindParam(':prix', $prix);
		$stmt->bindParam(':image', $image);
		$stmt->bindParam(':prix_revient', $prix_revient);
		$stmt->bindParam(':promo', $promo);
		$stmt->bindParam(':taille', $taille);
		$stmt->bindParam(':type', $type);
		$stmt->bindParam(':qualite', $qualite);
			
		$stmt->bindParam(':jeepest', $jeepest);
		$stmt->bindParam(':desmet', $desmet);
		$stmt->bindParam(':gsaa', $gsaa);
		$stmt->bindParam(':autre_frs', $autre_frs);
		$stmt->bindParam(':miltec', $miltec);
			
		$stmt->bindParam(':commande', $commande);
		$stmt->bindParam(':pp', $pp);
		$stmt->bindParam(':prix_frs', $prix_frs);
		$stmt->bindParam(':empl_comment', $empl_comment);
		
		$stmt->bindParam(':date_livraison', $date_livraison);
		$stmt->bindParam(':qte_livraison', $qte_livraison);
		
		$stmt->bindParam(':prod_commun_r2087', $prod_commun_r2087);
		$stmt->bindParam(':prod_commun_jeep', $prod_commun_jeep);
		$stmt->bindParam(':prod_commun_dodge', $prod_commun_dodge);
		$stmt->bindParam(':prod_commun_gmc', $prod_commun_gmc);
		
		$stmt->bindParam(':who', $who);

		$stmt->execute();	
		
		if ($stmt->rowCount() == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
	/**
	 * Vide la table complète
	 */
	function truncateTable() {
		$sql ="TRUNCATE TABLE produits;";
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		$stmt->execute();	
	}
}

/**
 * Classe d'affichage des informations d'un produit
 * Affiche sous forme d'un tableau, soi pour les admins, soi pour les clients
 * 
 * admins :	-affichage d'un lien pour supprimer ou modifier le produit
 * 			-affiche d'une colonne genre et groupe
 * clients :-affichage d'un lien pour ajouter le produit au panier
 * 			-affiche possible de la colonne groupe
 */
class produitDisplay {
	
	private $cat;
	private $sscat;
	private $pagination;
	private $showgroupe;
	private $showgenre;
	private $typelistclient;
	private $start;
	private $len;
	private $tri;
	private $typelien;
	private $lien;
	
	public static $PAGE_NORMAL = 0;
	public static $PAGE_AUTRE = 1;
	public static $PAGE_TOUS = 2;
	/**
	 * Nombre d'élément dans le tableau
	 *
	 * @var int
	 */
	private $count;
	/**
	 * Prépare l'affiche du tableau 
	 *
	 * @param string $cat Catégorie (genre) à afficher (peut être null si on affiche un groupe)
	 * @param string $sscat Catégorie (groupe) à afficher (peut être null si on affiche un genre)
	 * @param integer $typelien Type des liens à afficher (autres, tous ou alors [jeep,dodge,gmc])
	 */
	function __construct($cat, $sscat, $typelien) {
				
		$this->cat = $cat;
		$this->sscat = $sscat;		
		//valeurs pas défaut
		$this->showgroupe = false;
		$this->typelistclient = false;
		$this->pagination = null;
		$this->filtre = null;
		$this->count = 0;
		$this->start = null;
		$this->len = null;
		$this->tri = null;
		$this->sens = "ASC";
		$this->typelien = $typelien;
		
	}
	
	public function showGroupe($show) { $this->showgroupe = $show; }
	public function showGenre($show) { $this->showgenre = $show; }
	public function isClientList($show) { $this->typelistclient = $show; }
	public function setPagination($page) { $this->pagination = $page; }
	public function setInterval($start, $len) {$this->start = $start;$this->len = $len;}
	public function setTri($tri) { $this->tri = $tri; }
	public function setSens($sens) { $this->sens = $sens; }
	public function setTypeLien($type) { $this->typelien = $type;}
	
	/**
	 * Génère la base des liens, utilisé dans les liens entres les pages en footer, et les liens sur les noms des colonnes pour trier header
	 *
	 */
	private function genereLien($tri, $sens)
	{
		global $basePath;
		//si le genre est JEEP DODGE ou GMC, il doit apparaitre en minuscule dans l'url
		switch($this->typelien)
		{
			case self::$PAGE_NORMAL:
				$cat = url_encode(strtolower($this->cat));
				$plus = $cat;
				break;
			case self::$PAGE_AUTRE:
				$cat = url_encode($this->cat);		//on ne met pas en minuscule le nom du genre!
				$plus = 'autres';
				break;
			case self::$PAGE_TOUS:
				$cat = url_encode($this->cat);		//on ne met pas en minuscule le nom du genre!
				$plus = 'tous';
				break;
			default:
				$cat = url_encode($this->cat);		//on ne met pas en minuscule le nom du genre!
				break;
		}
		$sscat = url_encode($this->sscat);
		if($this->typelistclient) //page de type client
		{
			if($this->sscat != null)
			{
				if($this->typelien == self::$PAGE_NORMAL)
					$this->lien = $basePath.'index.php?categorie='.$plus.'&subcat='.$sscat;
				if($this->typelien == self::$PAGE_AUTRE)
					$this->lien = $basePath.'index.php?categorie='.$plus.'&cat='.$cat.'&subcat='.$sscat;
				if($this->typelien == self::$PAGE_TOUS)
					$this->lien = $basePath.'index.php?categorie='.$plus.'&filtre_cat='.$cat.'&filtre_sscat='.$sscat;
			}
			elseif($this->cat != null)
			{
				if($this->typelien == self::$PAGE_NORMAL)
					$this->lien = $basePath.'index.php?categorie='.$plus;
				if($this->typelien == self::$PAGE_AUTRE)
					$this->lien = $basePath.'index.php?categorie='.$plus.'&cat='.$cat;
				if($this->typelien == self::$PAGE_TOUS)
					$this->lien = $basePath.'index.php?categorie='.$plus.'&filtre_cat='.$cat;	
			}
			elseif($this->cat == null)
			{
				if($this->typelien == self::$PAGE_NORMAL)
					$this->lien = $basePath.'index.php';
				if($this->typelien == self::$PAGE_AUTRE)
					$this->lien = $basePath.'index.php?categorie='.$plus;
				if($this->typelien == self::$PAGE_TOUS)
					$this->lien = $basePath.'index.php?categorie='.$plus;	
			}
				
			//un tri est définit
			if($tri != null)
				$this->lien .= '&tri='.$tri.'&sens='.$sens;
			
		}
		else //page de type admin
		{
			if($this->sscat != null)
				$this->lien = $basePath.'index.php?page=admin&action=gererprod&filtre_cat='.$cat.'&filtre_sscat='.$sscat;
			elseif($this->cat != null)
				$this->lien = $basePath.'index.php?page=admin&action=gererprod&filtre_cat='.$cat;
			elseif($this->cat == null)
				$this->lien = $basePath.'index.php?page=admin&action=gererprod';
				
			if($tri != null)
				$this->lien .= '&tri='.$tri.'&sens='.$sens;
		}
		
		return $this->lien;
	}
	private function omdalpanier($reference, $designation, $prix, $genre, $idgroupe) {
		global $basePath;
		
		$ref_prod_assoc1 = ProduitDB::getProduitAssocie1($genre, $idgroupe);
		$prod_assoc1 = ProduitDB::getProduitByRef2($ref_prod_assoc1);
		
	?>	
		<div id="oModalajout" class="oModal">
	<div>
        <header> 
        Confirmation de mise au panier 
        <a href="#fermer" title="Fermer la fenêtre" class="droite"><img src="<?php echo $basePath; ?>public/images/cross.png" alt="SUPPRIMER" /></a>
        </header>
        <img style="position: absolute; margin-left:200px; margin-top:10px;" src="<?php echo $basePath; ?>public/images/check.png" width="30" height="30" />
        <div id="title_prod_add"><strong>Produit ajouté !</strong></div>
      <table width="600" height="100">
        	<tr>
           	  <td style="border-bottom: 1px solid #CCC; border-right: 1px solid #CCC;" width="483" height="50" align="center"><strong>Désignation</strong></td>
                <td style="border-bottom: 1px solid #CCC;" width="105" align="center"><strong>Prix HT</strong></td>
            </tr>
            <tr>
           	  <td style="border-right: 1px solid #CCC;" height="50"><strong><?php echo $designation; ?></strong><br /><?php echo $reference; ?></td>
                <td align="center"><?php echo $prix; ?> €</td>
            </tr>
        </table>
        <section>
          <input style="margin-left:100px; border: 1px solid #448112; background: #8FCC0C;" type="button" class="buttonPanier" value="Panier" onclick="location.href='<?php echo $basePath.'panier'; ?>';" />	
          <input style="margin-left: 150px;" type="button" value="Continuer" class="buttonPanier" onclick="location.href='#fermer';"/>	
        </section>
      <div id="title_cli_cde"><strong>Nos clients ont aussi commandés :</strong></div>
      <table width="600" height="100">
        	<tr>
                  <td style="border-bottom: 1px solid #CCC; border-right: 1px solid #CCC;" width="300" height="50" align="center"><img src="<?php echo $basePath; ?>public/images/produits/<?php echo $prod_assoc1->getReference;?>.jpg" width="250" height="200
                  " /></td>
                    <td style="border-bottom: 1px solid #CCC;" width="300" align="center"><img src="Documents/SMI/SMI/Site internet SMI/Site Origine SMI/www/smi/public/images/produits/75W80_2L.jpg" width="250" height="200
                  " /></td>
                </tr>
                <tr>
                  <td height="50" align="center" style="border-right: 1px solid #CCC;"><p><strong><?php echo $prod_assoc1->getDesignation;?></strong> - <?php echo $prod_assoc1->getReference;?><br />
                  Prix : <?php echo $prod_assoc1->getPrixHT; ?>€ HT</p></td>
                    <td align="center">150,00 €</td>
                </tr>
                <tr>
                  <td align="center" style="border-right: 1px solid #CCC; padding: 0px;" height="50"> <input type="button"  value="Voir" class="myButton" onclick="location.href='<?php echo $basePath; ?>description/<?php echo $prod_assoc1->getGenreName; ?>/<?php echo $prod_assoc1->getReference; ?>';"/></td>
                    <td align="center">150,00 €</td>
                </tr>
            </table>
	</div>	
</div>
<?php
	}
	private function displayJavascriptFunctionPro() {
		?>
		<script type="text/javascript">
		//<![CDATA[
			function addtocart(produit, id) {
				qte = document.getElementById('p'+id).value;
				remise = document.getElementById('r'+id).value;
				document.location = "<?php echo Zend_registry::get("basePath"); ?>addcart.php?action=add&id="+produit+"&nb="+qte+"&r="+remise;
			}
			
			function update_qte(id, val) {
				quantite = document.getElementById('p'+id).value;
				quantite = parseInt(quantite) + parseInt(val);
				if (quantite < 1) { quantite = 1; }
				if (quantite > 99) { quantite = 99; }
				document.getElementById('p'+id).value = quantite;
			}
		//]]>
		</script>
		<?php
	}
	private function displayJavascriptFunction() {
		?>
		<script type="text/javascript">
		//<![CDATA[
			
			function addtocart(produit, id) {
				qte = document.getElementById('p'+id).value;
				document.location = "<?php echo Zend_registry::get("basePath"); ?>panier/ajouter/"+produit+"/quantite/"+qte;
			}
			/*
			function addtocart(produit,ref,four)
			{
			qte = document.getElementById('p'+id).value;
			var xmlhttp = null;
			if (produit=="")
			  {
			  document.getElementById("txtHint").innerHTML="";
			  return;
			  }
			if (window.XMLHttpRequest)
			  {// code for IE7+, Firefox, Chrome, Opera, Safari
			  xmlhttp=new XMLHttpRequest();
			  }
			else
			  {// code for IE6, IE5
			  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
			  }
			xmlhttp.onreadystatechange=function()
			  {
			  if (xmlhttp.readyState==4 && xmlhttp.status==200)
				{
				document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
				}
			  }
			xmlhttp.open("GET","<?php echo Zend_registry::get("basePath"); ?>panier/ajouter/"+produit+"/quantite/"+qte,true);
			xmlhttp.send();
			} 
			*/
			function update_qte(id, val) {
				quantite = document.getElementById('p'+id).value;
				quantite = parseInt(quantite) + parseInt(val);
				if (quantite < 1) { quantite = 1; }
				if (quantite > 99) { quantite = 99; }
				document.getElementById('p'+id).value = quantite;
			}
		//]]>
		</script>
		<?php
	}
private function displayJavascriptFunctionAdmin() {
		?>
		<script type="text/javascript">
		//<![CDATA[

			$(document).ready(function(){
				$('#site #middle #produit #right_produit #ajouter_produit a').click(function(){
						$( "#site #middle #produit #right_produit #ajouter_produit a" ).effect( "shake",50 );
					});
				});				  
		/*
		$(document).ready(function(){
			$('#ajouter_produit').on('click', function() {
			  var $panier = $('#ajouter_produit');
			  var offset = $panier.offset();

			  var t = offset.top;
			  var l = offset.left;

			  $(this).animate({
				'top': t + 'px',
				'left': l + 'px'
			  }, 'slow', function() {
				$(this).fadeOut();
			  });
			});
		});	
		
		$(document).ready(function(){
			$('#ajouter_produit').on('click', function() {
				$( document ).click(function() {
				  $('#ajouter_produit').animate({ color: "red" });
				});
			});
		});		
		*/
			function addtocart(produit,id)
			{
			
			qte = document.getElementById('p'+id).value;
			var xmlhttp = null;
			if (produit=="")
			  {
			  document.getElementById("txtHint").innerHTML="";
			  return;
			  }
			if (window.XMLHttpRequest)
			  {// code for IE7+, Firefox, Chrome, Opera, Safari
			  xmlhttp=new XMLHttpRequest();
			  }
			else
			  {// code for IE6, IE5
			  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
			  }
			xmlhttp.onreadystatechange=function()
			  {
			  if (xmlhttp.readyState==4 && xmlhttp.status==200)
				{
				//document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
				//document.getElementById('articlepanier').innerHTML = "Article ajouté !";
				}
			  }
			xmlhttp.open("GET","<?php echo Zend_registry::get("basePath"); ?>panier/ajouter/"+produit+"/quantite/"+qte,true);
			xmlhttp.send();
			document.getElementById('articlepanier').innerHTML = "<span style='color:cyan;'>Article ajouté !</span>";
			document.getElementById('ajoutpanier'+produit).innerHTML = "<span style='color:red;'>Ajouté !</span>";
			setTimeout(function() { document.getElementById('ajoutpanier'+produit).innerHTML = "AJOUTER"; }, 5000);
			} 

			function update_qte(id, val) {
				quantite = document.getElementById('p'+id).value;
				quantite = parseInt(quantite) + parseInt(val);
				if (quantite < 1) { quantite = 1; }
				if (quantite > 99) { quantite = 99; }
				document.getElementById('p'+id).value = quantite;
			}
		//]]>
		</script>
		<?php
	}

// ----------NEW VERSION LIST PRODUIT 2.0---------
// --------------- UTILISATEUR ------------------

public function displayTable() {
		global $basePath;
		$this->displayJavascriptFunction();
		if($this->cat == "tous" || $this->cat == "all")
		$this->cat = null;
		echo '<table class="liste">';
			echo '<tr>';
				echo '<th colspan=11>Page ';
					//affiche les liens des numéros de pages, en précisant qu'on affiche les noms des variable ds l'url
					$this->pagination->printPagesLinks($this->genereLien($this->tri, $this->sens), true);	
				echo '</th>';
			echo '</tr>';
		echo '</table>';
		
		//affiche l'entête du tableau
		$this->displayHeader();	
		//récupère la liste des produits	
		
		$prodlist = ProduitDB::getProduitsList($this->cat, $this->sscat, $this->start, $this->len,$this->tri,$this->sens);			
		foreach($prodlist as $p) {
			//affiche la ligne avec le produit
			$this->displayLine($p);
		}	
		
		$this->displayFooter();	
}

private function displayHeader() {
		global $basePath;
		?>

		<tr>
		<?php
		if($this->sens == "ASC")
			$sens = "DESC";
		else
			$sens = "ASC";
			
	
		$imgtri = "";
		
		if($sens == "DESC")
			$imgtri = '<img src="'.$basePath.'public/images/up.png" alt="tri"/>';
		else
			$imgtri = '<img src="'.$basePath.'public/images/down.png" alt="tri"/>';
			
		$imgtri_reference = "";
		$imgtri_description = "";
		$imgtri_prix = "";
		if(isset($_GET['tri'])) {
			if($_GET['tri'] == "referenceproduit")
				$imgtri_reference = $imgtri;
			elseif($_GET['tri'] == "descriptionproduit")
				$imgtri_description = $imgtri;
			elseif($_GET['tri'] == "prixproduiteuro")
				$imgtri_prix = $imgtri;
			
		}
		
		global $translate;
	// Debut tableau -----------------------------
	echo '<table class="liste">';
		
		echo '<th style="color:blue;">Trier par :</th>';
		
		echo '<th>',$imgtri_reference,'<a href="',$this->genereLien("referenceproduit",$sens),'">',$translate->_('Référence'),'</a></th>';	
		
		echo '<th>',$imgtri_description,'<a href="',$this->genereLien("descriptionproduit",$sens),'">',$translate->_('Désignation'),'</a></th>';
		
		echo '<th>',$imgtri_prix,'<a href="',$this->genereLien("prixproduiteuro",$sens),'">Prix</a></th>';
				
		echo '</tr>';
	echo '<table>';
	}
	
private function displayLine(Produit $produit) {
		
		global $basePath;
		$comment = $produit->getEmpl_comment();
		
		//AFFICHAGE DESCRIPTION / GENRE / GROUPE
		$genreName = "";
		$groupeName = "";
		if($this->showgenre == true){
			$arrayGenreName = explodeCategorieName($produit->getGenreName());
		if($_SESSION["lang"] == "fr") {
								
			$genreNametr = $arrayGenreName[0];
									
		} else {
		$genreNametr = $arrayGenreName[1];
		}
			$genreName = $genreNametr." - ";
		}
		if($this->showgroupe == true){
								
			$groupeName = $produit->getGroupeName($_SESSION["lang"])." - ";
								
		} 
								
		$description = $genreName.$groupeName.$produit->getDesignation();
		
		
		//QUALITE

			$image_color = strtolower($produit->getQualite());
			if ($image_color == "new"){
				$style_border = "border: 1px solid #3399FF;";
				$qualite = "<strong style='color: #3399FF;'>NEUF</strong>";
			} elseif ($image_color == "nos"){
				$style_border = "border: 1px solid #FF6600;";
				$qualite = "<strong style='color: #FF6600;'>NOS</strong>";
			} elseif ($image_color == "rec"){
				$style_border = "border: 1px solid #00CC33;";
				$qualite = "<strong style='color: #00CC33; font-size: 12px;'>RENOVÉ</strong>";
			} elseif ($image_color == "use"){
				$style_border = "border: 1px solid #999900;";
				$qualite = "<strong style='color: #999900; font-size: 12px;'>OCCASION</strong>";
			} else {
				$style_border = "";
				$qualite = "";
			}	
		if ($comment == "ECHSTD"){
		
			if($this->count%2) {
			echo '<div id="produit" style="font-weight: bold;">';
			}else{
			echo '<div id="produit" style="background-color: #fff; font-weight: bold;" >';
			}
		
		} else {
		
			if($this->count%2) {
			echo '<div id="produit">';
			}else{
			echo '<div id="produit"	style="background-color: #fff;">';
			}
		
		}
		if (file_exists("public/images/produits/".$produit->getReference().".jpg")){
			echo '<div id="image_produit">';
					//IMAGE
					echo "<a href=\"".Zend_Registry::get('basePath')."public/images/produits/".$produit->getReference().".jpg?d=".date('Ymd')."\" rel=\"lytebox\" title=\"".$produit->getReference()."\" >";
						
				    echo "<img src=\"".Zend_Registry::get('basePath')."public/images/produits/".$produit->getReference().".jpg?d=".date('Ymd')."\" style=\"".$style_border." box-shadow: 1px 1px 2px 0px rgba(119, 119, 119, 0.8);\" height=\"82\" width=\"110\" />";
					
					echo "</a>";
			echo '</div>';
			echo '<div id="separation">';
			echo '</div>';
		}
			
		if (file_exists("public/images/produits/".$produit->getReference().".jpg")){	
			echo '<div id="middle_produit">';
		} else {
			echo '<div id="middle_produit" style="width:450px;">';
		}

					//DESIGNATION
					$style_descript =  'style="padding-top: 8px; height: 35px;"';
					
					if (file_exists("public/images/produits/".$produit->getReference().".jpg")){
						if (strlen($description) > 34 && strlen($description) < 68){
							$style_descript = "";
						}				
						if (strlen($description) > 69){
							$style_descript = 'style="padding-top: 8px; height: 35px; font-size:12px;"';
						}						
					} else {
						if (strlen($description) > 40 && strlen($description) < 100){
							$style_descript = 'style="width: 432px;"';
						} if (strlen($description) > 100){
							$style_descript = 'style="padding-top: 8px; height: 35px; width: 432px; font-size:12px;"';
						}
					}
					
					echo '<div id="description_produit" '.$style_descript.'>';
					
					if ($comment == "ECHSTD"){
						echo '<a href="'.Zend_Registry::get('basePath').'description/'.strtolower($produit->getGenreId()).'/'.$produit->getReference().'">';
						echo $description,' <a href="http://jeep-dodge-gmc.com/smi/index.php?page=aide&paiement=echange_std" target="_blank"><img src="'.$basePath.'public/images/aide.png" title="aide" /></a> ';
						echo '</a>';
					}else{
						echo '<a href="'.Zend_Registry::get('basePath').'description/'.strtolower($produit->getGenreId()).'/'.$produit->getReference().'">';
						echo $description;
						echo '</a>';
					}
					echo '</div>';
					
					//REFERENCE
					echo '<div id="reference_produit">';
					echo 'Réf :'.$produit->getReference();
					echo '</div>';
					//QUALITE
					
					echo '<div id="qualite_produit">';
					if ($qualite != ""){
						echo 'Qualité : '.$qualite;
					}
					echo '</div>';
					//STOCK
					if (file_exists("public/images/produits/".$produit->getReference().".jpg")){	
						echo '<div id="stock_produit">';
					} else {
						echo '<div id="stock_produit" style="margin-left:134px;">';
					}
					echo '</div>';
					
					//QUANTITE
					echo '<div id="quantite_produit">Quantité : ';
					?>
					<input type="text" maxlength="2" size="2" value="1" style="text-align:center;" id="p<?php echo $this->count; ?>"/>	
					</div>
					<div id="qté">
						<a style="padding:0px;margin:0px;border:0;" href="javascript:update_qte(<?php echo $this->count; ?>,1);"><img border="0" width="16" height="16" alt="Ajouter" style="margin:0px;" src="<?php echo $basePath; ?>public/images/add.png" /></a>
						<a style="padding:0px;margin:0px;border:0;" href="javascript:update_qte(<?php echo $this->count; ?>,-1);"><img border="0" width="16" height="16" alt="Retirer" style="margin:0px;" src="<?php echo $basePath; ?>public/images/moins.png" /></a>
					<?php
					echo '</div>';
					
			echo '</div>';
			echo '<div id="separation">';
			echo '</div>';
			echo '<div id="right_produit">';
					// PRIX TTC
					echo '<div id="prixttc_produit">';
						if($produit->getPrixHT() > 0)
						{
							printf("%.2f", calculPrixTTC($produit->getPrixHT()));
							echo "€ TTC";
						}
						else
						{
							echo 'Sur devis';
						}
					echo '</div>';
					// PRIX HT
					echo '<div id="prixht_produit">';
							if($produit->getPrixHT() > 0) {
								printf("%.2f", $produit->getPrixHT());
								echo '€ HT';
							} else {
								echo 'Sur devis';
							}
					echo '</div>';
					// AJOUT PANIER
					echo '<div id="ajouter_produit">';
						echo '<a href="javascript:addtocart(',$produit->getIdentifiant(),',',$this->count,');" title="Ajouter au panier">AJOUTER</a>';
					echo '</div>';
					echo '<span id="txtHint"></span>';
			echo '</div>';
		echo '</div>';
		

			
		$this->count++;
		
	}
private function displayFooter() {
		global $basePath;
		echo '<table class="liste">';
		echo '<tr>';
			echo '<th>';
		//affiche les liens des numéros de pages, en précisant qu'on affiche les noms des variable ds l'url
		$this->pagination->printPagesLinks($this->genereLien($this->tri, $this->sens), true);
			echo '</th>';
		echo '</tr>';
		echo '</table>';
		
		echo '<table class="liste">';
		echo '<tr>';
			echo '<th>';
				echo '<p style="text-align:center;">Page n° ',$this->pagination->getNumPage(),' sur ',$this->pagination->getNbPages(),' Affichage des produits [',$this->start,'-',($this->start+$this->len),'] sur ',ProduitDB::getProduitsListCount($this->cat, $this->sscat),'</p>';
			echo '</th>';
		echo '</tr>';
		echo '<tr><th style="font-weight: normal;"><span style="color: #FF6600; font-weight: bold;">NOS</span> (New Old Stock) Neuf de stock ancien | <span style="color: #3399FF; font-weight: bold;">NEUF</span> : neuf de fabrication actuelle | <span style="color: #999900; font-weight: bold;">OCCASION</span> : pieces de démontage déjà utilisées | <span style="color: #00CC33; font-weight: bold;">RENOVÉ</span> : pièces d\'occasion rénovées</th></tr>';
		echo '</table>';
	}
	
	// ----------NEW VERSION LIST PRODUIT 2.0---------
// --------------- PRO ------------------

public function displayTableProRevendeur() {
		global $basePath;
		$this->displayJavascriptFunctionPro();
		if($this->cat == "tous" || $this->cat == "all")
		$this->cat = null;
		echo '<table class="liste">';
			echo '<tr>';
				echo '<th colspan=11>Page ';
					//affiche les liens des numéros de pages, en précisant qu'on affiche les noms des variable ds l'url
					$this->pagination->printPagesLinks($this->genereLien($this->tri, $this->sens), true);	
				echo '</th>';
			echo '</tr>';
		echo '</table>';
		
		//affiche l'entête du tableau
		$this->displayHeaderPro();	
		//récupère la liste des produits	
		
		$prodlist = ProduitDB::getProduitsList($this->cat, $this->sscat, $this->start, $this->len,$this->tri,$this->sens);			
		foreach($prodlist as $p) {
			//affiche la ligne avec le produit
			$this->displayLineProRevendeur($p);
		}	
		
		$this->displayFooterPro();	
}

public function displayTableProGarage() {
		global $basePath;
		$this->displayJavascriptFunctionPro();
		if($this->cat == "tous" || $this->cat == "all")
		$this->cat = null;
		echo '<table class="liste">';
			echo '<tr>';
				echo '<th colspan=11>Page ';
					//affiche les liens des numéros de pages, en précisant qu'on affiche les noms des variable ds l'url
					$this->pagination->printPagesLinks($this->genereLien($this->tri, $this->sens), true);	
				echo '</th>';
			echo '</tr>';
		echo '</table>';
		
		//affiche l'entête du tableau
		$this->displayHeaderPro();	
		//récupère la liste des produits	
		
		$prodlist = ProduitDB::getProduitsList($this->cat, $this->sscat, $this->start, $this->len,$this->tri,$this->sens);			
		foreach($prodlist as $p) {
			//affiche la ligne avec le produit
			$this->displayLineProGarage($p);
		}	
		
		$this->displayFooterPro();	
}

private function displayHeaderPro() {
		global $basePath;
		?>

		<tr>
		<?php
		if($this->sens == "ASC")
			$sens = "DESC";
		else
			$sens = "ASC";
			
	
		$imgtri = "";
		
		if($sens == "DESC")
			$imgtri = '<img src="'.$basePath.'public/images/up.png" alt="tri"/>';
		else
			$imgtri = '<img src="'.$basePath.'public/images/down.png" alt="tri"/>';
			
		$imgtri_reference = "";
		$imgtri_groupe = "";
		$imgtri_genre = "";
		$imgtri_description = "";
		$imgtri_prix = "";
		if(isset($_GET['tri'])) {
			if($_GET['tri'] == "referenceproduit")
				$imgtri_reference = $imgtri;
			elseif($_GET['tri'] == "groupe")
				$imgtri_groupe = $imgtri;
			elseif($_GET['tri'] == "genre")
				$imgtri_genre = $imgtri;
			elseif($_GET['tri'] == "descriptionproduit")
				$imgtri_description = $imgtri;
			elseif($_GET['tri'] == "prixproduiteuro")
				$imgtri_prix = $imgtri;
			
		}
		
		global $translate;
	// Debut tableau -----------------------------
	echo '<table class="liste">';
		
		echo '<th style="color:blue;">Trier par :</th>';
		
		echo '<th>',$imgtri_reference,'<a href="',$this->genereLien("referenceproduit",$sens),'">',$translate->_('Référence'),'</a></th>';	
		
		if($this->showgenre == true)
			echo '<th>',$imgtri_genre,'<a href="',$this->genereLien("genre",$sens),'">',$translate->_('Genre'),'</a></th>';
			
		if($this->showgroupe == true)
			echo '<th>',$imgtri_groupe,'<a href="',$this->genereLien("groupe",$sens),'">',$translate->_('Groupe'),'</a></th>';

		echo '<th>',$imgtri_description,'<a href="',$this->genereLien("descriptionproduit",$sens),'">',$translate->_('Désignation'),'</a></th>';
		
		echo '<th>',$imgtri_prix,'<a href="',$this->genereLien("prixproduiteuro",$sens),'">Prix</a></th>';
				
		echo '</tr>';
	echo '<table>';
	}
	
private function displayLineProGarage(Produit $produit) {
		
		global $basePath;
		
///////////////////AFFICHAGE DESCRIPTION / GENRE / GROUPE
		$genreName = "";
		$groupeName = "";
		if($this->showgenre == true){
			$arrayGenreName = explodeCategorieName($produit->getGenreName());
			if($_SESSION["lang"] == "fr") {								
				$genreNametr = $arrayGenreName[0];									
			} else {
				$genreNametr = $arrayGenreName[1];
			}
			$genreName = $genreNametr." - ";
		}
		if($this->showgroupe == true){							
			$groupeName = $produit->getGroupeName($_SESSION["lang"])." - ";								
		} 								
		$description = $genreName.$groupeName.$produit->getDesignation();		
///////////////////QUALITE
		$qualite = ProduitDB::getQualite($produit->getQualite());
		$style_border = ProduitDB::getStyleQualite($produit->getQualite());
///////////////// ECHSTD
		$comment = $produit->getEmpl_comment();
		if ($comment == "ECHSTD"){		
			if($this->count%2) {
			echo '<div id="produit" style="font-weight: bold;">';
			}else{
			echo '<div id="produit" style="background-color: #fff; font-weight: bold;" >';
			}		
		} else {		
			if($this->count%2) {
			echo '<div id="produit">';
			}else{
			echo '<div id="produit"	style="background-color: #fff;">';
			}	
		}
////////////////////IMAGE		
		if (file_exists("public/images/produits/".$produit->getReference().".jpg")){
			echo '<div id="image_produit">';
			echo "<a href=\"".Zend_Registry::get('basePath')."public/images/produits/".$produit->getReference().".jpg?d=".date('Ymd')."\" rel=\"lytebox\" title=\"".$produit->getReference()."\" >";						
			echo "<img src=\"".Zend_Registry::get('basePath')."public/images/produits/".$produit->getReference().".jpg?d=".date('Ymd')."\" style=\"".$style_border." box-shadow: 1px 1px 2px 0px rgba(119, 119, 119, 0.8);\" height=\"82\" width=\"110\" />";			
			echo "</a>";
			echo '</div>';
			echo '<div id="separation">';
			echo '</div>';
		}			
		if (file_exists("public/images/produits/".$produit->getReference().".jpg")){	
			echo '<div id="middle_produit">';
		} else {
			echo '<div id="middle_produit" style="width:450px;">';
		}
//////////////////DESIGNATION
		$style_descript =  'style="padding-top: 8px; height: 35px;"';					
		if (file_exists("public/images/produits/".$produit->getReference().".jpg")){
			if (strlen($description) > 34 && strlen($description) < 68){
				$style_descript = "";
			}				
			if (strlen($description) > 69){
				$style_descript = 'style="padding-top: 8px; height: 35px; font-size:12px;"';
			}						
		} else {
			if (strlen($description) > 40 && strlen($description) < 100){
				$style_descript = 'style="width: 432px;"';
			} if (strlen($description) > 100){
				$style_descript = 'style="padding-top: 8px; height: 35px; width: 432px; font-size:12px;"';
			}
		}
//////////////////ECHSTD					
		echo '<div id="description_produit" '.$style_descript.'>';
		if ($comment == "ECHSTD"){
			echo '<a href="'.Zend_Registry::get('basePath').'description/'.strtolower($produit->getGenreId()).'/'.$produit->getReference().'">';
			echo $description,' <a href="http://jeep-dodge-gmc.com/smi/index.php?page=aide&paiement=echange_std" target="_blank"><img src="'.$basePath.'public/images/aide.png" title="aide" /></a> ';
			echo '</a>';
		}else{
			echo '<a href="'.Zend_Registry::get('basePath').'description/'.strtolower($produit->getGenreId()).'/'.$produit->getReference().'">';
			echo $description;
			echo '</a>';
		}
//////////////////////REMISE PRO					
		$remisepro = 0;
		$remisepro = ProduitDB::getRemiseProGarage($produit->getPromotion(), $produit->getQualite());
		if($remisepro != 0){
			echo '<span style="color: #900;"> - '.$remisepro.'%<span>';
		}
		echo '</div>';					
/////////////////////REFERENCE
		echo '<div id="reference_produit">';
		echo 'Réf :'.$produit->getReference();
		echo '</div>';					
/////////////////////QUALITE					
		echo '<div id="qualite_produit">';
		if ($qualite != ""){
			echo 'Qualité : '.$qualite;
		}
		echo '</div>';					
/////////////////////STOCK
		if (file_exists("public/images/produits/".$produit->getReference().".jpg")){	
			echo '<div id="stock_produit">';
		} else {
			echo '<div id="stock_produit" style="margin-left:134px;">';
		}
		$stock = $produit->getCommande();
		if ($stock == "?" || $produit->getPromotion() == "ooo" || $produit->getPrixHT() == "-1"){
			echo '<img src="'.$basePath.'public/images/stock_2.png" title="Introuvable" alt="Introuvable" />';
		}
		if ($produit->getPromotion() == "oo"){
			echo '<img src="'.$basePath.'public/images/stock_2.png" title="Introuvable" alt="Introuvable" />';
		}
		if (!empty($stock) && $stock != "?" && $produit->getPromotion() != "ooo" && $produit->getPrixHT() != "-1"){
			echo '<img src="'.$basePath.'public/images/stock_3.png" title="En cours de reappro" alt="Introuvable" />';
		}					
		if (empty($stock)){
			if ($produit->getPromotion() == "o" && $produit->getPrixHT() != "-1"){
				echo '<img src="'.$basePath.'public/images/stock_4.png" title="Stock à vérifier" alt="Stock à vérifier" />';
			} else {
				if ($produit->getPrixHT() != "-1"){
					echo '<img src="'.$basePath.'public/images/stock_5.png" title="Normalement en stock" alt="Normalement en stock" />';
				} 
			}
		}
		echo '</div>';					
/////////////////////QUANTITE
		echo '<div id="quantite_produit">Quantité : ';
?>
			<input type="text" maxlength="2" size="2" value="1" style="text-align:center;" id="p<?php echo $this->count; ?>"/>	
			<input type="hidden" value="<?php echo $remisepro;?>" id="r<?php echo $this->count; ?>"/>	
		</div>
		<div id="qté">
			<a style="padding:0px;margin:0px;border:0;" href="javascript:update_qte(<?php echo $this->count; ?>,1);"><img border="0" width="16" height="16" alt="Ajouter" style="margin:0px;" src="<?php echo $basePath; ?>public/images/add.png" /></a>
			<a style="padding:0px;margin:0px;border:0;" href="javascript:update_qte(<?php echo $this->count; ?>,-1);"><img border="0" width="16" height="16" alt="Retirer" style="margin:0px;" src="<?php echo $basePath; ?>public/images/moins.png" /></a>
<?php
		echo '</div>';					
	echo '</div>';
		echo '<div id="separation"></div>';
		echo '<div id="right_produit">';	
////////////////// PRIX HT après remise
		$prixpro = $produit->getPrixHT()-($produit->getPrixHT()*($remisepro/100));					
		echo '<div id="prixttc_produit">';
		if($produit->getPrixHT() > 0){
			printf("%.2f", $prixpro);
			echo "€ HT";
		} else {
			echo 'Sur devis';
		}
		echo '</div>';					
//////////////////// PRIX HT avant remise
		if($remisepro == 0){
			echo '<div id="prixht_produit">';
		} else {
			echo '<div id="prixht_produit" style="text-decoration : line-through;">';
		}
		if($produit->getPrixHT() > 0) {
			if($remisepro == 0){
				echo "Pas de remise !";
			} else {	
				echo "au lieu de ";
				printf("%.2f", $produit->getPrixHT());
				echo '€ HT';
			}
		} else {
			echo 'Sur devis';
		}
		echo '</div>';		
//////////////////// AJOUT PANIER
		echo '<div id="ajouter_produit">';
			echo '<a href="javascript:addtocart(',$produit->getIdentifiant(),',',$this->count,');" title="Ajouter au panier">AJOUTER</a>';
					echo '</div>';
			echo '</div>';
		echo '</div>';
////////////////////////////////////				
		$this->count++;	
	}	
	
private function displayLineProRevendeur(Produit $produit) {
		
		global $basePath;
		
///////////////////AFFICHAGE DESCRIPTION / GENRE / GROUPE
		$genreName = "";
		$groupeName = "";
		if($this->showgenre == true){
			$arrayGenreName = explodeCategorieName($produit->getGenreName());
			if($_SESSION["lang"] == "fr") {								
				$genreNametr = $arrayGenreName[0];									
			} else {
				$genreNametr = $arrayGenreName[1];
			}
			$genreName = $genreNametr." - ";
		}
		if($this->showgroupe == true){							
			$groupeName = $produit->getGroupeName($_SESSION["lang"])." - ";								
		} 								
		$description = $genreName.$groupeName.$produit->getDesignation();		
///////////////////QUALITE
		$qualite = ProduitDB::getQualite($produit->getQualite());
		$style_border = ProduitDB::getStyleQualite($produit->getQualite());
///////////////// ECHSTD
		$comment = $produit->getEmpl_comment();
		if ($comment == "ECHSTD"){		
			if($this->count%2) {
			echo '<div id="produit" style="font-weight: bold;">';
			}else{
			echo '<div id="produit" style="background-color: #fff; font-weight: bold;" >';
			}		
		} else {		
			if($this->count%2) {
			echo '<div id="produit">';
			}else{
			echo '<div id="produit"	style="background-color: #fff;">';
			}	
		}
////////////////////IMAGE		
		if (file_exists("public/images/produits/".$produit->getReference().".jpg")){
			echo '<div id="image_produit">';
			echo "<a href=\"".Zend_Registry::get('basePath')."public/images/produits/".$produit->getReference().".jpg?d=".date('Ymd')."\" rel=\"lytebox\" title=\"".$produit->getReference()."\" >";						
			echo "<img src=\"".Zend_Registry::get('basePath')."public/images/produits/mini/".$produit->getReference().".jpg?d=".date('Ymd')."\" style=\"".$style_border." box-shadow: 1px 1px 2px 0px rgba(119, 119, 119, 0.8);\" height=\"82\" width=\"110\" />";
			echo "</a>";
			echo '</div>';
			echo '<div id="separation">';
			echo '</div>';
		}			
		if (file_exists("public/images/produits/".$produit->getReference().".jpg")){	
			echo '<div id="middle_produit">';
		} else {
			echo '<div id="middle_produit" style="width:450px;">';
		}
//////////////////DESIGNATION
		$style_descript =  'style="padding-top: 8px; height: 35px;"';					
		if (file_exists("public/images/produits/".$produit->getReference().".jpg")){
			if (strlen($description) > 34 && strlen($description) < 68){
				$style_descript = "";
			}				
			if (strlen($description) > 69){
				$style_descript = 'style="padding-top: 8px; height: 35px; font-size:12px;"';
			}						
		} else {
			if (strlen($description) > 40 && strlen($description) < 100){
				$style_descript = 'style="width: 432px;"';
			} if (strlen($description) > 100){
				$style_descript = 'style="padding-top: 8px; height: 35px; width: 432px; font-size:12px;"';
			}
		}
//////////////////ECHSTD					
		echo '<div id="description_produit" '.$style_descript.'>';
		if ($comment == "ECHSTD"){
			echo '<a href="'.Zend_Registry::get('basePath').'description/'.strtolower($produit->getGenreId()).'/'.$produit->getReference().'">';
			echo $description,' <a href="http://jeep-dodge-gmc.com/smi/index.php?page=aide&paiement=echange_std" target="_blank"><img src="'.$basePath.'public/images/aide.png" title="aide" /></a> ';
			echo '</a>';
		}else{
			echo '<a href="'.Zend_Registry::get('basePath').'description/'.strtolower($produit->getGenreId()).'/'.$produit->getReference().'">';
			echo $description;
			echo '</a>';
		}
//////////////////////REMISE PRO					
		$remisepro = 0;
		$remisepro = ProduitDB::getRemiseProRevendeur($produit->getPromotion(), $produit->getQualite());
		if($remisepro != 0){
			echo '<span style="color: #900;"> - '.$remisepro.'%<span>';
		}
		echo '</div>';					
/////////////////////REFERENCE
		echo '<div id="reference_produit">';
		echo 'Réf :'.$produit->getReference();
		echo '</div>';					
/////////////////////QUALITE					
		echo '<div id="qualite_produit">';
		if ($qualite != ""){
			echo 'Qualité : '.$qualite;
		}
		echo '</div>';					
/////////////////////STOCK
		if (file_exists("public/images/produits/".$produit->getReference().".jpg")){	
			echo '<div id="stock_produit">';
		} else {
			echo '<div id="stock_produit" style="margin-left:134px;">';
		}
		$stock = $produit->getCommande();
		if ($stock == "?" || $produit->getPromotion() == "ooo" || $produit->getPrixHT() == "-1"){
			echo '<img src="'.$basePath.'public/images/stock_2.png" title="Introuvable" alt="Introuvable" />';
		}
		if ($produit->getPromotion() == "oo"){
			echo '<img src="'.$basePath.'public/images/stock_2.png" title="Introuvable" alt="Introuvable" />';
		}
		if (!empty($stock) && $stock != "?" && $produit->getPromotion() != "ooo" && $produit->getPrixHT() != "-1"){
			echo '<img src="'.$basePath.'public/images/stock_3.png" title="En cours de reappro" alt="Introuvable" />';
		}					
		if (empty($stock)){
			if ($produit->getPromotion() == "o" && $produit->getPrixHT() != "-1"){
				echo '<img src="'.$basePath.'public/images/stock_4.png" title="Stock à vérifier" alt="Stock à vérifier" />';
			} else {
				if ($produit->getPrixHT() != "-1"){
					echo '<img src="'.$basePath.'public/images/stock_5.png" title="Normalement en stock" alt="Normalement en stock" />';
				} 
			}
		}
		echo '</div>';					
/////////////////////QUANTITE
		echo '<div id="quantite_produit">Quantité : ';
?>
			<input type="text" maxlength="2" size="2" value="1" style="text-align:center;" id="p<?php echo $this->count; ?>"/>	
			<input type="hidden" value="<?php echo $remisepro;?>" id="r<?php echo $this->count; ?>"/>	
		</div>
		<div id="qté">
			<a style="padding:0px;margin:0px;border:0;" href="javascript:update_qte(<?php echo $this->count; ?>,1);"><img border="0" width="16" height="16" alt="Ajouter" style="margin:0px;" src="<?php echo $basePath; ?>public/images/add.png" /></a>
			<a style="padding:0px;margin:0px;border:0;" href="javascript:update_qte(<?php echo $this->count; ?>,-1);"><img border="0" width="16" height="16" alt="Retirer" style="margin:0px;" src="<?php echo $basePath; ?>public/images/moins.png" /></a>
<?php
		echo '</div>';					
	echo '</div>';
		echo '<div id="separation"></div>';
		echo '<div id="right_produit">';	
////////////////// PRIX HT après remise
		$prixpro = $produit->getPrixHT()-($produit->getPrixHT()*($remisepro/100));					
		echo '<div id="prixttc_produit">';
		if($produit->getPrixHT() > 0){
			printf("%.2f", $prixpro);
			echo "€ HT";
		} else {
			echo 'Sur devis';
		}
		echo '</div>';					
//////////////////// PRIX HT avant remise
		if($remisepro == 0){
			echo '<div id="prixht_produit">';
		} else {
			echo '<div id="prixht_produit" style="text-decoration : line-through;">';
		}
		if($produit->getPrixHT() > 0) {
			if($remisepro == 0){
				echo "Pas de remise !";
			} else {	
				echo "au lieu de ";
				printf("%.2f", $produit->getPrixHT());
				echo '€ HT';
			}
		} else {
			echo 'Sur devis';
		}
		echo '</div>';		
//////////////////// AJOUT PANIER
		echo '<div id="ajouter_produit">';
			echo '<a href="javascript:addtocart(',$produit->getIdentifiant(),',',$this->count,');" title="Ajouter au panier">AJOUTER</a>';
					echo '</div>';
			echo '</div>';
		echo '</div>';
////////////////////////////////////				
		$this->count++;	
	}
private function displayFooterPro() {
		global $basePath;
		echo '<table class="liste">';
		echo '<tr>';
			echo '<th>';
		//affiche les liens des numéros de pages, en précisant qu'on affiche les noms des variable ds l'url
		$this->pagination->printPagesLinks($this->genereLien($this->tri, $this->sens), true);
			echo '</th>';
		echo '</tr>';
		echo '</table>';
		
		echo '<table class="liste">';
		echo '<tr>';
			echo '<th>';
				echo '<p style="text-align:center;">Page n° ',$this->pagination->getNumPage(),' sur ',$this->pagination->getNbPages(),' Affichage des produits [',$this->start,'-',($this->start+$this->len),'] sur ',ProduitDB::getProduitsListCount($this->cat, $this->sscat),'</p>';
			echo '</th>';
		echo '</tr>';
		echo '<tr><th style="font-weight: normal;"><span style="color: #FF6600; font-weight: bold;">NOS</span> (New Old Stock) Neuf de stock ancien | <span style="color: #3399FF; font-weight: bold;">NEUF</span> : neuf de fabrication actuelle | <span style="color: #999900; font-weight: bold;">OCCASION</span> : pieces de démontage déjà utilisées | <span style="color: #00CC33; font-weight: bold;">RENOVÉ</span> : pièces d\'occasion rénovées</th></tr>';
		echo '<tr><th style="font-weight: normal;"><img src="'.$basePath.'public/images/stock_2.png" /> : Introuvable | <img src="'.$basePath.'public/images/stock_3.png" /> : En cours de réappro | <img src="'.$basePath.'public/images/stock_4.png" /> : Stock à vérifier | <img src="'.$basePath.'public/images/stock_5.png" /> : Normalement en stock</th></tr>';
		echo '</table>';
	}

// ----------NEW VERSION LIST PRODUIT 2.0---------
// --------------- ADMINISTREUR ------------------

public function displayTableAdmin() {
		global $basePath;
		$this->displayJavascriptFunctionAdmin();
		if($this->cat == "tous" || $this->cat == "all")
		$this->cat = null;
		echo '<table class="liste">';
			echo '<tr>';
				echo '<th colspan=11>Page ';
					//affiche les liens des numéros de pages, en précisant qu'on affiche les noms des variable ds l'url
					$this->pagination->printPagesLinks($this->genereLien($this->tri, $this->sens), true);	
				echo '</th>';
			echo '</tr>';
		echo '</table>';
		
		//affiche l'entête du tableau
		$this->displayHeaderAdmin();	
		//récupère la liste des produits	
		
		$prodlist = ProduitDB::getProduitsList($this->cat, $this->sscat, $this->start, $this->len,$this->tri,$this->sens);			
		foreach($prodlist as $p) {
			//affiche la ligne avec le produit
			$this->displayLineAdmin($p);
		}	
		
		$this->displayFooterAdmin();	
}

private function displayHeaderAdmin() {
		global $basePath;
		?>

		<tr>
		<?php
		if($this->sens == "ASC")
			$sens = "DESC";
		else
			$sens = "ASC";
			
	
		$imgtri = "";
		
		if($sens == "DESC")
			$imgtri = '<img src="'.$basePath.'public/images/up.png" alt="tri"/>';
		else
			$imgtri = '<img src="'.$basePath.'public/images/down.png" alt="tri"/>';
			
		$imgtri_reference = "";
		$imgtri_groupe = "";
		$imgtri_genre = "";
		$imgtri_description = "";
		$imgtri_prix = "";
		if(isset($_GET['tri'])) {
			if($_GET['tri'] == "referenceproduit")
				$imgtri_reference = $imgtri;
			elseif($_GET['tri'] == "groupe")
				$imgtri_groupe = $imgtri;
			elseif($_GET['tri'] == "genre")
				$imgtri_genre = $imgtri;
			elseif($_GET['tri'] == "descriptionproduit")
				$imgtri_description = $imgtri;
			elseif($_GET['tri'] == "prixproduiteuro")
				$imgtri_prix = $imgtri;
			
		}
		
		global $translate;
	// Debut tableau -----------------------------
	echo '<table class="liste">';
		
		echo '<th style="color:blue;">Trier par :</th>';
		
		echo '<th>',$imgtri_reference,'<a href="',$this->genereLien("referenceproduit",$sens),'">',$translate->_('Référence'),'</a></th>';	
		
		if($this->showgenre == true)
			echo '<th>',$imgtri_genre,'<a href="',$this->genereLien("genre",$sens),'">',$translate->_('Genre'),'</a></th>';
			
		if($this->showgroupe == true)
			echo '<th>',$imgtri_groupe,'<a href="',$this->genereLien("groupe",$sens),'">',$translate->_('Groupe'),'</a></th>';

		echo '<th>',$imgtri_description,'<a href="',$this->genereLien("descriptionproduit",$sens),'">',$translate->_('Désignation'),'</a></th>';
		
		echo '<th>',$imgtri_prix,'<a href="',$this->genereLien("prixproduiteuro",$sens),'">Prix</a></th>';
				
		echo '</tr>';
	echo '<table>';
	}
	
private function displayLineAdmin(Produit $produit) {
		
		global $basePath;
		$comment = $produit->getEmpl_comment();
		
		//AFFICHAGE DESCRIPTION / GENRE / GROUPE
		$genreName = "";
		$groupeName = "";
		if($this->showgenre == true){
			$arrayGenreName = explodeCategorieName($produit->getGenreName());
		if($_SESSION["lang"] == "fr") {
								
			$genreNametr = $arrayGenreName[0];
									
		} else {
		$genreNametr = $arrayGenreName[1];
		}
			$genreName = $genreNametr." - ";
		}
		if($this->showgroupe == true){
								
			$groupeName = $produit->getGroupeName($_SESSION["lang"])." - ";
								
		} 
								
		$description = $genreName.$groupeName.$produit->getDesignation();
		//QUALITE

			$image_color = strtolower($produit->getQualite());
			if ($image_color == "new"){
				$style_border = "border: 1px solid #3399FF;";
				$qualite = "<strong style='color: #3399FF;'>NEUF</strong>";
			} elseif ($image_color == "nos"){
				$style_border = "border: 1px solid #FF6600;";
				$qualite = "<strong style='color: #FF6600;'>NOS</strong>";
			} elseif ($image_color == "rec"){
				$style_border = "border: 1px solid #00CC33;";
				$qualite = "<strong style='color: #00CC33; font-size: 12px;'>RENOVÉ</strong>";
			} elseif ($image_color == "use"){
				$style_border = "border: 1px solid #999900;";
				$qualite = "<strong style='color: #999900; font-size: 12px;'>OCCASION</strong>";
			} else {
				$style_border = "";
				$qualite = "";
			}	
		if ($comment == "ECHSTD"){
		
			if($this->count%2) {
			echo '<div id="produit" style="font-weight: bold;">';
			}else{
			echo '<div id="produit" style="background-color: #fff; font-weight: bold;" >';
			}
		
		} else {
		
			if($this->count%2) {
			echo '<div id="produit">';
			}else{
			echo '<div id="produit"	style="background-color: #fff;">';
			}
		
		}
		if (file_exists("public/images/produits/".$produit->getReference().".jpg")){
			echo '<div id="image_produit">';
					//IMAGE
					echo "<a href=\"".Zend_Registry::get('basePath')."public/images/produits/".$produit->getReference().".jpg?d=".date('Ymd')."\" rel=\"lytebox\" title=\"".$produit->getReference()."\" >";
					echo "<img src=\"".Zend_Registry::get('basePath')."public/images/produits/".$produit->getReference().".jpg?d=".date('Ymd')."\" style=\"".$style_border." box-shadow: 1px 1px 2px 0px rgba(119, 119, 119, 0.8);\" height=\"82\" width=\"110\" />";
					echo "</a>";
			echo '</div>';
			echo '<div id="separation">';
			echo '</div>';
		}
			
		if (file_exists("public/images/produits/".$produit->getReference().".jpg")){	
			echo '<div id="middle_produit">';
		} else {
			echo '<div id="middle_produit" style="width:450px;">';
		}
					//DESIGNATION
					$style_descript =  'style="padding-top: 8px; height: 35px;"';
					
					if (file_exists("public/images/produits/".$produit->getReference().".jpg")){
						if (strlen($description) > 34 && strlen($description) < 68){
							$style_descript = "";
						}				
						if (strlen($description) > 69){
							$style_descript = 'style="padding-top: 8px; height: 35px; font-size:12px;"';
						}						
					} else {
						if (strlen($description) > 40 && strlen($description) < 100){
							$style_descript = 'style="width: 432px;"';
						} if (strlen($description) > 100){
							$style_descript = 'style="padding-top: 8px; height: 35px; width: 432px; font-size:12px;"';
						}
					}
					
					echo '<div id="description_produit" '.$style_descript.'>';
					if ($comment == "ECHSTD"){
						echo '<a href="'.Zend_Registry::get('basePath').'description/'.strtolower($produit->getGenreId()).'/'.$produit->getReference().'">';
						echo $description,' <a href="http://jeep-dodge-gmc.com/smi/index.php?page=aide&paiement=echange_std" target="_blank"><img src="'.$basePath.'public/images/aide.png" title="aide" /></a> ';
						echo '</a>';
					}else{
						echo '<a href="'.Zend_Registry::get('basePath').'description/'.strtolower($produit->getGenreId()).'/'.$produit->getReference().'">';
						echo $description;
						echo '</a>';
					}
					echo '</div>';
					
					//REFERENCE
					echo '<div id="reference_produit">';
					echo 'Réf :'.$produit->getReference();
					echo '</div>';
					//QUALITE
					
					echo '<div id="qualite_produit">';
					if ($qualite != ""){
						echo 'Qualité : '.$qualite;
					}
					echo '</div>';
					//STOCK
					if (file_exists("public/images/produits/".$produit->getReference().".jpg")){	
						echo '<div id="stock_produit">';
					} else {
						echo '<div id="stock_produit" style="margin-left:134px;">';
					}
					$stock = $produit->getCommande();
					if ($stock == "?" || $produit->getPromotion() == "ooo" || $produit->getPrixHT() == "-1"){
					echo '<img src="'.$basePath.'public/images/stock_2.png" title="Introuvable" alt="Introuvable" />';
					}
					if ($produit->getPromotion() == "oo"){
					echo '<img src="'.$basePath.'public/images/stock_2.png" title="Introuvable" alt="Introuvable" />';
					}
					if (!empty($stock) && $stock != "?" && $produit->getPromotion() != "ooo" && $produit->getPrixHT() != "-1"){
					echo '<img src="'.$basePath.'public/images/stock_3.png" title="En cours de reappro" alt="Introuvable" />';
					}
					
					if (empty($stock)){
						if ($produit->getPromotion() == "o" && $produit->getPrixHT() != "-1"){
					echo '<img src="'.$basePath.'public/images/stock_4.png" title="Stock à vérifier" alt="Stock à vérifier" />';
						} else {
							if ($produit->getPrixHT() != "-1"){
						echo '<img src="'.$basePath.'public/images/stock_5.png" title="Normalement en stock" alt="Normalement en stock" />';
							} else {
							
							}
						}
					}
					echo '</div>';
					
					//QUANTITE
					echo '<div id="quantite_produit">Quantité : ';
					?>
					<input type="text" maxlength="2" size="2" value="1" style="text-align:center;" id="p<?php echo $this->count; ?>"/>	
					</div>
					<div id="qté">
						<a style="padding:0px;margin:0px;border:0;" href="javascript:update_qte(<?php echo $this->count; ?>,1);"><img border="0" width="16" height="16" alt="Ajouter" style="margin:0px;" src="<?php echo $basePath; ?>public/images/add.png" /></a>
						<a style="padding:0px;margin:0px;border:0;" href="javascript:update_qte(<?php echo $this->count; ?>,-1);"><img border="0" width="16" height="16" alt="Retirer" style="margin:0px;" src="<?php echo $basePath; ?>public/images/moins.png" /></a>
					<?php
					echo '</div>';
					
			echo '</div>';
			echo '<div id="separation">';
			echo '</div>';
			echo '<div id="right_produit">';
					// PRIX TTC
					echo '<div id="prixttc_produit">';
						if($produit->getPrixHT() > 0)
						{
							printf("%.2f", calculPrixTTC($produit->getPrixHT()));
							echo "€ TTC";
						}
						else
						{
							echo 'Sur devis';
						}
					echo '</div>';
					// PRIX HT
					echo '<div id="prixht_produit">';
							if($produit->getPrixHT() > 0) {
								printf("%.2f", $produit->getPrixHT());
								echo '€ HT';
							} else {
								echo 'Sur devis';
							}
					echo '</div>';
					// AJOUT PANIER
					if($produit->getPrixHT() > 0) {
					echo '<div id="ajouter_produit">';
						echo '<a id="ajoutpanier'.$produit->getIdentifiant().'" onclick="javascript:addtocart(',$produit->getIdentifiant(),',',$this->count,');" href="#oModalajout'.$produit->getIdentifiant().'" title="Ajouter au panier">AJOUTER</a>';
					echo '</div>';
					echo '<span id="txtHint"></span>';
					echo omdalpanier($produit->getReference, $produit->getDesignation, $produit->getPrixHT, $produit->getGenreName, $produit->getGroupeId);
					// SUR DEVIS
					} else {
						echo '<div id="bouton_devis">';
						echo '<a href="#oModal'.$produit->getIdentifiant().'" title="demande de devis">DEVIS</a>';
						echo '</div>';								
										
?>
										<div id="oModal<?php echo $produit->getIdentifiant(); ?>" class="oModal">
											  <div>
												<header>  
												   <h2><strong>Demande de devis</strong></h2>
												   <h2><?php echo $produit->getReference()." - ".$produit->getDesignation(); ?></h2>
												   <a href="#fermer" title="Fermer la fenêtre" class="droite"><img src="<?php echo $basePath.'public/images/cross.png'; ?>" alt="SUPPRIMER" /></a>
												 </header>
												 <section>
																				

													<p style="margin-left:120px;">Merci de remplir le formulaire ci-dessous</P><br />
															<form style="margin-left:100px;" action="<?php echo $basePath;?>client/envoyer_devis.php" name="demandedevis" method="POST">
																<ul>
																	<li>Nom</li>
																	<li style="list-style:none; margin-bottom:10px;"><input type="text" id="nomdevis" name="nomdevis" value="" size="45" maxlength="100" /></li>
																	<script type="text/javascript">
																			var nomdevis = new LiveValidation('nomdevis');
																			nomdevis.add( Validate.Presence );
																	</script>
																</ul>
																	<input type="hidden" size="45" name="produitdevis" value="<?php echo $produit->getReference(); ?> | <?php echo $produit->getDesignation(); ?>" />
																<ul>
																	<li>Pr&eacute;nom</li>
																	<li style="list-style:none; margin-bottom:10px;"><input type="text" id="prenomdevis" name="prenomdevis" value="" size="45" maxlength="100" /></li>
																	<script type="text/javascript">
																			var prenomdevis = new LiveValidation('prenomdevis');
																			prenomdevis.add( Validate.Presence );
																	</script>
																</ul>
																<ul>
																	<li>T&eacute;l&eacute;phone</li>
																	<li style="list-style:none; margin-bottom:10px;"><input type="text" id="telephonedevis" name="telephonedevis" value="" size="45" maxlength="20" /></li>
																	<script type="text/javascript">
																			var telephonedevis = new LiveValidation('telephonedevis');
																			telephonedevis.add( Validate.Presence );
																	</script>
																</ul>
																<ul>
																	<li>Adresse e-mail</li>
																	<li style="list-style:none; margin-bottom:10px;"><input type="text" id="emaildevis" name="emaildevis" value="" size="45" maxlength="100" /></li>
																	<script type="text/javascript">
																			var emaildevis = new LiveValidation('emaildevis');
																			emaildevis.add( Validate.Presence );
																			emaildevis.add( Validate.Email );
																	</script>
																</ul>
																<ul>
																	<li style="margin-bottom:10px;">Quantit&eacute; : 
																	<input type="text" id="qtedevis" size="5" name="qtedevis" style="text-align:center;" value="1" /></li>
																	<script type="text/javascript">
																			var qtedevis = new LiveValidation('qtedevis');
																			qtedevis.add( Validate.Presence );
																	</script>
																</ul>
																<ul>
																	<li>Commentaire</li>
																	<li style="list-style:none; margin-bottom:10px; "><textarea name="commdevis" cols="35" rows="5" onKeyDown="limit2();" onKeyUp="limi2t();"></textarea>
																	<br />Reste <input readonly="readonly" type="text" name="reste" size="1" maxlength="3" value="500"> caract&egrave;res.</li>
																</ul>
																<?php
							// Genere un captcha
								$characts = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';	
								$characts .= '1234567890'; 
								$code_aleatoire = ''; 

								for($i=0;$i < 5;$i++)    //10 est le nombre de caractères
								{ 
									$code_aleatoire .= substr($characts,rand()%(strlen($characts)),1); 
								}
							?>
																<ul>
																	<li>Saisir le code suivant : <strong><?php echo $code_aleatoire; ?></strong></li>
																	<input type="hidden" name="code" id="code" value="<?php echo $code_aleatoire; ?>" />
																	<li style="list-style:none;"><input type="text" name="code2" id="code2" value="" /></li>
																	<script type="text/javascript">
																		var code2 = new LiveValidation('code2');
																		code2.add( Validate.Confirmation, { match: 'code' } );
																	</script>
																</ul>
												 <section>
												 <footer class="cf">
													<input style="padding:6px 0 6px 0; font:bold 13px Arial; background:#478bf9;color:#fff; border-radius:2px; width:100px; border:none;" class="btn droite" type="submit" value="Valider" />	
													</form>
												 </footer>

										</div>
									</div>
							<?php
									
											unset($_SESSION['refprodadd']);
											unset($_SESSION['designprodadd']);
											unset($_SESSION['qteprodadd']);
											unset($_SESSION['prodsdevis']);
											

					}
					
						
			echo '</div>';
		echo '</div>';
				
		$this->count++;
		
	}
private function displayFooterAdmin() {
		global $basePath;
		echo '<table class="liste">';
		echo '<tr>';
			echo '<th>';
		//affiche les liens des numéros de pages, en précisant qu'on affiche les noms des variable ds l'url
		$this->pagination->printPagesLinks($this->genereLien($this->tri, $this->sens), true);
			echo '</th>';
		echo '</tr>';
		echo '</table>';
		
		echo '<table class="liste">';
		echo '<tr>';
			echo '<th>';
				echo '<p style="text-align:center;">Page n° ',$this->pagination->getNumPage(),' sur ',$this->pagination->getNbPages(),' Affichage des produits [',$this->start,'-',($this->start+$this->len),'] sur ',ProduitDB::getProduitsListCount($this->cat, $this->sscat),'</p>';
			echo '</th>';
		echo '</tr>';
		echo '<tr><th style="font-weight: normal;"><span style="color: #FF6600; font-weight: bold;">NOS</span> (New Old Stock) Neuf de stock ancien | <span style="color: #3399FF; font-weight: bold;">NEUF</span> : neuf de fabrication actuelle | <span style="color: #999900; font-weight: bold;">OCCASION</span> : pieces de démontage déjà utilisées | <span style="color: #00CC33; font-weight: bold;">RENOVÉ</span> : pièces d\'occasion rénovées</th></tr>';
		echo '<tr><th style="font-weight: normal;"><img src="'.$basePath.'public/images/stock_2.png" /> : Introuvable | <img src="'.$basePath.'public/images/stock_3.png" /> : En cours de réappro | <img src="'.$basePath.'public/images/stock_4.png" /> : Stock à vérifier | <img src="'.$basePath.'public/images/stock_5.png" /> : Normalement en stock</th></tr>';
		echo '</table>';
	}
}
?>