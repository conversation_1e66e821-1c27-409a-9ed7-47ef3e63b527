<?php
/**
 * classe de gestion produit
 *
 */
class produit {
	/**
	 * réference du produit...
	 *
	 * @var varchar
	 */
	private $reference;
	/**
	 * désignation du produit...
	 *
	 * @var text
	 */
	private $designation;
	/**
	 *  prix HT du produit...
	 *
	 * @var decimal
	 */
	private $prixHT;
	/**
	 * promotion actuelle sur le produit...
	 *
	 * @var booleen
	 */
	private $promotion;
	/**
	 * nom du genre (catégorie) auquel appartient le produit
	 *
	 * @var string
	 */
	private $genre;
	/**
	 * nom du groupe (sous catégorie) auquel appartient le produit
	 *
	 * @var string
	 */
	private $groupe;
	/**
	 * identifiant du genre
	 * @var int
	 */
	private $genreid;
	/**
	 * identifiant du groupe
	 * @var int
	 */
	private $groupeid;
	/**
	 * identifiant du produit dans la bdd
	 *
	 * @var int
	 */
	private $identifiant;
	
	/**
	 * Image du produit
	 */
	private $image;
	/**
	 * retourne la reférence du produit...
	 *
	 * @return varchar
	 */
	public function getReference() {
		return $this->reference;
	}
	/**
	 * retourne la désignation du produit...
	 *
	 * @return text
	 */
	public function getDesignation() {
		return $this->designation;
	}
	/**
	 * retourne le prix HT du produit...
	 *
	 * @return decimal
	 */
	public function getPrixHT() {
		return $this->prixHT;
	}
	/**
	 * définit l'identifiant du groupe
	 * @param int 
	 */
	public function setGroupeId($id) {
		$this->groupeid = $id;
	}
	/**
	 * définit l'identifiant du genre
	 * @param int 
	 */
	public function setGenreId($id) {
		$this->genreid = $id;
	}
	/**
	 * renvoi l'identifiant du groupe
	 * @return int Renvoi l'identifiant
	 */
	public function getGroupeId() {
		return $this->groupeid;
	}
	/**
	 * renvoi l'identifiant du genre
	 * @return Renvoi l'identifiant
	 */
	public function getGenreId() {
		return $this->genreid;
	}
	/**
	 * retourne vrai si le produits est actuellement en promotion...
	 *
	 * @return booleen
	 */
	public function isPromotion() {
		return $this->promotion;
	}
	/**
	 * retourne le groupe du produit
	 *
	 * @return string
	 */
	public function getGroupeName($langue = "fr") {
		$arrayGroupeName = explodeCategorieName($this->groupe);
		if($langue == "fr") {
			return $arrayGroupeName[0];
		}
		else {
			return strtoupper($arrayGroupeName[1]);
		}
		return $this->groupe;
	}
	/**
	 * retourne le genre du produit
	 *
	 * @return string
	 */
	public function getGenreName() {
		return $this->genre;
	}
	/**
	 * définit le nom du groupe du produit
	 *
	 * @param string
	 */
	public function setGroupeName($groupe) {
		$this->groupe = $groupe;
	}
	/**
	 * définit le nom du genre du produit
	 *
	 * @param string
	 */
	public function setGenreName($genre) {
		$this->genre = $genre;
	}
	/**
	 * retourne l'identifiant du produit dans la bdd
	 *
	 * @return int
	 */
	public function getIdentifiant() {
		return $this->identifiant;
	}
	/**
	 * définit l'identifiant du produit dans la bdd
	 *
	 * @param int $id
	 */
	public function setIdentifiant($id) {
		$this->identifiant = $id;
	}
	/**
	 * modifie l'attribut réference...
	 *
	 * @param varchar $reference
	 */
	public function setReference($reference) {
		$this->reference = $reference;
	}
	/**
	 * modifier la désignation du produit...
	 *
	 * @param text $designation
	 */
	public function setDesignation($designation) {
		$this->designation = $designation;
	}
	/**
	 * modifie le prix du produit...
	 *
	 * @param decimal $prixHT
	 */
	public function setPrixHT($prixHT) {
		$this->prixHT = $prixHT;
	}
	/**
	 * modifie si le produit est actuellement en promotion...
	 *
	 * @param booleen $promotion
	 */
	public function setPromotion($promotion) {
		$this->promotion = $promotion;
	}
/**
	 * renvoi si le produit est actuellement en promotion...
	 *
	 * @param booleen $promotion
	 */
	public function getPromotion() {
		return $this->promotion;
	}
	/**
	 * modifie le genre du produit
	 *
	 * @param string $genre
	 */
	public function setGenre($genre) {
		$this->genre = $genre;
	}
	/**
	 * modifie le groupe du produit
	 *
	 * @param string $groupe
	 */
	public function setGroupe($groupe) {
		$this->groupe = $groupe;
	}
	
	public function setImage($image){
		$this->image = $image;
	}
	
	public function getImage(){
		return $this->image;
	}
	
	public function __construct($id = 0) {
		$this->setGroupe(null);
		$this->setGenre(null);
		if($id>0)
		{
			//on a un identifiant, donc on charge le produit à partir de la bdd
			$temp = produitDB::getProduitById($id);
			$this->setIdentifiant($temp->getIdentifiant());
			$this->setDesignation($temp->getDesignation());
			$this->setReference($temp->getReference());
			$this->setPrixHT($temp->getPrixHT());
			$this->setImage($temp->getImage());
		}
			
	}

}
/**
 * classe d'accés à la base de données...
 *
 */
class produitDB {
	/**
	 * Renvoi le nom d'un groupe par rapport à son id
	 */
	public static function getGroupeNameById($id)
	{
		$db = Zend_registry::get("database");

		$sql = "SELECT groupe FROM produits WHERE idgroupe=:id LIMIT 1;";
			
		$stmt = $db->prepare($sql);
		$stmt->bindParam(":id", $id);
		$stmt->execute();
		$data = $stmt->fetch();

		return $data['groupe'];	
	}
	/**
	 * Renvoi l'identifiant d'un groupe par rapport à son nom
	 * @param string $nom Nom du groupe
	 */
	public static function getGoupeIdByName($nom) {
		$db = Zend_registry::get("database");
		

		$sql = "SELECT idgroupe FROM produits WHERE groupe like (:nom) LIMIT 1;";
			
		$stmt = $db->prepare($sql);
		$stmt->bindParam(":nom", $nom);
		$stmt->execute();
		$data = $stmt->fetch();

		return $data['idgroupe'];	
	}
/**
	 * Renvoi la liste des noms de tous les genres
	 * 
	 * @param 
	 * @return array Liste de string
	 */
	public static function getGenresNames() {
		
		$namelist  = array(); //liste des noms des catégories parentes
		$db = Zend_registry::get("database");
		

		$sql = "SELECT DISTINCT genre FROM produits;";
			
		$stmt = $db->prepare($sql);
		$stmt->execute();
		$data = $stmt->fetchAll();

		foreach($data as $ligne) {
			array_push($namelist, $ligne['genre']);
		}

		return $namelist;	
	}
	/**
	 * Renvoi la liste des noms de tous les groupes d'un genre précis
	 * 
	 * @param string $id L'identifiant du genre
	 * @return array Liste de string
	 */
	public static function getGroupesNames($genre_id) {
		
		$namelist  = array(); //liste des groupes
		$db = Zend_registry::get("database");
		

		$sql = "SELECT DISTINCT groupe FROM produits WHERE genre Like (:genre);";
			
		$stmt = $db->prepare($sql);
		$stmt->bindParam(":genre", $genre_id);
		$stmt->execute();
		$data = $stmt->fetchAll();

		foreach($data as $ligne) {
			array_push($namelist, $ligne['groupe']);
		}

		return $namelist;	
	}
	/**
	 * Renvoi le nombre de résultats pour une requete
	 * 
	 */
	public static function getProduitsListCount($genre, $groupe)
	{
		$sql = "SELECT count(*) FROM produits ";
		
		if(!is_null($genre))
		{
			$sql .= "WHERE genre Like (:genre) ";
		}
		if(!is_null($groupe))
		{
			$sql .= "AND idgroupe=:idgroupe ";
		}

		
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		
		if(!is_null($genre))
			$stmt->bindParam(':genre',$genre);
			
		if(!is_null($groupe))
			$stmt->bindParam(':idgroupe',$groupe);
	
		$stmt->execute();
		$data = $stmt->fetchColumn();
		
		return $data;
	}
	/**
	 * Renvoi une liste de produits
	 * @param string $genre Identifiant du genre des produits
	 * @param string $groupe Identifiant du groupe des produits
	 * @param int $start Début de l'interval de résultats de la requete
	 * @param int $len Taille de l'interval de résultats de la requete
	 * @param string $sort Colonne sur laquelle trier les résultats
	 * @param string $sens Sens du tri ASC ou DESC
	 */
	public static function getProduitsList($genre, $groupe, $start, $len, $sort, $sens)
	{
		$prods = array();
		
		$sql = "SELECT idproduit, descriptionproduit, referenceproduit, prixproduiteuro, promo, idgroupe, groupe, genre,image FROM produits ";
	/*	
		$sql .= "WHERE genre Like ('".$genre."') AND idgroupe='".$groupe."' ORDER BY `".$sort."` ASC LIMIT ".$start.", ".$len.";";
$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);		
*/
		if(!is_null($genre))
		{
			$sql .= "WHERE genre Like ('".$genre."') ";
		}
		if(!is_null($groupe))
		{
			$sql .= "AND idgroupe='".$groupe."' ";
		}
		if(!is_null($sort))
		{
			$sql .= "ORDER BY `".$sort."` ".$sens." ";
		}
		$sql .= "LIMIT ".$start.", ".$len;

		
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);		

		$stmt->execute();
		$data = $stmt->fetchAll();
		
		foreach($data as $ligne) {
			
			$prod = new Produit();
			$prod->setDesignation($ligne['descriptionproduit']);
			$prod->setPrixHT($ligne['prixproduiteuro']);
			$prod->setReference($ligne['referenceproduit']);
			$prod->setIdentifiant($ligne['idproduit']);
			
			$prod->setGroupeId($ligne['idgroupe']);
			$prod->setGroupeName($ligne['groupe']);
			$prod->setGenreId($ligne['genre']);
			$prod->setGenreName($ligne['genre']);
			
			$prod->setPromotion($ligne['promo']);
			$prod->setImage($ligne['image']);
			array_push($prods, $prod);
			
		}
		
		return $prods;
	}
	/**
	 * renvoi un produit à partir de son identifiant
	 * 
	 * @param int $id Identifiant du produit dans la bdd
	 */
	function getProduitById($id) {
		
		$sql = "SELECT * FROM produits WHERE idproduit=:id;";

		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		
		$stmt->bindParam(':id',$id);
		
		$stmt->execute();
		$data = $stmt->fetch();
		
		//print_r($data);
		if(count($data) != 0){
			
			$prod = new Produit();
			$prod->setDesignation($data['descriptionproduit']);
			$prod->setPrixHT($data['prixproduiteuro']);
			$prod->setReference($data['referenceproduit']);
			$prod->setIdentifiant($data['idproduit']);
			
			$prod->setGroupeId($data['idgroupe']);
			$prod->setGroupeName($data['groupe']);
			$prod->setGenreId($data['genre']);
			$prod->setGenreName($data['genre']);
			
			$prod->setPromotion($data['promo']);
			$prod->setImage($data['image']);
			return $prod;
		}
		
		return null;
	}
	/**
	* Sauvegarde un produit dans la base de données
	*
	* @param Produit $produit Produit a sauvegarder
	* @param boolean $ajax Si on appelle cette méthode depuis un script AJAX ou pas
	* @return Renvoi le nombres de produits ajoutés dans la BDD
	**/
	function saveProduit($produit) {
	
		// Enregistrement dans la BDD avec test si la système à réussit à se connecter et à enregistrer les données
		// Connexion à la BDD
		$db = Zend_registry::get("database");
		
		// Préparation de la requête pour vérifier si le produit existe déjà
		$stmt = $db->prepare("SELECT * FROM produits WHERE descriptionproduit Like (:desc) AND referenceproduit Like (:ref) AND idgroupe Like (:idgroupe) AND genre Like (:genre);");
		
		$stmt->bindParam(':desc', $produit->getDesignation());
		$stmt->bindParam(':ref', $produit->getReference());
		$stmt->bindParam(':idgroupe', $produit->getGroupeId());
		$stmt->bindParam(':genre', $produit->getGenreId()); //l'identifiant du genre = le nom du genre (pas de diff)

		$stmt->execute();
		//on regarde le résultat
		if ($stmt->rowCount() == 0)
		{
			
			//le produit n'a pas été trouvé, réparation de la requête
			$stmt = $db->prepare("insert into produits (descriptionproduit, referenceproduit, prixproduiteuro, promo, idgroupe, groupe, genre, image) VALUES (:desc, :ref, :prix, :promo, :idgroupe, :groupe, :genre, :image)");
			
			$desc = utf8_encode($produit->getDesignation());
			$ref = utf8_encode($produit->getReference());
			$prix = $produit->getPrixHT();
			$idgroupe = $produit->getGroupeId();
			$groupe = utf8_encode($produit->getGroupeName());
			$genre = utf8_encode($produit->getGenreName());
			$image = utf8_encode($produit->getImage());
			$stmt->bindParam(':desc',$desc);
			$stmt->bindParam(':ref', $ref);
			$stmt->bindParam(':prix', $prix);	
			$stmt->bindParam(':idgroupe', $idgroupe);
			$stmt->bindParam(':groupe', $groupe);
			$stmt->bindParam(':genre', $genre);
			$stmt->bindParam(':image',$image);
			
			$bool = $produit->getPromotion();
			if($bool):
				$promo = '1';
			else:
				$promo = '0';
			endif;			
			$stmt->bindParam(':promo', $promo);			

			$stmt->execute();
			// On vérifie si on a bien une ligne de résultat : donc que tout c'est bien passé.
			// Si oui, on affiche un message de confirmation d'inscription
			if ($stmt->rowCount() == 1)
			{
				return 1;
			}
			else
			{
				
				echo '<tr><td></td><td></td><td></td><td>Impossible d\'enregistrer le produit : ',$produit->getDesignation(),'.</td></tr>';
			}
		}
		else
		{
			list($genrefr, $en) = explodeCategorieName($produit->getGenreName());
			list($groupefr, $en) = explodeCategorieName($produit->getGroupeName());
			//ici l'identifiant reprsente le numéro de la ligne où se trouve le produit dans le fichier
			echo '<tr>',$produit->getIdentifiant(),'<td>',$genrefr,'</td>';
			echo '<td>',$groupefr,'</td>'; 
			echo '<td>Le produit : ',$produit->getDesignation(),' est déjà présent dans la base de données.</td></tr>';
		}
		return 0;
	}
	/**
	* Lit chaque ligne d'un fichier et crée la liste des produits
	* Renvoi la liste des produits, et la liste des produits en double dans le fichier
	*
	* @param file $file
	* @param int $numcol_reference Numéro de la colonne contenant la référence
	* @param int $numcol_designation Numéro de la colonne contenant la désignation
	* @param int $numcol_prixht Numéro de la colonne contenant le prix HT
	* @param int $numcol_souscategorie Numéro de la colonne contenant la sous catégorie du produit
	* @param int $numcol_categorie Numéro de la colonne contenant la catégorie du produit
	* @param int $numcol_promo Numéro de la colonne contenant le nombres "d'étoiles", qui spécifie si le produit est en promo ou pas
	* @return Renvoi un tableau de produits, et tableau des genre, et un talbeau des groupes
	**/
	function readProduitsFromFile($file, $numcol_reference, $numcol_designation, $numcol_prixht, $numcol_souscategorie, $numcol_categorie, $numcol_promo) {
		
		$tabproduits = array(); //liste des produits
		$tabproduits_error = array(); //liste des produits présent plus d'une fois dans la base 
	
		$tabgenre = array();	//tableau des identifiants des genres
		$tabgroupe = array();	//tableau des identifiants des groupes
		
		$handle = fopen('./temp/'.$file, "r");
		//calcul le plus grand des numéros de colonnes 
		$maxcol = max($numcol_reference, $numcol_designation, $numcol_prixht, $numcol_souscategorie, $numcol_categorie, $numcol_promo);
		$row=0;
		while (($data = fgetcsv($handle, 1000, ";")) !== FALSE) {
		    $num = count($data); //nombre de champs sur la ligne
		    $row++;
		    
			//si le nombre de champs sur la ligne est inférieur au plus grand des numéros de colonnes donnés en paramètres, la ligne est invalide
			if($num < $maxcol)
			{
				echo '<tr><td>',$row,'</td><td></td><td></td><td>Impossible d\'importer le produit !</td></tr>';
			}
			else
			{
				$promo = $data[$numcol_promo-1];				//récupère si le produit est en promo ou pas
				//si c'est un produit en promotion
				
				if($promo == "xxx")
					$promo = true;
				else
					$promo = false;
				
				$groupe = $data[$numcol_souscategorie-1]; //nom de la sous catégorie du produit
				$genre = $data[$numcol_categorie-1]; //nom de la catégorie du produit;
				//enlève les espaces avant et après
				$genre = trim($genre);
				
				//l'identifiant du genre correspond à son nom (on a pas plus d'info !)
				$genreid = $genre;
				$genrename = $genre;
				
				if($groupe!="")
				{
					//on découpe l'identifiant du groupe ( ex : '01 00' )
					$groupeid = '';
					$c=0;

					while($c<strlen($groupe) && (is_numeric($groupe[$c]) || $groupe[$c]==' ')) {
						
						$groupeid .= $groupe[$c];
						$c++;
					}
					//enregistre le nom du groupe
					$groupename = trim(substr($groupe, $c));
					//enlève les espaces dans l'identifiant du groupe
					//str_replace("\xA0", "", $groupeid);
					$groupeid = str_replace(" ", "", $groupeid);
				}
				else
				{
					$groupeid = "";
					$groupename = "";
				}
				
				/////////////PARTIE COMPTAGE DES GENRES ///////////////
				$t = 0;
				while($t<count($tabgenre) && $tabgenre[$t]!=$genreid)
					$t++;
					
				//on a pas trouvé le genre, donc on l'ajoute à la liste
				if($t==count($tabgenre))
					array_push($tabgenre, $genreid);
					
				//////////////////////////////////////////////////////
				////////////PARTIE COMPTAGE DES GROUPES///////////////
				$t = 0;
				while($t<count($tabgroupe) && $tabgroupe[$t]!=$groupeid)
					$t++;
					
				//on a pas trouvé le genre, donc on l'ajoute à la liste
				if($t==count($tabgroupe))
					array_push($tabgroupe, $groupeid);
				////////////////////////////////////////////////////////
					

				
				//on récupère le prix tel qu'il est dans le fichier
				$prix_base = $data[$numcol_prixht-1];
				//on remplace les virgules par des . pour que ce soit un type numérique
				$prix_base = str_replace(',', '.', $prix_base);	
				//on vérifie que c'est un type numérique
				if(is_numeric($prix_base))
				{
					//c'est le cas, on connait le prix
					//elève les espaces dans le prix du produits
					//ex : 3 000,00 remplacé par 3000,00
					$prix = str_replace("\xA0", '', $prix_base);
				} 
				else 
				{
					//on ne connait pas le prix, 'sur devis'
					//on ne connait pas le prix, on met -1 pour dire que le produit est 'sur devis'
					$prix = -1.0;				
				}
				$designation =$data[$numcol_designation-1];
				$reference =$data[$numcol_reference-1];
				
				
				$produit = new Produit();									//crée un nouveau produit
				$produit->setIdentifiant($row);								//identifiant provisoir, ici le numéro de la ligne
				$produit->setReference($reference); 		//définit la référence produit
				$produit->setDesignation($designation); 	//définit la désignation
				$produit->setPrixHT($prix); 								//définit le prix
				$produit->setGroupeName($groupename);
				$produit->setGenreName($genrename);
				$produit->setGroupeId($groupeid);
				$produit->setGenreId($genreid);
				$produit->setPromotion($promo);		
				$produit->setImage('');	
				
				//ajoute le produit à la liste des produits
				array_push($tabproduits, $produit);
					

			}
		    
		}
		fclose($handle);

		return array($tabproduits,$tabgenre,$tabgroupe);
	}
	/**
	 * Lit toute la base de données des produits
	 * @return array Renvoi une liste de catégories contenant des sous-catégorires, contenant des produits
	 */
	function readProduitsFromDataBase() {
		$prods = array();
		
		$sql = "SELECT idproduit,descriptionproduit,referenceproduit,prixproduiteuro,promo,idgroupe,groupe,genre,image FROM produits ";
		
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		

		$stmt->execute();
		$data = $stmt->fetchAll();
		
		foreach($data as $ligne) {
			
			$prod = new Produit();
			$prod->setDesignation($ligne['descriptionproduit']);
			$prod->setPrixHT($ligne['prixproduiteuro']);
			$prod->setReference($ligne['referenceproduit']);
			$prod->setIdentifiant($ligne['idproduit']);
			
			$prod->setGroupeId($ligne['idgroupe']);
			$prod->setGroupeName($ligne['groupe']);
			$prod->setGenreId($ligne['genre']);
			$prod->setGenreName($ligne['genre']);
			
			$prod->setPromotion($ligne['promo']);
			$prod->setImage($ligne['image']);
			array_push($prods, $prod);
			
		}
		
		return $prods;

	}
	/**
	 * supprime un produit de la base de donnée
	 * @param Produit $prod Produit à supprimer
	 * @return Renvoi vrai si suppression réussi, sinon faux
	 */
	function removeProduit(Produit $prod) {
		
		$sql ="DELETE FROM produits WHERE idproduit=:id;";
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		$stmt->bindParam(':id',$prod->getIdentifiant());
		$stmt->execute();	
		if ($stmt->rowCount() == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
/**
	 * modifie un produit de la base de donnée
	 * @param Produit $prod Produit à modifier
	 * @return Renvoi vrai si modification réussi, sinon faux
	 */
	function modifProduit(Produit $prod) {
		
		$sql ="UPDATE produits SET descriptionproduit=:desc, referenceproduit=:ref, prixproduiteuro=:prix, image = :image WHERE idproduit=:id;";
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		
		$desc = $prod->getDesignation();
		$ref = $prod->getReference();
		$prix = $prod->getPrixHT();
	
		$image = $prod->getImage();
		$stmt->bindParam(':desc',$desc);
		$stmt->bindParam(':ref', $ref);
		$stmt->bindParam(':prix', $prix);	
		$stmt->bindParam(':image',$image);
		$stmt->bindParam(':id',$prod->getIdentifiant());

		$stmt->execute();	
		
		if ($stmt->rowCount() == 1)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
	/**
	 * Vide la table complète
	 */
	function truncateTable() {
		$sql ="TRUNCATE TABLE produits;";
		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		$stmt->execute();	
	}
}

/**
 * Classe d'affichage des informations d'un produit
 * Affiche sous forme d'un tableau, soi pour les admins, soi pour les clients
 * 
 * admins :	-affichage d'un lien pour supprimer ou modifier le produit
 * 			-affiche d'une colonne genre et groupe
 * clients :-affichage d'un lien pour ajouter le produit au panier
 * 			-affiche possible de la colonne groupe
 */
class produitDisplay {
	
	private $cat;
	private $sscat;
	private $pagination;
	private $showgroupe;
	private $showgenre;
	private $typelistclient;
	private $start;
	private $len;
	private $tri;
	private $typelien;
	private $lien;
	
	public static $PAGE_NORMAL = 0;
	public static $PAGE_AUTRE = 1;
	public static $PAGE_TOUS = 2;
	/**
	 * Nombre d'élément dans le tableau
	 *
	 * @var int
	 */
	private $count;
	/**
	 * Prépare l'affiche du tableau 
	 *
	 * @param string $cat Catégorie (genre) à afficher (peut être null si on affiche un groupe)
	 * @param string $sscat Catégorie (groupe) à afficher (peut être null si on affiche un genre)
	 * @param integer $typelien Type des liens à afficher (autres, tous ou alors [jeep,dodge,gmc])
	 */
	function __construct($cat, $sscat, $typelien) {
				
		$this->cat = $cat;
		$this->sscat = $sscat;		
		//valeurs pas défaut
		$this->showgroupe = false;
		$this->typelistclient = false;
		$this->pagination = null;
		$this->filtre = null;
		$this->count = 0;
		$this->start = null;
		$this->len = null;
		$this->tri = null;
		$this->sens = "ASC";
		$this->typelien = $typelien;
		
	}
	
	public function showGroupe($show) { $this->showgroupe = $show; }
	public function showGenre($show) { $this->showgenre = $show; }
	public function isClientList($show) { $this->typelistclient = $show; }
	public function setPagination($page) { $this->pagination = $page; }
	public function setInterval($start, $len) {$this->start = $start;$this->len = $len;}
	public function setTri($tri) { $this->tri = $tri; }
	public function setSens($sens) { $this->sens = $sens; }
	public function setTypeLien($type) { $this->typelien = $type;}
	
	/**
	 * Génère la base des liens, utilisé dans les liens entres les pages en footer, et les liens sur les noms des colonnes pour trier header
	 *
	 */
	private function genereLien($tri, $sens)
	{
		global $basePath;
		//si le genre est JEEP DODGE ou GMC, il doit apparaitre en minuscule dans l'url
		switch($this->typelien)
		{
			case self::$PAGE_NORMAL:
				$cat = url_encode(strtolower($this->cat));
				$plus = $cat;
				break;
			case self::$PAGE_AUTRE:
				$cat = url_encode($this->cat);		//on ne met pas en minuscule le nom du genre!
				$plus = 'autres';
				break;
			case self::$PAGE_TOUS:
				$cat = url_encode($this->cat);		//on ne met pas en minuscule le nom du genre!
				$plus = 'tous';
				break;
			default:
				$cat = url_encode($this->cat);		//on ne met pas en minuscule le nom du genre!
				break;
		}
		$sscat = url_encode($this->sscat);
		if($this->typelistclient) //page de type client
		{
			if($this->sscat != null)
			{
				if($this->typelien == self::$PAGE_NORMAL)
					$this->lien = $basePath.'index.php?categorie='.$plus.'&subcat='.$sscat;
				if($this->typelien == self::$PAGE_AUTRE)
					$this->lien = $basePath.'index.php?categorie='.$plus.'&cat='.$cat.'&subcat='.$sscat;
				if($this->typelien == self::$PAGE_TOUS)
					$this->lien = $basePath.'index.php?categorie='.$plus.'&filtre_cat='.$cat.'&filtre_sscat='.$sscat;
			}
			elseif($this->cat != null)
			{
				if($this->typelien == self::$PAGE_NORMAL)
					$this->lien = $basePath.'index.php?categorie='.$plus;
				if($this->typelien == self::$PAGE_AUTRE)
					$this->lien = $basePath.'index.php?categorie='.$plus.'&cat='.$cat;
				if($this->typelien == self::$PAGE_TOUS)
					$this->lien = $basePath.'index.php?categorie='.$plus.'&filtre_cat='.$cat;	
			}
			elseif($this->cat == null)
			{
				if($this->typelien == self::$PAGE_NORMAL)
					$this->lien = $basePath.'index.php';
				if($this->typelien == self::$PAGE_AUTRE)
					$this->lien = $basePath.'index.php?categorie='.$plus;
				if($this->typelien == self::$PAGE_TOUS)
					$this->lien = $basePath.'index.php?categorie='.$plus;	
			}
				
			//un tri est définit
			if($tri != null)
				$this->lien .= '&tri='.$tri.'&sens='.$sens;
			
		}
		else //page de type admin
		{
			if($this->sscat != null)
				$this->lien = $basePath.'index.php?page=admin&action=gererprod&filtre_cat='.$cat.'&filtre_sscat='.$sscat;
			elseif($this->cat != null)
				$this->lien = $basePath.'index.php?page=admin&action=gererprod&filtre_cat='.$cat;
			elseif($this->cat == null)
				$this->lien = $basePath.'index.php?page=admin&action=gererprod';
				
			if($tri != null)
				$this->lien .= '&tri='.$tri.'&sens='.$sens;
		}
		
		return $this->lien;
	}
	/**
	 * Affiche le pied du tableau avec la pagination en fonction des filtres
	 */
	private function displayFooter() {
		global $basePath;
		
		echo '<tr><th colspan=10>';

		//affiche les liens des numéros de pages, en précisant qu'on affiche les noms des variable ds l'url
		$this->pagination->printPagesLinks($this->genereLien($this->tri, $this->sens), true);
			
		echo '</th></tr>';
	}
	/**
	 * Affiche l'entête du tableau avec les bonnes colonnes
	 */
	private function displayHeader() {
		global $basePath;
		?>

		<tr>
		<?php
		if($this->sens == "ASC")
			$sens = "DESC";
		else
			$sens = "ASC";
			
	
		$imgtri = "";
		
		if($sens == "DESC")
			$imgtri = '<img src="'.$basePath.'public/images/up.png" alt="tri"/>';
		else
			$imgtri = '<img src="'.$basePath.'public/images/down.png" alt="tri"/>';
			
		$imgtri_reference = "";
		$imgtri_groupe = "";
		$imgtri_genre = "";
		$imgtri_description = "";
		$imgtri_prix = "";
		if(isset($_GET['tri'])) {
			if($_GET['tri'] == "referenceproduit")
				$imgtri_reference = $imgtri;
			elseif($_GET['tri'] == "groupe")
				$imgtri_groupe = $imgtri;
			elseif($_GET['tri'] == "genre")
				$imgtri_genre = $imgtri;
			elseif($_GET['tri'] == "descriptionproduit")
				$imgtri_description = $imgtri;
			elseif($_GET['tri'] == "prixproduiteuro")
				$imgtri_prix = $imgtri;
			
		}
		
		echo '<th>',$imgtri_reference,'<a href="',$this->genereLien("referenceproduit",$sens),'">Référence</a></th>';	
		
		if($this->showgenre == true)
			echo '<th>',$imgtri_genre,'<a href="',$this->genereLien("genre",$sens),'">Genre</a></th>';
			
		if($this->showgroupe == true)
			echo '<th>',$imgtri_groupe,'<a href="',$this->genereLien("groupe",$sens),'">Groupe</a></th>';
			
		echo '<th>',$imgtri_description,'<a href="',$this->genereLien("descriptionproduit",$sens),'">Désignation</a></th>';
		
		

		echo '<th>Image</th>';
		
		echo '<th>',$imgtri_prix,'<a href="',$this->genereLien("prixproduiteuro",$sens),'">HT €</a></th>';
		
		//c'est une liste pour les clients, afficher la colonne prix TTC
		if($this->typelistclient == true)
			echo '<th>TTC €</th>';
		
		//c'est une liste pour les clients, afficher la colonne qté et la colonne pour le bouton ajouter au panier
		if($this->typelistclient == true)
			echo '<th colspan=2>Qté</th><th></th>';
		
		//c'est une liste pour les admins, affiche les liens pour modifier et supprimer le produit
		if($this->typelistclient == false)
			echo '<th colspan=2>Actions</th>';
				
		echo '</tr>';
	}
	/**
	 * Affiche les focntion javascript qui permettent de modifier les qté et d'ajouter au panier
	 */
	private function displayJavascriptFunction() {
		?>
		<script type="text/javascript">
		//<![CDATA[
			function addtocart(produit, id) {
				qte = document.getElementById('p'+id).value;
				
				/*document.location = "<?php echo Zend_registry::get("basePath"); ?>index.php?page=panier&action=add&id="+produit+"&nb="+qte;*/
				document.location = "<?php echo Zend_registry::get("basePath"); ?>panier/ajouter/"+produit+"/quantite/"+qte;
			}
			
			
			function update_qte(id, val) {
				quantite = document.getElementById('p'+id).value;
				quantite = parseInt(quantite) + parseInt(val);
				if (quantite < 1) { quantite = 1; }
				if (quantite > 99) { quantite = 99; }
				document.getElementById('p'+id).value = quantite;
			}
		//]]>
		</script>
		<?php
	}
	/**
	 * Affiche les informations du produit sur un ligne dans un tableau
	 * 
	 * @param Produit $produit Produit à afficher sur une ligne
	 */
	private function displayLine(Produit $produit) {
		
		global $basePath;
					
		if($this->count%2)
			echo '<tr>';
		else
			echo '<tr class="imp">';
		

		echo '<td>',$produit->getReference(),'</td>';	
		
		if($this->showgenre == true){
			$arrayGenreName = explodeCategorieName($produit->getGenreName());
			if($_SESSION["lang"] == "fr") {
				$genreNametr = $arrayGenreName[0];
			}
			else {
				$genreNametr = $arrayGenreName[1];
			}
			echo '<td>',$genreNametr,'</td>';
		}
		if($this->showgroupe == true)
			echo '<td>',$produit->getGroupeName($_SESSION["lang"]),'</td>';
			
		echo '<td>',$produit->getDesignation(),'</td>';
		
		/*if($produit->getImage() == ''){
			echo '<td></td>';
		}
		else{
			echo '<td><a href="',Zend_Registry::get('basePath'),'public/images/produits/',$produit->getImage(),'" class="light" title="',Zend_Registry::get('translate')->_('afficher la photo'),'"><img src="',Zend_Registry::get('basePath'),'public/images/picture.png" /></a></td>';
		}*/
		if (file_exists("public/images/produits/".$produit->getReference().".jpg"))
			echo '<td><a href="',Zend_Registry::get('basePath'),'public/images/produits/',$produit->getReference(),'.jpg" class="light" title="',Zend_Registry::get('translate')->_('afficher la photo'),'"><img src="',Zend_Registry::get('basePath'),'public/images/picture.png" /></a></td>';
			//echo "<td>bouh</td>";
		else
			echo '<td></td>';
			
		//si il y a un prix, on affiche le prix
		if($produit->getPrixHT() > 0)
		{
			echo '<td class="td_right">';
			printf("%.2f", $produit->getPrixHT());
			echo ' €</td>';
		}
		else
			echo '<td class="td_right">Sur devis</td>';
		
		
		//c'est une liste pour les clients, afficher la colonne prix TTC
		if($this->typelistclient == true)
		{
			if($produit->getPrixHT() > 0)
			{
				echo '<td class="td_right">';
				printf("%.2f", calculPrixTTC($produit->getPrixHT()));
				echo ' €</td>';
			}
			else
			{
				echo '<td class="td_right">Sur devis</td>';
			}
		}
			
		
		//c'est une liste pour les clients, afficher la colonne qté et la colonne pour le bouton ajouter au panier
		if($this->typelistclient == true)
		{	
			?>
			<td>		
			
			
				<input type="text" maxlength="2" size="2" value="1" id="p<?php echo $this->count; ?>"/>
			</td>
			<td height="20" valign="top">
				
				<a style="padding:0;margin:0;border:0;" href="javascript:update_qte('<?php echo $this->count; ?>',1);"><img border="0" width="14" height="10" alt="Ajouter" src="<?php echo $basePath; ?>public/images/quantite-plus.gif" /></a><br />
				<a style="padding:0;margin:0;border:0;" href="javascript:update_qte('<?php echo $this->count; ?>',-1);"><img border="0" width="14" height="10" alt="Retirer" src="<?php echo $basePath; ?>public/images/quantite-moins.gif" /></a>
				
			</td>
			<?php
			echo '<td><a href="javascript:addtocart(',$produit->getIdentifiant(),',',$this->count,');" title="Ajouter au panier"><img src="',$basePath,'public/images/addto.png" alt="panier"/></a></td>';
		}
		//c'est une liste pour les admins, affiche les liens pour modifier et supprimer le produit
		if($this->typelistclient == false) {
			echo '<td class="td_center"><a href="',$basePath,'index.php?page=admin&action=gererprod&type=modif&id=',$produit->getIdentifiant(),'&id=',$produit->getIdentifiant(),'&cat=',$produit->getGenreName(),'&sscat=',$produit->getGroupeName(),'"><img src="',$basePath,'public/images/b_edit.png" alt="delete"/></a>';
			echo '<a href="',$basePath,'index.php?page=admin&action=gererprod&type=delete&id=',$produit->getIdentifiant(),'&id=',$produit->getIdentifiant(),'"><img src="',$basePath,'public/images/b_drop.png" alt="delete" onclick="return confirm(\'Voulez-vous vraiment supprimer ce produit ?\');"/></a></td>';
							
		}
				
		echo '</tr>';
		
		$this->count++;
		
	}
	/**
	 * Affiche la liste complète des produits, soi, de la catégorie complète, soi, de la sous-catégorie
	 */
	public function displayTable() {
		global $basePath;
		
		$this->displayJavascriptFunction();
		
		if($this->cat == "tous" || $this->cat == "all")
			$this->cat = null;
			
		echo '<p>Page n°',$this->pagination->getNumPage(),' sur ',$this->pagination->getNbPages(),' Affichage des produits [',$this->start,'-',($this->start+$this->len),'] sur ',ProduitDB::getProduitsListCount($this->cat, $this->sscat),'</p>';
		
		echo '<table class="liste">';
		//affiche l'entête du tableau
		$this->displayHeader();
		
		
			
		//récupère la liste des produits	
		$prodlist = ProduitDB::getProduitsList($this->cat, $this->sscat, $this->start, $this->len,$this->tri,$this->sens);			

		
		foreach($prodlist as $p) {
			//affiche la ligne avec le produit
			$this->displayLine($p);
		}	
		
		$this->displayFooter();
		
		echo '</table>';
	}

}

?>