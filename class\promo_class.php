<?php
class promo{
	
	private $produit=null;
	private $date=null;
	
	public function __construct(Produit $produit){
		$this->date = date('Y-m-d');
		$this->produit = $produit;
	}
	
	public function setProduit($produit) {
		$this->produit = $produit; 
	}
	public function setDate($date) {
		$this->date = $date; 
	}
	
	public function getProduit() {
		return $this->produit; 
	}
	public function getDate() {
		return $this->date;
	}
}

class promoDB{
	
	/**
	 * Supprime les promotions actuelles
	 */
	public static function deleteAllPromotions() {
		$sql = "truncate table promos;";

		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		
		$stmt->execute();
	}
	/**
	 * Sauvegarde la liste des promos en paramètres
	 * 
	 * @param array $promolist Liste des promos à sauvegarder
	 */
	public static function savePromotionsList($promolist) {
		
		foreach($promolist as $promo) {
			self::insertPromo($promo);
		}
		
	}
	/**
	 * Récupère la liste des produits pouvant être en promotion
	 * 
	 * @return produit[] Produits pouvant être en promo
	 */
	public static function getProduitsPromosList() {
		
		$prod_list = array();
		
		$sql = "SELECT * FROM produits WHERE promo=1";

		$db = Zend_registry::get("database");
		$stmt = $db->prepare($sql);
		
		$stmt->execute();
		$data = $stmt->fetchAll();
		
		foreach($data as $p) {
			$prod = new Produit();
			$prod->setDesignation($p['descriptionproduit']);
			$prod->setPrixHT($p['prixproduiteuro']);
			$prod->setReference($p['referenceproduit']);
			$prod->setIdentifiant($p['idproduit']);
			$prod->setPromotion($p['promo']);
			$prod->setGroupeId($p['idgroupe']);
			$prod->setGroupeName($p['groupe']);
			
			$prod->setGenreId($p['genre']);
			$prod->setGenreName($p['genre']);
			
			
			array_push($prod_list, $prod);
		}
		
		return $prod_list;
	}
	/**
	 * Récupère l'ensemble des promotions
	 *
	 * @return promo[]
	 */
	public static function getPromotionsList(){
		$db = Zend_Registry::get('database');
		$req = 'SELECT * FROM promos';
		$stmt = $db->prepare($req);
		$stmt->execute();
		$promos_list = array();
		$data = $stmt->fetchAll();
		if($data != null){
			foreach($data as $ligne){
				$pr = new promo(produitDB::getProduitById($ligne['idproduit']));

				$pr->setDate($ligne['date']);
				array_push($promos_list, $pr);
			}
		}
		return $promos_list;
	}
	
	/**
	 * Insère une promotion dans la base de données
	 *
	 * @param promo $a Promotion à ajouter
	 */
	public static function insertPromo(promo $a){
		
		$req = 'INSERT INTO promos (idproduit,date) VALUES (:id,:date)';
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($req);
		$stmt->bindParam(':id',$a->getProduit()->getIdentifiant());
		$date = $a->getDate();
		$stmt->bindParam(':date',$date);
		$stmt->execute();
		
	}
	
	/**
	 * Supprime une promo de la base de données
	 *
	 * @param promo $a Promotion à supprimer
	 */
	public static function deletePromo(promo $a){
		$req = 'DELETE FROM promo WHERE id = :id';
		$db = Zend_Registry::get('database');
		$stmt = $db->prepare($req);
		$stmt->bindParam(':id',$a->getIdentifiant());
		$stmt->execute();
	}
}

/**
 * Classe d'affichage des informations d'un produit
 * Affiche sous forme d'un tableau, soi pour les admins, soi pour les clients
 * 
 * admins :	-affichage d'un lien pour supprimer ou modifier le produit
 * 			-affiche d'une colonne genre et groupe
 * clients :-affichage d'un lien pour ajouter le produit au panier
 * 			-affiche possible de la colonne groupe
 */
class promoDisplay {
	
	private $typelistclient;

	/**
	 * Nombre d'élément dans le tableau
	 *
	 * @var int
	 */
	private $count;
	/**
	 * Prépare l'affiche du tableau 
	 *
	 * @param boolean $listclient Liste destiné à des clients ou à l'admin
	 */
	function __construct($listclient) {
				
		$this->typelistclient = $listclient;
	
		$this->count = 0;
	}
	

	/**
	 * Affiche le pied du tableau avec la pagination en fonction des filtres
	 */
	private function displayFooter() {
		echo '<tr><th colspan=10>';
		echo '</th></tr>';
	}
	/**
	 * Affiche l'entête du tableau avec les bonnes colonnes
	 */
	private function displayHeader() {
		global $basePath;
		global $translate;
		?>
		<tr>
		<?php if($this->typelistclient==true): ?>
		<th><?php echo $translate->_('Référence'); ?></th>
		<?php endif; ?>
		
		<th><?php echo $translate->_('Genre'); ?></th>
		<th><?php echo $translate->_('Groupe'); ?></th>
		<th><?php echo $translate->_('Désignation'); ?></th>
		<th><?php echo $translate->_('Image'); ?></th>
		
		<?php if($this->typelistclient==true): ?>
			<th><?php echo $translate->_('HT €'); ?></th>
			<th><?php echo $translate->_('TTC €'); ?></th>
			<th colspan="2"><?php echo $translate->_('Qté'); ?></th>
			<th></th>
		<?php else: ?>
			<th><?php echo $translate->_('Date de la promo'); ?></th>
			<th><?php echo $translate->_('Changer'); ?></th>		
		<?php endif; ?>	
		</tr>
		<?php
	}
	/**
	 * Affiche les focntion javascript qui permettent de modifier les qté et d'ajouter au panier
	 */
	private function displayJavascriptFunction() {
		?>
		<script type="text/javascript">
		//<![CDATA[
			function addtocart(produit, id) {
				qte = document.getElementById('p'+id).value;
				
				/*document.location = "<?php echo Zend_registry::get("basePath"); ?>index.php?page=panier&action=add&id="+produit+"&nb="+qte;*/
				/*document.location = "<?php echo Zend_registry::get("basePath"); ?>panier/ajouter/"+produit+"/quantite/"+qte;*/
				document.location = "<?php echo Zend_registry::get("basePath"); ?>panier/ajouter/"+produit+"/quantite/"+qte;
			}
			
			
			function update_qte(id, val) {
				quantite = document.getElementById('p'+id).value;
				quantite = parseInt(quantite) + parseInt(val);
				if (quantite < 1) { quantite = 1; }
				if (quantite > 99) { quantite = 99; }
				document.getElementById('p'+id).value = quantite;
			}
		//]]>
		</script>
		<?php
	}
	/**
	 * Affiche les informations du produit sur un ligne dans un tableau
	 * 
	 * @param Promo $promo Promotion à afficher sur une ligne
	 */
	private function displayLine(Promo $promo) {
		
		global $basePath;
		$produit = $promo->getProduit();
		
		if($this->count%2)
			echo '<tr>';
		else
			echo '<tr class="imp">';
		
		if($this->typelistclient)
			echo '<td>',$produit->getReference(),'</td>';
		$arrayGenreName = explodeCategorieName($produit->getGenreName());
		if($_SESSION["lang"] == "fr") {
			$genreNametr = $arrayGenreName[0];
		}
		else {
			$genreNametr = $arrayGenreName[1];
		}
		echo '<td>',$genreNametr,'</td>';
		echo '<td>',$produit->getGroupeName(),'</td>';
		echo '<td>',$produit->getDesignation(),'</td>';
		
		if (file_exists("public/images/produits/".$produit->getReference().".jpg"))
			{
		// IMAGE
		echo "<td>";
		echo "<a href=\"".Zend_Registry::get('basePath')."public/images/produits/".$produit->getReference().".jpg\" rel=\"lytebox\" title=\"".$produit->getReference()."\" >";
		echo "<img src=\"".Zend_Registry::get('basePath')."public/images/produits/".$produit->getReference().".jpg\" border=\"1\" height=\"40\" />";
		echo "</a>";
		echo "</td>";
			
			}	else {
		echo '<td></td>';
		}
			
		if($this->typelistclient) {
			//si il y a un prix, on affiche le prix
			if($produit->getPrixHT() > 0)
			{
				echo '<td>';
				printf("%.2f", $produit->getPrixHT());
				echo ' €</td>';
				echo '<td>';
				printf("%.2f", calculPrixTTC($produit->getPrixHT()));
				echo ' €</td>';
			}
			else 
			{
				echo '<td>Sur devis</td>';
				echo '<td>Sur devis</td>';
			}
			
			?>
			<td>		
			<input type="text" maxlength="2" size="2" value="1" id="p<?php echo $this->count; ?>"/>
			</td>
			
			<td>
			<a href="javascript:update_qte('<?php echo $this->count; ?>',1);"><img width="14" height="10" alt="Ajouter" src="<?php echo $basePath; ?>public/images/quantite-plus.gif" /></a><br />
			<a href="javascript:update_qte('<?php echo $this->count; ?>',-1);"><img width="14" height="10" alt="Retirer" src="<?php echo $basePath; ?>public/images/quantite-moins.gif" /></a>
			</td>
			<?php
			echo '<td><a href="javascript:addtocart(',$produit->getIdentifiant(),',',$this->count,');" title="Ajouter au panier"><img src="',$basePath,'public/images/addto.png" alt="panier"/></a></td>';
			
		} else {
			echo '<td>',$promo->getDate(),'</td>';
			echo '<td><a href="',$basePath,'index.php?page=admin&action=gererpromo&random=1&promo=',$this->count,'" title="Changer" ><img src="',$basePath,'public/images/random.png" alt="Cliquez pour générer un autre produit à mettre en promotion."/></a></td>';
		}
			

		echo '</tr>';

		$this->count++;
		
	}
	/**
	 * Affiche la liste complète des produits, soi, de la catégorie complète, soi, de la sous-catégorie
	 */
	public function displayTable() {
		global $basePath;
	
		$promo_list = promoDB::getPromotionsList();
		$this->displayJavascriptFunction();
		echo '<table class="liste">';
		//affiche l'entête du tableau
		$this->displayHeader();
		
		foreach($promo_list as $promo) {
			$this->displayLine($promo);
		}
		
		
		$this->displayFooter();
		
		echo '</table>';
	}

}