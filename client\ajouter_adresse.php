<?php

// Fichier créé par <PERSON><PERSON><PERSON>

// Ce fichier PHP permet d'ajouter une adresse au client
require_once dirname(__FILE__).'../../class/adresse_class.php';

// Création de la page html virtuelle
echo '<h1>'.$translate->_('Carnet d\'adresse : ajout').'</h1><br/><br/>';

$afficheform=true;

if(isset($_POST['nomrue'])) // On se trouve sur la page de traitement
{
	// Déclaration des variables :
	// Formulaire :
	$nomrue=htmlentities($_POST['nomrue'], ENT_QUOTES, $charSet='UTF-8');
	$nomrue=$_POST['nomrue'];
	//$nomrue=stripslashes($nomrue);
	$cp=htmlentities($_POST['cp']);
	$ville=htmlentities($_POST['ville'], ENT_QUOTES, $charSet='UTF-8');
	$pays=htmlentities($_POST['pays'], ENT_QUOTES, $charSet='UTF-8');
	$type=true;
	$nom=$user->getNom();
	$prenom=$user->getPrenom();
	$raisonsocial = htmlentities($_POST['raisonsocial'], ENT_QUOTES, $charSet='UTF-8');
	$email=$user->getEmail();
	
	// On teste si les champs ont bien été remplis 
	if (!empty($_POST['nomrue']) && !empty($_POST['cp']) && !empty($_POST['ville']) && !empty($_POST['pays']))
	{
		$afficheform=false;
		//Affichage d'un récapitulatif des données inscrites
		echo '<h2>'.$translate->_('Récapitulatif de votre adresse').'</h2>';
		echo '<div class="info messageBox"><p>';
		afficheAdresse ($raisonsocial,$nomrue, $cp, $ville, $pays);
		echo '</p></div>';
		echo '<br/><br/>';
		echo '<h2>'.$translate->_('Résultat des modifications').'</h2>';
		
		// Enregistrement dans la BDD avec test si la système à réussit à se connecter et à enregistrer les données
		$adresse = new adresse();
		$adresse->setRaisonSocial($raisonsocial);
		$adresse->setNomRue($nomrue);
		$adresse->setCodePostal($cp);
		$adresse->setVille($ville);
		$adresse->setPays($pays);
		$adresse->setType($type);
		$adresseDB = new adresseDB();
		// On vérifie que tout c'est bien passé dans l'enregistrement
		if ($adresseDB->save($email, $adresse, true))
		{
			echo '<div class="valide messageBox"><p>'.$translate->_('La nouvelle adresse a bien été enregistrée').'.</p></div>';
		}
		else
		{
			echo '<div class="error messageBox"><p>'.$translate->_('Aucun changement détécté').'.</p></div>';
		}
	}
	else
	{
		echo '<div class="error messageBox"><p>'.$translate->_('Vous devez spécifier au moins votre <b>nom de rue</b>, votre <b>code postal</b>, votre <b>ville</b> et votre <b>pays</b>.<br/> Le numéro de rue est à remplir que si necessaire').'.</p></div>';
	}
}


  /////////////////
 // FORMULAIRE //
/////////////////


if ($afficheform)
{
	if ($user->isAuthenticated)
	{
		// Initialisation des variables
		$email=$user->getEmail();
		$nom=$user->getNom();
		$prenom=$user->getPrenom();
		$raisonsocial = $nom.' '.$prenom; 

		// Test si le client possède déjà une adresse de facturation
		$adresseDB = new adresseDB;
		$type = true; // Type de l'adresse, est égale à Vrai si il s'agit d'une adresse de facturation
		$adresseCherche = $adresseDB->getAdresseByClient($user);
		// Création d'un formulaire utilisant la  méthode POST
		
		// Contenu du formulaire d'ajout des coordonnées d'un client 
		// Si le client possède déjà une adresse de facturation : On affiche le formulaire pré-remplis
		?>
		<div class="form">
		<form action="index.php?page=user&action=ajouter_adresse" method="POST">
		<fieldset class="input">
			<ol>
				<li>
					<label for="raisonsociale"><?php echo $translate->_('Raison sociale').' :';?></label>
					<input type="text" name="raisonsocial" value="<?php echo $raisonsocial; ?>" class="indispensable"/><b class="reditalique">* </b>
				</li>
				<li>
					<label for="adresse"><?php echo $translate->_('Adresse').' :';?></label>
					<input type="text" name="nomrue" value="" class="indispensable"/><b class="reditalique">* </b>
				</li>
				<li>
					<label for="cp"><?php echo $translate->_('Code Postal').' :';?></label>
					<input type="text" name="cp" value="" class="indispensable"/><b class="reditalique">* </b>
				</li>
				<li>
					<label for="ville"><?php echo $translate->_('Ville').' :';?></label>
					<input type="text" name="ville" value="" class="indispensable"/><b class="reditalique">* </b>
				</li>
				<li>
					<label for="pays"><?php echo $translate->_('Pays').' :';?></label>
					<?php /*<input type="text" name="pays" value="" class="indispensable"/>*/ ?>
					<select name="pays">
						<option value="France">France</option>
						<option value="Algérie">Algérie</option>
						<option value="Allemagne">Allemagne</option>
						<option value="Arabie Saoudite">Arabie Saoudite</option>
						<option value="Argentine">Argentine</option>
						<option value="Autriche">Autriche</option>
						<option value="Belgique">Belgique</option>
						<option value="Cameroun">Cameroun</option>
						<option value="Canada">Canada</option>
						<option value="Chine">Chine</option>
						<option value="Chypre">Chypre</option>
						<option value="Côte d Ivoire">Côte d Ivoire</option>
						<option value="Danemark">Danemark</option>
						<option value="Espagne">Espagne</option>
						<option value="Estonie">Estonie</option>
						<option value="Etats Unis">Etats Unis</option>
						<option value="Finlande">Finlande</option>
						<option value="Gabon">Gabon</option>
						<option value="Grèce">Grèce</option>
						<option value="Hongrie">Hongrie</option>
						<option value="Irlande">Irlande</option>
						<option value="Italie">Italie</option>
						<option value="Lettonie">Lettonie</option>
						<option value="Lituanie">Lituanie</option>
						<option value="Luxembourg">Luxembourg</option>
						<option value="Madère">Madère</option>
						<option value="Malte">Malte</option>
						<option value="Maroc">Maroc</option>
						<option value="Monaco">Monaco</option>
						<option value="Nouvelle Calédonie">Nouvelle Calédonie</option>
						<option value="Pays Bas">Pays Bas</option>
						<option value="Pologne">Pologne</option>
						<option value="Portugal">Portugal</option>
						<option value="République Tchèque">République Tchèque</option>
						<option value="Royaume uni">Royaume uni</option>
						<option value="Russie">Russie</option>
						<option value="Sénégal">Sénégal</option>
						<option value="Slovaquie">Slovaquie</option>
						<option value="Slovénie">Slovénie</option>
						<option value="Suède">Suède</option>
						<option value="Suisse">Suisse</option>
						<option value="Togo">Togo</option>
						<option value="Tunisie">Tunisie</option>
					</select><b class="reditalique">* </b>
				</li>
				<li>
					<label class="reditalique"><?php echo '* ',$translate->_('Champs obligatoires'); ?></label>
				</li>
			</ol>
		</fieldset>
		<fieldset class="submit">
			<ol>
				<li>
					<input type="submit" name="envoyer" value="<?php echo $translate->_('Ajouter');?>" class="btn_submit"/>
				</li>
			</ol>
		</fieldset>
		</form></div><?php
	}
	else
	{
		// Le client n'est pas connecté ->, message d'erreur.
		echo '<div class="error messageBox"><p>'.$translate->_('Pour ajouter ou modifier votre adresse, vous devez vous connecter, merci').'.</p></div>';
	}
}
?>
<br/>
<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>index.php?page=user&action=infos"><?php echo $translate->_('Retour');?></a>
</p>
