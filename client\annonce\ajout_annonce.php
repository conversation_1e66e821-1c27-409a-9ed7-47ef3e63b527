<?php
// Page pour l'ajout d'une nouvelle annonces
if(isset($_POST['add_annonce'])){
	$error = false;
	$errorMessage = "";
		
	if($_POST['detail'] != "" && $_POST['sujet'] != "" && $_POST['prix'] != "" && $_POST['type'] != "" 
		&& $_POST['genre'] != "" && isset($_POST['operation']) && $_POST['operation']!= "")
	{
	   // traitement de l'ajout
		require_once dirname(__FILE__).'/../../class/annonce_class.php';
		$a = new annonce();
		$a->setClient($user);
		$a->setDetail(stripslashes($_POST['detail']));
		$a->setFax($_POST['fax']);
		$a->setGenre($_POST['genre']);
		$a->setOperation($_POST['operation']);
		$a->setPrix($_POST['prix']);
		$a->setStatus(0);
		$a->setSujet(stripslashes($_POST['sujet']));
		$a->setTelephone($_POST['telephone']);
		$a->setType($_POST['type']);+
		$a->setImage($_POST['image']);	
			
	}
	else{
		$error = true;
	   	$errorMessage = $translate->_('Des champs obligatoires n\'ont pas été renseigné');
	}
	
	if(!$error){
		annonceDB::insertAnnonce($a);
		require_once dirname(__FILE__).'/list_annonce.php';
	}
	else{
		require_once dirname(__FILE__).'/view/ajout.php';
	}
}
else{
	// affichage du formulaire d'ajout
	require_once dirname(__FILE__).'/view/ajout.php';
}