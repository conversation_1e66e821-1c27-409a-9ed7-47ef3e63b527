<?php
// page pour l'edition des annonces du client
if(isset($_POST['edit_annonce']))
{
	// traitement de l'edition
	$error = false;
	$errorMessage = "";
		
	if($_POST['detail'] != "" && $_POST['sujet'] != "" 
		&& $_POST['prix'] != "" && $_POST['type'] != "" 
		&& $_POST['genre'] != "" && $_POST['operation']!= "")
	{
	   // traitement de l'ajout
		require_once dirname(__FILE__).'/../../class/annonce_class.php';
		
		$a = annonceDB::getAnnonceById($_GET['id']);
		$a->setDetail(stripslashes($_POST['detail']));
		$a->setFax($_POST['fax']);
		$a->setGenre($_POST['genre']);
		$a->setOperation($_POST['operation']);
		$a->setPrix($_POST['prix']);
		$a->setStatus(0);
		$a->setSujet(stripslashes($_POST['sujet']));
		$a->setTelephone($_POST['telephone']);
		$a->setType($_POST['type']);
		$a->setImage($_POST['image'])
		
		if(!$error)
		{
			annonceDB::updateAnnonce($a);
			require_once dirname(__FILE__).'/list_annonce.php';
		}
		else
		{
			require_once dirname(__FILE__).'/view/edit.php';
		}
	}
}
else
{
	// affichage du formulaire d'ajout
	require_once dirname(__FILE__).'/../../class/annonce_class.php';
	$a = annonceDB::getAnnonceById($_GET['id']);
	include dirname(__FILE__).'/view/edit.php';
}
?>