<?php
	// Affichage de la liste des annonces proposer par le client
	require_once dirname(__FILE__).'/../../class/annonce_class.php';
	$annonces = annonceDB::getAnnoncesByClient($user);
	// Affichage de la liste des annonces du cients
?>
<div id="container" style="	margin-left:160px; margin-bottom:70px;  margin-top:10px;">
      <div id="banner-fade" style="box-shadow: 5px 5px 5px 0px #9b9b9b;">

        <!-- start Basic Jquery Slider -->
        <ul class="bjqs">
		   <li><img src="<?php echo $basePath; ?>public/images/bandeau/bandeau_pinup.jpg"/></li>
		   <li><a href="http://www.jeep-dodge-gmc.com/smi/consommable/4400" ><img src="<?php echo $basePath; ?>public/images/bandeau/bandeau_peinture.jpg"/></a></li>
		   <li><a href="http://jeep-dodge-gmc.com/smi/bourse" ><img src="<?php echo $basePath; ?>public/images/bandeau/bandeau_rubrique_bourse.jpg"/></a></li>
		   <li><a href="http://jeep-dodge-gmc.com/smi/index.php?page=search&in=ALL&method=AND&valuetosearch=ampoule" target="_blank" ><img src="<?php echo $basePath; ?>public/images/bandeau/NOUVO_AMP.jpg"/></a></li>
		   <li><a href="https://www.facebook.com/SMIjeepdodgegmc" target="_blank" ><img src="<?php echo $basePath; ?>public/images/bandeau/facebook.jpg"/></a></li>

        </ul>
        <!-- end Basic jQuery Slider -->

      </div>
</div>
      <!-- End outer wrapper -->

      <script>
        jQuery(document).ready(function($) {

          $('#banner-fade').bjqs({
			animtype      : 'slide',
			height        : 150,
			width         : 600,
			responsive    : true,
			showcontrols : false,
			randomstart   : true
          });

        });
      </script>
<h1><?php echo $translate->_('Gestion de mes Annonces'); ?><a style="margin-left:330px;" href="<?php echo $basePath; ?>index.php?page=user&action=subscribe&annonce=oui"><img src="<?php echo $basePath; ?>public/images/add.png" alt="ajouter_annonces"/><?php echo $translate->_(' Ajouter votre annonce !'); ?></a></h1>
<?php if(count($annonces) != 0): ?>
	<table class="liste">
		<tr>
			<th><?php echo $translate->_('Référence'); ?></th>
			<th><?php echo $translate->_('Opération'); ?></th>
			<th><?php echo $translate->_('Sujet'); ?></th>
			<th><?php echo $translate->_('Prix'); ?></th>
			<th><?php echo $translate->_('Date'); ?></th>
			<th><?php echo $translate->_('Status'); ?></th>
			<th><?php echo $translate->_('Photo'); ?></th>
			<th></th>
			<th></th>
			<th></th>
		</tr>
		<?php foreach($annonces as $annonce): ?>
			<tr align="center">
				<td><?php echo $annonce->getId(); ?></td>
				<td><?php echo $translate->_($annonce->getOperation()); ?></td>
				<td><?php echo $annonce->getSujet(); ?></td>
				<td><?php echo $annonce->getPrix(); ?> € </td>
				<td><?php echo  date('d/m/Y',strtotime($annonce->getDate())); ?></td>
				<td><?php if($annonce->getStatus() == 1) 
						{ 
							?>
							<img src="<?php echo $basePath; ?>public/images/accept.png" title="<?php echo $translate->_('l\'annonce a été validé par un administrateur');?>" />
							<?php
						}
						else {
							?>
							<img src="<?php echo $basePath; ?>public/images/time.png" title="<?php echo $translate->_('L\'annonce est en attente de validation');?>" />
							<?php
						}
					?>
				</td>
				<td>
					<?php 
						if($annonce->getImage() != ''){
							?>
								<a href="<?php echo $basePath.'public/images/annonces/'.$annonce->getImage(); ?>" class="light" title="<?php echo $translate->_('afficher la photo');?>"><img src="<?php echo $basePath; ?>public/images/picture.png" /></a>
							<?php
						} 
					?>
				</td>
				<td>
					<a href="<?php echo $basePath; ?>annonces/<?php echo $annonce->getId(); ?>" title="<?php echo $translate->_('afficher les détails');?>"><img src="<?php echo $basePath ?>public/images/zoom.png" /></a>
				</td>
				<?php
				/*
				<td>
					<a href="<?php echo $basePath; ?>index.php?page=user&action=annonces&act=edit&id=<?php echo $annonce->getId(); ?>" title="<?php echo $translate->_('modifier l\'annonce');?>"><img src="<?php echo $basePath.'public/images/b_edit.png' ?>" /></a>
				</td>
				*/
				?>
				<td>
					<a onclick="return confirm('Supprimer')" href="<?php echo $basePath; ?>index.php?page=user&action=annonces&act=suppr&id=<?php echo $annonce->getId(); ?>" title="<?php echo $translate->_('supprimer l\'annonce');?>"><img src="<?php echo $basePath.'public/images/cross.png' ?>" /></a>
				</td>
			</tr>
		<?php endforeach; ?>
	</table>
<?php else: ?>
	<p><?php echo '<div class="info messageBox"><p>',$translate->_('Vous n\'avez pas encore d\'annonces'),'</p></div>'; ?></p>
<?php endif; ?>