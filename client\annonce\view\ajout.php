
<?php if(isset($errorMessage) && $errorMessage != ""): ?>
	<p class="error messageBox "><?php echo $errorMessage; ?></p>
<?php endif; ?>
<div class="form-style-10">
<h1><?php echo $translate->_('Ajouter une annonce'); ?></h1>

					<?php
										
					$result = $database->prepare("SELECT id FROM annonces ORDER BY id DESC LIMIT 1") or die ("requete r invalid");
					$result->execute();
					$lastid = $result->fetch();
					$idplus = $lastid['id'] + 1;
					$idplus = $idplus.date('dmY');
					
					?>
    <table>
		<tr>
			<td>
<script>

// "myAwesomeDropzone" is the camelized version of the HTML element's ID
Dropzone.options.dzannonce = {
  paramName: "file", // The name that will be used to transfer the file
  maxFilesize: 0.5, // MB
  acceptedFiles: ".jpg",
};

</script>			
				<form id="dzannonce" style="margin-right:10px;" name="formimage" action="<?php echo $basePath; ?>client/annonce/upload_annonce.php?ref=<?php echo $idplus; ?>" class="dropzone"></form><br />
			</td>
			<td>
			<div class="inner-wrap">
			 - Votre image doit être en .jpg (minuscule)<br />
			 - Elle ne doit pas dépasser une taille de 500ko<br />
			 - Pour un affichage optimal nous conseillons la dimension 1024 x 768 pixels<br />
			</div>
			</td>
		</tr>
	</table>	
	</div>
	<div class="form-style-10">
		<div class="inner-wrap">
				<form action="" method="post" enctype="multipart/form-data">
				
				<input type="hidden" name="image"  value="<?php echo $idplus; ?>.jpg"/>
				
					
				    <table style="margin-bottom:20px; width:300px;">
						<tr>
							<td style="width:125px;">
							Je vends : <input type="radio" value="Vend" id="vend" class="radio"  name="operation" checked="checked">
							</td><td style="width:50px;">
							ou
							</td><td style="width:125px;">
							J'achète : <input type="radio" value="Achat" id="achat" class="radio" name="operation">
							</td>
						</tr>
					</table>	

					<label for="sujet" class="label"><?php echo $translate->_('Sujet'); ?> (max 80 lettres)<b class="reditalique"> * </b> : </label>
					<textarea rows="1" placeholder="Nommer clairement votre produit, ne rajoutez pas 'Vendre' ou 'Achete' !" onfocus="this.placeholder = ''" maxlength="80" type="text" name="sujet"><?php echo @$_POST['sujet']; ?></textarea>
				
				
					<label for="detail" class="label"><?php echo $translate->_('Détail'); ?><b class="reditalique"> * </b> :</label>
					<textarea onfocus="this.placeholder = ''" placeholder="Faites une description détaillé du produit, ajouter des dimensions, poids, couleurs,..." name="detail"><?php echo @$_POST['detail']; ?></textarea>
					<table style="width:740px;">
					<tr>
					<td>
					<label for="genre" class="label"><?php echo $translate->_('Genre'); ?><b class="reditalique"> * </b> :</label>
					<select name="genre" >
						<?php
							// on cree une liste d'option
							require_once dirname(__FILE__).'/../../../class/produit_class.php';
							$c = produitDB::getGenresNames();
							
							foreach($c as $cat) {
								$cat1 = explode("|", $cat);
								?>
								<option value="<?php echo $cat1[0]; ?>"><?php echo $cat1[0]; ?></option>
								<?php
							}
						 ?>
					</select>
					</td>
					<td>
					<label for="type" class="label"><?php echo $translate->_('Type'); ?> (ou modèle) <b class="reditalique"> * </b> :</label>
					<input type="text" name="type" value="<?php echo @$_POST['type']; ?>" />
					</td>
					</tr>
					</table>
				
					<label for="prix" class="label"><?php echo $translate->_('Prix'); ?><b class="reditalique"> * </b> (euro) :</label>
					<input type="text" name="prix" value="<?php echo @$_POST['prix']; ?>"/>
				
					<table style="width:740px;">
					<tr>
					<td>
					<label for="telephone" class="label"><?php echo $translate->_('Téléphone'); ?> :</label>
					<input type="text" name="telephone" value="<?php echo @$_POST['telephone']; ?>"/>
					</td>
					<td>
					<label for="fax" class="label"><?php echo $translate->_('Fax'); ?> :</label>
					<input type="text" name="fax" value="<?php echo @$_POST['fax']; ?>"/>
					</td>
					</tr>
					</table>
				
					<label class="reditalique"><?php echo '* ',$translate->_('Champs obligatoires'); ?></label>
		</div>
		<div class="button-section">
				
					<label for="submit"></label>
					<input type="submit" name="add_annonce" value="<?php echo $translate->_('Ajouter'); ?>" />

		</div>
	</form>
</div>