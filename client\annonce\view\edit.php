<h1><?php echo $translate->_('Modifier une annonce'); ?></h1>
<?php if(isset($errorMessage) && $errorMessage != ""): ?>
	<p class="messagebox error"><?php echo $errorMessage; ?></p>
<?php endif; ?>
<div id="form_Annonce">
	<form action="" method="post" enctype="multipart/form-data">
		<fieldset class="input">
			<ol>
				<li>
					<label for="sujet" class="label"><?php echo $translate->_('Sujet'); ?> :</label>
					<input type="text" name="sujet" value="<?php echo $a->getSujet(); ?>" class="inputNormal indispensable"/><b class="reditalique">* </b>
				</li>
				<li>
					<label for="detail" class="label"><?php echo $translate->_('Détail'); ?> :</label>
					<textarea name="detail" class="inputNormal indispensable"><?php echo $a->getDetail(); ?></textarea><b class="reditalique">* </b>
				</li>
				<li>
					<label for="operation" class="label"><?php echo $translate->_('Opération'); ?> :</label>
					<span class="inputDroit">
					<?php if($a->getOperation() == "Achat"): ?>
						<input type="radio" value="Vend" id="vend" class="radio"  name="operation"><label for="vend"><?php echo $translate->_('Vend');?></label>
						<input type="radio" value="Achat" id="achat" class="radio" name="operation" checked="checked"><label for="achat"><?php echo $translate->_('Achat');?></label>
					<?php else: ?>
						<input type="radio" value="Vend" id="vend" class="radio"  name="operation" checked="checked"><label for="vend"><?php echo $translate->_('Vend');?></label>
						<input type="radio" value="Achat" id="achat" class="radio" name="operation"><label for="achat"><?php echo $translate->_('Achat');?></label>
					<?php endif; ?>
					</span><b class="reditalique">* </b>
				</li>
				<li>
					<label for="genre" class="label"><?php echo $translate->_('Genre'); ?> :</label>
					<select name="genre" >
						<option value="<?php echo $a->getGenre(); ?>"><?php echo $a->getGenre(); ?></option>
						<?php
							// on cree une liste d'option
							require_once dirname(__FILE__).'/../../../class/produit_class.php';
							$c = produitDB::getGenresNames();
							
							foreach($c as $cat) {
								$cat1 = explode("|", $cat);
								?>
								<option value="<?php echo $cat1[0]; ?>"><?php echo $cat1[0]; ?></option>
								<?php
							}
						 ?>
					</select><b class="reditalique">* </b>
				</li>
				<li>
					<label for="type" class="label"><?php echo $translate->_('Type'); ?> :</label>
					<input type="text" name="type" value="<?php echo $a->getType(); ?>" class="inputNormal indispensable"/><b class="reditalique">* </b>
				</li>
				<li>
					<label for="prix" class="label"><?php echo $translate->_('Prix'); ?> (euro) :</label>
					<input type="text" name="prix" value="<?php echo $a->getPrix(); ?>" class="inputNormal indispensable"/><b class="reditalique">* </b>
				</li>
				<li>
					<label for="image" class="label"><?php echo $translate->_('Image'); ?> :</label>
					<input type="file" name="image"  class="inputNormal"/>
				</li>
				<li>
					<label for="telephone" class="label"><?php echo $translate->_('Téléphone'); ?> :</label>
					<input type="text" name="telephone" value="<?php echo $a->getTelephone(); ?>"  class="inputNormal"/>
				</li>
				<li>
					<label for="fax" class="label"><?php echo $translate->_('Fax'); ?> :</label>
					<input type="text" name="fax" value="<?php echo $a->getFax(); ?>"  class="inputNormal"/>
				</li>
				<li>
					<label class="reditalique"><?php echo '* ',$translate->_('Champs obligatoires'); ?></label>
				</li>
			</ol>
		</fieldset>
		<fieldset class="submit">
			<ol>
				<li>
					<label for="submit"></label>
					<input type="submit" name="edit_annonce" value="<?php echo $translate->_('Modifier'); ?>" />
				</li>
			</ol>
		</fieldset>
	</form>
</div>
<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>index.php?page=user&action=annonces"><?php echo $translate->_('Retour');?></a>
</p>