<?php

header('Content-type: text/html; charset=iso-8859-1');  

require_once '../init.php';

$cp = $_GET['cp'];

$result = $database->prepare('SELECT ville FROM ville_france WHERE code_postal = :cp') or die ("requete r1 invalid");

$result->execute(['cp' => $cp]);

$result2 = $database->prepare("SELECT COUNT(*) AS cp_invalid FROM ville_france WHERE code_postal = :cp") or die ("requete test facture invalid");

$result2->execute(['cp' => $cp]);

$cp_invalid = $result2->fetch();
$cp_invalid2 = $cp_invalid['cp_invalid'];
if ($cp_invalid2 >= 1){

?>

	

				<select name="ville">
				<?php
				while ($tab = $result->fetch()) {
				
					echo '<option value="'.$tab['ville'].'">'.$tab['ville'].'</option>';
				
					}
					?>
				</select>
				
<?php

} else {

echo "<img src='".$basePath."public/images/attention.png' alt='attention'/>Le code postal indiqu&eacute; est invalide !"; 

}

?>
