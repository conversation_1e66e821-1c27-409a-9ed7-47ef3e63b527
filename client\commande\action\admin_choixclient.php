<?php
	require_once dirname(__FILE__).'/../../../init.php';
	require_once dirname(__FILE__).'/../../../class/internaute_class.php';
	require_once dirname(__FILE__).'/../../../class/client_class.php';
	require_once dirname(__FILE__).'/../../../class/panier_class.php';
	require_once dirname(__FILE__).'/../../../class/produit_class.php';
		
	$user = null;
	$user = unserialize($_SESSION['user1']);
	
	///////RECALCUL LE PANIER POUR LES PRO AVEC LEUR REMISE
	$mailclient = $_POST['email'];
	$result = $database->prepare("
	SELECT * FROM clients WHERE emailclient = '$mailclient'") or die ("requete result invalid");
	$result->execute();
	$indiceclient = $result->fetch();
	if($indiceclient['indiceclient'] == "5" || $indiceclient['indiceclient'] == "6"){
			$user->getPanier()->recalculerPro($mailclient);
	}
	///////////////////////////////////////////////////////	
		
	// on ajoute l'email dans la commande
	

	$user_temp = clone $user;
	$user = new client($_POST['email']);
	$user->setPanier($user_temp->getPanier());
	$_SESSION['user1'] = serialize($user);

	$_SESSION['iscommandeadmin'] = 1;
	header('Location: '.$basePath.'commande/adresse');
	?>