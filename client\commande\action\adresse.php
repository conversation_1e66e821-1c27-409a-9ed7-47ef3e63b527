<?php
	//@todo ajouter la traduction
	require_once dirname(__FILE__).'/../../../init.php';
	require_once dirname(__FILE__).'/../../../visiteur/login.php';
	require_once 'adresse_class.php';
	require_once dirname(__FILE__).'/../../../class/af_class.php';

	
	// variable post filtré
			
			$charset='utf-8';
			
			$raisonsocial = $_POST['raisonsocial'];
			$raisonsocial = htmlentities($raisonsocial, ENT_NOQUOTES, $charset);
			$raisonsocial = preg_replace('#&([A-za-z])(?:acute|cedil|circ|grave|orn|ring|slash|th|tilde|uml);#', '\1', $raisonsocial);
			$raisonsocial = preg_replace('#&([A-za-z]{2})(?:lig);#', '\1', $raisonsocial); // pour les ligatures e.g. '&oelig;'
			$raisonsocial = preg_replace("#&[^;]+;#", '', $raisonsocial); // supprime les autres caractères
			$raisonsocial = str_replace("'", '', $raisonsocial); // supprime les autres caractères
			
			$nomrue = $_POST['nomrue'];
			$nomrue = htmlentities($nomrue, ENT_NOQUOTES, $charset);
			$nomrue = preg_replace('#&([A-za-z])(?:acute|cedil|circ|grave|orn|ring|slash|th|tilde|uml);#', '\1', $nomrue);
			$nomrue = preg_replace('#&([A-za-z]{2})(?:lig);#', '\1', $nomrue); // pour les ligatures e.g. '&oelig;'
			$nomrue = preg_replace("#&[^;]+;#", '', $nomrue); // supprime les autres caractères
			$nomrue = str_replace("'", '', $nomrue); // supprime les autres caractères

	// traitement du formulaire de choix d'adresse, avec redirection vers choix mode_paiement si ok, réaffichage du formulaire sinon
	unset($_SESSION['commande']['id']); // dans le cas ou a déja commander dans cette session, on doit supprimer l'id de la commande precedente
	if(isset($_POST['idadresse'])){
		if(isset($_POST['retraitmagasin']) && $_POST['retraitmagasin'] == true){
			// on veut retirer la commande en magasin, donc pas besoin des adresses de livraison et de facturation
			$_SESSION['commande']['livraison'] = null;
			//$_SESSION['commande']['facturation'] = null;
			$_SESSION['commande']['surplace'] = true;
			
			$ad_fact = afDB::getAdresseByClient($user->getEmail());
			
			$adresse = new adresse();
			$adresse->setNomRue(strtolower($ad_fact->getNomRue()));
			$adresse->setCodePostal($ad_fact->getCodePostal());
			$adresse->setRaisonSocial(strtoupper($ad_fact->getRaisonSocial()));
			$adresse->setVille($ad_fact->getVille());
			$adresse->setPays($ad_fact->getPays());
			
			$_SESSION['commande']['facturation'] = serialize($adresse);
			
			// on redirige vers la page de recap
			header('HTTP/1.1 302 Found');
			header('Location: '.$basePath.'commande/recapitulatif');
		}
		else{
			$_SESSION['commande']['surplace'] = false;
			// on vient du bonne endroit
			$idadresse = $_POST['idadresse'];
			$adresse = null;
			$erreur = "";
			if ($idadresse != "-2"){
			if($idadresse == "0"){
				// on veut ajouter une nouvelle adresse
				// on verifie que le format des champs est correct
				// on ajoute la nouvelle adresse
				try{
					// on verifie que les champs obligatoire soit tous là
					
					if(!(isset($raisonsocial) && $raisonsocial != "" && 	
					   isset($nomrue) && $nomrue != "" &&
					   isset($_POST['cp']) && $_POST['cp']  != "" &&
					   isset($_POST['ville']) && $_POST['ville'] != "" &&
					   isset($_POST['pays']) && $_POST['pays'] != "")){
						throw new Exception("Des champs obligatoire sont vides");
					}
					
					// on verifie le format des champs
					
					$adresse = new adresse();
					$adresse->setNomRue(strtolower($nomrue));
					$adresse->setCodePostal($_POST['cp']);
					$adresse->setRaisonSocial($raisonsocial);
					$adresse->setVille(strtoupper($_POST['ville']));
					$adresse->setPays($_POST['pays']);
					adresseDB::save($user->getEmail(),$adresse,true);
					
				}
				catch(Exception $e){
					$erreur = $e->getMessage();
				}
				
			}
			else{
				// on veut utiliser une adresse existante (mais on peut l'avoir modifier => donc mettre a jour l'adresse dans la table
				// on verifie que l'id correspond bien a une adresse du client
				
				$adresses = adresseDB::getAdressesByClient($user);
	
				try{
					if(count($adresses) == 0){
						// l'utilisateur n'a pas d'adresse, donc il peut seulement en ajouter une
						$_POST['idadresse'] = 0;
						include 'adresse.php'; 
					}
					foreach($adresses as $a){
						if($a->getId() == $idadresse){
							$adresse = $a;
							break;
						}
					}
					if($adresse == null){
						// l'adresse n'appartient pas a cet utilisateur
						throw new Exception('Cette adresse ne vous appartient pas');
					}
					// cette adresse appartient bien cet utilisateur, mise à jour de l'adresse
					// verification du format des différent champs avant de faire la mise à jour
					if(empty($raisonsocial) || empty($nomrue) || empty($_POST['cp']) || empty($_POST['ville']) || empty($_POST['pays'])){
						throw new Exception('Des champs obligatoire sont vides');
					}
					/*
					if($adresse->getRaisonSocial() != $raisonsocial ||	
					   $adresse->getNomRue() != $nomrue ||
					   $adresse->getCodePostal() != $_POST['cp'] ||
					   $adresse->getVille() != $_POST['ville'] ||
					   $adresse->getPays() != $_POST['pays']){
					   	$_POST['idadresse'] = 0;
						include 'adresse.php';   	
					}
					
					
					adresseDB::save($user->getEmail(),$adresse,false);
					*/
					
				$adresse = new adresse();
				$adresse->setId($_POST['idadresse']);
				$adresse->setNomRue(strtolower($nomrue));
				$adresse->setCodePostal($_POST['cp']);
				$adresse->setRaisonSocial($raisonsocial);
				$adresse->setVille(strtoupper($_POST['ville']));
				$adresse->setPays($_POST['pays']);
				
				adresseDB::update($adresse);
					
				}
				catch(Exception $e){
					// il y a eu des erreurs
					$erreur = $e->getMessage();
				}
				

			}
			} else
			{
				$adresse = new adresse();
				$adresse->setNomRue(strtolower($nomrue));
				$adresse->setCodePostal($_POST['cp']);
				$adresse->setRaisonSocial($raisonsocial);
				$adresse->setVille(strtoupper($_POST['ville']));
				$adresse->setPays($_POST['pays']);
			}
			if(empty($erreur)){
				if(isset($_GET['type']) && $_GET['type'] == 'fact'){
					// on est en train de traiter l'adresse de facturation
					$_SESSION['commande']['facturation'] = serialize($adresse);
					// on redirige vers la page pour afficher le recap
					header('HTTP/1.1 302 Found');
					header('Location: '.$basePath.'commande/recapitulatif');
				}
				else{
					$_SESSION['commande']['livraison'] = serialize($adresse);
					if((isset($_POST['same']) && $_POST['same'] == 1 )){
						// si la case a été coché, on ajoute également l'adresse de facturation
						$_SESSION['commande']['facturation'] = serialize($adresse);
						// on redirige vers la page pour afficher le recap de la commande
						header('HTTP/1.1 302 Found');
						header('Location: '.$basePath.'commande/recapitulatif');
					}
					else{
						// on redirige vers la page pour choisir l'adresse de facturation
						header('HTTP/1.1 302 Found');
						header('Location: '.$basePath.'commande/adresse_fact');
					}		
				}
			}
			else{
				$_SESSION['POST'] = $_POST;
				// il y a eu des erreurs, on redirige vers la bonne page
				$_SESSION['adresse_erreur'] = $erreur;
				header('HTTP/1.1 302 Found');
				if(isset($_GET['type'])){
					header('Location: '.$basePath.'commande/adresse_fact');
				}
				else{
					header('Location: '.$basePath.'commande/adresse');
				}
			}	
		}
		
	}
	else{
		// on redirige vers la page d'accueil, car on ne doit jamais pouvoir atteindre ce fichier avec une requete autre que post
		header('HTTP/1.1 404 Not Found');
	}
	?>

	