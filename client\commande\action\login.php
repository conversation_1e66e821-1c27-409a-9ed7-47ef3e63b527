<?php
	require_once dirname(__FILE__).'/../../../init.php';
	// @todo ajouter la traduction
	//	chargement des classes utilisateur,client,visiteur,administrateur
	require_once dirname(__FILE__).'/../../../class/internaute_class.php';
	require_once dirname(__FILE__).'/../../../class/client_class.php';
	require_once dirname(__FILE__).'/../../../class/panier_class.php';
	require_once dirname(__FILE__).'/../../../class/produit_class.php';
	
	//démarrage de la session
	
	
	$user = null;
	$erreur = false;
	$user = unserialize($_SESSION['user1']);
	
	///////RECALCUL LE PANIER POUR LES PRO AVEC LEUR REMISE
	$mailclient = strtolower($_POST['email']);
	$result = $database->prepare("
	SELECT * FROM clients WHERE emailclient = '$mailclient'") or die ("requete result invalid");
	$result->execute();
	$indiceclient = $result->fetch();
	if($indiceclient['indiceclient'] == "5" || $indiceclient['indiceclient'] == "6"){
			$user->getPanier()->recalculerPro($mailclient);
	}
	///////////////////////////////////////////////////////	

	$translate = Zend_Registry::get('translate');
	

	$_SESSION['login_error'] = "";
	// l'utilisateur vient de valider le formulaire de connexion
	if($mailclient === '' || $_POST['password'] === ''){
		$_SESSION['login_error'] = $translate->_('adresse email ou mot de passe incorrect');
		$erreur = true;
	}
	else{
		$user_temp = clone $user;
		$user = new client($mailclient);
		if($user->checkPassword($_POST['password']) !== true){
			//si le password est incorrect
			$user = clone $user_temp;
			$_SESSION['login_error'] = $translate->_('adresse email ou mot de passe incorrect');
			$erreur = true;
		}
		else{
			// le mot de passe est correct
			// on transfère le panier;	
			$user->setPanier($user_temp->getPanier());
		}
	}
	$_SESSION['user1'] = serialize($user);
	
	if($erreur == false){
		// l'authentification a été réussi, redirection vers la page suivante
		header('HTTP/1.1 302 Found');
		if(isset($_POST['annonce'])){
			header('Location: '.$basePath.'index.php?page=user&action=annonces&act=ajout');
		} else {
			if($user->isAdmin == true){
				header('Location: '.$basePath.'panier/valider');
			}
			else{
				header('Location: '.$basePath.'commande/adresse');
			}
		}	
	}
	else{
		$_SESSION['POST'] = $_POST;
		// on doit réafficher la page du formulaire de connexion
		header('HTTP/1.1 302 Found');
		header('Location: '.$basePath.'panier/valider');
	}