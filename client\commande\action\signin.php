<?php
//@todo ajouter la traduction
require_once dirname(__FILE__).'/../../../init.php';
require_once dirname(__FILE__).'/../../../utils/form_utile.php';
//	chargement des classes utilisateur,client,visiteur,administrateur
require_once dirname(__FILE__).'/../../../class/internaute_class.php';
require_once dirname(__FILE__).'/../../../class/client_class.php';
require_once dirname(__FILE__).'/../../../class/af_class.php';
	

// inscription d'un nouvelle utilisateur dans la base de données
try{
	// 1) on doit verifier que tous les champs sont present et non sont pas vide
	if(!(isset($_POST['email']) && $_POST['email'] != "" &&
	   isset($_POST['nom']) && $_POST['nom'] != "" &&
	   isset($_POST['prenom']) && $_POST['prenom'] != "" &&
	   isset($_POST['motdepasse']) && $_POST['motdepasse'] != "" &&
	   isset($_POST['motdepasseconfirme']) && $_POST['motdepasseconfirme']  != "" &&
	   isset($_POST['tel']) && $_POST['tel']  != "" &&
	   isset($_POST['raisonsocial']) && $_POST['raisonsocial'] != "" &&
	   isset($_POST['nomrue']) && $_POST['nomrue'] != "" &&
	   isset($_POST['cp']) && $_POST['cp'] != "" &&
	   isset($_POST['ville']) && $_POST['ville'] != "" &&
	   isset($_POST['pays']) && $_POST['pays'] != ""
	)){
		throw new Exception("Des champs obligatoire sont vides");
	}
	
		function stripAccents($str, $charset='utf-8')
		{
			$str = htmlentities($str, ENT_NOQUOTES, $charset);
			$str = preg_replace('#&([A-za-z])(?:acute|cedil|circ|grave|orn|ring|slash|th|tilde|uml);#', '\1', $str);
			$str = preg_replace('#&([A-za-z]{2})(?:lig);#', '\1', $str); // pour les ligatures e.g. '&oelig;'
			$str = preg_replace('#&[^;]+;#', '', $str); // supprime les autres caractères
			$str = str_replace("'", '', $str); // supprime les autres caractères
			
			return $str;
		}

		function delSpace($str2)
		{

			$str2 = preg_replace('#&[^;]+;-_.#', '', $str2); // supprime les caractères speciaux
			$str2 = preg_replace('# #', '', $str2); // supprime les espaces
			
			return $str2;

		}
		
	$nom=strtoupper(stripAccents($_POST['nom']));
	$prenom=strtolower(stripAccents($_POST['prenom']));
	$adresse=strtolower($_POST['email']);
	$mdp=$_POST['motdepasse'];
	$mdpc=$_POST['motdepasseconfirme'];
	$tel=delSpace($_POST['tel']);
	$fax=delSpace($_POST['fax']);

	$raisonsocial = stripAccents($_POST['raisonsocial']);
	$nomrue=strtolower(stripAccents($_POST['nomrue']));
	$cp=delSpace($_POST['cp']);
	$ville=strtoupper(stripAccents($_POST['ville']));
	$pays=$_POST['pays'];

	// 2) verifier le format des champs
	if($_POST['motdepasse'] != $_POST['motdepasseconfirme']){
		throw new Exception("Erreur : Les mots de passe sont différents");
	}
	if(!verifierAdresseMail($_POST['email'])){
		throw new Exception("Erreur : L'adresse mail n'est pas valide");		
	}	
	if(strlen($_POST['motdepasse'])< 6){
		throw new Exception("Erreur : Il faut un mot de passe de 6 caractères minimum");
	}
	
	// 3) Enregistrement du nouveau client
	
	if(clientDB::getClientByEmail($_POST['email']) != null){
		throw new Exception("Cette adresse email est déja utiliser par un client<br />Si vous avez perdu votre mot de passe, cliquez sur 'Mot de passe oublié'");
	}

	$client = new client();
	$client->setEmail($adresse);
	$client->setNom($nom);
	$client->setPrenom($prenom);
	$client->setPassword(md5($mdp));
	$client->setTel($tel);
	$client->setFax($fax);
	$date = date("Y-m-d");
	$client->setDatecreate($date);
	$client->setVehiculeprefer("");
	
	$client->setRaisonSocial($raisonsocial);
	$client->setNomRue($nomrue);
	$client->setCodePostal($cp);
	$client->setVille($ville);
	$client->setPays($pays);
	
	$client->setAf_Raisonsocial($raisonsocial);
	$client->setAf_Nomrue($nomrue);
	$client->setAf_Codepostal($cp);
	$client->setAf_Ville($ville);
	$client->setAf_Pays($pays);
	
	if(clientDB::saveNew($client) == false){
		throw new Exception("Erreur lors de l'enregistrement du client");
	}
	
	// Envoie d'un mail pour avertir le client qu'il est bien inscrit (permet de vérifier aussi que l'email est bien correct
	/*$headers="From:<EMAIL>\r\n";
	$objet= $translate->_('Inscription sur www.jeep-dodge-gmc.com');
	$sitesmi="www.jeep-dodge-gmc.com";*/
	// Contenu du message
	require_once dirname(__FILE__).'/../../mail_devenir_client.php';
	
	$_SESSION['infos'] = "Un email viens de vous être envoyé, il contient vos informations de connexion.";
	
	// Inscription à la newsletter
	
	if (isset($_POST['news'])){
	
	$mail = $adresse;
	
	$result = $database->prepare("
	SELECT count(adresse_mail) as doublon FROM newsletter WHERE adresse_mail = '$mail'
	") or die ("requete select");
	
	$result->execute();

	while ($row = $result->fetch()) {
	
	$doublon = $row['doublon'];
	$date = date("Y-m-d");
	
	}

		if ($doublon == 0){
	
			$result2 = $database->query("
			
			INSERT INTO newsletter (
			adresse_mail,
			date_valid_news
			) 
						
			VALUES (
			'".$mail."',
			'".$date."'
			
			)") or die ("requete insert ligne nd invalid");
				
		} else {
		
		}
	}
	
	// 4) connexion de l'utilisateur
	$user = unserialize($_SESSION['user1']);
	$client->setPanier($user->getPanier());
	$client->setEmail($adresse);
	$client->isAuthenticated = true;
	$_SESSION['user1'] = serialize($client);
	
	// 5) Redirection vers l'etape 2 de la commande
	header('HTTP/1.1 302 Found');
	if(isset($_POST['annonce'])){
		header('Location: '.$basePath.'index.php?page=user&action=annonces&act=ajout');
	} else {
	header('Location: '.$basePath);
	}
}
catch(Exception $e){
	$_SESSION['POST'] = $_POST;
	$_SESSION['signin_error'] = $translate->_($e->getMessage());
	// redirection vers la page pour l'inscription
	header('HTTP/1.1 302 Found');
	header('Location: '.$basePath.'utilisateur/s_inscrire');
}