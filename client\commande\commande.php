<?php
		if($user->isAuthenticated || isset($_SESSION['order_id'])){
			// si l'utilisateur est authentifié
			if($user->isAdmin){
				// si l'utilisateur est l'admin, il doit d'abord choisir pour quelle client il passe commande
				if(($_GET['page'] == 'panier' && $_GET['action'] == 'valider')){
					// afficher une page ou l'admin peut choisir le client
					require_once dirname(__FILE__).'/view/admin_choixclient.php';
				}
				else if($_GET['action'] == 'adresse'){
					require_once dirname(__FILE__).'/view/adresse.php';
				}
				else if($_GET['action'] == 'recap'){
					require_once dirname(__FILE__).'/view/recap.php';	
				}
				else if($_GET['action'] == 'pay'){
					require_once dirname(__FILE__).'/view/paiement.php';
				}
				else if($_GET['action'] == 'fin'){
					require_once dirname(__FILE__).'/view/fin_commande.php';
				}
				else if($_GET['action'] == 'erreur'){
					require_once dirname(__FILE__).'/view/erreur_commande.php';
				}
			}
			else{
				if($_GET['action'] == 'adresse' || ($_GET['page'] == 'panier' && $_GET['action'] == 'valider')){
					require_once dirname(__FILE__).'/view/adresse.php';
				}
				else if($_GET['action'] == 'recap'){
					require_once dirname(__FILE__).'/view/recap.php';	
				}
				else if($_GET['action'] == 'pay'){
					require_once dirname(__FILE__).'/view/paiement.php';
				}
				else if($_GET['action'] == 'fin'){
					require_once dirname(__FILE__).'/view/fin_commande.php';	
				}
				else if($_GET['action'] == 'erreur'){
					require_once dirname(__FILE__).'/view/erreur_commande.php';
				}
			}
		}
		else{
			require_once dirname(__FILE__).'/view/login_or_subscribe.php';
		}
	