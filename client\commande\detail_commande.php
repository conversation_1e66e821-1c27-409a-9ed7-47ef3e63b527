<?php
	// affichage du detail d'une commande, seulement si c'est commande appartient a l'utilisateur connecté ou si l'utilisateur est admin
	require_once dirname(__FILE__).'/../../class/commande_static_class.php';
	$commande = commande_staticDB::getCommandeById($_GET['id']);
	
	if($user->isAuthenticated == true){	
	
	if($user->isAdmin || $user->getEmail() == $commande->getEmail()){	
	$signataire = $commande->getIscommandeadmin();
	if ($signataire == "0"){
		$sign = "Client";
	} else {
		$sign = $signataire;
	}
	
	
?>

<div class="form-style-10">	
<div class="section"><span>¤</span>Detail de la commande <?php echo $commande->getId(); ?></div>

<table>
<tr>
<td>
<div class="inner-wrap" style="width:340px; height:110px;">
<?php	
	if($user->isAdmin){
?>				
    Date : <?php echo date('d/m/Y',strtotime($commande->getDate())); ?><br />
	Raison sociale : <a href="<?php echo $basePath; ?>index.php?page=admin&action=gererclient&type=modif&id=<?php echo $commande->getEmail(); ?>"><?php echo $commande->getNomprenom(); ?>&nbsp;&nbsp;<img src='<?php echo $basePath; ?>public/images/zoom.png'/></a><br />
	E-mail : <a href="mailto:<?php echo $commande->getEmail(); ?>"><?php echo $commande->getEmail(); ?></a><br />
	Téléphone : <?php echo $commande->getTel(); ?><br />
	Mode de paiement : <?php echo $commande->getModepaiement(); ?><br />
	Signataire : <?php echo $sign; ?><br />
<?php	
	} else {
?>
	Date : <?php echo date('d/m/Y',strtotime($commande->getDate())); ?><br />
	Raison sociale : <?php echo $commande->getNomprenom(); ?><br />
	E-mail : <?php echo $commande->getEmail(); ?><br />
	Téléphone : <?php echo $commande->getTel(); ?><br />
	Mode de paiement : <?php echo $commande->getModepaiement(); ?><br />
	Signataire : <?php echo $sign; ?><br />
	
<?php
	}
?>
</div>
</td><td>
<div class="inner-wrap" style="width:340px; height:110px;">
<?php	
// -- cde facturée	

// Affiche le numero de facture		
$idcde = $commande->getId();
$result = $database->prepare("SELECT count('$idcde') AS cal_idcde FROM facture WHERE id_command = '$idcde'") or die ("requete r1 invalid");
$result->execute();

while ($tab = $result->fetch()) {
	if ($tab['cal_idcde'] >= "1"){
				
	$result2 = $database->prepare("SELECT * FROM facture WHERE id_command = '$idcde' order by id_facture desc limit 1") or die ("requete r2 invalid");
	$result2->execute();
						
	while ($tab2 = $result2->fetch()) {

?>
	<a  href='<?php echo $basePath; ?>client/commande/impression_facture.php?id=<?php echo $idcde; ?>' target="_blank">
	Facturée numero <b> <?php echo $tab2['id_facture']; ?> </b><img src='<?php echo $basePath; ?>public/images/zoom.png'/></a> <br />
	<a  href='<?php echo $basePath; ?>client/commande/impression_commande.php?id=<?php echo $idcde; ?>' target='_blank'><img style="margin-top:10px;" src='<?php echo $basePath; ?>public/images/gtk-print.png'/><b> Imprimer le bon de commande</b></a><br />
<?php	
	}
	} else {
	
// cde non facturée		
		if($user->isAdmin){
?>	
	<a href='<?php echo $basePath; ?>index.php?page=admin&action=facture&type=edit&idcommande=<?php echo $idcde; ?>'> <img src='<?php echo $basePath; ?>public/images/facture.png'/><b> Editer la facture</b></a><br />
	<a href='<?php echo $basePath; ?>client/commande/impression_commande.php?id=<?php echo $idcde; ?>' target='_blank'><img src='<?php echo $basePath; ?>public/images/gtk-print.png'/><b> Imprimer le bon de commande</b></a><br />
<?php		
		} else {
?>
	<a href='<?php echo $basePath; ?>client/commande/impression_commande.php?id=<?php echo $idcde; ?>' target='_blank'><img src='<?php echo $basePath; ?>public/images/gtk-print.png'/><b> Imprimer le bon de commande</b></a>
<?php						
		}
	}					
}
			
/////////////// AFFICHE LA ZONE SUIVI COLIS ////////////////////
				
				if ($commande->getStatut() == "Envoyée"){
				
					$id = $_GET['id'];
					$result6 = $database->prepare("SELECT COUNT(*) AS nb_fact FROM facture WHERE id_command = '$id'") or die ("requete r6 invalid");
					$result6->execute();
					$nb_fact = $result6->fetch();
					
					if ($nb_fact['nb_fact'] != 0){
						
						$result5 = $database->prepare("SELECT * FROM facture WHERE id_command = '$id'") or die ("requete r5 invalid");
						$result5->execute();
						
						while ($tab5 = $result5->fetch()) {
							$info_sup = explode(";", $tab5['info_sup']);
							$post626 = substr($info_sup[0], 0, 4);
							
							if (isset($info_sup[1]) && $info_sup[1] != "" && $post626 == "626P"){
								
			?>
					<br />Suivi colis numéro : <a target="_blank" href="http://www.laposte.fr/particulier/outils/suivre-vos-envois?code=<?php echo $info_sup[1]; ?>" ><strong><?php echo $info_sup[1]; ?> </strong><img src='<?php echo $basePath; ?>public/images/zoom.png'/></a>
			<?php
							}
						}
					}
				}
?>
</div>
</td>
</tr>
</table>
<?php	
// --- AFFICHE LES ADRESSES 		
		
if($commande->getRetraitmagasin()){
?>

	<table>
	<tr>
	<td>
	<div class="inner-wrap" style="width:340px;">
		<h1>Adresse de facturation</h1>
		<?php echo stripslashes($commande->getAdressefact()); ?>
	</div>
	</td><td>	
	<div class="inner-wrap" style="width:340px; font-size:14px;">
		Vous avez choisi le mode de livraison <strong>"retrait en magasin"</strong><br />
		Les frais de port ne sont donc pas calculés !<br />
		Le retrait en magasin est valable seulement pour : <br />
		 - un retrait sur place <br />
		 - un reliquat d'une commande précédente <br />
		 - une livraison sur une bourse <br />
	</div>
	</td>
	</tr>
	</table>

<?php
} else {
?>

	<table>
		<tr>
			<td>
	<div class="inner-wrap" style="width:340px;">
		<h1>Adresse de facturation</h1>
		<?php echo stripslashes($commande->getAdressefact()); ?>
	</div>				
			</td><td>
		<div class="inner-wrap" style="width:340px;">
		<h1>Adresse de Livraison</h1>
		<?php echo stripslashes($commande->getAdresselivr()); ?>
		</div>				
			</td>
		</tr>
	</table>

<?php
}	
	
// affichage du detail de la commande
	$lignePs = $commande->getPanier();
// @todo afficher le detail de la commande
?>
<div class="inner-wrap">
<table style="font-size:14px; width: 740px;" border="0" cellspacing="0" cellpadding="0">
	<tr>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000;"></th>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000; padding-right:3px; padding-top:7px; padding-bottom:7px;"><?php echo $translate->_('Référence'); ?></th>
		<th style="text-align:center; background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000;"><?php echo $translate->_('Genre'); ?></th>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000;text-align:center;"><?php echo $translate->_('Groupe'); ?></th>
<?php
if ($user->isAdmin == true){
		echo '<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000;"></th>';
}
?>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000;"><?php echo $translate->_('Désignation'); ?></th>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000; width:65px;"><?php echo $translate->_('Prix HT'); ?></th>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000; width:65px;"><?php echo $translate->_('Quantité'); ?></th>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000; width:65px;"><?php echo $translate->_('Total HT'); ?></th>
<?php
if($user->isAdmin == true){
		echo '<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000;">ND</th>';
}
?>	
	</tr>
			
	<script language="JavaScript" src="<?php echo $basePath;?>public/js/formlimiterdecompter.js"></script>
	<form action="<?php echo $basePath; ?>admin/commande/modif_commande.php" method="post" name="statut" >

			<?php 
			
			$i = 0;
			
			foreach($lignePs->getLignePanier() as $k => $ligneP):	
			
			
			//QUALITE
					
					$reference = $ligneP->getReference();
					$db = Zend_registry::get("database");
					$result_color = $db->prepare("
					SELECT * FROM produits WHERE referenceproduit = '$reference'
					") or die ("requete r1 invalid");
					$result_color->execute();
					$color = $result_color->fetch();
				if($user->isAdmin){	
					$result_nd = $db->prepare("SELECT produit_nd FROM static_lignes_commandes WHERE produit_reference = '$reference' AND commande_id = '$idcde'") or die ("requete result_nd invalid");
					$result_nd->execute();
					$nd = $result_nd->fetch();	
				}
				if($user->isAdmin){	
					$result_nondispo = $db->prepare("SELECT dispo FROM non_dispo WHERE reference = '$reference'") or die ("requete result_nondispo invalid");
					$result_nondispo->execute();
					$non_dispo = $result_nondispo->fetch();	
				}
				if ($user->isAdmin && $nd['produit_nd'] == "1" && $non_dispo['dispo'] == "1"){
					echo '<tr style="border-bottom:1px dotted black; background-color:#FF6600;">';
					$ND = "<span style='color:red;'>Non Disponible</span>";
				} elseif ($user->isAdmin && $nd['produit_nd'] == "1" && $non_dispo['dispo'] == "2"){
					echo '<tr style="border-bottom:1px dotted black; background-color:#248202;">';
					$ND = "<span style='color:green;'>Disponible</span>";
				} else {
					echo '<tr style="border-bottom:1px dotted black;">';
					$ND = "";
				}
						if (file_exists("public/images/produits/".$ligneP->getReference().".jpg"))
						{
							// IMAGE
						echo '<td style="padding-left:3px;padding-top:3px; padding-bottom:3px;background-color:white;border-bottom: 2px solid #000000;" >';
						echo "<a href=\"".Zend_Registry::get('basePath')."public/images/produits/".$ligneP->getReference().".jpg?date=".date('ymd')."\" rel=\"lytebox\" title=\"".$ligneP->getReference()."\" >";
						echo "<img src=\"".Zend_Registry::get('basePath')."public/images/produits/".$ligneP->getReference().".jpg?date=".date('ymd')."\" style=\"margin-top:3px;\" height=\"40\" />";
						echo "</a>";
						echo "</td>";
			
						}else{
							echo '<td style="padding-left:3px; padding-top:8px; padding-bottom:8px;background-color:white;border-bottom: 2px solid #000000;"></td>';
						}	
				?>
					<td style="padding-left:3px; padding-top:8px; padding-bottom:8px;background-color:white;border-bottom: 2px solid #000000;" ><?php echo $ligneP->getReference(); ?></td>
					<?php $tab = explode(" / ",$ligneP->getGenregroupe()); 
					if(empty($tab[2])){
					$tab[2]=$tab[1];
					}
					?>
					<td style="font-size:11px; padding-left:3px;padding-top:8px; padding-bottom:8px;background-color:white;border-bottom: 2px solid #000000;" ><?php echo $tab[0]; ?></td>
					<td style="font-size:11px; padding-left:3px;padding-top:8px; padding-bottom:8px;background-color:white;border-bottom: 2px solid #000000;" ><?php echo $tab[2]; ?></td>
				<?php
					if ($user->isAdmin == true){
					
						$stock = $color['commande'];
						$stock2 = $color['promo'];
						
						if ($stock == "?" || $stock2 == "ooo" || $ligneP->getPuHT() == "-1"){
						$result_stock = '<img src="'.$basePath.'public/images/stock_2.png" />';
						}
								if ($stock2 == "oo"){
								$result_stock = '<img src="'.$basePath.'public/images/stock_2.png" />';
								}
								if (!empty($stock) && $stock != "?" && $stock2 != "ooo" && $ligneP->getPuHT() != "-1"){
								$result_stock = '<img src="'.$basePath.'public/images/stock_3.png" />';
								}
								
								if (empty($stock)){
									if ($stock2 == "o"){
								$result_stock = '<img src="'.$basePath.'public/images/stock_4.png" />';
									} else {
								$result_stock = '<img src="'.$basePath.'public/images/stock_5.png" />';
									}
								}
						echo '<td style="padding-left:3px; padding-top:8px; padding-bottom:8px;background-color:white;border-bottom: 2px solid #000000;">'.$result_stock.'</td>';
					}
					$genre = explode('/',$ligneP->getGenregroupe());
					$genre2 = str_replace(' ','',$genre[0]);
					if($user->isAdmin){
						if ($nd['produit_nd'] == "0"){
						?>
						<td style="padding-left:3px;padding-top:8px; padding-bottom:8px;background-color:white;border-bottom: 2px solid #000000;" ><a target="_blank" href="<?php echo $basePath; ?>description/<?php echo strtolower($genre2); ?>/<?php echo $ligneP->getReference(); ?>" ><?php echo $ligneP->getDesignation(); ?> </a></td>
						<?php
						} else {
						?>
						<td style="padding-left:3px;padding-top:8px; padding-bottom:8px;background-color:white;border-bottom: 2px solid #000000;"><a target="_blank" href="<?php echo $basePath; ?>description/<?php echo strtolower($genre2); ?>/<?php echo $ligneP->getReference(); ?>" ><?php echo $ligneP->getDesignation(); ?> </a><b><?php echo $ND; ?></b></td>
						<?php
						}
					} else {
					?>
					<td style="padding-left:3px;padding-top:8px; padding-bottom:8px;background-color:white;border-bottom: 2px solid #000000;" ><?php echo $ligneP->getDesignation(); ?></td>
					<?php
					}
					?>
					<td style="padding-left:3px;padding-top:8px; padding-bottom:8px;background-color:white;border-bottom: 2px solid #000000;" align="right"><?php printf("%.2f", $ligneP->getPuHT());?> €</td>						
					<td style="padding-left:3px; padding-top:8px; padding-bottom:8px;background-color:white;border-bottom: 2px solid #000000;" align="center"><?php echo $ligneP->getQte();?></td>
					<td style="padding-left:3px;padding-top:8px; padding-bottom:8px;background-color:white;border-bottom: 2px solid #000000;" align="right"><?php printf("%.2f", $ligneP->getSomme());?> €</td>
					<?php
					if($user->isAdmin){			
						if ($nd['produit_nd'] == "0"){
						echo '<td style="padding-left:3px;padding-top:8px; padding-bottom:8px;background-color:white;border-bottom: 2px solid #000000;"><input type="checkbox" name="nd[]" value="'.$ligneP->getReference().'"></td>';
						}else{
						echo '<td style="padding-left:3px;padding-top:8px; padding-bottom:8px;background-color:white;border-bottom: 2px solid #000000;"><input type="checkbox" name="dispo[]" value="'.$ligneP->getReference().'"></td>';
						}
					}
					?>
				 </tr>
			<?php endforeach; ?>
			<tr>
				<?php
					if ($user->isAdmin == true){
					echo '<td colspan="5" rowspan="5"></td>';
					} else {
					echo '<td colspan="4" rowspan="5"></td>';
					}
				?>
				<td align="right"><?php echo $translate->_('Montant total HT');?> :</td>
				<td></td>
				<td></td>
				<td style="padding-right:3px;padding-top:4px; padding-bottom:4px;" align="right"><?php printf('%.2f',$commande->getMontanttotalHT()); ?> €</td>
			</tr>
			<tr>
			<?php
			if (($commande->getRemise_Fdp()) == "0"){
			?>
				<td align="right"><?php echo $translate->_('Frais de port + emballage'); ?> : </td>
			<?php
			} else {
			?>
				<td align="right"><?php echo $translate->_('Frais de port remisé de -'); ?><?php printf('%.2f',$commande->getRemise_Fdp()); ?>€ : </td>
			<?php
			}
			?>
				<td></td>
				<td></td>
				<td style="padding-right:3px;padding-top:4px; padding-bottom:4px;" align="right"><?php printf('%.2f',$commande->getFdp()); ?> €</td>
			</tr>
			<tr>
				<td align="right">TVA <?php printf('%.2f',$commande->getTauxTVA()); ?>% : </td>
				<td></td>
				<td></td>
				<td style="padding-right:3px;padding-top:4px; padding-bottom:4px;" align="right"><?php printf('%.2f',$commande->getMontantTVA()); ?> €</td>
			</tr>
			<tr>
				<td style="border-top:2px solid black;border-bottom:2px solid black;" align="right"><b><?php echo $translate->_('Total de la commande TTC'); ?> : </b></td>
				<td style="border-top:2px solid black;border-bottom:2px solid black;"> </td>
				<td style="border-top:2px solid black;border-bottom:2px solid black;"></td>
				<td style="color:white;background-color:black;border-top:2px solid black;border-bottom:2px solid black;" align="right"><b><?php printf('%.2f',$commande->getMontanttotalTTC());//+$commande->getFraisDePort()); ?> €</b></td>
				<td style="background-color:black;border-top:2px solid black;border-bottom:2px solid black;"></td>
			</tr>
		</table>
</div>		
		<?php
		// si le client est admin, il faudra afficher le formulaire pour qu'il puissent modifier le status de la commande
		if($user->isAdmin){
			// affichage du formulaire de modification
			$message_pb_pay = "
			Bonjour, %0D%0A 
			%0D%0A 
			Nous avons bien reçu votre commande numéro ".$commande->getId().", cependant nous avons rencontré un problème lors de la validation de votre paiement.%0D%0A 
			Le plus rapide est de refaire une nouvelle fois votre commande avec un moyen de paiement valide.
			Vous pouvez aussi nous contacter par mail : <EMAIL> ou par téléphone : ***********.56. afin de nous transmettre un moyen de paiement valide.%0D%0A 
			%0D%0A 
			Cordialement, %0D%0A 
			Service commercial.%0D%0A 
			%0D%0A 
			
			-------------------------------------%0D%0A 
			Hello,%0D%0A 
			%0D%0A 
			We received your order number ".$commande->getId()." and there is problem with your payment.%0D%0A 
			Thank you to contact us by mail: <EMAIL> to ship your order in the shortest delays.%0D%0A 
			%0D%0A 
			Best regardd%0D%0A 
			Customer services%0D%0A 
			%0D%0A 
			
			-------------------------------------%0D%0A 
			Surplus Militaires et Industriels %0D%0A 
			D1092, La gare %0D%0A 
			38840 La Sone %0D%0A 
			FRANCE %0D%0A 
			Tel : +33 (0) 476 644 356 %0D%0A 
			Fax : +33 (0) 476 644 555 %0D%0A 
			@ : <EMAIL> %0D%0A 
			Web : www.jeep-dodge-gmc.com %0D%0A 
			-------------------------------------  %0D%0A ";
			
			$message_pb_nd = "
			Bonjour, %0D%0A 
			%0D%0A 
			Nous avons bien reçu votre commande numéro ".$commande->getId().", cependant la ou les pièces suivantes ne sont pas disponibles :%0D%0A 
			%0D%0A 
			%0D%0A 
			Merci de nous contacter par mail : <EMAIL> ou par téléphone : ***********.56. afin de savoir si vous souhaitez ou non votre commande partielle.%0D%0A 
			%0D%0A 
			PS : Lors d’un paiement par Carte Bancaire nous ne débitons que la somme facturée. Les pièces manquantes ne sont pas facturées et donc pas débitées de votre compte. 
			Vous serez prévenu automatiquement par e-mail de leur réapprovisionnement, vous bénéficierez alors de frais de port gratuit pour l'expédition du reliquat. 
			%0D%0A 
			Cordialement, %0D%0A 
			Service commercial.%0D%0A 
			%0D%0A 
			-------------------------------------%0D%0A 
			Hello,%0D%0A 
			%0D%0A 
			We received your order number ".$commande->getId().", but some parts are not available:%0D%0A
			%0D%0A 
			%0D%0A 
			Thank you to contact us <NAME_EMAIL> if you want or not your partial order.%0D%0A
			PS: When paying by credit card we debit the invoice amount. Missing parts are not invoiced and is therefore not debited from your account. We will contact you by email when their resupply.%0D%0A
			%0D%0A 
			Regards%0D%0A 
			Customer services%0D%0A 
			%0D%0A 
			-------------------------------------%0D%0A 
			Surplus Militaires et Industriels %0D%0A 
			D1092, La gare %0D%0A 
			38840 La Sone %0D%0A 
			FRANCE %0D%0A 
			Tel : +33 (0) 476 644 356 %0D%0A 
			Fax : +33 (0) 476 644 555 %0D%0A 
			@ : <EMAIL> %0D%0A 
			Web : www.jeep-dodge-gmc.com %0D%0A 
			-------------------------------------  %0D%0A ";
			
			$message_pb_qualite = "
			Bonjour, %0D%0A 
			%0D%0A 
			Nous avons bien reçu votre commande numéro ".$commande->getId().", cependant la ou les pièces suivantes nécessitent une validation de votre part :%0D%0A 
			%0D%0A 
			%0D%0A 
			Merci de nous contacter par mail : <EMAIL> ou par telephone : ***********.56. afin de savoir si vous souhaitez maintenir ou non votre commande.%0D%0A 
			%0D%0A 
			Cordialement, %0D%0A 
			Service commercial.%0D%0A 
			%0D%0A 
			-------------------------------------%0D%0A 
			Hello,%0D%0A 
			%0D%0A 
			We received your order number ".$commande->getId().", but some parts need quality validation : %0D%0A
			%0D%0A 
			%0D%0A 
			Thank you to contact us <NAME_EMAIL> if you want this/these parts or not.%0D%0A
			%0D%0A 
			Regards%0D%0A 
			Customer services%0D%0A 
			%0D%0A 
			-------------------------------------%0D%0A 
			Surplus Militaires et Industriels %0D%0A 
			D1092, La gare %0D%0A 
			38840 La Sone %0D%0A 
			FRANCE %0D%0A 
			Tel : +33 (0) 476 644 356 %0D%0A 
			Fax : +33 (0) 476 644 555 %0D%0A 
			@ : <EMAIL> %0D%0A 
			Web : www.jeep-dodge-gmc.com %0D%0A 
			-------------------------------------  %0D%0A ";
			
			$message_complement_trans = "
			Bonjour,%0D%0A
			%0D%0A
			Nous avons bien reçu votre commande cependant le poids des pièces dépasses les frais de port automatiquement calculés.%0D%0A
			Un complément de frais de port de … est donc nécessaire pour l’envoie de votre colis.%0D%0A
			Vous pouvez le faire par carte bancaire (par téléphone), chèque, virement ou paypal (<EMAIL>). Merci.%0D%0A
			%0D%0A
			Plus d'informations pour comment procéder ici : http%3A%2F%2Fwww.jeep-dodge-gmc.com%2Fsmi%2Findex.php%3Fpage%3Daide%26type%3Dfdpsup %0D%0A
			%0D%0A
			Cordialement.%0D%0A
			Le service expédition.%0D%0A
			%0D%0A
			-------------------------------------%0D%0A 
			%0D%0A
			Hello,%0D%0A
			%0D%0A
			We have received your order however weight of the package exceeded the automatically calculated shipping costs.%0D%0A
			An additional shipping cost about ... is necessary for sending your package.%0D%0A
			You can do it by credit card (by phone), bank transfer or PayPal (<EMAIL>). Thank you.%0D%0A
			%0D%0A
			More information for how proceed here : http%3A%2F%2Fwww.jeep-dodge-gmc.com%2Fsmi%2Findex.php%3Fpage%3Daide%26type%3Dfdpsup %0D%0A
			%0D%0A
			Regards.%0D%0A
			The shipping service.%0D%0A
			%0D%0A
			-------------------------------------%0D%0A 
			Surplus Militaires et Industriels %0D%0A 
			D1092, La gare %0D%0A 
			38840 La Sone %0D%0A 
			FRANCE %0D%0A 
			Tel : +33 (0) 476 644 356 %0D%0A 
			Fax : +33 (0) 476 644 555 %0D%0A 
			@ : <EMAIL> %0D%0A 
			Web : www.jeep-dodge-gmc.com %0D%0A 
			-------------------------------------  %0D%0A ";
			
			if (isset($_SESSION['who']) && $_SESSION['who'] != ""){
			$who = $_SESSION['who'];
			} else {
			$who = "";
			}
			
			?>
			<script type="text/javascript">
			
			function msgcommentpaiement() {
			var ladate=new Date();
			var date = ladate.getDate()+"/"+(ladate.getMonth()+1)+"/"+ladate.getFullYear();
			var valeur=document.getElementById('commentdetail').value;
			document.getElementById('commentdetail').value=valeur+date+" fait mail pour pb de paiement. <?php echo $who; ?>";
			
			}
			
			function msgcommentnd() {
			var ladate=new Date();
			var date = ladate.getDate()+"/"+(ladate.getMonth()+1)+"/"+ladate.getFullYear();
			var valeur=document.getElementById('commentdetail').value;
			document.getElementById('commentdetail').value=valeur+date+" fait mail pour pb de nd. <?php echo $who; ?>";
			
			}
			
			function msgcommentqualite() {
			var ladate=new Date();
			var date = ladate.getDate()+"/"+(ladate.getMonth()+1)+"/"+ladate.getFullYear();
			var valeur=document.getElementById('commentdetail').value;
			document.getElementById('commentdetail').value=valeur+date+" fait mail pour pb de qualite. <?php echo $who; ?>";
			
			}
			
			function msgcommentfdp() {
			var ladate=new Date();
			var date = ladate.getDate()+"/"+(ladate.getMonth()+1)+"/"+ladate.getFullYear();
			var valeur=document.getElementById('commentdetail').value;
			document.getElementById('commentdetail').value=valeur+date+" fait mail pour pb de fdp. <?php echo $who; ?>";
			
			}
			
			
			</script>	
<?php
	
	/////////////// AFFICHE LA ZONE COMMENTAIRE ////////////////////
?>
<div class="inner-wrap">
	<div class="section"><span>¤</span>Commentaire (Visible pour le client) : &nbsp;</div>
	<label>Attention, quand vous changer le statut d'une commande, un email est envoyé au client.</label>	
	<textarea id="commentdetail" name="comment" rows="3" maxlength="199" ><?php echo $commande->getComment(); ?></textarea><br /><br />
			
			<script type="text/javascript">
			fieldlimiter.setup({
			//Récupération des données du champ
			thefield: document.statut.comment,
			//On limite le champ à 10 caratères
			maxlength: 199,
			//id pour le retour des informations
			statusids: ["george-status"],
			//Lorsque l'on appuie sur une touche
			//on vérifie si le texte n'est pas trop long
			onkeypress:function(maxlength, curlength){
			if (curlength<maxlength)
			//Bordure du champ en gris si le nombre n'est pas dépasser
			this.style.border="2px solid gray"
			else
			//Bordure du champ en rouge si le nombre est dépasser
			this.style.border="2px solid red"
			}
			})
			</script>		
	<div class="section"><span>¤</span>Envoyer un e-mail au client pour un problème de :  &nbsp;</div>		
		<table style="margin-top:15px; width:730px; text-align:center;">
		<tr>
		<td>
			<a class="buttonhref" href="mailto:<?php echo $commande->getEmail(); ?>?subject=Commande <?php echo $commande->getId(); ?> : Problème paiement&body=<?php echo $message_pb_pay; ?>" onclick="msgcommentpaiement();">PAIEMENT</a>
		</td><td>
			<a class="buttonhref" href="mailto:<?php echo $commande->getEmail(); ?>?subject=Commande <?php echo $commande->getId(); ?> : Problème pièce(s) non disponible(s)&body=<?php echo $message_pb_nd; ?>" onclick="msgcommentnd();">NON DISPO</a>
		</td><td>
			<a class="buttonhref" href="mailto:<?php echo $commande->getEmail(); ?>?subject=Commande <?php echo $commande->getId(); ?> : Problème qualité&body=<?php echo $message_pb_qualite; ?>" onclick="msgcommentqualite();">QUALITE</a>
		</td><td>	
			<a class="buttonhref" href="mailto:<?php echo $commande->getEmail(); ?>?subject=Commande <?php echo $commande->getId(); ?> : Problème FDP&body=<?php echo $message_complement_trans; ?>" onclick="msgcommentfdp();">FDP</a>
		</td>
		</tr>
		</table>
	</div>
	<div class="inner-wrap">
	<div class="section"><span>¤</span>Modifier la commande</div>
	<table>
	<tr>
	<td>
						<label for="stat">Etat de la commande :</label>
							<select name="stat">
								<option value="1" <?php echo ($commande->getStatut() == "Attente de paiement")?"selected='selected'":''; ?>>Attente de paiement</option>
								<option value="2" <?php echo ($commande->getStatut() == "Paiement validé")?"selected='selected'":''; ?>>Paiement validé</option>
								<option value="3" <?php echo ($commande->getStatut() == "En cours de préparation")?"selected='selected'":''; ?>>En cours de préparation</option>
								<option value="4" <?php echo ($commande->getStatut() == "Envoyée")?"selected='selected'":''; ?>>Envoyée</option>
								<option value="5" <?php echo ($commande->getStatut() == "En cours de réapprovisionnement")?"selected='selected'":''; ?>>Attente de réappro</option>
								<option value="6" <?php echo ($commande->getStatut() == "Sur place")?"selected='selected'":''; ?>>Sur place</option>
								<option value="-1" <?php echo ($commande->getStatut() == "Annulée")?"selected='selected'":''; ?>>Annulée</option>
								<option value="7" <?php echo ($commande->getStatut() == "Réponse client")?"selected='selected'":''; ?>>Réponse client</option>
								<option value="8" <?php echo ($commande->getStatut() == "Scellius Annule")?"selected='selected'":''; ?>>Scellius Annule</option>
							</select>
	</td><td>						
						<input style="width: 14px; margin-left: 50px;" type="checkbox" name="no_send" id="no_send" />Changer sans prévenir				
	</td>
	</tr>
	<tr>
	<td></td><td>
	<input style="width: 14px; margin-left: 50px;" type="checkbox" name="imprim" id="imprim" value="1" />Cocher si vous avez imprimé
	</td>
	</tr>
	</table>
	</div>
	<div class="button-section">
					<input type="hidden" name="idcommande" value="<?php echo $commande->getId(); ?>" />
					<?php if (isset($_GET['filtre'])){ ?>
					<input type="hidden" name="filtre" value="<?php echo $_GET['filtre']; ?>" />
					<?php } else { ?>
					<input type="hidden" name="filtre" value="0" />
					<?php } ?>
					<input type="submit" name="modifier" value="Modifier" class="btn_submit"/>
	</div>
</div>	
			</form>
			<?php	
		} else {
?>	
		<div class="inner-wrap">
		<div class="section"><span>¤</span>Commentaire : &nbsp;</div>
		<textarea placeholder="Aucun commentaire sur cette commande" disabled name="comment" rows="3"><?php echo $commande->getComment(); ?></textarea>
		<br />
		<div class="section"><span>¤</span>Nous contacter par e-mail pour un problème de :  &nbsp;</div>		
			<table style="margin-top:15px; width:730px; text-align:center;">
			<tr>
			<td>
				<a class="buttonhref" href="mailto:<EMAIL>?subject=Commande <?php echo $commande->getId(); ?> : Problème paiement" >PAIEMENT</a>
			</td><td>
				<a class="buttonhref" href="mailto:<EMAIL>?subject=Commande <?php echo $commande->getId(); ?> : Problème pièce(s) non disponible(s)" >NON DISPO</a>
			</td><td>
				<a class="buttonhref" href="mailto:<EMAIL>?subject=Commande <?php echo $commande->getId(); ?> : Problème qualité" >QUALITE</a>
			</td><td>	
				<a class="buttonhref" href="mailto:<EMAIL>?subject=Commande <?php echo $commande->getId(); ?> : Problème FDP" >FDP</a>
			</td>
			</tr>
			</table>
		</div>
<?php		
		}
		
	} else {
		// vous n'avez pas le droit d'afficher cette commande
		echo '<div class="error messageBox"><strong>Vous n\'avez pas l\'accés pour afficher cette commande !<br /><br /></strong>';
		echo "Si il s'agit d'une erreur de notre site Internet merci de prendre contact avec nous par e-mail : <a href='mailto:<EMAIL>'>mailto:<EMAIL></a><br /><br />";
		echo "Sinon votre adresse IP a été enregistrée : ";
		echo $_SERVER["REMOTE_ADDR"];
		echo " elle sera transmise aux autorités compétantes pour tentative de piraterie !";
		echo '</div>';
	}
	
} else {
		echo '<div class="error messageBox"><strong>Vous n\'avez pas l\'accés pour afficher cette commande !<br /><br /></strong>';
		echo "Si il s'agit d'une erreur de notre site Internet merci de prendre contact avec nous par e-mail : <a href='mailto:<EMAIL>'>mailto:<EMAIL></a><br /><br />";
		echo "Sinon votre adresse IP a été enregistrée : ";
		echo $_SERVER["REMOTE_ADDR"];
		echo " elle sera transmise aux autorités compétantes pour tentative de piraterie !";
		echo '</div>';
}
?>	
</div>