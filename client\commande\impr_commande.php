<?php
	header("Content-Type: text/html; charset=UTF-8");
	
	// page qui permet d'imprimer un bon de commande
	require_once dirname(__FILE__).'/../../init.php';
	require_once dirname(__FILE__).'/../../visiteur/login.php';
	require_once 'commande_static_class.php';
	require_once dirname(__FILE__).'/../../utils/form_utile.php';
	$cs = commande_staticDB::getCommandeById($_GET['id']);

	if($cs->getEmail() == $user->getEmail() || $user->isAdmin)
	{
		echo "<html><head></head><body onload=\"window.print()\"><pre>";
		echo "\n--------------------\n";
		echo "Commande Internet pour : Surplus Militaires et Industriels\n";
		echo "D1092, La Gare - 38840 LA SONE - FRANCE\n";
		echo "E-mail : <EMAIL> / Tél. : (+33) 476 644 356\n";
		echo "+ de 500 tonnes de pièces / Site : http://jeep-dodge-gmc.com\n"; 
		echo "--------------------\n\n";
		echo "Commande du : ".date('d/m/Y',strtotime($cs->getDate()))."\n";
		echo "N° de Commande : ".$cs->getId()."\n";
		echo "Mode de paiement : ".$cs->getModepaiement()."\n";
		echo "Identifiant client : ".$cs->getEmail()."\n";
		echo "Téléphone : ".$cs->getTel()."\n";
		echo "Fax : ".$cs->getFax()."\n\n";
		
			
		if ($cs->getRetraitmagasin())
			echo "Commande à RETIRER en magasin.\n\n";
		else {
			echo "Adresse de LIVRAISON :\n";
			echo str_replace("<br />","\n",$cs->getAdresselivr())."\n";
		}
		
		echo "Adresse de FACTURATION :\n";
		echo str_replace("<br />","\n",$cs->getAdressefact())."\n";
	

		echo "--------------------\n";
		echo "Contenu de la commande\n";
		echo "--------------------\n\n";
		foreach($cs->getPanier()->getLignePanier() as $k => $lignePs)
		{
			echo $translate->_('Référence')." : ".$lignePs->getReference()."\n";
			echo $translate->_('Genre')." / ".$translate->_('Groupe')." : ".$lignePs->getGenregroupe()."\n";
			echo $translate->_('Désignation')." : ".$lignePs->getDesignation()."\n";
			echo $translate->_('Quantité')." * ".$translate->_('Prix unitaire HT')." : ".$lignePs->getQte()." * ";
			printf("%.2f", $lignePs->getPuHT());
			echo " €\n";
			echo $translate->_('Somme HT')." : ";
			printf("%.2f", $lignePs->getSomme());
			echo " €\n\n";
		}
		echo "--------------------\n";
		echo "Montant de la commande\n";
		echo "--------------------\n\n";
		echo $translate->_('Montant total HT')." : ";
		printf('%.2f',$cs->getMontanttotalHT());
		echo " €\n";
		echo $translate->_('Frais de port + emballage')." : ";
		printf('%.2f',$cs->getFdp());
		echo " €\n";
		echo $translate->_('TVA 19,6%')." : ";
		printf('%.2f',$cs->getMontantTVA());
		echo " €\n\n";
		echo "====================\n";
		echo $translate->_('Montant total TTC')." : ";
		printf('%.2f',$cs->getMontanttotalTTC());
		echo " €\n";
		echo "====================\n";
		echo "</pre></body></html>";
	} else {
		echo "Erreur";
	}
?>