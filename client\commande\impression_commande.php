<?php

	// page qui permet d'imprimer un bon de commande
	require_once dirname(__FILE__).'/../../init.php';
	require_once dirname(__FILE__).'/../../visiteur/login.php';


if($user->isAuthenticated == true || isset($_SESSION['order_id'])){	
	header("Content-Type: text/html; charset=UTF-8");
	require_once 'commande_static_class.php';
	require_once dirname(__FILE__).'/../../utils/form_utile.php';
	
	if(isset($_SESSION['order_id'])){
		$cs = commande_staticDB::getCommandeById($_SESSION['order_id']);
	}
	
	if(isset($_GET['id'])){
		$cs = commande_staticDB::getCommandeById($_GET['id']);
	}
	
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="<?php echo $_SESSION['lang'];?>">
<head>
<title>Commande Internet n&deg;<?php echo $cs->getId(); ?></title>
<link href="imprim_commande.css" rel="stylesheet" type="text/css" />
<meta http-equiv="Content-Type" content="text/xhtml; charset=UTF-8"/>

<!-- Generateur de code bar -->

		<script type="text/javascript" src="<?php echo $basePath;?>lib/barcode/sample/jquery-1.3.2.min.js"></script>  
		<script type="text/javascript" src="<?php echo $basePath;?>lib/barcode/jquery-ui-1.7.custom.min.js"></script> 		
		<script type="text/javascript" src="<?php echo $basePath;?>lib/barcode/jquery-barcode-last.min.js"></script>
		
<?php

if($cs->getModepaiement() == "Paypal"){

 $num_regl = $cs->getNum_regl();

?>

 		<script language="Javascript">
			function codebar(){
					
			$("#bcTarget").barcode("<?php echo $num_regl; ?>", "code128", {barWidth:2, barHeight:75});
			window.print();		
			}
		</script>
		
</head>

<body onload="codebar()">

<?php

}

if(isset($_POST['num_regl'])){

 $num_regl = $_POST['num_regl'];

?>

 		<script language="Javascript">
			function codebar(){
					
			$("#bcTarget").barcode("<?php echo $num_regl; ?>", "code128", {barWidth:2, barHeight:75});
			window.print();		
			}
		</script>
		
</head>

<body onload="codebar()">

<?php

} elseif(!isset($_POST['num_regl']) && $cs->getModepaiement() == "Paypal") {

?>

</head>

<body>

<?php

} else {

?>

</head>

<body onload="window.print()">

<?php

}

?>

<div id="global">

  <div id="en_tete">
  
    <p class="titre_cde" align="center">Commande Internet n&deg;<?php echo $cs->getId(); ?> </p>
    <p align="center">Surplus Militaires et Industriels - D1092, la gare - 38840 La S&ocirc;ne - T&eacute;l : ***********.56</p>

  </div>
  
  
  
  <div id="donnees_client" style="width:795px; margin-left:5px;">
  
  <strong>Identifiant client :</strong>  <?php echo $cs->getEmail(); ?>
  
   - <strong>Date de commande :</strong> <?php echo date('d/m/Y',strtotime($cs->getDate())); ?> - 
   
   <strong>Sign. : </strong><?php echo $cs->getIscommandeadmin(); ?>&nbsp;
   
   
   
  </div>
  <div id="adresse">
    <div class="ad_fact">
	  <h2>Adresse facturation</h2>
		<?php echo $cs->getAdressefact();?>
    </div>
	<?php
			if ($cs->getRetraitmagasin()){
			echo "<strong>Commande &agrave; RETIRER en magasin.</strong>";
			} else {
	?>
	  <div class="ad_liv">
	  <h2>Adresse livraison</h2>
		<?php echo $cs->getAdresselivr();?>
		<?php
		echo "T&eacute;l : ".$cs->getTel()."";
		echo '<br />';
		echo "Fax : ".$cs->getFax()."";
		?>
    </div>
	<?php
	}
	?>
  </div>
  <div id="zone_regl">
  <?php
  if ($cs->getRetraitmagasin()){
 $adliv = $cs->getAdressefact();
 $pays = explode("<br />", $adliv);
 } else {
 $adfac = $cs->getAdresselivr();
 $pays = explode("<br />", $adfac);
 }
  if($pays[3] == "France"){

	echo '<div id="regl">';
	echo 'Mode de paiement : <div class="paiement">'.$cs->getModepaiement().'</div>';
	echo 'Pays : <div class="paiement">'.$pays[3].'<br /></div>';
  } else {
  
    echo '<div id="regl" style="border-style: dotted; background-color: #FFFFFF; ">';
	echo 'Mode de paiement : <div class="paiement" style="font-size: 15px;">'.$cs->getModepaiement().'</div>';
	echo 'Pays : <div class="paiement" style="font-size: 38px;">'.$pays[3].'<br /></div>';
  
  }
  
  ?>
  </div>
 <?php
   if($pays[3] == "France"){

	echo '<div id="type_colis">S&nbsp;&nbsp;&nbsp;&nbsp; M&nbsp;&nbsp;&nbsp;&nbsp; L&nbsp;&nbsp;&nbsp;&nbsp; XL <br /> ou <br />  .................. Kg</div>';

  } else {
  
	echo '<div id="type_colis">L&nbsp;&nbsp;&nbsp;&nbsp; XL<br />UE - OM - INT<br />.................. Kg</div>';
  
  }

  echo '</div>';
  
 if(isset($_POST['num_regl']) && $cs->getModepaiement() == "Paypal"){
 
 $num_regl = $_POST['num_regl'];
 $id_cde = $_GET['id'];
 
 $req2 = $database->query("UPDATE static_commandes SET num_regl = '$num_regl' WHERE id = '$id_cde'") or die ("requete update num_regl invalid");
 
  ?>

		<div id="bcTarget" style="border: 1px solid #C0C0C0; margin: 0 auto; text-align: center;"></div>
 <?php
 
 } 
 
 if ($cs->getModepaiement() == "Paypal"){
 
   ?>

		<div id="bcTarget" style="border: 1px solid #C0C0C0; margin: 0 auto; text-align: center;"></div>
 <?php
 
 } 
 
  if(!isset($_POST['num_regl']) && $cs->getModepaiement() == "Paypal" && $num_regl == "") {
 
 ?>
 <form action="<?php echo $basePath; ?>client/commande/impression_commande.php?id=<?php echo $cs->getId(); ?>" method="post" class="form2">
	<fieldset class="input">
		<legend><?php echo $translate->_('Ajouter numero reglement'),' :';?></legend>
		
		<input type="text" name="num_regl" value="" />
		<input type="submit" value="Ok" name="submit" />
		
	</fieldset>
</form>
<?php
 
 }
if ($cs->getComment() != ""){
 ?>
 <div id="donnees_client" style="width:795px; height:40px; margin-left:5px; background-color:yellow;">
 <strong style="text-decoration:underline;">Commentaire :</strong> <?php echo $cs->getComment(); ?>
 </div>
 <?php
 
 }
 
 ?>
 
<div id="produit">
<table width="811" border="1" cellspacing="0" cellpadding="0">
  <tr>
    <th width="101" scope="col">R&eacute;f&eacute;rence</th>
    <th width="188" scope="col">Genre / Groupe</th>
<?php
	if ($user->isAdmin == true){
	echo '<th></th>';
	}
?>
    <th width="314" scope="col">D&eacute;signation</th>
	<th width="31" scope="col">Img</th>
    <th width="31" scope="col">Prix HT </th>
    <th width="30" scope="col">Qt&eacute;</th>
    <th width="53" scope="col">Somme HT </th>
	<?php
	if ($user->isAdmin == true){
	echo '<th width="45" scope="col">V&eacute;rif</th>';
	}
?>   
  </tr>
	<?php
	
	foreach($cs->getPanier()->getLignePanier() as $k => $lignePs){
  echo "<tr>";
			
			$reference = $lignePs->getReference();
			$result_color = $database->prepare("
			SELECT * FROM produits WHERE referenceproduit = '$reference'
			") or die ("requete r1 invalid");
			$result_color->execute();
			$color = $result_color->fetch();
			
  			echo '<td>'.$lignePs->getReference().'</td>';
			$tab = explode(" / ",$lignePs->getGenregroupe());
			if(empty($tab[2])){
				$tab[2]=$tab[1];
			}
			echo '<td>'.substr($tab[0]." / ".$tab[2],0 ,20).'</td>';
			
			if ($user->isAdmin == true){
			
				$stock = $color['commande'];
				$stock2 = $color['promo'];
				
				if ($stock == "?" || $stock2 == "ooo" || $lignePs->getPuHT() == "-1"){
				$result_stock = '<img src="'.$basePath.'public/images/stock_2.png" />';
				}
						if ($stock2 == "oo"){
						$result_stock = '<img src="'.$basePath.'public/images/stock_2.png" />';
						}
						if (!empty($stock) && $stock != "?" && $stock2 != "ooo" && $lignePs->getPuHT() != "-1"){
						$result_stock = '<img src="'.$basePath.'public/images/stock_3.png" />';
						}
						
						if (empty($stock)){
							if ($stock2 == "o"){
						$result_stock = '<img src="'.$basePath.'public/images/stock_4.png" />';
							} else {
						$result_stock = '<img src="'.$basePath.'public/images/stock_5.png" />';
							}
						}
				echo '<td>'.$result_stock.'</td>';
			}
			echo '<td>'.$lignePs->getDesignation();
			echo '</td>';
			echo '<td><img src="'.$basePath.'public/images/produits/'.$lignePs->getReference().'.jpg?date='.date('ymd').'" width="42" height="30" /></td>';
			echo '<td>&nbsp;';
			printf("%.2f", $lignePs->getPuHT());
			echo '&euro;</td>';
		if ($lignePs->getQte()> 1){
			echo '<td><strong><div align="center" style="font-size:16px;">'.$lignePs->getQte().'</div></strong></td>';
		}else{
			echo '<td><div align="center" style="font-size:10px;">'.$lignePs->getQte().'</div></td>';
		}
			echo '<td>';
			printf("%.2f", $lignePs->getSomme());
			echo '&euro;</td>';
			if ($user->isAdmin == true){
			echo '<td>&nbsp;</td>';
			}
  echo "</tr>";
  }
?>
</table>

  </div>
    <div id="total">
	<table width="240" border="0" cellspacing="0" cellpadding="0">

  <tr>
    <td width="103">Total HT : </td>
    <td width="137"><?php printf('%.2f',$cs->getMontanttotalHT());?>&euro;</td>
  </tr>
  <tr>
    <td>Frais de port + emballage : </td>
    <td><?php printf('%.2f',$cs->getFdp());?>&euro;</td>
  </tr>
  <tr>
    <td>TVA <?php printf('%.2f',$cs->getTauxTVA());?>% : </td>
    <td><?php printf('%.2f',$cs->getMontantTVA());?>&euro;</td>
  </tr>
  <tr>
    <th scope="col">Total TTC : </th>
    <th scope="col"><?php printf('%.2f',$cs->getMontanttotalTTC());?>&euro;</th>
  </tr>
</table>

	
	</div>
<?php
if ($cs->getModepaiement() == "Virement bancaire"){
	
	echo '<img style="margin-left:25px;" src="'.$basePath.'public/images/rib.png" />';

}

?>
</div>

</body>
</html>
<?php
} else {
header("Location: http://jeep-dodge-gmc.com/smi/404");
}
?>
