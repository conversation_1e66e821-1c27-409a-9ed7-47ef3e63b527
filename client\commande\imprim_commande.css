html, body {
	margin: 0px;
	padding: 0px;
}
#global {
	height: 29.7cm;
	width: 21cm;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-size: 14px;
}
#global .titre_cde {
	font-size: 24px;
}
#global #en_tete p {
	margin: 0px;
	padding: 0px;
}
#global #donnees_client {
	display: block;
	height: 20px;
	width: 700px;
	border: 1px solid #000000;
	background-color: #CCFFCC;
	padding-top: 5px;
	padding-left: 5px;
	margin-top: 10px;
	margin-left: 43px;
	padding-bottom: 5px;
}

#global #adresse {
	margin-top: 10px;
	margin-right: 0px;
	margin-bottom: 0px;
	margin-left: 0px;
	height: 155px;
}
#global #adresse .ad_fact {
	background-color: #CCFF99;
	height: 145px;
	width: 325px;
	float: left;
	margin-left: 40px;
	margin-right: 50px;
	border: 2px dotted #000000;
	padding-top: 5px;
	padding-left: 5px;
}
#global #adresse .ad_liv {
	height: 145px;
	width: 325px;
	background-color: #FFCC33;
	float: left;
	border: 2px double #000000;
	padding-top: 5px;
	padding-left: 5px;
}
#global #adresse h2 {
	font-size: 16px;
	height: 20px;
	width: 200px;
	display: block;
	text-decoration: underline;
	margin: 0px;
	padding: 0px;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
}
#global #zone_regl {
	margin-top: 10px;
	margin-right: 0px;
	margin-bottom: 0px;
	margin-left: 0px;
	height: 155px;
}
#global #zone_regl #regl {
	background-color: #99CCFF;
	height: 130px;
	width: 300px;
	margin-left: 80px;
	margin-top: 10px;
	border: 2px double #000000;
	padding-top: 3px;
	padding-left: 5px;
	display: block;
	float: left;
}
#global #zone_regl #type_colis {
	height: 130px;
	width: 300px;
	margin-left: 10px;
	margin-top: 10px;
	border: 2px double #000000;
	padding-top: 3px;
	padding-left: 5px;
	display: block;
	text-align:center;
	font-size: 30px;
	float: left;
}
#global #regl .paiement {
	height: 25px;
	width: 200px;
	margin-left: 50px;
	text-align: center;
	vertical-align: middle;
	font-size: 18px;
	font-weight: bold;
}

#global #produit {
	height: auto;
	width: 780px;
	margin-top: 10px;
	margin-left: 7px;
}
#global #produit table {
	border: 0px solid #666666;
	font-size: 12px;
}
#global #produit tr {
	border-top-width: 0px;
	border-right-width: 0px;
	border-bottom-width: 0px;
	border-left-width: 0px;
	border-top-style: none;
	border-right-style: none;
	border-bottom-style: none;
	border-left-style: none;
}
#global #produit th {
	border-top-width: 2px;
	border-right-width: 0px;
	border-bottom-width: 2px;
	border-left-width: 0px;
	border-top-style: solid;
	border-right-style: none;
	border-bottom-style: solid;
	border-left-style: none;
	color: #000000;
	background-color: #999933;
	font-size: 12px;
	border-bottom-color: #000000;
	border-top-color: #000000;
}
#global #produit td {
	border-top-width: 0px;
	border-right-width: 1px;
	border-bottom-width: 1px;
	border-left-width: 1px;
	border-top-style: none;
	border-right-style: none;
	border-bottom-style: solid;
	border-left-style: none;
	border-right-color: #000000;
	border-bottom-color: #000000;
	border-left-color: #000000;
	padding-top: 5px;
	padding-bottom: 5px;
}
#global #total {
	height: 120px;
	width: 250px;
	margin-left: 538px;
	margin-right: 6px;
	margin-top: 10px;
	display: block;
}
#global #total table {
	margin-right: auto;
	margin-left: auto;
	font-size: 12px;
	border-top-width: 1px;
	border-top-style: solid;
	border-top-color: #000000;
	border-right-color: #000000;
	border-bottom-color: #000000;
	border-left-color: #000000;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}
#global #total th {
	padding-top: 3px;
	padding-bottom: 3px;
	text-align: right;
	border-top-width: 1px;
	border-bottom-width: 1px;
	border-top-style: solid;
	border-bottom-style: solid;
	border-top-color: #000000;
	border-right-color: #000000;
	border-bottom-color: #000000;
	border-left-color: #000000;
}
#global #total td {
	padding-top: 3px;
	padding-bottom: 3px;
	text-align: right;
	border-top-width: 1px;
	border-bottom-width: 1px;
	border-top-style: solid;
	border-bottom-style: solid;
	border-top-color: #000000;
	border-right-color: #000000;
	border-bottom-color: #000000;
	border-left-color: #000000;
}
