<h1><?php echo $translate->_('Mes commandes');?></h1>
<?php
	// @todo ajouter la traduction
	// @todo changer la date en d/m/Y
	// affichage de la liste des commandes du client connecter
	require_once dirname(__FILE__).'/../../class/commande_static_class.php';
	$commandes  = commande_staticDB::getCommandesByClient($user);	
	
	if(count($commandes) == 0){
		// il n'y a pas de commande
		?><div class="info messageBox"><p><?php echo $translate->_('Il n\'y a pas de commande');?></p></div><?php
	}
	else{
		// il y a des commandes
		?>

		<table class="table-fill" style="width:95%;">
			<tr>
				<th><?php echo $translate->_('N°');?></th>
				<th><?php echo $translate->_('Date');?></th>
				<th><?php echo $translate->_('Paiement');?></th>
				<th><?php echo $translate->_('Status');?></th>
				<th><?php echo $translate->_('Total HT');?></th>
				<th><?php echo $translate->_('Total TTC');?></th>
				<th></th>
			</tr>
		<?php
			foreach($commandes as $commande){
				?>
				<tr style="text-align:center">
					<td class="text-center"><?php echo $commande->getId(); ?></td>
					<td class="text-center"><?php echo date('d/m/Y',strtotime($commande->getDate())); ?></td>
					<td>
						<?php echo $translate->_($commande->getModepaiement()); ?>
					</td>
					<td>
						<?php echo $commande->getStatut(); ?>
					</td>
					<td class="text-right"><?php printf('%.2f €',$commande->getMontanttotalHT()); ?></td>
					<td class="text-right"><?php printf('%.2f €',$commande->getMontanttotalTTC()); ?></td>
					<td class="text-center">
						<a href="<?php echo $basePath; ?>commandes/<?php echo $commande->getId(); ?>">
							<img src="<?php echo $basePath; ?>public/images/zoom.png" title="<?php echo $translate->_('afficher les détails');?>"/>
						</a>
					</td>
				</tr>
				<?php
			}
		?>
		</table>
		<?php
	}
	
	if(count($commandes) == 0){
		// il n'y a pas de commande
		?><div class="info messageBox"><p><?php echo $translate->_('Il n\'y a pas de facture');?></p></div><?php
	}
	else{
		// il y a des commandes
		?>
				<h1><?php echo $translate->_('Mes factures');?></h1>
					<table class="table-fill" style="width:95%;">
						<tr>

							<th>Numéro</th>
							<th>Date</th>
							<th>Montant HT</th>
							<th>Montant TTC</th>
							<th>Genre</th>
							<th></th>
						</tr>
					
					<?php
						
						$email = $user->getEmail();
						
						$result9 = $database->prepare("SELECT * FROM facture WHERE emailclient ='$email' ORDER BY id_facture DESC") or die ("requete r9 invalid");
						$result9->execute();
						
						while ($tab8 = $result9->fetch()) {
						
						?>
						<tr>
							<td class="text-center"><?php echo $tab8['id_facture']; ?></td>
							<td class="text-center"><?php echo date('d/m/Y',strtotime($tab8['date_facture'])); ?></td>
							<td class="text-right"><?php printf('%.2f €',$tab8['totht']); ?></td>
							<td class="text-right"><?php printf('%.2f €',$tab8['ttc']); ?></td>
							<td class="text-center"><?php echo $tab8['genre']; ?></td>
							<td class="text-center">
								<a href="<?php echo $basePath; ?>client/commande/impression_facture.php?fact=<?php echo $tab8['id_facture']; ?>" target='_blank'>
									<img src="<?php echo $basePath; ?>public/images/zoom.png" />
								</a>
							</td>
						</tr>
						<?php
						}

					?>

					</table>
		<?php
		
	}
	