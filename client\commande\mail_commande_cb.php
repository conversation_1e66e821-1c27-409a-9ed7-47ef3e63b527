<?php 
$content_html = '
<?xml version="1.0" encoding="ISO-8859-1"?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="fr" lang="fr">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1" />
	<title>Commande n&deg;'.$cs->getId().'</title>
	<style type="text/css">
	body,table { font-family: "Verdana"; font-size: 10pt }
	table.blueframe { border: solid 1px #00F }
	table.grayframe { border: solid 1px #333 }
	table.grayframe th { background-color: #7DB1FF; text-align: center; font-weight: bold; border-bottom: solid 1px #333 }
	h1 { text-align: center; font-weight: bold; font-size: 11pt }
	h3 { font-family: <PERSON><PERSON><PERSON>, Arial, sans-serif; font-size: 10pt; font-weight: bold; color: #2D72B2; text-align: left; border-bottom: solid 1px #2D72B2; width: 98% }
	.phonenumber { white-space: nowrap }
	</style>
</head>

<body>

<table width="100%">
<tr>
<td align="left">
<a href="http://www.jeep-dodge-gmc.com/"><img src="http://www.jeep-dodge-gmc.com'.$basePath.'public/images/logo.png" alt="jeep-dodge-gmc.com" border="0" /></a>
</td></tr>
</table>

<table class="blueframe" width="800" cellpadding="6" cellspacing="0" border="0">
	<th colspan="2">
		<h1>Commande n&deg;'.$cs->getId().'</h1>
	</th>
	<tr >
		<td colspan="2">
		<p>Date : '.date('d/m/Y',strtotime($cs->getDate())).'<br />
		Mode paiement : '.htmlspecialchars($cs->getModepaiement()).'<br />
		Identifiant client : '.$cs->getEmail().'<br />
		T&eacute;l&eacute;phone : '.$cs->getTel().'<br />
		Fax / Mobile : '.$cs->getFax().'</p>
		<p>Commentaire : '.$cs->getComment().'</p>
		<p>&nbsp;</p></td>
	</tr>
	<tr>
		<td>
			<strong>Adresse de facturation :</strong><br />
			'.$cs->getAdressefact().'
		</td>
		<td>
			<strong>Adresse de livraison :</strong><br />
';	
	if ($cs->getRetraitmagasin())
$content_html .= 'Commande à RETIRER en magasin';
	else 
	{
	
	$content_html .= $cs->getAdresselivr();
		
	}		
$content_html .='
		</td>
	</tr>
	<tr>
		<td colspan="2">
		
		<table width="700">
			<th>
				R&eacute;f&eacute;rence
			</th>
			<th>
				Genre / Groupe
			</th>
			<th>
				D&eacute;signation
			</th>
			<th>
				Prix unitaire HT
			</th>
			<th>
				Quantit&eacute;
			</th>
			<th>
				Somme HT
			</th>
';
foreach($cs->getPanier()->getLignePanier() as $k => $lignePs)
{
$content_html .= '			
			<tr>
				<td>'.$lignePs->getReference().'</td>
				<td>'.$lignePs->getGenregroupe().'</td>
				<td>'.$lignePs->getDesignation().'</td>
				<td align="right">'.$lignePs->getPuHT().' &euro;</td>
				<td align="center">'.$lignePs->getQte().'</td>
				<td align="right">'.number_format($lignePs->getSomme(),2, '.', '').' &euro;</td>
			</tr>
';
}
$content_html .= '
		</table>
		
		</td>
	</tr>
	<tr>
		<td colspan="2">
			<p><strong>Montants de la commande</strong><br />
			Montant total HT : '.$cs->getMontanttotalHT().' &euro;<br />
			Frais de port + emballage HT : '.$cs->getFdp().' &euro;<br />
			TVA '.$cs->getTauxTVA().'% : '.$cs->getMontantTVA().' &euro;<br />
			<strong>Montant total TTC : '.$cs->getMontanttotalTTC().' &euro; </strong></p>
		<p> Nous vous remercions, votre commande est prise en charge pour exp&eacute;dition sous 24 &agrave; 48H (jours ouvrables). Les pieces d&#39;occasions, N.O.S. ou r&eacute;nov&eacute;es peuvent rallonger le d&eacute;lai de pr&eacute;paration.<br />
              <br />
          Cordialement, l\'&eacute;quipe SMI </p>
		</td>
	</tr>
	
</table>
<br />
<br />
<img src="http://www.jeep-dodge-gmc.com'.$basePath.'public/images/accueil/bandeau_1-4_pub.jpg" border="0" />
</body>

</html>
';
/*
-- Instruction Module envoi d'un email en php -- 

Les variables :

*/

$client_nom = $cs->getEmail();

//Message UNICODE

ob_start();
	
	echo "--------------------\n";
	echo "Commande Internet pour : Surplus Militaires et Industriels\n";
	echo "+ de 500 tonnes de pièces / Site : http://jeep-dodge-gmc.com\n"; 
	echo "E-mail : <EMAIL> / Tél. : (+33) 476 644 356\n";
	echo "--------------------\n\n";
	echo "Commande du : ".date('d/m/Y',strtotime($cs->getDate()))."\n";
	echo "N° de Commande : ".$cs->getId()."\n";
	echo "Mode de paiement : ".$cs->getModepaiement()."\n";
	echo "Identifiant client : ".$cs->getEmail()."\n";
	echo "Téléphone : ".$cs->getTel()."\n";
	echo "Fax : ".$cs->getFax()."\n\n";
	
	echo "Adresse de FACTURATION :\n";
	echo str_replace("<br />","\n",$cs->getAdressefact())."\n";
	
	if ($cs->getRetraitmagasin())
		echo "Commande à RETIRER en magasin.\n\n";
	else {
		echo "Adresse de LIVRAISON :\n";
		echo str_replace("<br />","\n",$cs->getAdresselivr())."\n\n";
	}

	echo "--------------------\n";
	echo "Contenu de la commande\n";
	echo "--------------------\n\n";
	foreach($cs->getPanier()->getLignePanier() as $k => $lignePs)
	{
		echo $translate->_('Référence')." : ".$lignePs->getReference()."\n";
		echo $translate->_('Genre')." / ".$translate->_('Groupe')." : ".$lignePs->getGenregroupe()."\n";
		echo $translate->_('Désignation')." : ".$lignePs->getDesignation()."\n";
		echo $translate->_('Quantité')." * ".$translate->_('Prix unitaire HT')." : ".$lignePs->getQte()." * ";
		printf("%.2f", $lignePs->getPuHT());
		echo " €\n";
		echo $translate->_('Somme HT')." : ";
		printf("%.2f", $lignePs->getSomme());
		echo " €\n\n";
	}
	echo "--------------------\n";
	echo "Montants de la commande\n";
	echo "--------------------\n\n";
	echo $translate->_('Montant total HT')." : ";
	printf('%.2f',$cs->getMontanttotalHT());
	echo " €\n";
	echo $translate->_('Frais de port + emballage')." : ";
	printf('%.2f',$cs->getFdp());
	echo " €\n";
	echo $translate->_('TVA ').$cs->getTauxTVA()."% : ";
	printf('%.2f',$cs->getMontantTVA());
	echo " €\n\n";
	echo "====================\n";
	echo $translate->_('
	')." : ";
	printf('%.2f',$cs->getMontanttotalTTC());
	echo " €\n";
	echo "====================\n";

$content = ob_get_clean();
			
$exp_mail = "<EMAIL>";
$exp_nom = "Jeep-Dodge-Gmc.com";

		
$mail = $client_nom; // Déclaration de l'adresse de destination.
$test_mail = preg_match("#^[a-z0-9._-]+@(hotmail|live|msn).[a-z]{2,4}$#", $mail);
if ($test_mail === "1"){ // On filtre les serveurs qui présentent des bogues.
$passage_ligne = "\r\n";
}else{
$passage_ligne = "\n";
}
//=====Déclaration des messages au format texte et au format HTML.
$message_txt = $content;
$message_html = $content_html;
//==========
 
//=====Définition du sujet.
$sujet = 'Commande '.$cs->getId().'';
//=========
 
//=====Création de la boundary.
$boundary = "-----=".md5(rand());
//==========
 
//=====Création du header de l'e-mail.
$header = "From: \"".$exp_nom."\"<".$exp_mail.">".$passage_ligne;
$header.= "Reply-To: \"".$exp_nom."\"<".$exp_mail.">".$passage_ligne;
$header.= "MIME-Version: 1.0".$passage_ligne;
$header.= "Content-Type: multipart/alternative;".$passage_ligne." boundary=\"".$boundary."\"".$passage_ligne;
//==========

//=====Ajout du message au format texte.
$message = 'This is a multi-part message in MIME format.'.$passage_ligne;
$message.= $passage_ligne."--".$boundary.$passage_ligne;
$message.= "Content-Type: text/plain; charset=\"ISO-8859-1\"".$passage_ligne;
$message.= "Content-Transfer-Encoding: 8bit".$passage_ligne;
$message.= $passage_ligne.$message_txt.$passage_ligne;
//==========
 
//=====Ajout du message au format HTML.
$message.= $passage_ligne."--".$boundary.$passage_ligne;
$message.= "Content-Type: text/html; charset=\"ISO-8859-1\"".$passage_ligne;
$message.= "Content-Transfer-Encoding: 8bit".$passage_ligne;
$message.= $passage_ligne.$message_html.$passage_ligne;
//==========
 
//=====On ferme la boundary.
$message.= $passage_ligne."--".$boundary.$passage_ligne;

//=====Envoi de l'e-mail.
mail($mail,$sujet,$message,$header);
 
//==========
 ?>