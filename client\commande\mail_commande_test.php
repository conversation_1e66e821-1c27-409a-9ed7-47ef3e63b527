<?php 

require_once dirname(__FILE__).'/../../class/commande_class.php';
require_once dirname(__FILE__).'/../../class/adresse_class.php';
require_once dirname(__FILE__).'/../../class/commande_static_class.php';
require_once '../../init.php';

$cs = commande_staticDB::getCommandeById('18648');

$content_html = '
<table style="font-family:Arial;" cellspacing="0" cellpadding="0" border="0" bgcolor="#ffffff" width="100%" style="font-size:0; margin: 0px; padding: 0px; border-collapse: collapse; border-spacing: 0;">
  <tr>
    <td valign="top" align=center>
<table cellspacing="0" cellpadding="0" border="0" bgcolor="#cccc66" style="font-size:0; margin: 0px; padding: 0px; border-collapse: collapse; border-spacing: 0;">
  <tr>
    <td valign="top" rowspan="3" ><a href="http://jeep-dodge-gmc.com/smi/" ><img src="http://jeep-dodge-gmc.com/smi/newsletter/images/NEWSLETTER-MODELE_01.gif" style="display:block;"/></a></td>
    <td colspan="3" valign="top"><a href="mailto:<EMAIL>" ><img src="http://jeep-dodge-gmc.com/smi/newsletter/images/NEWSLETTER-MODELE_02.gif" style="display:block;"/></a></td>
    <td colspan="1" valign="top"><img src="http://jeep-dodge-gmc.com/smi/newsletter/images/NEWSLETTER-MODELE_03.gif" style="display:block;"/></td>
  </tr>
  <tr>
    <td valign="top"><a href="https://twitter.com/jeep_dodge_gmc"><img src="http://jeep-dodge-gmc.com/smi/newsletter/images/NEWSLETTER-MODELE_04.gif" style="display:block;"/></a></td>
    <td valign="top"><a href="https://plus.google.com/u/0/107839788516299581970/about"><img src="http://jeep-dodge-gmc.com/smi/newsletter/images/NEWSLETTER-MODELE_05.gif" style="display:block;"/></a></td>
    <td valign="top"><a href="https://www.facebook.com/surplus.militaitresetindustriels"><img src="http://jeep-dodge-gmc.com/smi/newsletter/images/NEWSLETTER-MODELE_06.gif" style="display:block;"/></a></td>
    <td valign="top"><a href="http://jeep-dodge-gmc.com/smi/index.php?page=newsletter"><img src="http://jeep-dodge-gmc.com/smi/newsletter/images/NEWSLETTER-MODELE_07.gif" style="display:block;"/></a></td>
  </tr>
  <tr>  
    <td colspan="4" valign="top"><img src="http://jeep-dodge-gmc.com/smi/newsletter/images/NEWSLETTER-MODELE_08.gif" style="display:block;"/></td>
  </tr> 
</table>

<table style="background-color:white; margin-left:auto;margin-right:auto; width:650px; border:solid black;">
	<th colspan="2" style="background-color:black; color:white;"  >
		<h1 style="margin-top:10px; margin-bottom:10px;">Commande n&deg;'.$cs->getId().'</h1>
	</th>
	<tr >
		<td colspan="2" style="border-bottom:solid black; padding-left:10px;" >
		<p>Date : '.date('d/m/Y',strtotime($cs->getDate())).'<br />
		Mode paiement : '.htmlspecialchars($cs->getModepaiement()).'<br />
		Identifiant client : '.$cs->getEmail().'<br />
		T&eacute;l&eacute;phone : '.$cs->getTel().'<br />
		Fax / Mobile : '.$cs->getFax().'</p>
		<p>Commentaire : '.$cs->getComment().'</p>
		<p>&nbsp;</p></td>
	</tr>
	<tr>
		<td style="padding-left:10px;">
			<strong>Adresse de facturation :</strong><br />
			'.$cs->getAdressefact().'
		</td>
		<td>
			<strong>Adresse de livraison :</strong><br />
';	
	if ($cs->getRetraitmagasin())
$content_html .= 'Commande à RETIRER en magasin';
	else 
	{
	
	$content_html .= $cs->getAdresselivr();
		
	}		
$content_html .='
		</td>
	</tr>
	<tr>
		<td colspan="2">
		
		<table width="630" cellspacing="0" cellpadding="0" style="margin-left:auto; margin-right:auto; margin-top:10px;">
			<th style="background-color:black; color:white; font-size:11px;">
				R&eacute;f&eacute;rence
			</th>
			<th style="background-color:black; color:white; font-size:11px;">
				Genre / Groupe
			</th>
			<th style="background-color:black; color:white; font-size:11px;">
				D&eacute;signation
			</th>
			<th style="background-color:black; color:white; font-size:11px;">
				Prix unitaire HT
			</th>
			<th style="background-color:black; color:white; font-size:11px;">
				Quantit&eacute;
			</th>
			<th style="background-color:black; color:white; font-size:11px; width:50px;">
				Total HT
			</th>
';
foreach($cs->getPanier()->getLignePanier() as $k => $lignePs)
{
$content_html .= '			
			<tr>
				<td style="border-bottom:solid black; font-size:11px;">'.$lignePs->getReference().'</td>
				<td style="border-bottom:solid black; font-size:11px;">'.$lignePs->getGenregroupe().'</td>
				<td style="border-bottom:solid black; font-size:11px;">'.$lignePs->getDesignation().'</td>
				<td align="right" style="border-bottom:solid black; font-size:11px;">'.$lignePs->getPuHT().' &euro;</td>
				<td align="center" style="border-bottom:solid black; font-size:11px;">'.$lignePs->getQte().'</td>
				<td align="right" style="border-bottom:solid black; font-size:11px;">'.number_format($lignePs->getSomme(),2, '.', '').' &euro;</td>
			</tr>
';
}
$content_html .= '
		</table>
		
		</td>
	</tr>
	<tr>
		<td colspan="2" style="text-align:right; padding-right:10px;">
			<p><strong>Montants de la commande</strong><br />
			Montant total HT : '.number_format($cs->getMontanttotalHT(),2, '.', '').' &euro;<br />
			Frais de port + emballage HT : '.number_format($cs->getFdp(),2, '.', '').' &euro;<br />
			TVA '.$cs->getTauxTVA().'% : '.number_format($cs->getMontantTVA(),2, '.', '').' &euro;<br />
			<strong>Montant total TTC : '.number_format($cs->getMontanttotalTTC(),2, '.', '').' &euro; </strong></p>
			<p>Vous aller recevoir prochainement un e-mail  vous informant du statut de votre commande. Nous rappellons que les exp&eacute;dtions s\'effectuent le mardi et jeudi matin. <br />
              <br />
          Cordialement, l\'&eacute;quipe SMI </p>
		</td>
	</tr>
	
</table>
</td>
</tr>
</table>
';


/*
-- Instruction Module envoi d'un email en php -- 

Les variables :

*/

$client_nom = $cs->getEmail();
			
$exp_mail = "<EMAIL>";
$exp_nom = "Jeep-Dodge-Gmc.com";

		
$mail = $client_nom; // Déclaration de l'adresse de destination.
$test_mail = preg_match("#^[a-z0-9._-]+@(hotmail|live|msn).[a-z]{2,4}$#", $mail);
if ($test_mail === "1"){ // On filtre les serveurs qui présentent des bogues.
$passage_ligne = "\r\n";
}else{
$passage_ligne = "\n";
}
 
//=====Définition du sujet.
$sujet = 'Commande '.$cs->getId().'';
//=========
 
//=====Création du header de l'e-mail.

$header = 'MIME-Version: 1.0' . "".$passage_ligne;
$header.= 'Content-type: text/html; charset=iso-8859-1' . "".$passage_ligne;
$header.= "From: \"".$exp_nom."\"<".$exp_mail.">".$passage_ligne;
$header.= "Reply-To: \"".$exp_nom."\"<".$exp_mail.">".$passage_ligne;

//=====Envoi de l'e-mail.
mail($mail,$sujet,$content_html,$header);
//==========

echo $content_html;
 ?>