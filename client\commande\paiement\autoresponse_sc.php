<?php
	
	
	function getSignature ($params,$key)
	{
		/**
			* Fonction qui calcule la signature.
			* $params : tableau contenant les champs reçus dans l'IPN.
			* $key : clé de TEST ou de PRODUCTION
		*/
		//Initialisation de la variable qui contiendra la chaine à chiffrer
		$contenu_signature = "";
		//Tri des champs par ordre alphabétique
		ksort($params);
		foreach($params as $nom=>$valeur){
			//Récupération des champs vads_
			if (substr($nom,0,5)=='vads_'){
				//Concaténation avec le séparateur "+"
				$contenu_signature .= $valeur."+";
			}
		}
		
		//Ajout de la clé en fin de chaine
		$contenu_signature .= $key;
		//Encodage base64 de la chaine chiffrée avec l'algorithme HMAC-SHA-256
		$sign = base64_encode(hash_hmac('sha256',$contenu_signature, $key, true));
		return $sign;
	}
	
	
	// lorsque le client valide le formulaire de paiement, ce fichier est executé.
	//	$message = "message=$_POST[DATA]";
	
	
	// Initialisation du chemin du fichier de log (à modifier)
	//   ex :
	//    -> Windows : $logfile="c:\\repertoire\\log\\logfile.txt";
	//    -> Unix    : $logfile="/home/<USER>/log/logfile.txt";
	//
	
	$logfile="/home/<USER>/www/smi/scellius/log.txt";
	
	// Ouverture du fichier de log en append
	
	$fp=fopen($logfile, "a");
	
	//  analyse du code retour
	// OK, Sauvegarde des champs de la réponse
	fwrite( $fp, "-------------------------------------------\n");
	$Erreur = "O";
	if (empty ($_POST)){
		fwrite( $fp, "Le POST est vide !!!\n");
		
		}else{
		fwrite( $fp, "Données reçues :\n");
		
		if (isset($_POST['vads_hash'])){
			fwrite( $fp, "Form API notification detected\n");
			//Signature computation
			$Signature = getSignature( $_POST, '5KLmzxQLYOgKCzGu');
			//Signature verification
			$Erreur = "N";
			if( $_POST['signature'] != $Signature ) {
				$Erreur = "O";
				fwrite( $fp, "Erreur de Signature !!!\n");
			}
			//Order Update			
			foreach ($_POST as $k => $v) {
				fwrite( $fp, $k . " = " .$v . "\n");
			}
			fwrite( $fp, "Signature Calculée = " . $Signature . "\n");
		}
	}
	
	if ($Erreur == "N") {	
		// on modifie l'etat de la commande
		require_once dirname(__FILE__).'/../../../init.php';
		require_once 'commande_static_class.php';
		switch ($_POST['vads_auth_result']) {
			case '00':
			// on modifie la commande car le paiement a été accepté
			$cs = commande_staticDB::getCommandeById($_POST['vads_order_id']);
			$cs->setStatut("Paiement validé");
			fwrite( $fp, "Commande " . $_POST['vads_order_id'] . " validée ...\n");
			commande_staticDB::saveCommande($cs,false);
			// envoyé un mail au client a cette endroit
			//require_once dirname(__FILE__).'/../mail_commande.php';
			break;
			default:
			// on supprime la commmande dans les autres cas
			commande_staticDB::deleteCommande($_POST['vads_order_id']);
			fwrite( $fp, "Commande " . $_POST['vads_order_id'] . " supprimée ...\n");
			break;
		}
	}
	
	fclose ($fp);
?>		