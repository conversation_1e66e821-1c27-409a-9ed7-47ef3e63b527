<div class="form-style-10">	
<div class="section"><span>¤</span>Paiement de la commande</div>
<div class="inner-wrap">
<label>Vous allez être redirigé sur le Site Sécurisé de La Banque Postale</label>
<?php

	//afficherMenuCommande(6);
	// page 'call_request.php' pour le paiement en ligne (fichier inclut par paiement.php)
	//	Affectation des paramètres obligatoires
//@todo mettre le merchant_id dans la table paramètre
	$req = "SELECT valeur FROM parametres WHERE cle = 'merchant_id'";
	$db = Zend_Registry::get('database');
	$row = $db->query($req)->fetch();
	$parm="merchant_id=".$row['valeur'];
//choisir la langue d'affichage
	$id_cde = $cs->getId();
	$req2 = "SELECT * FROM  adresses_facturation
	INNER JOIN  static_commandes ON static_commandes.client_email = adresses_facturation.af_emailclient 
	WHERE static_commandes.id = '$id_cde'";
	
	$row2 = $db->query($req2)->fetch();
	//$nom_pays = $af_pays = trim(str_replace(' ','',strtolower($row2['af_pays'])));
	$nom_pays = $af_pays = trim($row2['af_pays']);
	
	$req3 = "SELECT * FROM  pays WHERE nom_pays LIKE '$nom_pays'";
	
	$row3 = $db->query($req3)->fetch();

	if (empty($row3['langue_pays'])){
	
	$language = "en";
	
	} else {
	
	$language = $row3['langue_pays'];
	
	}

	
	$parm="$parm merchant_country=fr";
	// on doit mettre le montant en centimes d'euro
	$amount = round((($cs->getMontanttotalTTC())*100), 0);
	$parm="$parm amount=".$amount;
	$parm="$parm currency_code=978";


	// Initialisation du chemin du fichier pathfile (à modifier)
	    //   ex :
	    //    -> Windows : $parm="$parm pathfile=c:\\repertoire\\pathfile";
	    //    -> Unix    : $parm="$parm pathfile=/home/<USER>/pathfile";
	    
	$parm="$parm pathfile=/home/<USER>/www/smi/scellius/param/pathfile";

	//		Si aucun transaction_id n'est affecté, request en génère
	//		un automatiquement à partir de heure/minutes/secondes
	//		Référez vous au Guide du Programmeur pour
	//		les réserves émises sur cette fonctionnalité
	//
	$parm="$parm transaction_id=".$cs->getId();


	//		Affectation dynamique des autres paramètres
	// 		Les valeurs proposées ne sont que des exemples
	// 		Les champs et leur utilisation sont expliqués dans le Dictionnaire des données
	//
	//$parm="$parm normal_return_url=http://jeep-dodge-gmc.com/smi/client/commande/paiement/response.php";
	//$parm="$parm cancel_return_url=http://jeep-dodge-gmc.com/smi/client/commande/paiement/response.php";
	//$parm="$parm automatic_response_url=http://jeep-dodge-gmc.com/smi/client/commande/paiement/autoresponse.php";
	$parm="$parm language=".$language;
	//$parm="$parm payment_means=CB,2,VISA,2,MASTERCARD,2";
	//		$parm="$parm header_flag=no";
	$parm="$parm capture_day=90";
    $parm="$parm capture_mode=VALIDATION";
	//		$parm="$parm capture_day=";
	//		$parm="$parm capture_mode=";
	//		$parm="$parm bgcolor=";
	//		$parm="$parm block_align=";
	//		$parm="$parm block_order=";
	//		$parm="$parm textcolor=";
	//		$parm="$parm receipt_complement=";
	//		$parm="$parm caddie=mon_caddie";
	//		$parm="$parm customer_id=";
	$parm="$parm customer_email=".$cs->getEmail();
	//		$parm="$parm customer_ip_address=";
	//		$parm="$parm data=";
	//		$parm="$parm return_context=";
	//		$parm="$parm target=";
	//$parm="$parm order_id=".$_SESSION['commande']['id'];


	//		Les valeurs suivantes ne sont utilisables qu'en pré-production
	//		Elles nécessitent l'installation de vos fichiers sur le serveur de paiement
	//
	// 		$parm="$parm normal_return_logo=";
	// 		$parm="$parm cancel_return_logo=";
	// 		$parm="$parm submit_logo=";
	$parm="$parm logo_id=merchant.gif";
	$parm="$parm logo_id2=merchant.gif";
	$parm="$parm advert=merchant.gif";
	// 		$parm="$parm background_id=";
	// 		$parm="$parm templatefile=";


	//		insertion de la commande en base de données (optionnel)
	//		A développer en fonction de votre système d'information

	// Initialisation du chemin de l'executable request (à modifier)
	// ex :
	// -> Windows : $path_bin = "c:\\repertoire\\bin\\request";
	// -> Unix    : $path_bin = "/home/<USER>/bin/request";
	//

	$path_bin = '/home/<USER>/www/smi/scellius/bin/request';
  

	//	Appel du binaire request
	$result=exec("$path_bin $parm");

	//	sortie de la fonction : $result=!code!error!buffer!
	//	    - code=0	: la fonction génère une page html contenue dans la variable buffer
	//	    - code=-1 	: La fonction retourne un message d'erreur dans la variable error

	//On separe les differents champs et on les met dans une variable tableau
	$tableau = explode ("!", "$result");

	//	récupération des paramètres

	$code = @$tableau[1];
	$error = @$tableau[2];
	$message = @$tableau[3];

	//  analyse du code retour
	//@todo, modifier le design des erreurs, et de l'autre page aussi
	if (( $code == "" ) && ( $error == "" ) ){
		echo '<p class="messageBox erreur">';
  		echo 'erreur appel request<br />';
  		echo 'executable request non trouve '.$path_bin;
  		echo '</p>';
	}
	else if ($code != 0){//	Erreur, affiche le message d'erreur
		echo '<p class="messageBox erreur>';
		echo 'Erreur appel API de paiement.<br />';
		echo 'message erreur : '.$error;
		echo '</p>';
	}
	else {//	OK, affiche le formulaire HTML
		# OK, affichage du mode DEBUG si activé
		print (" $error <br/>");
		print ("  $message <br/>");
	}
?>
</div>
</div>	