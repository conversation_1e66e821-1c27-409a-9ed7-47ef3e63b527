<div class="form-style-10">	
<div class="section"><span>¤</span>Paiement de la commande</div>
<div class="inner-wrap">
<label>Vous allez être redirigé sur le Site Sécurisé de La Banque Postale</label>
<?php

	$req = "SELECT valeur FROM parametres WHERE cle = 'merchant_id'";
	$db = Zend_Registry::get('database');
	$row = $db->query($req)->fetch();
	$parm="merchant_id=".$row['valeur'];
//choisir la langue d'affichage
	$id_cde = $cs->getId();
	$req2 = "SELECT * FROM  adresses_facturation
	INNER JOIN  static_commandes ON static_commandes.client_email = adresses_facturation.af_emailclient 
	WHERE static_commandes.id = '$id_cde'";
	
	$row2 = $db->query($req2)->fetch();
	//$nom_pays = $af_pays = trim(str_replace(' ','',strtolower($row2['af_pays'])));
	$nom_pays = $af_pays = trim($row2['af_pays']);
	
	$req3 = "SELECT * FROM  pays WHERE nom_pays LIKE '$nom_pays'";
	
	$row3 = $db->query($req3)->fetch();

	if (empty($row3['langue_pays'])){
	
	$language = "en";
	
	} else {
	
	$language = $row3['langue_pays'];
	
	}


function getSignature ($params,$key)
{
/**
* Fonction qui calcule la signature.
* $params : tableau contenant les champs à envoyer dans le formulaire.
* $key : clé de TEST ou de PRODUCTION
*/


//Initialisation de la variable qui contiendra la chaine à chiffrer
$contenu_signature = "";

//Tri des champs par ordre alphabétique
ksort($params);
foreach($params as $nom=>$valeur){
	//Récupération des champs vads_
	if (substr($nom,0,5)=='vads_'){

		//Concaténation avec le séparateur "+"
		$contenu_signature .= $valeur."+";
	}
}

//Ajout de la clé en fin de chaine
$contenu_signature .= $key;

//Encodage base64 de la chaine chiffrée avec l'algorithme HMAC-SHA-256
$signature = base64_encode(hash_hmac('sha256',$contenu_signature, $key, true));
return $signature;
}


$amount = round((($cs->getMontanttotalTTC())*100), 0);
date_default_timezone_set('UTC');

$PrmScellius = array(
	'vads_action_mode'	=> 'INTERACTIVE',
	'vads_amount'		=> $amount,
	'vads_ctx_mode'		=> 'PRODUCTION',
	'vads_currency'		=> '978',
	'vads_cust_email'	=> $cs->getEmail(),	
	'vads_cust_id'		=> '0',
	'vads_order_id'		=> $cs->getId(),
	'vads_page_action'	=> 'PAYMENT',
	'vads_payment_cards'=> 'CB;VISA;MASTERCARD',
	'vads_payment_config' => 'SINGLE',
	'vads_site_id'		=> '30774574',
	'vads_trans_date'	=> date('YmdHis'),
	'vads_trans_id'		=> date('His'),
	'vads_version'		=> 'V2'
);

$Signature = getSignature( $PrmScellius, '5KLmzxQLYOgKCzGu');

print ('<form id="sc" method="POST" action="https://scelliuspaiement.labanquepostale.fr/vads-payment/">');
print ('<input type="hidden" name="vads_action_mode" value="'. $PrmScellius['vads_action_mode'] .'" />');
print ('<input type="hidden" name="vads_amount" value="'. $PrmScellius['vads_amount'] . '" />');
print ('<input type="hidden" name="vads_currency" value="'. $PrmScellius['vads_currency'] . '" />');
print ('<input type="hidden" name="vads_cust_id" value="'. $PrmScellius['vads_cust_id'] . '" />');
print ('<input type="hidden" name="vads_cust_email" value="'. $PrmScellius['vads_cust_email'] . '" />');
print ('<input type="hidden" name="vads_ctx_mode" value="'. $PrmScellius['vads_ctx_mode'] . '" />');
print ('<input type="hidden" name="vads_order_id" value="'. $PrmScellius['vads_order_id'] . '" />');
print ('<input type="hidden" name="vads_page_action" value="'. $PrmScellius['vads_page_action'] . '" />');
print ('<input type="hidden" name="vads_payment_cards" value="'. $PrmScellius['vads_payment_cards'] . '" />');
print ('<input type="hidden" name="vads_payment_config" value="'. $PrmScellius['vads_payment_config'] . '" />');
print ('<input type="hidden" name="vads_site_id" value="'. $PrmScellius['vads_site_id'] . '" />');
print ('<input type="hidden" name="vads_trans_date" value="'. $PrmScellius['vads_trans_date'] . '" />');
print ('<input type="hidden" name="vads_trans_id" value="'. $PrmScellius['vads_trans_id'] . '" />');
print ('<input type="hidden" name="vads_version" value="'. $PrmScellius['vads_version'] . '" />');
print ('<input type="hidden" name="signature" value="' . $Signature . '"/>');
print ('<input type="submit" name="payer" value="Payer '. round((($cs->getMontanttotalTTC())), 2) . '€"/>');
print ('</form>');

?>
</div>
</div>	