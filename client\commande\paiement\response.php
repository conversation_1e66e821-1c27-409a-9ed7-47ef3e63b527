<?php

	require_once dirname(__FILE__).'/../../../init.php';
	require_once dirname(__FILE__).'/../../../visiteur/login.php';
	require_once 'commande_static_class.php';

	// Récupération de la variable cryptée DATA
	$message="message=$_POST[DATA]";
	
	// Initialisation du chemin du fichier pathfile (à modifier)
    //   ex :
    //    -> Windows : $pathfile="pathfile=c:\\repertoire\\pathfile";
    //    -> Unix    : $pathfile="pathfile=/home/<USER>/pathfile";
   
   $pathfile="pathfile=/home/<USER>/www/smi/scellius/param/pathfile";

	// Initialisation du chemin de l'executable response (à modifier)
	// ex :
	// -> Windows : $path_bin = "c:\\repertoire\\bin\\response";
	// -> Unix    : $path_bin = "/home/<USER>/bin/response";
	//

	$path_bin = "/home/<USER>/www/smi/scellius/bin/response";

	// Appel du binaire response

	$result=exec("$path_bin $pathfile $message");

	//	Sortie de la fonction : !code!error!v1!v2!v3!...!v29
	//		- code=0	: la fonction retourne les données de la transaction dans les variables v1, v2, ...
	//				: Ces variables sont décrites dans le GUIDE DU PROGRAMMEUR
	//		- code=-1 	: La fonction retourne un message d'erreur dans la variable error


	//	on separe les differents champs et on les met dans une variable tableau

	$tableau = explode ("!", $result);
	//	Récupération des données de la réponse

	$code = $tableau[1];
	$error = $tableau[2];
	$merchant_id = $tableau[3];
	$merchant_country = $tableau[4];
	$amount = $tableau[5];
	$transaction_id = $tableau[6];
	$payment_means = $tableau[7];
	$transmission_date= $tableau[8];
	$payment_time = $tableau[9];
	$payment_date = $tableau[10];
	$response_code = $tableau[11];
	$payment_certificate = $tableau[12];
	$authorisation_id = $tableau[13];
	$currency_code = $tableau[14];
	$card_number = $tableau[15];
	$cvv_flag = $tableau[16];
	$cvv_response_code = $tableau[17];
	$bank_response_code = $tableau[18];
	$complementary_code = $tableau[19];
	$complementary_info = $tableau[20];
	$return_context = $tableau[21];
	$caddie = $tableau[22];
	$receipt_complement = $tableau[23];
	$merchant_language = $tableau[24];
	$language = $tableau[25];
	$customer_id = $tableau[26];
	$order_id = $tableau[27];
	$customer_email = $tableau[28];
	$customer_ip_address = $tableau[29];
	$capture_day = $tableau[30];
	$capture_mode = $tableau[31];
	$data = $tableau[32];

	
	// dans tous les cas, on doit vider le panier de l'utilisateur
	$user->setPanier(new panier());
	// et on doit vider les informations concernant la commande en cours
	$_SESSION['commande'] = array();
	
	if (( $code == "" ) && ( $error == "" ) ){
		// pas de code de retour => impossible de trouver l'api
		$cs = commande_staticDB::getCommandeById($transaction_id);
		$cs->setStatut("erreur scellius");
		commande_staticDB::saveCommande($cs,false);
		$_SESSION['error_code'] = 1;
  		// redirection vers une page indiquant une erreur
  		header('HTTP/1.1 302 Found');
		header('Location: '.$basePath.'commande/erreur');
 	}
	else if ( $code != 0 ){
		// code = 0 => erreur lors de l'appel a l'api
		$cs = commande_staticDB::getCommandeById($transaction_id);
		$cs->setStatut("erreur scellius");
		commande_staticDB::saveCommande($cs,false);
		$_SESSION['error_code'] = 1;
		// redirection vers un page d'erreur
		header('HTTP/1.1 302 Found');
		header('Location: '.$basePath.'commande/erreur');
	}
	else {
		if($response_code == '00'){
			// le paiement a été accepter
			$cs = commande_staticDB::getCommandeById($transaction_id);
			$cs->setStatut("Paiement validé");
			commande_staticDB::saveCommande($cs,false);
			require_once dirname(__FILE__).'/../mail_commande.php';
			$_SESSION['order_id'] = $cs->getId();
			header('Location: https://www.jeep-dodge-gmc.com/smi/commande/fin');
		}
		else{
			// le paiement n'as pas été accepter

			$cs = commande_staticDB::getCommandeById($transaction_id);
			$cs->setStatut("Paiement refusé");
			commande_staticDB::saveCommande($cs,false);
			$_SESSION['error_code'] = 2;
			header('HTTP/1.1 302 Found');
			header('Location: '.$basePath.'commande/erreur');
		
		}
	}