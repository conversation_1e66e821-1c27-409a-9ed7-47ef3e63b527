<?php
	
	require_once dirname(__FILE__).'/../../../init.php';

    /* lire le formulaire provenant du système PayPal et ajouter 'cmd'
    $req = 'cmd=_notify-validate';
    
    foreach ($_POST as $key => $value) {
        $value = urlencode(stripslashes($value));
        $req .= "&$key=$value";
    }
	
	// renvoyer au système PayPal pour validation
    $header .= "POST /cgi-bin/webscr HTTP/1.0\r\n";
    $header .= "Content-Type: application/x-www-form-urlencoded\r\n";
    $header .= "Content-Length: " . strlen($req) . "\r\n\r\n";
    $fp = fsockopen ('www.sandbox.paypal.com', 443, $errno, $errstr, 30);
	*/
	
	$item_name = $_POST['item_name'];
    $item_number = $_POST['item_number'];
    $payment_status = $_POST['payment_status'];
    $payment_amount = $_POST['mc_gross'];
    $payment_currency = $_POST['mc_currency'];
    $txn_id = $_POST['txn_id'];
    $receiver_email = $_POST['receiver_email'];
    $payer_email = $_POST['payer_email'];
    $id = $_POST['custom'];
	
	//test des variables
	
	$req1 = $database->prepare("SELECT * FROM static_commandes WHERE id = '$item_number'") or die ("requete r1 invalid");
	$req1->execute();
	$test1 = $req1->fetch();
	
	if ($payment_status == "Completed") {

			if ($test1['commande_montantotalttc'] == $payment_amount){
			
			$req2 = $database->query("UPDATE static_commandes SET num_regl = '$txn_id' WHERE id = '$item_number'") or die ("requete r2 invalid");
			
			} else {
		
			$req2 = $database->query("UPDATE static_commandes SET num_regl = 'erreur total' WHERE id = '$item_number'") or die ("requete r2 invalid");
		
			}	
	} else {
	
		$req2 = $database->query("UPDATE static_commandes SET num_regl = 'erreur statut' WHERE id = '$item_number'") or die ("requete r2 invalid");
	
	}
	
	/*
	if (!$fp) {
    // ERREUR HTTP
	
	$req2 = $database->query("UPDATE static_commandes SET num_regl = 'erreur http' WHERE id = '$item_number'") or die ("requete update num_regl invalid");
	
    } else {
	
        fputs ($fp, $header . $req);
		
        while (!feof($fp)) {
		
            $res = fgets ($fp, 1024);
			// transaction valide
			
            if (strcmp ($res, "VERIFIED") == 0) {
							// vérifier que payment_status a la valeur Completed
						if ( $payment_status == "Completed") {
							// vérifier que txn_id n'a pas été précédemment traité: Créez une fonction qui va interroger votre base de données
							if (VerifIXNID($txn_id) == 0) {
								// vérifier que receiver_email est votre adresse email PayPal principale
								if ( "<EMAIL>" == $receiver_email) {
									// vérifier que payment_amount et payment_currency sont corrects
									// traiter le paiement
									
									$req2 = $database->query("UPDATE static_commandes SET num_regl = '$txn_id' WHERE id = '$item_number'") or die ("requete update num_regl invalid");

								 }
					  else {
						// Mauvaise adresse email paypal
						$req2 = $database->query("UPDATE static_commandes SET num_regl = 'erreur email' WHERE id = '$item_number'") or die ("requete update num_regl invalid");
					  }
					}
					else {
						// ID de transaction déjà utilisé
						$req2 = $database->query("UPDATE static_commandes SET num_regl = 'erreur id transaction' WHERE id = '$item_number'") or die ("requete update num_regl invalid");
							}
					}
				  else {
						// Statut de paiement: Echec
						$req2 = $database->query("UPDATE static_commandes SET num_regl = 'erreur statut paiement' WHERE id = '$item_number'") or die ("requete update num_regl invalid");
				  }
				  
            }
            else if (strcmp ($res, "INVALID") == 0) {
                // Transaction invalide   
						$req2 = $database->query("UPDATE static_commandes SET num_regl = 'erreur transaction invalide' WHERE id = '$item_number'") or die ("requete update num_regl invalid");	
            }
        }
        fclose ($fp);
    }
	*/


?>

