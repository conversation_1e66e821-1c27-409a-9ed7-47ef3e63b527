<?php
	require_once dirname(__FILE__).'/../../../init.php';
	require_once dirname(__FILE__).'/../../../visiteur/login.php';
	require_once 'commande_static_class.php'; 
	
	// dans tous les cas, on doit vider le panier de l'utilisateur
	$user->setPanier(new panier());
	// et on doit vider les informations concernant la commande en cours
	$_SESSION['commande'] = array();

			// le paiement a été accepter
			$id = $_GET['id'];
			$cs = commande_staticDB::getCommandeById($id);
			$cs->setStatut("Paiement validé");
			commande_staticDB::saveCommande($cs,false);
			require_once dirname(__FILE__).'/../mail_commande.php';
			$_SESSION['order_id'] = $cs->getId();
			header('HTTP/1.1 302 Found');
			header('Location: '.$basePath.'commande/fin');
