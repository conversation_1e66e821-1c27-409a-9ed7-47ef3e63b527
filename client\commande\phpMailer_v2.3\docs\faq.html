﻿<html>
<head>
<title>PHPMailer FAQ</title>
<style>
body, p {
  font-family: Arial, Helvetica, sans-serif;
  font-size: 12px;
}
div.width {
  width: 500px;
  text-align: left;
}
</style>
</head>
<body bgcolor="#FFFFFF">
<center>
<div class="width">
<h2>PHPMailer FAQ</h2>
<ul>

  <li><b style="background-color: #FFFF00">Q:</b> <b>I&#039;m using the SMTP mailer and I keep on getting a timeout message
  well before the X seconds I set it for.  What gives?</b><br />
  <b style="background-color: #FFFF00">A:</b> PHP versions 4.0.4pl1 and earlier have a bug in which sockets timeout
  early.  You can fix this by re-compiling PHP 4.0.4pl1 with this fix:
  <a href="timeoutfix.diff">timeoutfix.diff</a>. Otherwise you can wait for the new PHP release.<br /><br /></li>

  <li><b style="background-color: #FFFF00">Q:</b> <b>I am concerned that using include files will take up too much
  processing time on my computer.  How can I make it run faster?</b><br />
  <b style="background-color: #FFFF00">A:</b>  PHP by itself is very fast.  Much faster than ASP or JSP running on
  the same type of server.  This is because it has very little overhead compared
  to its competitors and it pre-compiles all of
  its code before it runs each script (in PHP4).  However, all of
  this compiling and re-compiling can take up a lot of valuable
  computer resources.  However, there are programs out there that compile
  PHP code and store it in memory (or on mmaped files) to reduce the
  processing immensely.  Two of these: <a href="http://apc.communityconnect.com">APC
  (Alternative PHP Cache)</a> and <a href="http://bwcache.bware.it/index.htm">Afterburner</a>
  (<a href="http://www.mm4.de/php4win/mod_php4_win32/">Win32 download</a>)
  are excellent free tools that do just this.  If you have the money
  you might also try <a href="http://www.zend.com">Zend Cache</a>, it is
  even faster than the open source varieties.  All of these tools make your
  scripts run faster while also reducing the load on your server. I have tried
  them myself and they are quite stable too.<br /><br /></li>

  <li><b style="background-color: #FFFF00">Q:</b> <b>What mailer gives me the best performance?</b><br />
  <b style="background-color: #FFFF00">A:</b> On a single machine the <b>sendmail (or Qmail)</b> is fastest overall.
  Next fastest is mail() to give you the best performance. Both do not have the overhead of SMTP.
  If you have you have your mail server on a another machine then
  SMTP is your only option, but you do get the benefit of redundant mail servers.<br />
  If you are running a mailing list with thousands of names, the fastest mailers in order are: SMTP, sendmail (or Qmail), mail().<br /><br /></li>

  <li><b style="background-color: #FFFF00">Q:</b> <b>When I try to attach a file with on my server I get a
  "Could not find {file} on filesystem error".  Why is this?</b><br />
  <b style="background-color: #FFFF00">A:</b> If you are using a Unix machine this is probably because the user
  running your web server does not have read access to the directory in question.  If you are using Windows,
  then the problem probably is that you have used single backslashes to denote directories (\).
  A single backslash has a special meaning to PHP so these are not
  valid.  Instead use double backslashes ("\\") or a single forward
  slash ("/").<br /><br /></li>

</ul>

</div>
</center>

</body>
</html>
