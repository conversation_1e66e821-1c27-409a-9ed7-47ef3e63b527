<p>The example file &quot;test_mail.php&quot; contents include:</p>
<div style="width: 600px; background-color: #CCCCCC;">
<code>
&lt;?php<br>
<br>
include_once('../class.phpmailer.php');<br>
<br>
$mail    = new PHPMailer();<br>
<br>
$body    = $mail->getFile('contents.html');<br>
<br>
$body    = eregi_replace("[\]",'',$body);<br>
$subject = eregi_replace("[\]",'',$subject);<br>
<br>
$mail->From     = "<EMAIL>";<br>
$mail->FromName = "First Last";<br>
<br>
$mail->Subject = "PHPMailer Test Subject";<br>
<br>
$mail->AltBody = "To view the message, please use an HTML compatible email viewer!"; // optional, comment out and test<br>
<br>
$mail->MsgHTML($body);<br>
<br>
$mail->AddAddress("<EMAIL>", "<PERSON> Do<PERSON>");<br>
<br>
if(!$mail->Send()) {<br>
  echo 'Failed to send mail';<br>
} else {<br>
  echo 'Mail sent';<br>
}<br>
<br>
?&gt;
</code>
</div>
<br>
Although you could use full compabitility with PHPMailer 1.7.3, this example
shows how to use the new features. If you view 'contents.html', you will note
that there is a background image used in the &lt;body tag as well as an image used
with a regular &lt;img tag. Here&#39;s what the HTML file looks like:<br>
<br>
<div style="width: 600px; background-color: #CCCCCC;">
<code>
&lt;body background="images/bkgrnd.gif" style="margin: 0px;"&gt;<br>
&lt;div style="width: 640px; font-family: Arial, Helvetica, sans-serif; font-size: 11px;"&gt;<br>
&lt;div align="center"&gt;&lt;img src="images/phpmailer.gif" style="height: 90px; width: 340px"&gt;&lt;/div&gt;&lt;br&gt;<br>
&lt;br&gt;<br>
&nbsp;This is a test of PHPMailer v2.0.0 rc1.&lt;br&gt;<br>
&lt;br&gt;<br>
This particular example uses &lt;strong&gt;HTML&lt;/strong&gt;, with a &lt;div&gt; tag and inline&lt;br&gt;<br>
styles.&lt;br&gt;<br>
&lt;br&gt;<br>
Also note the use of the PHPMailer at the top with no specific code to handle<br>
including it in the body of the email.&lt;/div&gt;<br>
&lt;/body&gt;<br>
</code>
</div>
<br>
A few things to notice in the PHP script that generates the email:
<ul>
  <li>the use of $mail-&gt;AltBody is completely optional. If not used, PHPMailer
  will use the HTML text with htmlentities().</li>
  <li>the background= and &lt;img src= images were processed without any directives
  or methods from the PHP script</li>
  <li>there is no specific code to define the image type ... that is handled
  automatically by PHPMailer when it parses the images</li>
  <li>we are using a new class method '$mail->MsgHTML($body)' ... that is what will handle the parsing of the images and creating the AltBody text</li>
</ul>
<p>Of course, you can still use PHPMailer the same way you have in the past.
That provides full compatibility with all existing scripts, while new scripts
can take advantage of the new features.</p>
<p>Modify test_mail.php now with your own email address and try it out.</p>
To see what the email SHOULD look like in your HTM
<div style="display:none"></div>
