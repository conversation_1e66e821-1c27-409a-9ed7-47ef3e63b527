<?xml version="1.0" encoding="iso-8859-1"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
  <html xmlns="http://www.w3.org/1999/xhtml">
		<head>
			<!-- template designed by <PERSON> -->
			<title></title>
			<link rel="stylesheet" href="media/stylesheet.css" />
			<meta http-equiv='Content-Type' content='text/html; charset=iso-8859-1'/>
		</head>
		<body>
						<a name="top"></a>
<h2>[PHPMailer] element index</h2>
<a href="elementindex.html">All elements</a>
<br />
<div class="index-letter-menu">
	<a class="index-letter" href="elementindex_PHPMailer.html#a">a</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#b">b</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#c">c</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#d">d</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#e">e</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#f">f</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#h">h</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#i">i</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#m">m</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#n">n</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#p">p</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#q">q</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#r">r</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#s">s</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#t">t</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#u">u</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#v">v</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#w">w</a>
</div>

	<a name="a"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">a</div>
		<div style="float: right"><a href="#top">top</a></div>
		<div style="clear: both"></div>
	</div>
	<dl>
			<dt class="field">
						<span class="var-title">$AltBody</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$AltBody">PHPMailer::$AltBody</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets the text-only body of the message.  This automatically sets the  email to multipart/alternative.  This body can be read by mail  clients that do not have HTML email capability such as mutt. Clients  that can read HTML will view the normal Body.</div>
					</dd>
			<dt class="field">
						<span class="method-title">AddAddress</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodAddAddress">PHPMailer::AddAddress()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Adds a &quot;To&quot; address.</div>
					</dd>
			<dt class="field">
						<span class="method-title">AddAttachment</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodAddAttachment">PHPMailer::AddAttachment()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Adds an attachment from a path on the filesystem.</div>
					</dd>
			<dt class="field">
						<span class="method-title">AddBCC</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodAddBCC">PHPMailer::AddBCC()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Adds a &quot;Bcc&quot; address. Note: this function works  with the SMTP mailer on win32, not with the &quot;mail&quot;  mailer.</div>
					</dd>
			<dt class="field">
						<span class="method-title">AddCC</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodAddCC">PHPMailer::AddCC()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Adds a &quot;Cc&quot; address. Note: this function works  with the SMTP mailer on win32, not with the &quot;mail&quot;  mailer.</div>
					</dd>
			<dt class="field">
						<span class="method-title">AddCustomHeader</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodAddCustomHeader">PHPMailer::AddCustomHeader()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Adds a custom header.</div>
					</dd>
			<dt class="field">
						<span class="method-title">AddEmbeddedImage</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodAddEmbeddedImage">PHPMailer::AddEmbeddedImage()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Adds an embedded attachment.  This can include images, sounds, and  just about any other document.  Make sure to set the $type to an  image type.  For JPEG images use &quot;image/jpeg&quot; and for GIF images  use &quot;image/gif&quot;.</div>
					</dd>
			<dt class="field">
						<span class="method-title">AddReplyTo</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodAddReplyTo">PHPMailer::AddReplyTo()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Adds a &quot;Reply-to&quot; address.</div>
					</dd>
			<dt class="field">
						<span class="method-title">AddStringAttachment</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodAddStringAttachment">PHPMailer::AddStringAttachment()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Adds a string or binary attachment (non-filesystem) to the list.</div>
					</dd>
			<dt class="field">
						<span class="method-title">Authenticate</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#methodAuthenticate">SMTP::Authenticate()</a> in class.smtp.php</div>
							<div class="index-item-description">Performs SMTP authentication.  Must be run after running the  Hello() method.  Returns true if successfully authenticated.</div>
					</dd>
		</dl>
	<a name="b"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">b</div>
		<div style="float: right"><a href="#top">top</a></div>
		<div style="clear: both"></div>
	</div>
	<dl>
			<dt class="field">
						<span class="var-title">$Body</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$Body">PHPMailer::$Body</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets the Body of the message.  This can be either an HTML or text body.</div>
					</dd>
		</dl>
	<a name="c"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">c</div>
		<div style="float: right"><a href="#top">top</a></div>
		<div style="clear: both"></div>
	</div>
	<dl>
			<dt class="field">
						<span class="var-title">$CharSet</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$CharSet">PHPMailer::$CharSet</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets the CharSet of the message.</div>
					</dd>
			<dt class="field">
						<span class="var-title">$ConfirmReadingTo</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$ConfirmReadingTo">PHPMailer::$ConfirmReadingTo</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets the email address that a reading confirmation will be sent.</div>
					</dd>
			<dt class="field">
						<span class="var-title">$ContentType</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$ContentType">PHPMailer::$ContentType</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets the Content-type of the message.</div>
					</dd>
			<dt class="field">
						<span class="var-title">$CRLF</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#var$CRLF">SMTP::$CRLF</a> in class.smtp.php</div>
							<div class="index-item-description">SMTP reply line ending</div>
					</dd>
			<dt class="field">
						<span class="include-title">class.phpmailer.php</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/_class_phpmailer_php.html">class.phpmailer.php</a> in class.phpmailer.php</div>
					</dd>
			<dt class="field">
						<span class="include-title">class.smtp.php</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/_class_smtp_php.html">class.smtp.php</a> in class.smtp.php</div>
					</dd>
			<dt class="field">
						<span class="method-title">ClearAddresses</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodClearAddresses">PHPMailer::ClearAddresses()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Clears all recipients assigned in the TO array.  Returns void.</div>
					</dd>
			<dt class="field">
						<span class="method-title">ClearAllRecipients</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodClearAllRecipients">PHPMailer::ClearAllRecipients()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Clears all recipients assigned in the TO, CC and BCC  array.  Returns void.</div>
					</dd>
			<dt class="field">
						<span class="method-title">ClearAttachments</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodClearAttachments">PHPMailer::ClearAttachments()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Clears all previously set filesystem, string, and binary  attachments.  Returns void.</div>
					</dd>
			<dt class="field">
						<span class="method-title">ClearBCCs</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodClearBCCs">PHPMailer::ClearBCCs()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Clears all recipients assigned in the BCC array.  Returns void.</div>
					</dd>
			<dt class="field">
						<span class="method-title">ClearCCs</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodClearCCs">PHPMailer::ClearCCs()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Clears all recipients assigned in the CC array.  Returns void.</div>
					</dd>
			<dt class="field">
						<span class="method-title">ClearCustomHeaders</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodClearCustomHeaders">PHPMailer::ClearCustomHeaders()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Clears all custom headers.  Returns void.</div>
					</dd>
			<dt class="field">
						<span class="method-title">ClearReplyTos</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodClearReplyTos">PHPMailer::ClearReplyTos()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Clears all recipients assigned in the ReplyTo array.  Returns void.</div>
					</dd>
			<dt class="field">
						<span class="method-title">Close</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#methodClose">SMTP::Close()</a> in class.smtp.php</div>
							<div class="index-item-description">Closes the socket and cleans up the state of the class.</div>
					</dd>
			<dt class="field">
						<span class="method-title">Connect</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#methodConnect">SMTP::Connect()</a> in class.smtp.php</div>
							<div class="index-item-description">Connect to the server specified on the port specified.</div>
					</dd>
		</dl>
	<a name="d"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">d</div>
		<div style="float: right"><a href="#top">top</a></div>
		<div style="clear: both"></div>
	</div>
	<dl>
			<dt class="field">
						<span class="var-title">$do_debug</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#var$do_debug">SMTP::$do_debug</a> in class.smtp.php</div>
							<div class="index-item-description">Sets whether debugging is turned on</div>
					</dd>
			<dt class="field">
						<span class="method-title">Data</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#methodData">SMTP::Data()</a> in class.smtp.php</div>
							<div class="index-item-description">Issues a data command and sends the msg_data to the server</div>
					</dd>
		</dl>
	<a name="e"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">e</div>
		<div style="float: right"><a href="#top">top</a></div>
		<div style="clear: both"></div>
	</div>
	<dl>
			<dt class="field">
						<span class="var-title">$Encoding</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$Encoding">PHPMailer::$Encoding</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets the Encoding of the message. Options for this are &quot;8bit&quot;,  &quot;7bit&quot;, &quot;binary&quot;, &quot;base64&quot;, and &quot;quoted-printable&quot;.</div>
					</dd>
			<dt class="field">
						<span class="var-title">$ErrorInfo</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$ErrorInfo">PHPMailer::$ErrorInfo</a> in class.phpmailer.php</div>
							<div class="index-item-description">Holds the most recent mailer error message.</div>
					</dd>
			<dt class="field">
						<span class="method-title">Expand</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#methodExpand">SMTP::Expand()</a> in class.smtp.php</div>
							<div class="index-item-description">Expand takes the name and asks the server to list all the  people who are members of the _list_. Expand will return  back and array of the result or false if an error occurs.</div>
					</dd>
		</dl>
	<a name="f"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">f</div>
		<div style="float: right"><a href="#top">top</a></div>
		<div style="clear: both"></div>
	</div>
	<dl>
			<dt class="field">
						<span class="var-title">$From</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$From">PHPMailer::$From</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets the From email address for the message.</div>
					</dd>
			<dt class="field">
						<span class="var-title">$FromName</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$FromName">PHPMailer::$FromName</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets the From name of the message.</div>
					</dd>
		</dl>
	<a name="h"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">h</div>
		<div style="float: right"><a href="#top">top</a></div>
		<div style="clear: both"></div>
	</div>
	<dl>
			<dt class="field">
						<span class="var-title">$Helo</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$Helo">PHPMailer::$Helo</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets the SMTP HELO of the message (Default is $Hostname).</div>
					</dd>
			<dt class="field">
						<span class="var-title">$Host</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$Host">PHPMailer::$Host</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets the SMTP hosts.  All hosts must be separated by a   semicolon.  You can also specify a different port   for each host by using this format: [hostname:port]   (e.g. &quot;smtp1.example.com:25;smtp2.example.com&quot;).</div>
					</dd>
			<dt class="field">
						<span class="var-title">$Hostname</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$Hostname">PHPMailer::$Hostname</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets the hostname to use in Message-Id and Received headers   and as default HELO string. If empty, the value returned   by SERVER_NAME is used or 'localhost.localdomain'.</div>
					</dd>
			<dt class="field">
						<span class="method-title">Hello</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#methodHello">SMTP::Hello()</a> in class.smtp.php</div>
							<div class="index-item-description">Sends the HELO command to the smtp server.</div>
					</dd>
			<dt class="field">
						<span class="method-title">Help</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#methodHelp">SMTP::Help()</a> in class.smtp.php</div>
							<div class="index-item-description">Gets help information on the keyword specified. If the keyword</div>
					</dd>
		</dl>
	<a name="i"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">i</div>
		<div style="float: right"><a href="#top">top</a></div>
		<div style="clear: both"></div>
	</div>
	<dl>
			<dt class="field">
						<span class="method-title">IsError</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodIsError">PHPMailer::IsError()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Returns true if an error occurred.</div>
					</dd>
			<dt class="field">
						<span class="method-title">IsHTML</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodIsHTML">PHPMailer::IsHTML()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets message type to HTML.</div>
					</dd>
			<dt class="field">
						<span class="method-title">IsMail</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodIsMail">PHPMailer::IsMail()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets Mailer to send message using PHP mail() function.</div>
					</dd>
			<dt class="field">
						<span class="method-title">IsQmail</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodIsQmail">PHPMailer::IsQmail()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets Mailer to send message using the qmail MTA.</div>
					</dd>
			<dt class="field">
						<span class="method-title">IsSendmail</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodIsSendmail">PHPMailer::IsSendmail()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets Mailer to send message using the $Sendmail program.</div>
					</dd>
			<dt class="field">
						<span class="method-title">IsSMTP</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodIsSMTP">PHPMailer::IsSMTP()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets Mailer to send message using SMTP.</div>
					</dd>
		</dl>
	<a name="m"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">m</div>
		<div style="float: right"><a href="#top">top</a></div>
		<div style="clear: both"></div>
	</div>
	<dl>
			<dt class="field">
						<span class="var-title">$Mailer</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$Mailer">PHPMailer::$Mailer</a> in class.phpmailer.php</div>
							<div class="index-item-description">Method to send mail: (&quot;mail&quot;, &quot;sendmail&quot;, or &quot;smtp&quot;).</div>
					</dd>
			<dt class="field">
						<span class="method-title">Mail</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#methodMail">SMTP::Mail()</a> in class.smtp.php</div>
							<div class="index-item-description">Starts a mail transaction from the email address specified in  $from. Returns true if successful or false otherwise. If True  the mail transaction is started and then one or more Recipient  commands may be called followed by a Data command.</div>
					</dd>
		</dl>
	<a name="n"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">n</div>
		<div style="float: right"><a href="#top">top</a></div>
		<div style="clear: both"></div>
	</div>
	<dl>
			<dt class="field">
						<span class="method-title">Noop</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#methodNoop">SMTP::Noop()</a> in class.smtp.php</div>
							<div class="index-item-description">Sends the command NOOP to the SMTP server.</div>
					</dd>
		</dl>
	<a name="p"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">p</div>
		<div style="float: right"><a href="#top">top</a></div>
		<div style="clear: both"></div>
	</div>
	<dl>
			<dt class="field">
						<span class="var-title">$Password</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$Password">PHPMailer::$Password</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets SMTP password.</div>
					</dd>
			<dt class="field">
						<span class="var-title">$PluginDir</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$PluginDir">PHPMailer::$PluginDir</a> in class.phpmailer.php</div>
							<div class="index-item-description">Path to PHPMailer plugins.  This is now only useful if the SMTP class  is in a different directory than the PHP include path.</div>
					</dd>
			<dt class="field">
						<span class="var-title">$Port</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$Port">PHPMailer::$Port</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets the default SMTP server port.</div>
					</dd>
			<dt class="field">
						<span class="var-title">$Priority</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$Priority">PHPMailer::$Priority</a> in class.phpmailer.php</div>
							<div class="index-item-description">Email priority (1 = High, 3 = Normal, 5 = low).</div>
					</dd>
			<dt class="field">
						PHPMailer
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html">PHPMailer</a> in class.phpmailer.php</div>
							<div class="index-item-description">PHPMailer - PHP email transport class</div>
					</dd>
		</dl>
	<a name="q"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">q</div>
		<div style="float: right"><a href="#top">top</a></div>
		<div style="clear: both"></div>
	</div>
	<dl>
			<dt class="field">
						<span class="method-title">Quit</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#methodQuit">SMTP::Quit()</a> in class.smtp.php</div>
							<div class="index-item-description">Sends the quit command to the server and then closes the socket  if there is no error or the $close_on_error argument is true.</div>
					</dd>
		</dl>
	<a name="r"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">r</div>
		<div style="float: right"><a href="#top">top</a></div>
		<div style="clear: both"></div>
	</div>
	<dl>
			<dt class="field">
						<span class="method-title">Recipient</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#methodRecipient">SMTP::Recipient()</a> in class.smtp.php</div>
							<div class="index-item-description">Sends the command RCPT to the SMTP server with the TO: argument of $to.</div>
					</dd>
			<dt class="field">
						<span class="method-title">Reset</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#methodReset">SMTP::Reset()</a> in class.smtp.php</div>
							<div class="index-item-description">Sends the RSET command to abort and transaction that is  currently in progress. Returns true if successful false  otherwise.</div>
					</dd>
		</dl>
	<a name="s"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">s</div>
		<div style="float: right"><a href="#top">top</a></div>
		<div style="clear: both"></div>
	</div>
	<dl>
			<dt class="field">
						<span class="var-title">$Sender</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$Sender">PHPMailer::$Sender</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets the Sender email (Return-Path) of the message.  If not empty,  will be sent via -f to sendmail or as 'MAIL FROM' in smtp mode.</div>
					</dd>
			<dt class="field">
						<span class="var-title">$Sendmail</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$Sendmail">PHPMailer::$Sendmail</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets the path of the sendmail program.</div>
					</dd>
			<dt class="field">
						<span class="var-title">$SMTPAuth</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$SMTPAuth">PHPMailer::$SMTPAuth</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets SMTP authentication. Utilizes the Username and Password variables.</div>
					</dd>
			<dt class="field">
						<span class="var-title">$SMTPDebug</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$SMTPDebug">PHPMailer::$SMTPDebug</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets SMTP class debugging on or off.</div>
					</dd>
			<dt class="field">
						<span class="var-title">$SMTPKeepAlive</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$SMTPKeepAlive">PHPMailer::$SMTPKeepAlive</a> in class.phpmailer.php</div>
							<div class="index-item-description">Prevents the SMTP connection from being closed after each mail  sending.  If this is set to true then to close the connection  requires an explicit call to SmtpClose().</div>
					</dd>
			<dt class="field">
						<span class="var-title">$SMTP_PORT</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#var$SMTP_PORT">SMTP::$SMTP_PORT</a> in class.smtp.php</div>
							<div class="index-item-description">SMTP server port</div>
					</dd>
			<dt class="field">
						<span class="var-title">$Subject</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$Subject">PHPMailer::$Subject</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets the Subject of the message.</div>
					</dd>
			<dt class="field">
						<span class="method-title">Send</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#methodSend">SMTP::Send()</a> in class.smtp.php</div>
							<div class="index-item-description">Starts a mail transaction from the email address specified in</div>
					</dd>
			<dt class="field">
						<span class="method-title">Send</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodSend">PHPMailer::Send()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Creates message and assigns Mailer. If the message is  not sent successfully then it returns false.  Use the ErrorInfo  variable to view description of the error.</div>
					</dd>
			<dt class="field">
						<span class="method-title">SendAndMail</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#methodSendAndMail">SMTP::SendAndMail()</a> in class.smtp.php</div>
							<div class="index-item-description">Starts a mail transaction from the email address specified in</div>
					</dd>
			<dt class="field">
						<span class="method-title">SendOrMail</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#methodSendOrMail">SMTP::SendOrMail()</a> in class.smtp.php</div>
							<div class="index-item-description">Starts a mail transaction from the email address specified in</div>
					</dd>
			<dt class="field">
						<span class="method-title">SetLanguage</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodSetLanguage">PHPMailer::SetLanguage()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets the language for all class error messages.  Returns false  if it cannot load the language file.  The default language type  is English.</div>
					</dd>
			<dt class="field">
						<span class="method-title">SMTP</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#methodSMTP">SMTP::SMTP()</a> in class.smtp.php</div>
							<div class="index-item-description">Initialize the class so that the data is in a known state.</div>
					</dd>
			<dt class="field">
						SMTP
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html">SMTP</a> in class.smtp.php</div>
							<div class="index-item-description">SMTP is rfc 821 compliant and implements all the rfc 821 SMTP  commands except TURN which will always return a not implemented  error. SMTP also provides some utility methods for sending mail  to an SMTP server.</div>
					</dd>
			<dt class="field">
						<span class="method-title">SmtpClose</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#methodSmtpClose">PHPMailer::SmtpClose()</a> in class.phpmailer.php</div>
							<div class="index-item-description">Closes the active SMTP session if one exists.</div>
					</dd>
		</dl>
	<a name="t"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">t</div>
		<div style="float: right"><a href="#top">top</a></div>
		<div style="clear: both"></div>
	</div>
	<dl>
			<dt class="field">
						<span class="var-title">$Timeout</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$Timeout">PHPMailer::$Timeout</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets the SMTP server timeout in seconds. This function will not   work with the win32 version.</div>
					</dd>
			<dt class="field">
						<span class="method-title">Turn</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#methodTurn">SMTP::Turn()</a> in class.smtp.php</div>
							<div class="index-item-description">This is an optional command for SMTP that this class does not  support. This method is here to make the RFC821 Definition  complete for this class and __may__ be implimented in the future</div>
					</dd>
		</dl>
	<a name="u"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">u</div>
		<div style="float: right"><a href="#top">top</a></div>
		<div style="clear: both"></div>
	</div>
	<dl>
			<dt class="field">
						<span class="var-title">$Username</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$Username">PHPMailer::$Username</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets SMTP username.</div>
					</dd>
		</dl>
	<a name="v"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">v</div>
		<div style="float: right"><a href="#top">top</a></div>
		<div style="clear: both"></div>
	</div>
	<dl>
			<dt class="field">
						<span class="var-title">$Version</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$Version">PHPMailer::$Version</a> in class.phpmailer.php</div>
							<div class="index-item-description">Holds PHPMailer version.</div>
					</dd>
			<dt class="field">
						<span class="method-title">Verify</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/SMTP.html#methodVerify">SMTP::Verify()</a> in class.smtp.php</div>
							<div class="index-item-description">Verifies that the name is recognized by the server.</div>
					</dd>
		</dl>
	<a name="w"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">w</div>
		<div style="float: right"><a href="#top">top</a></div>
		<div style="clear: both"></div>
	</div>
	<dl>
			<dt class="field">
						<span class="var-title">$WordWrap</span>
					</dt>
		<dd class="index-item-body">
			<div class="index-item-details"><a href="PHPMailer/PHPMailer.html#var$WordWrap">PHPMailer::$WordWrap</a> in class.phpmailer.php</div>
							<div class="index-item-description">Sets word wrapping on the body of the message to a given number of  characters.</div>
					</dd>
		</dl>

<div class="index-letter-menu">
	<a class="index-letter" href="elementindex_PHPMailer.html#a">a</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#b">b</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#c">c</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#d">d</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#e">e</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#f">f</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#h">h</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#i">i</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#m">m</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#n">n</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#p">p</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#q">q</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#r">r</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#s">s</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#t">t</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#u">u</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#v">v</a>
	<a class="index-letter" href="elementindex_PHPMailer.html#w">w</a>
</div>	</body>
</html>