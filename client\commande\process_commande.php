<?php
	// script permettant de passer commande
	// - etape 1: on verifie que l'utilisateur est connecté
	//			  si il n'est pas connecté -> ecran pour ce connecter, ou s'inscrire (comme sur amazon)
	// - etape 2: choix de l'adresse/création d'une nouvelle adresse
	// - etape3 : choix du mode de paiement
	//		- carte de crédit : trouver comment ça marche
	//		- (cheque) : mettre la commande en attente de reception du paiement.
	//		- (contre remboursement) : mettre la commande en preparation
	// - etape 4: afficher le recap de la commande (avec un bouton pour imprimer)
	// toute les informations  necessaire au passage de la commande seront stocké dans la session,
	// le temps que le client valide complement ça session
	if($user->isAuthenticated === true){ // etape 1
		if(!isset($_SESSION['commande'])){
			$_SESSION['commande'] = array(); // initialisation des informations pour la commande
			$_SESSION['commande']['step'] = 1;			
		}
		
		switch ($_SESSION['commande']['step']) {
			case 1:
				// choix de l'adresse de facturation
				break;
			case 2:
				// choix de l'adresse de livraison
				break;
			case 3:
				// choix du mode de paiement
				break;
			case 4:
				// récapitulatif de la commande
				break;
		}
		
		
		
		
	}
	else{
		// affichage de la page de connexion/inscription
		?>
			<form action="" method="post">
				<fieldset class="input">
					<legend>Déja inscrit</legend>
					<?php 
						if($loginError == true){
							echo '<p class="error messageBox">',$_SESSION['errorLogin'],'</p>';
						}
					?>
					<ol>
						<li>
							<label for="email">Email :</label>
							<input type="text" name="email" value="" />
						</li>
						<li>
							<label for="passwd">Mot de passe :</label>
							<input type="password" name="passwd" value="" />
						</li>
					</ol>
				</fieldset>
				<fieldset class="submit">
					<ol>
						<li>
							<input type="submit" name="loginform" value="connexion" />
						</li>
					</ol>
				</fieldset>
			</form>
			<form action="" method="post">
				<fieldset class="input">
					<legend>Nouveau client</legend>
					<ol>
						<li>
							<label for="nom">*Nom :</label>
							<input type="text" name="nom" value="" />
						</li>
						<li>
							<label for="prenom">*Prénom :</label>
							<input type="text" name="nom" value="" />
						</li>
						<li>
							<label for="email">*Email :</label>
							<input type="text" name="nom" value="" />
						</li>
						<li>
							<label for="passwd1">*Mot de passe :</label>
							<input type="password" name="passwd1" value="" />
						</li>
						<li>
							<label for="passwd2">*Retapez le mot de passe :</label>
							<input type="password" name="passwd2" value="" />
						</li>
						<li>
							<label for="telephone">*Téléphone :</label>
							<input type="text" name="telephone" value="" />
						</li>
						<li>
							<label for="fax">Fax :</label>
							<input type="text" name="fax" value="" />
						</li>
					</ol>
				</fieldset>
				<fieldset class="submit">
					<ol>
						<li>
							<input type="submit" name="signupform" value="s'inscrire" />
						</li>
					</ol>
				</fieldset>
			</form>
		<?php
	}
?>