<?php
	// page dans laquel l'admin doit choisir pour qu'elle client il passe commande
	$clients = clientDB::getClientsList();
	//@todo ajouter un lien pour créé un nouveau client
?>
<h1>Choix du client</h1>
		
		<p style="margin-left:10px;" >Recherchez un client par Email, <PERSON>m, Prenom, CP ou Ville</p>
		<br />
		<form style="margin-left:10px;" method="get" action="<?php echo $basePath,'index.php'?>">
		<input type="hidden" name="page" value="panier"/>
		<input type="hidden" name="action" value="valider" />
		<input type="hidden" name="action2" value="rechercher2" />
      	<input class="inputnewstyle" style="border:1px solid #cccccc" onblur="if (this.value == '') {this.value = 'Rechercher';}" onfocus="if (this.value == 'Rechercher') {this.value = '';}" type="text" name="recherche" value="Rechercher" length="40" />			
		<input class="submitnewstyle" type="submit" value="Ok" /><br />
		
		</form>
		<br />
<?php

if (isset($_GET['action2']) && ($_GET['action2'] == "rechercher2")){

	//require_once(dirname(__FILE__).'/../../../class/client_class.php');
	require_once(dirname(__FILE__).'/../../../class/pagination_class.php');
?>

<table style="margin-left:10px;" class="liste">
	<tr>
		<th><?php echo $translate->_('Email'); ?></th>
		<th><?php echo $translate->_('Indice'); ?></th>
		<th></th>
		<th><?php echo $translate->_('Raison social'); ?></th>
		<th><?php echo $translate->_('Adresse'); ?></th>
		<th><?php echo $translate->_('Code postal'); ?></th>
		<th><?php echo $translate->_('Ville'); ?></th>
		<th><?php echo $translate->_('Pays'); ?></th>
		<th><?php echo $translate->_('Passer commande'); ?></th>
	</tr>
	<?php

		if(isset($_GET['numpage']))
			$numpage = $_GET['numpage'];
		else
			$numpage = 1;
			
		//création d'un objet pour paginer la page
		$pagination = new Pagination(0, 30, 20, $numpage);
		
		$recherche = $_GET['recherche'];
		$result = $database->prepare("
		SELECT * FROM adresses_facturation
		INNER JOIN clients  
		ON emailclient = af_emailclient
		WHERE UPPER(af_emailclient) LIKE UPPER('%".$recherche."%')
		OR UPPER(af_raisonsocial) LIKE UPPER('%".$recherche."%')
		OR UPPER(af_codepostal) LIKE UPPER('%".$recherche."%')
		OR UPPER(af_ville) LIKE UPPER('%".$recherche."%')
		OR UPPER(af_pays) LIKE UPPER('%".$recherche."%')
		OR UPPER(emailclient) LIKE UPPER('%".$recherche."%')
		OR UPPER(nomclient) LIKE UPPER('%".$recherche."%')
		OR UPPER(prenomclient) LIKE UPPER('%".$recherche."%')
		order by af_raisonsocial
		") or die ("requete r1 invalid");	
		
		$result->execute();
		
		while ($row = $result->fetch()) {
			$pagination->incremente();
			if($pagination->isTimeToRender())
			{
				?>
				<tr <?php echo (($pagination->getCount()%2)?'class="imp"':''); ?>>
					<td>
						<?php echo $row['af_emailclient']; ?>
					</td>
					<td>
						<?php
						
							$idcde = $row['af_emailclient'];
							$result5 = $database->prepare("SELECT * FROM clients WHERE emailclient = '$idcde'") or die ("requete r5 invalid");
							$result5->execute();
							
							while ($tab4 = $result5->fetch()) {
								if($tab4['indiceclient'] == "1"){
								echo '<div align="center"><img src="'.$basePath.'public/images/rond_vert.png" alt="vert"/></div>';
								}
								if($tab4['indiceclient'] == "2"){
								echo '<div align="center"><img src="'.$basePath.'public/images/rond_rouge.png" alt="rouge"/></div>';
								}
								if($tab4['indiceclient'] == "3"){
								echo '<div align="center"><img src="'.$basePath.'public/images/rond_noir.png" alt="noir"/></div>';
								}
								if($tab4['indiceclient'] == "4"){
								echo '<div align="center"><img src="'.$basePath.'public/images/medaille.png" alt="medaille"/></div>';
								}
								if($tab4['indiceclient'] == "5"){
								echo '<div align="center"><img src="'.$basePath.'public/images/pro.png" alt="pro"/></div>';
								}
								if($tab4['indiceclient'] == "6"){
								echo '<div align="center"><img src="'.$basePath.'public/images/garage.png" alt="pro"/></div>';
								}
								if($tab4['infos'] == ""){
								echo "<td></td>";
								} else {
								echo '<div id="info1" style="position: absolute; left: 610px; top: 87px; height: 21px; visibility: hidden;border: 2px solid #FF6600;width: 300px;padding-top: 3px;padding-bottom: 3px; padding-left: 3px; height: auto;"><strong>Commentaire : </strong>'.$tab4['infos'].'</div>';
								echo '<td><div align="center"><img src="'.$basePath.'public/images/comment.png" alt="comment" onmousemove="afficher(\'info1\')" onmouseout="masquer(\'info1\')"/></div></td>';
								}
							}
						
						?>
					</td>
					<td>
						<?php echo $row['af_raisonsocial']; ?>
					</td>
					<td>
						<?php echo $row['af_nomrue']; ?>
					</td>
					<td>
						<?php echo $row['af_codepostal']; ?>
					</td>
					<td>
						<?php echo $row['af_ville']; ?>
					</td>		
					<td>
						<?php echo $row['af_pays']; ?>
					</td>
					<td>
					<div align="center">
					<form action="<?php echo $basePath;?>client/commande/action/admin_choixclient.php" method="post">
					<input type="hidden" name="email" value="<?php echo $row['af_emailclient']; ?>"/>
					<input type="submit" value="." name="submit" class="commande" />
					</form>
					</div>
					</td>

				</tr>
				<?php
			}
		}
	?>
	<tr>
		<th colspan=10><?php $pagination->printPagesLinks($basePath.'index.php?page=panier&action=valider&action2=rechercher2&recherche='.$recherche.'', true); ?></th>
		
	</tr>
</table>
<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>index.php?page=admin&action=administration"><?php echo $translate->_('Retour');?></a>
</p>
<?php
}
?>