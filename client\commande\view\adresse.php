<?php
	// @todo faire la traduction
	// la variable get['type'] contient le type d'adresse, rien pour livraison, 'fact' sinon
	require_once dirname(__FILE__).'/../../../class/adresse_class.php';
	require_once dirname(__FILE__).'/../../../class/af_class.php';
	require_once dirname(__FILE__).'/../../../utils/form_utile.php';
	if(!isset($_SESSION['commande']['surplace'])){
		$_SESSION['commande']['surplace'] = false;
	}
	
	if(isset($_GET['type']) && $_GET['type'] == 'fact' && $_SESSION['commande']['surplace'] == true){
		// si on veut afficher la page pour les adresses de facturation alors que l'on a choisi le retrait en magasin
		//, on est rediriger vers la page pour les adresses de livraison
		unset($_GET['type']);
	}
	
	
?>


<script type="text/javascript">
//<![CDATA[
	function setAdresse(adresse){
		adresse_a = adresse.split(';');
		document.getElementById('txt_idadresse').value = adresse_a[0];
		document.getElementById('txt_raisonsocial').value = adresse_a[1];
		document.getElementById('txt_nomrue').value = adresse_a[2];
		document.getElementById('txt_cp').value = adresse_a[3];
		document.getElementById('txt_ville').value = adresse_a[4];
		document.getElementById('txt_pays').value = adresse_a[5];
	}
	
	function disableForm(el){
		// fonction qui desactive tout les champs du formulaire 
		// (si on recupère les produit en magasin, on n'a pas besoin de mettre d'adresse de livraison
		document.getElementById('txt_raisonsocial').disabled = el.checked;
		document.getElementById('txt_nomrue').disabled = el.checked;
		document.getElementById('txt_cp').disabled = el.checked;
		document.getElementById('txt_ville').disabled = el.checked;
		document.getElementById('txt_pays').disabled = el.checked;
		document.getElementById('same').disabled = el.checked;
		
		d = document.getElementsByName('usethis');
		
		for(i = 0; i< d.length; i++){
			d.item(i).disabled = el.checked;
		}
		
	}
	//function prompt_comment() {
	//var comment = prompt('Merci indiquer la date de retrait ou le nom de la bourse.'); 
	//}
	
	function prompt_comment(str)
	{
		var xmlhttp = null;
		var comment = prompt('Merci indiquer la date de retrait ou le nom de la bourse.'); 
		if (str=="")
		  {
		  document.getElementById("txtHint").innerHTML="";
		  return;
		  }
		if (window.XMLHttpRequest)
		  {// code for IE7+, Firefox, Chrome, Opera, Safari
		  xmlhttp=new XMLHttpRequest();
		  }
		else
		  {// code for IE6, IE5
		  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
		  }
		xmlhttp.onreadystatechange=function()
		 {
		 if (xmlhttp.readyState==4 && xmlhttp.status==200)
		{
			document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
		}
		}
		xmlhttp.open("GET","<?php echo $basePath; ?>client/add_comment.php?commentaire="+comment,true);
		xmlhttp.send();
	}
	
	function add_comment(str)
	{
		var xmlhttp = null;
		if (str=="")
		  {
		  document.getElementById("txtHint").innerHTML="";
		  return;
		  }
		if (window.XMLHttpRequest)
		  {// code for IE7+, Firefox, Chrome, Opera, Safari
		  xmlhttp=new XMLHttpRequest();
		  }
		else
		  {// code for IE6, IE5
		  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
		  }
		xmlhttp.onreadystatechange=function()
		 {
		 if (xmlhttp.readyState==4 && xmlhttp.status==200)
		{
			document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
		}
		}
		xmlhttp.open("GET","<?php echo $basePath; ?>client/add_comment.php?initcom&commentaire="+str,true);
		xmlhttp.send();
	}	
	
	function oui_nd() {
       if (confirm("Si oui, en cas de produits non disponibles votre commande sera en attente de validation de votre part.\nElle ne partira pas le jour de la préparation des commandes !")) { // Clic sur OK
		if (window.XMLHttpRequest)
		  {// code for IE7+, Firefox, Chrome, Opera, Safari
		  xmlhttp=new XMLHttpRequest();
		  }
		else
		  {// code for IE6, IE5
		  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
		  }
		xmlhttp.onreadystatechange=function()
		 {
		 if (xmlhttp.readyState==4 && xmlhttp.status==200)
		{
			document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
		}
		}
		xmlhttp.open("GET","<?php echo $basePath; ?>client/add_comment.php?commentaire=Avertir si produit ND! ",true);
		xmlhttp.send();
       } else {
			document.getElementById("nonnd").checked = true;
	   }
	}
	
	function oui_mag() {
		if (window.XMLHttpRequest)
		  {// code for IE7+, Firefox, Chrome, Opera, Safari
		  xmlhttp=new XMLHttpRequest();
		  }
		else
		  {// code for IE6, IE5
		  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
		  }
		xmlhttp.onreadystatechange=function()
		 {
		 if (xmlhttp.readyState==4 && xmlhttp.status==200)
		{
			document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
		}
		}
		xmlhttp.open("GET","<?php echo $basePath; ?>client/add_comment.php?commentaire=Magazine Offert ",true);
		xmlhttp.send();
	}
//]]>
</script>

<?php 
	$action = '';
	if(isset($_GET['type'])){
		//echo '<h1>',$translate->_('Adresse de facturation'),'</h1>';
		$action = $basePath.'commande/tr_adresse_fact';
		//afficherMenuCommande(3);
		
		$ad_fact = afDB::getAdresseByClient($user->getEmail());
		?>
<form action="<?php echo $action;?>" method="post" id="addr_form">
	<div class="form-style-10">
		
		<div class="inner-wrap">
		<h1>Informations relatives au frais de port</h1>
- Les frais de port sont calculés <strong>automatiquement</strong> au moment du choix du mode de paiement.<br />
- Les frais de port sont calculés pour un poids maximum <strong>de 30kg</strong> et d'une longueur maximum <strong>de 1m50</strong>.<br />
- Les frais de port sont calculés pour la<strong> France métropolitaine</strong> et l'<strong>Europe</strong>, cependant nous autorisons <strong>les autres pays</strong> &agrave; passer commande mais il se peut qu'un ajustement des frais de port soient demand&eacute;s .<br />
- Si les frais de port ne sont <strong>pas suffisants</strong> nous nous réservons le droit de mettre en attente votre commande et de prendre contact avec vous pour obtenir un supplément de frais de port.<br />
- Pour toutes commandes de<strong> vitre, glace ou pare-brise</strong>, un emballage spécial ( consigné ) est demandé pour l'expédition de celles-ci. Merci de bien vouloir prendre contact avec nous avant la validation de votre commande.<br />
- Pour toutes commandes d'<strong>outillage</strong>, des frais supplémentaires  de 16€ HT sont ajoutés pour les articles qui ne sont pas en stock dans notre magasin. Une commande est donc passée chez notre fournisseur, il faut donc compter un délai supplémentaire de 1 semaine pour la livraison.<br />
		</div>

		<?php
		if (!isset($_SESSION['iscommandeadmin'])){
		?>

		<div class="inner-wrap">

			<h1><?php echo $translate->_('Conditions générales de vente'),' :';?></h1>
				<table>
				<tr>
				<td>
				J'ai lu et j'accepte <a style="color: #0099CC; text-decoration: underline;" href="<?php echo $basePath; ?>visiteur/condition_generale.php" target="_blank">les conditions générales de vente</a>
				</td><td>	
					<input type="checkbox" style="width:14px; margin-top:8px; margin-left: 15px;" name="condition" id="condition" />
					<script type="text/javascript">
						var condition = new LiveValidation('condition');
						condition.add( Validate.Acceptance );
					</script>
				</td>
				</tr>
				</table>
		</div>
		<?php
		}
		?>
		<div class="inner-wrap">
			<h1><?php echo $translate->_('Disponibilité des produits'),' :';?></h1>
				Je souhaite être averti si l'un de mes produits est non disponible : 
				<span style="color: #0099CC;">non<input style="width: 25px; margin-top:7px;" type="radio" id ="nonnd" CHECKED name="dispo" value= "non"/></span>
				<span style="color: #0099CC;">oui<input style="width: 25px; margin-top:7px;" type="radio" name="dispo" value= "oui" onclick="oui_nd();"/></span>
		</div>
		<div class="inner-wrap">
			<h1><?php echo $translate->_('Abonnement newsletter'),' :';?></h1>
				Je souhaite être abonné à la newsletter : 
				<span style="color: #0099CC;">non<input style="width: 25px; margin-top:7px;" type="radio" CHECKED name="news" value= "non"/></span>
				<span style="color: #0099CC;">oui<input style="width: 25px; margin-top:7px;" type="radio" name="news" value= "oui" /></span>
		</div>

<?php
/* ------ MAGAZINE OFFERT ----------

		<fieldset>
			<legend><?php echo $translate->_('Magazine offert'),' :';?></legend>
				<li>
				<label style="text-align:center; width:580px;">Je souhaite recevoir gratuitement un magazine aléatoire avec ma commande : </label>
				<span style="color: #0099CC;">non<input style="width: 25px; margin-top:7px;" type="radio" id ="nonnd" CHECKED name="mag" value= "non"/></span>
				<span style="color: #0099CC;">oui<input style="width: 25px; margin-top:7px;" type="radio" name="mag" value= "oui" onclick="oui_mag();"/></span>
				<span style="font-size:12px;">(Offre valable du 1er au 31 Juillet 2016, pour toutes commandes de plus de 100€ HT d'achat minimum)</span>
		</fieldset>
*/
?>
			<div class="button-section">
				<input type="hidden" name="raisonsocial" value="<?php echo stripslashes($ad_fact->getRaisonSocial()); ?>" />
				<input type="hidden" name="nomrue" value="<?php echo stripslashes($ad_fact->getNomRue()); ?>" />
				<input type="hidden" name="cp" value="<?php echo stripslashes($ad_fact->getCodePostal()); ?>" />
				<input type="hidden" name="ville" value="<?php echo stripslashes($ad_fact->getVille()); ?>" />
				<input type="hidden" name="pays" value="<?php echo stripslashes($ad_fact->getPays()); ?>" />
				<input type="hidden" id="same" name="same" value="0" />
				<input type="hidden" name="idadresse" value="-2" />
				<input type="submit" name="adresse_form" value="<?php echo $translate->_('Suivant');?> &gt;&gt;" class="btn_submit"/>
			</div>
		</div>
		</form>
		<?php
	}
	else{
		//echo '<h1>',$translate->_('Choix de l\'adresse de livraison'),'</h1>';
		$action = $basePath.'commande/tr_adresse';
		//afficherMenuCommande(2);
	//}
	$data = array('idadresse' => '0');
	// si on a déja remplit le formulaire et que POST est vide
	if(isset($_SESSION['POST']) && count($_SESSION['POST'])){
		$data = $_SESSION['POST'];	
	}
	else{
		$adresse = new adresse();
		if(!isset($_GET['type']) && isset($_SESSION['commande']['livraison'])){
			$adresse = unserialize($_SESSION['commande']['livraison']);
		}
		else if(isset($_GET['type']) && $_GET['type'] == 'fact' && isset($_SESSION['commande']['facturation'])){
			$adresse = unserialize($_SESSION['commande']['facturation']);
		}
		$data['idadresse'] = $adresse->getId();
		$data['raisonsocial'] = $adresse->getRaisonSocial();
		$data['nomrue'] = $adresse->getNomRue();
		$data['cp'] = $adresse->getCodePostal();
		$data['ville'] = $adresse->getVille();
		$data['pays'] = $adresse->getPays();
	}
	
	// si non est sur la page pour la livraison
	// sinon on est sur la page de facturation
?>
<?php if(isset($_SESSION['infos']) && $_SESSION['infos'] != ""): ?>
<p class="info messageBox"><?php echo $_SESSION['infos']; ?></p>
<?php endif; ?>
<form action="<?php echo $action;?>" method="post" id="addr_form">

	<?php if(isset($_SESSION['adresse_erreur'])): ?>
	<p class="error messageBox"><?php echo $_SESSION['adresse_erreur']; ?></p>
	<?php
		unset($_SESSION['adresse_erreur']); 
		endif; ?>
	<div class="form-style-10">
	<div class="section"><span>¤</span>Retrait magasin</div>
	<div class="inner-wrap">
	
				<?php if(!isset($_GET['type']) || $_GET['type'] != 'fact'): ?>
				<table>
					<tr>
					<td>
					<label style="margin-right:15px;" for="retraitmagasin">Cochez cette case pour un retrait en magasin / reliquat commande précédente : </label>
					<?php
						$checked = "";
						if($_SESSION['commande']['surplace'] == true){
							$checked = 'checked="checked"';
						}
					?>
					</td><td>
					<input type="checkbox" style="width:14px; margin-top:3px;" value="true" name="retraitmagasin" id="retraitmagasin" onclick="javascript:prompt_comment()" <?php echo $checked; ?> />

				<span id="txtHint"></span>
				</td>
				</tr>
				</table>
			<?php endif; ?>
				
	</div>
	<div style="margin-bottom:30px;" class="button-section">
		<strong><input type="button" value="Suivant>>" onclick="this.form.submit();"></strong>
	</div>
	
<! --- Début Modif Bourses 	--->

		<div class="section"><span>¤</span>Retrait Bourses</div>
	<div class="inner-wrap">
	
				<?php if(!isset($_GET['type']) || $_GET['type'] != 'fact'): ?>
				<table>
			
<! --- Début Modif Automatisme 	--->			
<?php				
			$result99 = $database->prepare("SELECT * FROM Facture_Options ORDER BY DateDeb") or die ("requete r99 invalid");
			$result99->execute();
			while ($Facture_Options = $result99->fetch()) {
			
			$op_Commentaire = $Facture_Options['Commentaire'];
?>
			<tr>
			<td>
				<label style="margin-right:15px;" for="retraitmagasin"><?php echo $op_Commentaire; ?></label>
					<?php
						$checked = "";
						if($_SESSION['commande']['surplace5'] == true){
							$checked = 'checked="checked"';
						}
					?>
			</td><td>
			<input type="checkbox" style="width:14px; margin-top:3px;" value="true" name="retraitmagasin" id="retraitmagasin" onclick="javascript:add_comment('<?php echo $op_Commentaire; ?>')" <?php echo $checked; ?>; />

				<span id="txtHint"></span>
				</tr>				

			
<?php			}?>
<! --- Fin Modif Automatisme 	--->
				
			
				</table>
			<?php endif; ?>
				
	</div>
	<div style="margin-bottom:30px;" class="button-section">
		<strong><input type="button" value="Suivant>>" onclick="this.form.submit();"></strong>
	</div>

<! --- Fin Modif Bourses 	--->	
	
	
		<div class="section"><span>¤</span>Expédition à domicile</div>
		<table>
			<tr>
			<td>
				<div class="inner-wrap" style="width:335px; height:490px;">
				<h1>Adresse Facturation</h1>
			<?php
			
			// Fonction pour rapatrier les info de l'adresse de facturation lors de l'inscription
			
			$user10 = (unserialize($_SESSION['user1']));
			$email = $user10->getemail();
			$result2 = $database->prepare("SELECT * FROM adresses_facturation WHERE af_emailclient='".$email."'") or die ("requete r2 invalid");
			$result2->execute();
			while ($adresse_facture = $result2->fetch()) {
			
			$af_raisonsocial = $adresse_facture['af_raisonsocial'];
			$af_nomrue = $adresse_facture['af_nomrue'];
			$af_codepostal = $adresse_facture['af_codepostal'];
			$af_ville = $adresse_facture['af_ville'];
			$af_pays = $adresse_facture['af_pays'];
			
			}
			
			$result3 = $database->prepare("SELECT * FROM adresses WHERE emailclient='".$email."'") or die ("requete r3 invalid");
			$result3->execute();
			while ($adresse_livraison = $result3->fetch()) {
			
			$liv_raisonsocial = $adresse_livraison['raisonsocial'];
			$liv_nomrue = $adresse_livraison['nomrue'];
			$liv_codepostal = $adresse_livraison['codepostal'];
			$liv_ville = $adresse_livraison['ville'];
			$liv_pays = $adresse_livraison['pays'];
			
			}
			
			?>

				<label style="text-align:left;" for="raisonsocial_af"><?php echo $translate->_('Raison Social'),' :';?></label>
				<input type="text" name="raisonsocial_af" id="txt_raisonsocial_af" disabled="disabled" value="<?php echo $af_raisonsocial; ?>" />

				<label style="text-align:left;" for="adresse_af"><?php echo $translate->_('Adresse'),' :';?></label>
				<input type="text" name="nomrue_af" value="<?php echo $af_nomrue; ?>" id="txt_nomrue_af"  disabled="disabled" />

				<label style="text-align:left;" for="cp_af"><?php echo $translate->_('Code Postal'),' :';?></label>
				<input type="text" name="cp_af" value="<?php echo $af_codepostal; ?>" id="txt_cp_af" disabled="disabled"/>

				<label style="text-align:left;" for="ville_af"><?php echo $translate->_('Ville'),' :';?></label>
				<input type="text" name="ville_af" value="<?php echo $af_ville; ?>" id="txt_ville_af" disabled="disabled"/>

				<label style="text-align:left;" for="pays"><?php echo $translate->_('Pays'),' :';?></label>
				<input type="text" name="pays_af" value="<?php echo $af_pays; ?>" id="txt_pays_af" disabled="disabled"/>
				
				<label>Si vous désirez changer votre adresse de facturation, merci de contacter l'équipe du site par e-mail : <a href="mailto:<EMAIL>"><EMAIL></a> ou par téléphone au ***********.56</label>
				</div>
			</td>
			<td>
	<div class="inner-wrap" style="width:335px; height:490px;">		

			<h1>Adresse Livraison</h1>
			
			<?php
			
			// Fonction pour rapatrier les info de l'adresse de facturation lors de l'inscription
			
			?>
			<script>
			
			function clearText(el) {
			
				// set text box reference
				var raisonsocial = document.getElementById('txt_raisonsocial');
				var nomrue = document.getElementById('txt_nomrue');
				var cp = document.getElementById('txt_cp');
				var ville_inter = document.getElementById('ville_inter');

				
				// clear text box
				raisonsocial.value = '';
				nomrue.value = '';
				cp.value = '';
				ville_inter.value = '';

				
				//disabled
				
				raisonsocial.removeAttribute("readOnly");
				nomrue.removeAttribute("readOnly");
				cp.removeAttribute("readOnly");
				ville_inter.removeAttribute("readOnly");

				
				//change le style 
				
				raisonsocial.style.cssText='background-color:#ffffdd; color:#000000';
				nomrue.style.cssText='background-color:#ffffdd; color:#000000';
				cp.style.cssText='background-color:#ffffdd; color:#000000';
				ville_inter.style.cssText='background-color:#ffffdd; color:#000000';

			}
			window.onload = function ()
			{
				document.getElementById("view_ville").innerHTML='<input type="text" name="ville" style="color:#666666; background-color:#ffffff;" id="ville_inter" readOnly="readOnly" value="<?php if(empty($liv_ville)){echo $af_ville;}else{echo $liv_ville;}?>" id="txt_ville" class="indispensable"/>';
				document.getElementById("liv_diff").innerHTML='<input style="width: 25px; text-align:center;" type="checkbox" value="" name="checkbox_l" id="checkbox_l" onclick="javascript:clearText(this);" checked="checked" />';
			}
			
			function choix_pays()
			{
			var pays=document.getElementById("txt_pays").value;
			if (pays=="France"){	
					var cp = document.getElementById("txt_cp").value;
					var xmlhttp = null;
					if (cp=="")
					  {
					  document.getElementById("view_ville").innerHTML="";
					  return;
					  }
					if (window.XMLHttpRequest)
					  {// code for IE7+, Firefox, Chrome, Opera, Safari
					  xmlhttp=new XMLHttpRequest();
					  }
					else
					  {// code for IE6, IE5
					  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
					  }
					xmlhttp.onreadystatechange=function()
					  {
					  if (xmlhttp.readyState==4 && xmlhttp.status==200)
						{
						document.getElementById("view_ville").innerHTML=xmlhttp.responseText;
						}
					  }
					xmlhttp.open("GET","../client/choix_ville.php?cp="+cp,true);
					xmlhttp.send();
					
				}
				else
				{
					document.getElementById("view_ville").innerHTML = '<input id="f7" type="text" name="ville" value="" class="indispensable"/>';
				}
				
			} 
			
			</script>

				<label style="text-align:center;"><b>D&eacute;cocher si livraison diff&eacute;rente</b>
				<span name="liv_diff" id="liv_diff"></span></label>

				<label style="text-align:left;" for="raisonsocial">Raison Social<b class="reditalique"> * </b> : </label>
				<input type="text" name="raisonsocial" style="color:#666666; background-color:#ffffff;" maxlength="35" readOnly="readOnly" id="txt_raisonsocial" value="<?php if(empty($liv_raisonsocial)){echo $af_raisonsocial;}else{echo $liv_raisonsocial;}?>"  class="indispensable"/>

				<label style="text-align:left;" for="adresse">Adresse<b class="reditalique"> * </b> : </label>
				<input type="text" name="nomrue"  style="color:#666666; background-color:#ffffff;" maxlength="27" readOnly="readOnly" value="<?php if(empty($liv_nomrue)){echo $af_nomrue;}else{echo $liv_nomrue;}?>" id="txt_nomrue" class="indispensable"/>

				<label style="text-align:left;" for="cp">Code postal <b class="reditalique"> * </b> : </label>
				<input type="text" name="cp" style="color:#666666; background-color:#ffffff;" onChange="choix_pays()"  maxlength="9" readOnly="readOnly" value="<?php if(empty($liv_codepostal)){echo $af_codepostal;}else{echo $liv_codepostal;}?>" id="txt_cp" class="indispensable"/>

				<label style="text-align:left;" for="pays">Pays<b class="reditalique"> * </b> : </label>
				<select name="pays" id="txt_pays" onchange="choix_pays(this.options[this.selectedIndex].value)" onclick="choix_pays(this.options[this.selectedIndex].value)" >
				<?php
				
				$result = $database->prepare("SELECT * FROM pays order by nom_pays ASC") or die ("requete r1 invalid");
				$result->execute();
				
				?>
					<option value="<?php if(empty($liv_pays)){echo $af_pays;}else{echo $liv_pays;}?>" selected="selected"><?php if(empty($liv_pays)){echo $af_pays;}else{echo $liv_pays;}?></option>
				<?php
				
				while ($tab = $result->fetch()) {
				
					echo '<option style="width: 225px;" value="'.$tab['nom_pays'].'">'.$tab['nom_pays'].'</option>';
					
				}
						
					?>
				</select>

			<div id="ville">
				<label style="text-align:left;" for="ville">Ville <b class="reditalique"> * </b> :</label>
				<span name="ville" id="view_ville"></span>
			</div>

				<label class="reditalique"><?php echo '* ',$translate->_('Champs obligatoires'); ?></label>
				<br />
	</div>	
	</td>
	</tr>
	</table>
	<div class="button-section">
		<input type="hidden" id="same" name="same" value="0" />
	
	<?php
	$result3 = $database->prepare("select count(*) from adresses where emailclient = '$email'") or die ("requete r3 invalid");
	$result3->execute();
	$count_ad = $result3->fetch();

	if ($count_ad != "0"){
	
	$result4 = $database->prepare("select * from adresses where emailclient = '$email' ORDER BY idadresse DESC LIMIT 1") or die ("requete r4 invalid");
	$result4->execute();
	$id_adresse = $result4->fetch();
	?>
	
	<input type="hidden" name="idadresse" id="txt_idadresse" value="<?php echo $id_adresse['idadresse']; ?>" />
	
	<?php
	
	} else {
	
	?>
	
	<input type="hidden" name="idadresse" id="txt_idadresse" value="<?php echo @$data['idadresse']; ?>" />
	
	<?php
	}
	?>
		<input type="submit" name="adresse_form" value="<?php echo $translate->_('Suivant');?> &gt;&gt;" class="btn_submit"/>
	</div>
</form>
</div>
<script type="text/javascript">
//<!CDATA[
	
<?php
	if($_SESSION['commande']['surplace'] == true){
		echo "document.getElementById('retraitmagasin').checked = true;";
		echo "disableForm(document.getElementById('retraitmagasin'));";
	}
?>
//]]>
</script>

<?php }
	$_SESSION['POST'] = array();
?>