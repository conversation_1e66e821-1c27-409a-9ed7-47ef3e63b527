<?php
	// @todo ajouter la traduction
	// Page permettant de faire le choix entre s'inscrire ou ce connecter
	require_once dirname(__FILE__).'/../../../utils/form_utile.php';	
	if (!isset($_GET['annonce'])){
	
	//afficherMenuCommande(1);
	
	}
	
?>

<?php 
	if(isset($_SESSION['login_error']) && $_SESSION['login_error'] != ""){
		echo '<p class="error messageBox">',$_SESSION['login_error'],'</p>';
		$_SESSION['login_error'] = "";	
	}
?>
				
<form action="<?php echo $basePath;?>client/commande/action/login.php" method="post">

<?php

// RETOUR a la partie ANNONCE

if (isset($_GET['annonce'])){
	
	echo '<input type="hidden" name="annonce" value="oui" />';
	
}

?>
<div class="form-style-10">
	<h1 style="padding-top:8px; padding-bottom:22px;" >Déjà client ?</h1>
<div class="inner-wrap" style="width:740px;">
		    <table style="margin-left:60px;">
			<tr>
			<td>
					<label for="email"><?php echo $translate->_('E-mail'); ?> :</label>
					<input style="width:250px; margin-right:30px;" id="f20" type="text" name="email" value="<?php echo @$_SESSION['POST']['email']; ?>"/>
					<script type="text/javascript">
						var f20 = new LiveValidation('f20');
						f20.add( Validate.Presence );
					</script>
			</td><td>		
					<label for="email"><?php echo $translate->_('Mot de passe'); ?> :</label>
					<input style="margin-right:30px;" type="password" name="password" value=""/>
			</td><td>
			<div class="button-section" style="margin-top:23px; margin-left:30px;">
				<input type="submit" name="suivant" value="<?php echo $translate->_('Valider');?>"/>
			</div>
			</td>
			</tr>
			</table>
</div>
</div>
</form>

<form name="form1" action="<?php echo $basePath; ?>client/commande/action/signin.php" method="POST">
<?php
if (isset($_GET['annonce'])){
	
	echo '<input type="hidden" name="annonce" value="oui" />';
	
}
?>
<script>
function copie()
{

	document.forms['form1'].elements['f4'].value = document.forms['form1'].elements['f1'].value.toUpperCase() + " " + document.forms['form1'].elements['f2'].value.toLowerCase();

}

function disabled_prenom()
{
	document.getElementById('f2').readOnly = false;
	document.getElementById('f2').style.backgroundColor = "white";
}

function test_email(str)
	{
		if (str=="")
		  {
		  document.getElementById("txtHint").innerHTML="";
		  return;
		  }
		if (window.XMLHttpRequest)
		  {// code for IE7+, Firefox, Chrome, Opera, Safari
		  xmlhttp=new XMLHttpRequest();
		  }
		else
		  {// code for IE6, IE5
		  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
		  }
		xmlhttp.onreadystatechange=function()
		  {
		  if (xmlhttp.readyState==4 && xmlhttp.status==200)
			{
			document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
			}
		  }
		xmlhttp.open("GET","<?php echo $basePath; ?>client/test_email.php?mail="+str,true);
		xmlhttp.send();
	} 
function choix_pays(pays)
	{
		if (pays=="France"){
				
			var cp = document.getElementById("f6").value;
			var xmlhttp = null;
			if (cp=="")
			  {
			  document.getElementById("view_ville").innerHTML="Veuillez saisir un code postal";
			  return;
			  }
			if (window.XMLHttpRequest)
			  {// code for IE7+, Firefox, Chrome, Opera, Safari
			  xmlhttp=new XMLHttpRequest();
			  }
			else
			  {// code for IE6, IE5
			  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
			  }
			xmlhttp.onreadystatechange=function()
			  {
			  if (xmlhttp.readyState==4 && xmlhttp.status==200)
				{
				document.getElementById("view_ville").innerHTML=xmlhttp.responseText;
				}
			  }
			xmlhttp.open("GET","<?php echo $basePath; ?>client/choix_ville.php?cp="+cp,true);
			xmlhttp.send();
		}
		else
		{
			document.getElementById("view_ville").innerHTML = '<input id="f7" type="text" name="ville" value=""/>';
		}
	} 
</script>


		<?php 
			if(isset($_SESSION['signin_error']) && $_SESSION['signin_error'] != ""){
				echo '<p class="error messageBox">',$_SESSION['signin_error'],'</p>';
				$_SESSION['signin_error'] = "";	
			}
		?>
	<div class="form-style-10">
		<h1 style="padding-top:8px; padding-bottom:22px;" >Devenir client</h1>
		    <table>
			<tr>
			<td>
			<div class="inner-wrap" style="width:345px; height:530px;">
				
					<label style="margin-top:10px;" for="nom"><?php echo $translate->_('Nom'); ?> <b class="reditalique"> * </b> :</label>
					<input id="f1" type="text"  name="nom" size=30 maxlength=60 value="<?php echo @$_SESSION['POST']['nom']; ?>" onchange="disabled_prenom()" required />

					<label  style="margin-top:10px;"  for="prenom"><?php echo $translate->_('Prénom'); ?> <b class="reditalique"> * </b> :</label>
					<input style="background-color:#eaeaea;" id="f2"type="text"  name="prenom" size=30 maxlength=60 value="<?php echo @$_SESSION['POST']['prenom']; ?>" onchange="copie()" readonly required />

					<label style="margin-top:10px;"  for="email"><?php echo $translate->_('E-mail'); ?> <b class="reditalique"> * </b> :</label>
					<input id="f21" type="email" onchange="test_email(this.value)" name="email" size=30 maxlength=100 value="<?php echo @$_SESSION['POST']['email']; ?>" required />

				<span id="txtHint"></span>

					<label style="margin-top:10px;" for="password"><?php echo $translate->_('Mot de passe'); ?> <b class="reditalique"> * </b> :</label>
					<input id="myPasswordField" type="password" name="motdepasse" size=20 maxlength=20 value=""/>

					<label style="margin-top:10px;" for="password2"><?php echo $translate->_('Confirmer le mot de passe'); ?> :</label>
					<input id="f19" type="password" name="motdepasseconfirme" size=20 maxlength=20 value=""/>
					<script type="text/javascript">
						var f19 = new LiveValidation('f19');
						f19.add(Validate.Confirmation, { match: 'myPasswordField'} );
		            </script>

					<label style="margin-top:10px;" for="tel"><?php echo $translate->_('Téléphone'); ?><b class="reditalique"> * </b> (ex: 0476644356) : </label>
					<input id="f3" type="tel" name="tel" size=20 maxlength=20 value="<?php echo @$_SESSION['POST']['tel']; ?>" required />

					<label style="margin-top:10px;" for="fax"><?php echo $translate->_('Fax'); ?> ou Fixe :</label>
					<input type="text" name="fax" size=20 maxlength=20 value="<?php echo @$_SESSION['POST']['fax']; ?>"/>
			</div>
			</td>
			<td>
			<div class="inner-wrap" style="width:345px; height:530px;">
					<h1>Adresse de facturation</h1>
					<label for="raisonsociale">Raison Social ou NOM Prénom <b class="reditalique"> * </b> : </label>
					<input id="f4" type="text" name="raisonsocial" value="<?php echo @$_SESSION['POST']['raisonsocial']; ?>" required />

					<label for="adresse">Adresse <b class="reditalique"> * </b> : </label>
					<input onfocus="this.placeholder = ''" placeholder="rue, chemin, route, avenue, boulevard..." id="f5" type="text" name="nomrue" value="<?php echo @$_SESSION['POST']['nomrue']; ?>" required />

				<div id="cp" >
					<label for="cp">Code postal <b class="reditalique"> * </b> : </label>
					<input id="f6" type="text" name="cp" onchange="choix_pays(document.getElementById('pays').options[document.getElementById('pays').selectedIndex].value)" value="<?php echo @$_SESSION['POST']['cp']; ?>" required />
				</div>

					<label for="pays">Pays <b class="reditalique"> * </b> : </label>
					<select id="pays" name="pays" onchange="choix_pays(this.options[this.selectedIndex].value)" required onclick="choix_pays(this.options[this.selectedIndex].value)">
					<?php
				$result = $database->prepare("SELECT * FROM pays order by nom_pays ASC") or die ("requete r1 invalid");
				$result->execute();

				echo '<option value="" selected="selected">Selectionner</option>';

				while ($tab = $result->fetch()) {
				
					echo '<option style="width: 225px;" value="'.$tab['nom_pays'].'">'.$tab['nom_pays'].'</option>';
				
					}
					?>
					</select>

				<div id="ville">
					<label for="ville">Ville <b class="reditalique"> * </b> : </label>
					<span name="ville" id="view_ville"></span>
				</div>
			<br />	
			<label>Inscription à notre Newsletter :<input type="checkbox" style="width:16px; margin-left:50px;" name="news" id="news" /></label>

			<label>Cette adresse de facturation est fixe, pour la modifier il faudra contacter l'équipe du site par mail ou par téléphone.<br />
			<br />
			Vous pourrez par la suite spécifier une adresses de livraison différentes de la facturation.<br /></label>
			<br />
			</div>
			</td>
			</tr>
          </table>	
		<div class="button-section">
			<input type="submit" value="Valider">
		</div>
	</div>
</form>
<?php
	$_SESSION['POST'] = array(); 