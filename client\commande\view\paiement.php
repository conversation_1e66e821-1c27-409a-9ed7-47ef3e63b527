<?php
require_once 'commande_class.php';
require_once 'adresse_class.php';
require_once 'commande_static_class.php';

//ini_set("display_errors",0);error_reporting(0);




// affichage du recap après le paiement
// gestion du paiement
// traitement du dernier formulaire
if ($user->getPanier()->getTotalHT() > 0){

if ($_POST['mode'] == "123"){

echo "<div class='messageBox error'>Vous avez oublié de sélectionner un mode de paiement!</div>";	
include dirname(__FILE__).'/recap.php';
	
} else {
	
$commande1 = new commande();

$commande1->setPanier($user->getPanier());
$commande1->setDateCommande(date('Y-m-d'));

$commande1->setTauxTVA($user->getPanier()->getTauxTVA());


/* COMMANDE STATIC */
$cs = new commande_static();
$cs->setDate(date('Y-m-d'));
if($_SESSION['commande']['surplace'] != true) {
	switch ($_POST['mode']) {
		case 'cb': // paiement par carte de credit
				$cs->setModepaiement("Carte de crédit");
				$cs->setStatut("Attente de paiement");
				break;
		case 'ch': // paiement par cheque
				$cs->setModepaiement("Chèque");
				$cs->setStatut("Attente de paiement");
				break;
		case 'pp': // paiement par paypal
				$cs->setModepaiement("Paypal");
				$cs->setStatut("Attente de paiement");
				break;
		case 'mc': // paiement par mandat cash
				$cs->setModepaiement("Mandat cash");
				$cs->setStatut("Attente de paiement");
				break;
		case 'vi': // paiement par virement bancaire
				$cs->setModepaiement("Virement bancaire");
				$cs->setStatut("Attente de paiement");
				break;
		case 'cr': // paiement par contre remboursement
				$cs->setModepaiement("Contre remboursement");
				$cs->setStatut("Paiement validé");
				break;
	}
}
$cs->setEmail($user->getEmail());
$cs->setNomprenom($user->getPrenom().' '.$user->getNom());
$cs->setTel($user->getTel());
$cs->setFax($user->getFax());
if (isset($_POST['comment'])) {
	$cs->setComment($_POST['comment']);
} 
if (isset($_POST['iscommandeadmin'])) {
	$cs->setIscommandeadmin($_POST['iscommandeadmin']);
} else {
	$cs->setIscommandeadmin(0);
}
/* COMMANDE STATIC */


if($_SESSION['commande']['surplace'] == true){
	//pas de paiement car retrait en magasin
	//$commande1->setFactAdresse(null);
	$commande1->setFactAdresse(unserialize($_SESSION['commande']['facturation']));
	$commande1->setLivreAdresse(null);
	$commande1->setModePaiement(0);
	$commande1->setStatut(commande::ST_ATTENTE);
	$commande1->setRetraitMagasin(1);
	
	/* COMMANDE STATIC */
	$facturation = $commande1->getFactAdresse();
	$adfact = stripslashes($facturation->getRaisonSocial()).'<br />'.stripslashes($facturation->getNomRue()).'<br />'.stripslashes($facturation->getCodePostal()).' '.stripslashes($facturation->getVille()).'<br />'.stripslashes($facturation->getPays()).'<br />';
	$cs->setAdressefact($adfact);
	$cs->setRetraitmagasin(1);
	$cs->setModepaiement("Sur place");
		switch ($_POST['mode']) {
		case 'cb': // paiement par carte de credit
				$cs->setModepaiement("Carte de crédit");
				$cs->setStatut("Sur place");
				break;
		case 'ch': // paiement par cheque
				$cs->setModepaiement("Chèque");
				$cs->setStatut("Sur place");
				break;
		case 'pp': // paiement par paypal
				$cs->setModepaiement("Paypal");
				$cs->setStatut("Sur place");
				break;
		case 'mc': // paiement par mandat cash
				$cs->setModepaiement("Espèce");
				$cs->setStatut("Sur place");
				break;
		case 'cbc': // paiement par carte bancaire comptoir
				$cs->setModepaiement("CB comptoir");
				$cs->setStatut("Sur place");
				break;
		case 'vi': // paiement par virement bancaire
				$cs->setModepaiement("Virement bancaire");
				$cs->setStatut("Sur place");
				break;
	}
	/* COMMANDE STATIC */
}else{
	$commande1->setRetraitMagasin(false);
	$commande1->setFactAdresse(unserialize($_SESSION['commande']['facturation']));
	$commande1->setLivreAdresse(unserialize($_SESSION['commande']['livraison']));
	switch ($_POST['mode']) {
		case 'cb': // paiement par carte de credit
			$commande1->setModePaiement(commande::MD_CB);
			$commande1->setStatut(commande::ST_ATTENTE_PAY);
			break;
		case 'ch': // paiement par cheque
			$commande1->setModePaiement(commande::MD_CH);
			$commande1->setStatut(commande::ST_ATTENTE_PAY);
			break;
		case 'pp': // paiement par paypal
			$commande1->setModePaiement(commande::MD_PP);
			$commande1->setStatut(commande::ST_ATTENTE_PAY);
			break;
		case 'mc': // paiement par mandat cash
			$commande1->setModePaiement(commande::MD_MC);
			$commande1->setStatut(commande::ST_ATTENTE_PAY);
			break;
		case 'vi': // paiement par virement bancaire
			$commande1->setModePaiement(commande::MD_VI);
			$commande1->setStatut(commande::ST_ATTENTE_PAY);
			break;
		case 'cr': // paiement par contre remboursement
			// @todo mettre le montant du contre rembourssement dans la table paramètres
			$db = Zend_Registry::get('database');
			$row = $db->query("SELECT * FROM parametres WHERE cle = 'contre_remb'")->fetch();
			$mtCR = $row['valeur']; // montant du contre remboursement a ajouter au frais de port
			$commande1->getPanier()->setFraisDePort($commande1->getPanier()->getFraisDePort()+$mtCR);
			$commande1->setModePaiement(commande::MD_CR);
			$commande1->setStatut(commande::ST_ATTENTE);
			break;
	}
	
	/* COMMANDE STATIC */
	$facturation = $commande1->getFactAdresse();
	$adfact = stripslashes($facturation->getRaisonSocial()).'<br />'.stripslashes($facturation->getNomRue()).'<br />'.stripslashes($facturation->getCodePostal()).' '.stripslashes($facturation->getVille()).'<br />'.stripslashes($facturation->getPays()).'<br />';
	$cs->setAdressefact($adfact);
	$livraison = $commande1->getLivreAdresse();
	$adlivr = stripslashes($livraison->getRaisonSocial()).'<br />'.stripslashes($livraison->getNomRue()).'<br />'.stripslashes($livraison->getCodePostal()).' '.stripslashes($livraison->getVille()).'<br />'.stripslashes($livraison->getPays()).'<br />';
	$cs->setAdresselivr($adlivr);
	$cs->setRetraitmagasin(0);
	/* COMMANDE STATIC */
}



// On rempli le panier
$lPs1 = $commande1->getPanier();
$panier_s = $cs->getPanier();
$fdp_sup = 0;
foreach($lPs1->getLignePanier() as $k1 => $lP1) {
	$p1 = $lP1->getProduit();
	////////////REMISE PRO/////////////////
	$remisepro = 0;
	if($user->isAuthenticated){
	$mailclient = $user->getEmail();
		if ($p1->getGenreName() == "JEEP" || $p1->getGenreName() == "DODGE" || $p1->getGenreName() == "GMC" || $p1->getGenreName() == "RENAULT"){
			$remisepro = ProduitDB::getPrixPro(strtolower($p1->getPromotion()), $p1->getQualite(), $p1->getPrixHT(), $mailclient);
		}
	}
	$prixHT = $p1->getPrixHT()-$remisepro;	
	$remisepropourcentage = ProduitDB::getRemisePro(strtolower($p1->getPromotion()),$p1->getQualite(), $mailclient);
	$qualite = ProduitDB::getQualiteNoStyle($p1->getQualite());
	///////////////////////////////////////
	$lignep_s = new lignepanier_static();
	$lignep_s->setReference($p1->getReference());
	$lignep_s->setGenregroupe($p1->getGenreName().' / '.$p1->getGroupeId().' / '.$p1->getGroupeName());
	
	if ($remisepro =! 0 && !empty($remisepro)){
	$lignep_s->setDesignation($p1->getDesignation()." - ".$qualite." - ".$remisepropourcentage."%");
	}else{
	$lignep_s->setDesignation($p1->getDesignation()." - ".$qualite);	
	}
	$lignep_s->setQte($lP1->getQte());
	$lignep_s->setPuHT($prixHT);
	$lignep_s->setSomme($prixHT*$lP1->getQte());
	$panier_s->ajouterProduit($lignep_s);
}
$db2 = Zend_Registry::get('database');
$row2 = $db2->query("SELECT * FROM parametres WHERE cle = 'fdp_kt'")->fetch();
$fdp_kt = $row2['valeur'];

if(isset($_POST['remise_fdp'])){
$remise_fdp = $_POST['remise_fdp'];
} else {
$remise_fdp = 0;
}

if(isset($_POST['remise_jc'])){
$remise_jc = $_POST['remise_jc'];
} else {
$remise_jc = 0;
}
		
$commande1->setFraisDePort(($commande1->getPanier()->getFraisDePort()) - $remise_fdp);
		
$commande1->setTotalHT($user->getPanier()->getTotalHT());

/* COMMANDE STATIC */

$commande1->getPanier()->recalculerPro($user->getEmail()); // BFO 03/10/25025

$totht = $commande1->getTotalHT();
//$totht = $user->getPanier()->getTotalHT();
$cs->setMontanttotalHT(number_format($totht, 2, '.', ''));
$cs->setRemise_MontanttotalHT(number_format($remise_jc, 2, '.', ''));
$port = $commande1->getFraisDePort();
$cs->setFdp(number_format($port, 2, '.', ''));
$cs->setRemise_Fdp(number_format($remise_fdp, 2, '.', ''));
$txtva = $commande1->getTauxTVA();
$cs->setTauxTVA(number_format($txtva, 2, '.', ''));
$tva = ((($commande1->getTotalHT() - $remise_jc) *$commande1->getTauxTVA())/100) + (($commande1->getFraisDePort()*$commande1->getTauxTVA())/100);
$cs->setMontantTVA(number_format($tva, 2, '.', ''));
//$totttc = $commande1->getTotalTTC();
$totttc = $totht - $remise_jc + $port + $tva;
$cs->setMontanttotalTTC(number_format($totttc, 2, '.', ''));

/* COMMANDE STATIC */

if(isset($_POST['mode']) && $_POST['mode'] == 'cb'){
	// paiement par carte de credit
	// affichage de la partie avec le choix de la carte de credit
	
	if(!isset($_SESSION['commande']['id'])){ // truc a la con pour empecher de passer 2 fois la même commande
		// si on a pas encore passé de commande
		/*commandeDB::saveCommande($user->getEmail(),$commande1,true);
		$_SESSION['commande']['id'] = $commande1->getId();*/
		
		/* COMMANDE STATIC */
		commande_staticDB::saveCommande($cs,true);
		$_SESSION['commande']['id'] = $cs->getId();
		$_SESSION['order_id'] = $cs->getId();
		/* COMMANDE STATIC */
		
		$Suffixe = '_sc'; // Force paiement CB // Remise en ancien mode
		if ($cs->getEmail() == '<EMAIL>' ) {
			$Suffixe = '_sc';
		}
		require_once dirname(__FILE__).'/../paiement/request' . $Suffixe . '.php';
		
	} else {
	
		commande_staticDB::deleteCommande($_SESSION['commande']['id']);
		echo '<div class="error messageBox">';
		echo $_SESSION['commande']['id'];
  		echo 'Une erreur de chargement ou de retour en arri&egrave;re a &eacute;t&eacute; d&eacute;tect&eacute;e.<br />';
  		echo 'Merci de valider à nouveau votre panier : <a style="color:blue;" href="http://jeep-dodge-gmc.com/smi/panier" >cliquer ici !</a>';
  		echo '</div>';
	
	}
		
	
}
elseif(isset($_POST['mode']) && $_POST['mode'] == 'pp'){

	if(!isset($_SESSION['commande']['id'])){ // truc a la con pour empecher de passer 2 fois la même commande
		// si on a pas encore passé de commande
		/*commandeDB::saveCommande($user->getEmail(),$commande1,true);
		$_SESSION['commande']['id'] = $commande1->getId();*/
		
		/* COMMANDE STATIC */
		commande_staticDB::saveCommande($cs,true);
		$_SESSION['commande']['id'] = $cs->getId();
		/* COMMANDE STATIC */
	

	// Paiement sur le site paypal
	?>
<!-- --------------------------------------------------------------------------------------------------------------------- -->
<div id="smart-button-container">
      <div style="text-align: center;">
        <div id="paypal-button-container"></div>
      </div>
    </div>

<!--  <script src="https://www.paypal.com/sdk/js?client-id=contact_api1.jeep-dodge-gmc.com&enable-funding=venmo&currency=EUR" data-sdk-integration-source="button-factory"></script> 
-->

<script src="https://www.paypal.com/sdk/js?client-id=ATWdvZa7kZIW22ZF5Mw0q5xOnmvUSyGeMbCEPjhFSk7IcqXzyxOgJp_E6G5FdeUhO3MQNva9-hH3F26I&disable-funding=card&enable-funding=venmo&currency=EUR" data-sdk-integration-source="button-factory"></script>
 
  <script>
    function initPayPalButton() {
      paypal.Buttons({
        style: {
          shape: 'rect',
          color: 'gold',
          layout: 'vertical',
          label: 'paypal',
          
        },

        createOrder: function(data, actions) {
          return actions.order.create({
<?php

//            echo 'purchase_units: [{"description":"Commande ' .$cs->getId().'" ,"amount":{"currency_code":"EUR","value":'.number_format($totttc, 2, '.', '').',"breakdown":{"item_total":{"currency_code":"EUR","value":'.number_format($totht, 2, '.', '').'},"shipping":{"currency_code":"EUR","value":0},"tax_total":{"currency_code":"EUR","value":'.number_format($tva, 2, '.', '').'}}}}]';
$TTC = number_format($totttc, 2, '.', '');
$HT  = number_format($totttc, 2, '.', '');
$TVA = 0;
            echo 'purchase_units: [{"description":"Commande '.$cs->getId().'","amount":{"currency_code":"EUR","value":'.$TTC.',"breakdown":{"item_total":{"currency_code":"EUR","value":'.$HT.'},"shipping":{"currency_code":"EUR","value":0},"tax_total":{"currency_code":"EUR","value":'.$TVA.'}}}}]';
?>
          });
        },

        onApprove: function(data, actions) {
          return actions.order.capture().then(function(orderData) {
            
            // Full available details
            const transaction = orderData.purchase_units[0].payments.captures[0];
            // alert(`Transaction ${transaction.status}: ${transaction.id}`);

            // Show a success message within this page, e.g.
            const element = document.getElementById('paypal-button-container');
            element.innerHTML = '';

            document.cookie = "rgltpp=" + transaction.id + "; path=/";
            window.location.href="https://jeep-dodge-gmc.com/smi/paiement_pp_ok.php";
            
          });
        },

        onError: function(err) {
          console.log(err);
        }
      }).render('#paypal-button-container');
    }
    initPayPalButton();
  </script>

<!-- --------------------------------------------------------------------------------------------------------------------- -->
<?php
	} else {
	
		echo '<div class="error messageBox">';
  		echo 'Une erreur de chargement ou de retour en arri&egrave;re a &eacute;t&eacute; d&eacute;tect&eacute;e.<br />';
  		echo 'Merci de valider à nouveau votre paiement : <a style="color:blue;" href="http://jeep-dodge-gmc.com/smi/commande/recapitulatif" >cliquer ici !</a>';
  		echo '</div>';
	
	}
	
} else {
	if(!isset($_SESSION['commande']['id']))
	{		
		/* COMMANDE STATIC */
		commande_staticDB::saveCommande($cs,true);
		/* COMMANDE STATIC */
		
		// envoyé un mail au client a cette endroit
		require_once dirname(__FILE__).'/../mail_commande.php';
		//$_SESSION['commande']['id'] = $commande1->getId();
		$_SESSION['commande']['id'] = $cs->getId();
	}
	else{
		//$commande1->setId($_SESSION['commande']['id']);
		$cs->setId($_SESSION['commande']['id']);
	}
	include dirname(__FILE__).'/fin_commande.php';
}
}

} else {
	
	echo '<p class="error messageBox">Le Panier est vide !</p>';
	
}
?>