<div class="form-style-10">	
<div class="section"><span>¤</span>Récapitulatif de la commande</div>
<?php
// @todo ajouter la traduction
// affichage du récapitulatif de la commande en cours
require_once 'adresse_class.php';
require_once dirname(__FILE__).'/../../../class/af_class.php';

if ($user->getPanier()->getTotalHT() > 0){
//afficherMenuCommande(4);
$oui_pays_liv_ue = 1;
$oui_pays_fact_ue = 1;
// affichage des adresses livraison et de facturation, ou mettre retrait en magasin
$facturation = unserialize($_SESSION['commande']['facturation']);
$db = Zend_Registry::get('database');
$client_email = $user->getEmail();
// affiche le numero de tva intra com

$tva_intra_com = $db->query("SELECT * FROM clients WHERE emailclient = '$client_email'")->fetch();

if($_SESSION['commande']['surplace'] == true){
	// si retrait en magasin, definir le frais de port a 0
	$user->getPanier()->setFraisDePort(0);
	// affichage retrait des produits en magasin
	
	?>
	<table>
	<tr>
	<td>
	<div class="inner-wrap" style="width:340px;">
		<h1>Adresse de facturation</h1>

					<?php echo stripslashes($facturation->getRaisonSocial()); ?><br/>
					<?php echo stripslashes($facturation->getNomRue()); ?><br />
					<?php echo stripslashes($facturation->getCodePostal()),' ',stripslashes($facturation->getVille()); ?><br />
					<?php echo stripslashes($facturation->getPays()); ?><br />
	
	</div>
	</td><td>
	<div class="inner-wrap" style="width:340px; font-size:14px;">
		Vous avez choisi le mode de livraison <strong>"retrait en magasin"</strong><br />
		Les frais de port ne sont donc pas calculés !<br />
		Le retrait en magasin est valable seulement pour : <br />
		 - un retrait sur place <br />
		 - un reliquat d'une commande précédente <br />
		 - une livraison sur une bourse <br />
	</div>
	</td>
	</tr>
	</table>
	<?php
	
} else {
	$livraison = unserialize($_SESSION['commande']['livraison']);
	$reqe = "SELECT * FROM frais_port WHERE min <= ? AND max >= ?";
	$stmt = $db->prepare($reqe);
	$getTotalHT = $user->getPanier()->getTotalHT();
	$stmt->bindParam(1,$getTotalHT);
	$stmt->bindParam(2,$getTotalHT);
	$stmt->execute();
	$res = $stmt->fetch();
	$pays_liv = stripslashes(trim($livraison->getPays()));
	if ($pays_liv == 'France'){
	$user->getPanier()->setFraisDePort(($user->getPanier()->getTotalHT()*$res['taux']/100) + $res['fixe']);
	}else{
	$user->getPanier()->setFraisDePort(($user->getPanier()->getTotalHT()*$res['taux']/100) + $res['fixe'] + 12);
	
	$pays_ue = $db->query("SELECT * FROM pays WHERE nom_pays = '$pays_liv'")->fetch();
	$oui_pays_liv_ue = $pays_ue['union_europ'];
	
	$pays_ue1 = $db->query("SELECT * FROM pays WHERE nom_pays = '$pays_liv'")->fetch();
	$oui_pays_fact_ue = $pays_ue1['union_europ'];
	
	}
	
	$facturation = unserialize($_SESSION['commande']['facturation']);
	?>
	<table>
		<tr>
			<td>
	<div class="inner-wrap" style="width:340px;">
		<h1>Adresse de facturation</h1>
					<?php echo stripslashes($facturation->getRaisonSocial()); ?><br/>
					<?php echo stripslashes($facturation->getNomRue()); ?><br />
					<?php echo stripslashes($facturation->getCodePostal()),' ',stripslashes($facturation->getVille()); ?><br />
					<?php echo stripslashes($facturation->getPays()); ?><br />
	</div>				
			</td><td>
	<div class="inner-wrap" style="width:340px;">
		<h1>Adresse de Livraison</h1>
					<?php echo stripslashes($livraison->getRaisonSocial()); ?><br/>
					<?php echo stripslashes($livraison->getNomRue()); ?><br />
					<?php echo stripslashes($livraison->getCodePostal()),' ',stripslashes($livraison->getVille()); ?><br />
					<?php echo stripslashes($livraison->getPays()); ?><br />
		</div>				
			</td>
		</tr>
	</table>
	<?php
}	

// affichage de l'ensemble des produits du panier (sans que l'on puissent le modifier
$lignePs = $user->getPanier()->getLignePanier();?>
<div class="inner-wrap">
<table style="font-size:14px; width: 740px;" border="0" cellspacing="0" cellpadding="0">
	<tr>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000; padding-left:3px; padding-top:7px; padding-bottom:7px;"><?php echo $translate->_('Référence'); ?></th>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000;"><?php echo $translate->_('Genre'); ?></th>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000;text-align:center;"><?php echo $translate->_('Groupe'); ?></th>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000;"><?php echo $translate->_('Désignation'); ?></th>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000; width:65px;"><?php echo $translate->_('Prix HT'); ?></th>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000; width:65px;"><?php echo $translate->_('Quantité'); ?></th>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000; width:65px;"><?php echo $translate->_('Total HT'); ?></th>
		<th style="background-color:#313131; color:white;border-top: 2px solid #000000;border-bottom: 2px solid #000000;"></th>
	</tr>
	<?php 
	
	foreach($lignePs as $k => $ligneP):
			$p = $ligneP->getProduit();	?>
			<tr>
				<td style="padding-left:3px;padding-top:8px; padding-bottom:8px;background-color:white;border-bottom: 2px solid #000000;"><?php echo $p->getReference(); ?></td>
				<td style="font-size:11px;background-color:white;border-bottom: 2px solid #000000; text-align:center;"><?php echo $p->getGenreName(); ?></td>
				<?php
				
				// code promo
				
				$REMISE_20 = false;
				$REMISE_10 = false;
				$mois_dernier = date("Y-m-d", strtotime('+1 month'));
				$msg_promo = "";
				
				if (isset($_POST['code_promo'])){
				
				$code_promo = strtoupper($_POST['code_promo']);
				$code_promo2 = explode("_",$code_promo);
					
					// remise jeu concours
					if ($code_promo2[0] == "FP"){
					$test_code_promo_fp = $db->query("SELECT * FROM facture WHERE emailclient = '$client_email' ORDER BY date_facture DESC LIMIT 1")->fetch();
															
						if ($test_code_promo_fp['code_promo'] == $code_promo && $test_code_promo_fp['date_facture'] < $mois_dernier ){
						$REMISE_20 = true;
						$msg_promo = "Code valide!";
						}
						if ($test_code_promo_fp['code_promo'] != $code_promo){
						$REMISE_20 = false;
						$msg_promo = "<strong>Code promo Invalide!</strong> <br /><div style='margin-left:285px'> Merci de l'inscrire en commentaire avec votre numéro de facture.</div>";
						}
						if ($test_code_promo_fp['date_facture'] > $mois_dernier ){
						$REMISE_20 = false;
						$msg_promo = "Date code promo d&eacute;pass&eacute;e!";
						}
			
					}
					
					// remise FDP
					if ($code_promo2[0] == "JC"){
					$test_code_promo_jc = $db->query("SELECT * FROM jeu_concours_gagnant WHERE emailclient = '$client_email' ORDER BY date DESC LIMIT 1")->fetch();
					
						if ($test_code_promo_jc['code_promo'] == $code_promo && $test_code_promo_jc['date'] < $mois_dernier && $test_code_promo_jc['gagnant'] == "1"){
						$REMISE_10 = true;
						}
						if ($test_code_promo_jc['code_promo'] != $code_promo || $test_code_promo_jc['gagnant'] != "1"){
						$REMISE_10 = false;
						$msg_promo = "<strong>Code promo Invalide!</strong> <br /><div style='margin-left:285px'> Merci de l'inscrire en commentaire.</div>";
						}
						if ($test_code_promo_jc['date'] > $mois_dernier){
						$REMISE_10 = false;
						$msg_promo = "Date code promo d&eacute;pass&eacute;e!";
						}
						
					} else {
					
					$msg_promo = "<strong>Code promo Invalide!</strong> <br /><div style='margin-left:285px'> Merci de l'inscrire en commentaire avec votre numéro de facture.</div>";
					
					}
				}
				
				//QUALITE
					
					$reference = $p->getReference();
					$db = Zend_registry::get("database");
					$result_color = $db->prepare("
					SELECT * FROM produits WHERE referenceproduit = '$reference'
					") or die ("requete r1 invalid");
					$result_color->execute();
					$color = $result_color->fetch();
					$image_color = substr(strtolower($color['qualite']),0,3);
							
					if ($image_color == "new"){
					$style_border = "border: 1px solid #3399FF;";
					$qualite = "<strong style='color: #3399FF;'>NEUF</strong>";
					} elseif ($image_color == "nos"){
					$style_border = "border: 1px solid #FF6600;";
					$qualite = "<strong style='color: #FF6600;'>NOS</strong>";
					} elseif ($image_color == "rec"){
					$style_border = "border: 1px solid #00CC33;";
					$qualite = "<strong style='color: #00CC33;'>RENOVÉ</strong>";
					} elseif ($image_color == "use"){
					$style_border = "border: 1px solid #999900;";
					$qualite = "<strong style='color: #999900;'>OCCASION</strong>";
					} else {
					$style_border = "";
					$qualite = "";
					}
				
					////////////REMISE PRO/////////////////
					$remisepro = 0;
					if($user->isAuthenticated){
						$mailclient = $user->getEmail();
//						if ($p->getGenreName() == "JEEP" || $p->getGenreName() == "DODGE" || $p->getGenreName() == "GMC" || $p->getGenreName() == "RENAULT"){
							$remisepro = ProduitDB::getPrixPro(strtolower($p->getPromotion()), $image_color, $p->getPrixHT(), $mailclient);
//						}
					}
					$prixHT = $p->getPrixHT()-$remisepro;	
					$remisepropourcentage = ProduitDB::getRemisePro(strtolower($p->getPromotion()), $image_color, $mailclient);							
					///////////////////////////////////////
				
				?>
				<td style="font-size:11px;background-color:white;border-bottom: 2px solid #000000;"><?php echo $p->getGroupeName(); ?></td>
				<td style="padding-left:3px;background-color:white;border-bottom: 2px solid #000000;font-weight: bold;"><?php echo $p->getDesignation(); ?> <?php echo $qualite; ?>
				<?php
							if ($remisepro =! 0 && !empty($remisepro)){
							echo '<span style="color: #900;"> - '.$remisepropourcentage.'%<span>';
							}
				?>	
				</td>
				<td style="background-color:white;border-bottom: 2px solid #000000;" align="right"><?php printf("%.2f",$prixHT);?> €</td>						
				<td style="background-color:white;border-bottom: 2px solid #000000;" align="center"><?php echo $ligneP->getQte();?></td>
				<td style="background-color:white;border-bottom: 2px solid #000000; padding-right:3px;" align="right"><?php printf("%.2f", $prixHT*$ligneP->getQte());?> €</td>
			 </tr>
	<?php endforeach; ?>
	<tr>
		<td colspan="3" rowspan="5"></td>
<?php $user->getPanier()->recalculerPro($user->getEmail()); ?>
		<td align="right"><?php echo $translate->_('Montant total HT');?> :</td>
		<td></td>
		<td></td>
		<td align="right" style="padding-right:3px;padding-top:4px; padding-bottom:4px;"><?php printf('%.2f',$user->getPanier()->getTotalHT()); ?> €</td>
	</tr>
	<?php
		
		// Remise -10% total HT commande
		
		$remise_ht = 0;
		
		if ($REMISE_10 == true){
		$remise_ht = $user->getPanier()->getTotalHT()*0.1;
		?>
	<tr>
		<td align="right"><?php echo $translate->_('Remise HT -10%'); ?> : </td>
		<td></td>
		<td></td>
		<td align="right" style="padding-right:3px;padding-top:4px; padding-bottom:4px;">- <?php printf('%.2f',$remise_ht); ?> €</td>
	</tr>
		<?php
		}
		?>
	<tr>
		<td align="right"><?php echo $translate->_('Frais de port + emballage HT'); ?> : </td>

		<td></td>
		<td></td>
		<?php
		$FDP = $user->getPanier()->getFraisDePort();
		?>
		<td align="right" style="padding-right:3px;padding-top:4px; padding-bottom:4px;"><?php printf('%.2f',$FDP); ?> €</td>
	</tr>
		<?php
		
		// Remise -20% FDP
		
		$FDP2 = 0;
		if ($REMISE_20 == true){
		?>
	<tr>
		<td align="right"><?php echo $translate->_('Remise -20% FDP'); ?> : </td>
		<td></td>
		<td></td>
		<td align="right" style="padding-right:3px;padding-top:4px; padding-bottom:4px;">- <?php printf('%.2f',$FDP*0.2); ?> €</td>
	</tr>
		<?php
		$FDP3 = $FDP*0.2;
		$FDP2 = $FDP-($FDP3);
		$FDP = $FDP2;
		} else {
		
		$FDP3 = 0;
		
		}
		?>
	<tr>
	<?php
	
	$db3 = Zend_Registry::get('database');
	$row3 = $db3->query("SELECT * FROM parametres WHERE cle = 'tva'")->fetch();
// Affiche la TVA ou non en fonction du pays	
	if($oui_pays_liv_ue == 1 || $oui_pays_fact_ue == 1){
	
		if($tva_intra_com['tva_intra_com'] != ""){
		
		$tva = 0;
		$user->getPanier()->setTauxTVA(0);
		
		} else {
		
		$tva = $row3['valeur'];
		
		}
	
	} else {
	
	$tva = 0;
	$user->getPanier()->setTauxTVA(0);
	
	}
	
	?>
		<td align="right">TVA <?php printf('%.2f',$tva); ?>% : </td>
		<td></td>
		<td></td>
		<td align="right" style="padding-right:3px;padding-top:4px; padding-bottom:4px;"><?php printf('%.2f',($user->getPanier()->getTotalHT() + $FDP - $remise_ht)*($tva/100)); ?> €</td>
	</tr>
	<tr>
		<td style="padding-top:7px; padding-bottom:7px;border-top:2px solid black;border-bottom:2px solid black;" align="right"><b><?php echo $translate->_('Total de la commande TTC'); ?> : </b></td>
		<td style="border-top:2px solid black;border-bottom:2px solid black;"> </td>
		<td style="border-top:2px solid black;border-bottom:2px solid black;"></td>
		<td style="color:white;background-color:black;border-top:2px solid black;border-bottom:2px solid black;" align="center"><b><?php printf('%.2f',($user->getPanier()->getTotalHT() + $FDP - $remise_ht)*(1+($tva/100))); ?> €</b></td>
		<td style="background-color:black;border-top:2px solid black;border-bottom:2px solid black;"></td>
	</tr>
</table>
<?php 
// Affichage de la liste des moyens de paiement disponible (sous la forme d'une liste de bouton radio)
// ou marquer reglement en magasin si retrait en magasin
$db = Zend_Registry::get('database');
$row = $db->query("SELECT * FROM parametres WHERE cle = 'contre_remb'")->fetch();
/*
<form action="<?php echo $basePath; ?>commande/recapitulatif" method="post">
	<fieldset class="input">
		<legend><?php echo $translate->_('Ajouter un code promotionnel'),' :';?></legend>
		
		<input type="text" name="code_promo" value="" />
		<input type="submit" value="Ok" name="submit_promo" />
		<?php echo $msg_promo; ?>
		
	</fieldset>
</form>
*/
?>
<form action="<?php echo $basePath; ?>commande/paiement" method="post" name="paiement">
<?php
if (isset($_SESSION['iscommandeadmin']) && $_SESSION['iscommandeadmin'] == 1){
	if (isset($_SESSION['who']) && $_SESSION['who'] != ""){
?>	

<input type="hidden" name="iscommandeadmin" value="<?php echo $_SESSION['who']; ?>"/>

<?php
	}
}
?>
		<script language="JavaScript" src="<?php echo $basePath;?>public/js/formlimiterdecompter.js"></script>
	</div>	
	<div class="inner-wrap">
		<h1><?php echo $translate->_('Ajouter un commentaire'),' :';?></legend><div id="comment-status"></h1>
		
		<textarea rows="3" name="comment" maxlength="199"><?php 
		
		echo $tva_intra_com['tva_intra_com'];
		
		if(isset($_SESSION['commande']['commentaire'])){
		
		echo $_SESSION['commande']['commentaire'];		
		
        $db = Zend_Registry::get('database');
        $client_email = $user->getEmail();
        $commentaire = $_SESSION['commande']['commentaire'];
        $lines = explode("\n", $commentaire);
        $found_8_mai_2025 = false;

        // Vérifie d'abord si une entrée existe pour ce client
        $check_stmt = $db->prepare("SELECT COUNT(*) FROM sondage WHERE emailsondage = ? AND libellesondage LIKE '%8 mai 2025%'");
        $check_stmt->execute([$client_email]);
        $exists = $check_stmt->fetchColumn() > 0;

        foreach ($lines as $line) {
            if (strpos($line, '8 mai 2025') !== false) {
                $found_8_mai_2025 = true;
                if ($exists) {
                    // Met à jour l'entrée existante
                    $stmt = $db->prepare("UPDATE sondage SET cochesondage = 'Y' WHERE libellesondage = ? AND emailsondage = ?");
                    $stmt->execute([$line, $client_email]);
                } else {
                    // Crée une nouvelle entrée
                    $stmt = $db->prepare("INSERT INTO sondage (libellesondage, emailsondage, cochesondage) VALUES (?, ?, 'Y')");
                    $stmt->execute([$line, $client_email]);
                }
            }
        }

        if (!$found_8_mai_2025) {
            if ($exists) {
                // Met à jour l'entrée existante à 'N'
                $stmt = $db->prepare("UPDATE sondage SET cochesondage = 'N' WHERE libellesondage LIKE '%8 mai 2025%' AND emailsondage = ?");
                $stmt->execute([$client_email]);
            } else {
                // Crée une nouvelle entrée avec 'N'
                $stmt = $db->prepare("INSERT INTO sondage (libellesondage, emailsondage, cochesondage) VALUES ('8 mai 2025', ?, 'N')");
                $stmt->execute([$client_email]);
            }
        }
		}
		
		?></textarea>
		<label>limité à 200 lettres</label>
		
		<script type="text/javascript">
		fieldlimiter.setup({
		//Récupération des données du champ
		thefield: document.paiement.comment,
		//On limite le champ à 10 caratères
		maxlength: 199,
		//id pour le retour des informations
		statusids: ["george-status"],
		//Lorsque l'on appuie sur une touche
		//on vérifie si le texte n'est pas trop long
		onkeypress:function(maxlength, curlength){
		if (curlength<maxlength)
		//Bordure du champ en gris si le nombre n'est pas dépasser
		this.style.border="2px solid gray"
		else
		//Bordure du champ en rouge si le nombre est dépasser
		this.style.border="2px solid red"
		}
		})
		</script>
		
	</fieldset>
	</div>	
	<script type="text/javascript">
	function changeregl(regl) {
		var spancbc = document.getElementById('spancbc');
		if (spancbc != null ) {
			document.getElementById('spancbc').style.textShadow  = "none";
			document.getElementById('spancbc').style.color  = "#004D73";
			document.getElementById('tdcbc').style.backgroundColor  = "#D4D4D4";
		}
		var spanch = document.getElementById('spanch');
		if (spanch != null ) {
			document.getElementById('spanch').style.textShadow  = "none";
			document.getElementById('spanch').style.color  = "#004D73";
			document.getElementById('tdch').style.backgroundColor  = "#D4D4D4";
		}
		var spanmc = document.getElementById('spanmc');
		if (spanmc != null ) {
			document.getElementById('spanmc').style.textShadow  = "none";
			document.getElementById('spanmc').style.color  = "#004D73";
			document.getElementById('tdmc').style.backgroundColor  = "#D4D4D4";
		}

		document.getElementById('tdcb').style.backgroundColor  = "#D4D4D4";
		document.getElementById('tdpp').style.backgroundColor  = "#D4D4D4";
		document.getElementById('tdvi').style.backgroundColor  = "#D4D4D4";
		
		document.getElementById('spancb').style.color  = "#004D73";
		document.getElementById('spanpp').style.color  = "#004D73";
		document.getElementById('spanvi').style.color  = "#004D73";
		
		document.getElementById('spancb').style.textShadow  = "none";
		document.getElementById('spanpp').style.textShadow  = "none";
		document.getElementById('spanvi').style.textShadow  = "none";


		document.getElementById('modpay').value = regl;
		document.getElementById('td'+regl).style.backgroundColor  = "#004D73";
		document.getElementById('span'+regl).style.color  = "white";

	}
	</script>
	<input style="display:none;" type="text" value="123" id="modpay" name="mode" />	
		<div class="inner-wrap">
		<h1><?php echo $translate->_('Choix du mode de paiement'),' :';?></h1>
				<input type="hidden" name="remise_jc" value="<?php echo $remise_ht; ?>" />
		<table class="table-paiement">
		
			<?php 
			if($_SESSION['commande']['surplace'] == true){ 
			?>
				<tr>
					<td id="tdcb" onclick="changeregl('cb');"><img src="<?php echo $basePath; ?>public/images/cb.png" />
					<span id="spancb">Carte de credit sur Internet</span>
					<div class="label_paiement">Carte VISA, MASTER CARD, CB accépté - Paiement sécurisé - Le plus rapide</div></td>
				</tr>
				<tr>
					<td id="tdch" onclick="changeregl('ch');"><img src="<?php echo $basePath; ?>public/images/ch.png" />
					<span id="spanch">Cheque</span>
					<div class="label_paiement">Réservé pour la France - Délai long </div></td>
				</tr>
				<tr>
					<td id="tdcbc" onclick="changeregl('cbc');"><img src="<?php echo $basePath; ?>public/images/cbc.png" />
					<span id="spancbc">Carte de credit au Comptoir</span>
					<div class="label_paiement">Réservé pour un paiement par carte de crédit sur place </div></td>
				</tr>
				<tr>        
					<td id="tdmc" onclick="changeregl('mc');"><img src="<?php echo $basePath; ?>public/images/es.png" />
					<span id="spanmc">Espece</span>
					<div class="label_paiement">Réservé pour paiement en espèce sur place </div></td>
				</tr>
				<tr>        
					<td id="tdpp" onclick="changeregl('pp');"><img src="<?php echo $basePath; ?>public/images/pp.png" />
					<span  id="spanpp">Paypal</span>
					<div class="label_paiement">Nécessite un compte PayPal - Paiement sécurisé </div></td>
				</tr>
				<tr>        
					<td id="tdvi" onclick="changeregl('vi');"><img src="<?php echo $basePath; ?>public/images/vi.png" />
					<span id="spanvi">Virement bancaire</span>
					<div class="label_paiement">Transfert d'argent de banque à banque - Délai long</div></td>
				</tr>
			<?php
			
			} else {

			?>
				<input type="hidden" name="remise_fdp" value="<?php echo $FDP3; ?>" />
					<tr>
						<td id="tdcb" onclick="changeregl('cb');"><img src="<?php echo $basePath; ?>public/images/cb.png" />
						<span id="spancb">Carte de credit</span>
						<div class="label_paiement">Carte VISA, MASTER CARD, CB accépté - Paiement sécurisé - Le plus rapide</div></td>
					</tr>
					<tr>        
						<td id="tdpp" onclick="changeregl('pp');"><img src="<?php echo $basePath; ?>public/images/pp.png" />
						<span  id="spanpp">Paypal</span>
						<div class="label_paiement">Nécessite un compte PayPal - Paiement sécurisé </div></td>
					</tr>
					<tr>        
						<td id="tdvi" onclick="changeregl('vi');"><img src="<?php echo $basePath; ?>public/images/vi.png" />
						<span id="spanvi">Virement bancaire</span>
						<div class="label_paiement">Transfert d'argent de banque à banque - Délai long</div></td>
					</tr>
			<?php
			if (stripos($livraison->getPays(),"france") !== false) {
			?>
				<tr>
					<td id="tdch" onclick="changeregl('ch');"><img src="<?php echo $basePath; ?>public/images/ch.png" />
					<span id="spanch">Cheque</span>
					<div class="label_paiement">Réservé pour la France - Délai long </div></td>
				</tr>
				<tr>        
					<td id="tdmc" onclick="changeregl('mc');"><img src="<?php echo $basePath; ?>public/images/es.png" />
					<span id="spanmc">Mandat Cash</span>
					<div class="label_paiement">Réglement en espèce à La Poste - Réservé pour la France - Délai long</div></td>
				</tr>
				
<?php 
					} 
			} 
?>
	</table>		
	</div>
	<div class="button-section">

		<input type="submit" onclick="document.forms.paiement.submit();" class="btn_submit" style="width:200px;" value="<?php echo $translate->_('Terminer la commande');?>" name="paiement" />

	</div>
</form>

</div>
<?php

} else {
	
	echo '<p class="error messageBox">Le Panier est vide !</p>';
	
}

?>




