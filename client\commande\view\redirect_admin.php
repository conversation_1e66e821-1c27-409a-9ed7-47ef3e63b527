<?php
	require_once dirname(__FILE__).'/../../../init.php';
	require_once dirname(__FILE__).'/../../../class/internaute_class.php';
	require_once dirname(__FILE__).'/../../../class/client_class.php';
	
	
	// on ajoute l'email dans la commande
if (isset($_SESSION['iscommandeadmin']) && $_SESSION['iscommandeadmin'] == 1) {	
	$user = null;
	$user = unserialize($_SESSION['user1']);
	$user_temp = clone $user;
	$user = new client($_GET['email']);
	$user->setPanier($user_temp->getPanier());
	$_SESSION['user1'] = serialize($user);
	header('Location: '.$basePath.'index.php?page=admin&action=facture&type=edit&idcommande='.$_GET['cde'].'');
} else {

echo "Pirate !!!!!!!!";

}
	?>