<div class="center"><h1><img src="<?php echo $basePath; ?>public/images/moncompte.gif" alt="<?php echo $translate->_('Inscription'); ?>"/><span><?php echo $translate->_('Inscription'); ?></span></h1></div>
<?php
	require_once dirname(__FILE__).'/../class/af_class.php';

	$afficheform=true;
	
if(isset($_POST['nom'])) // on se trouve sur la page de traitement
{

		function stripAccents($str, $charset='utf-8')
		{
			$str = htmlentities($str, ENT_NOQUOTES, $charset);
			
			$str = preg_replace('#&([A-za-z])(?:acute|cedil|circ|grave|orn|ring|slash|th|tilde|uml);#', '\1', $str);
			$str = preg_replace('#&([A-za-z]{2})(?:lig);#', '\1', $str); // pour les ligatures e.g. '&oelig;'
			$str = preg_replace('#&[^;]+;#', '', $str); // supprime les autres caractères
			
			return $str;
		}

		function delSpace($str2)
		{

			$str2 = preg_replace('#&[^;]+;-_.#', '', $str2); // supprime les caractères speciaux
			$str2 = preg_replace('# #', '', $str2); // supprime les espaces
			
			return $str2;
		}
	// Déclaration des variables :
		// Formulaire
	$nom=strtoupper(stripAccents($_POST['nom']));
	$prenom=strtolower(stripAccents($_POST['prenom']));
	$adresse=strtolower($_POST['email']);
	$mdp=$_POST['motdepasse'];
	$mdpc=$_POST['motdepasseconfirme'];
	$tel=delSpace($_POST['tel']);
	$fax=delSpace($_POST['fax']);

	$raisonsocial = stripAccents($_POST['raisonsocial']);
	$nomrue=strtolower(stripAccents($_POST['nomrue']));
	$cp=delSpace($_POST['cp']);
	$ville=strtoupper(stripAccents($_POST['ville']));
	$pays=$_POST['pays'];
	
		// Traitement
	$toutok = true;
	$error = "";
	/*--------------------------------------------------------*/
	/*------- TEST DES VALEURS --------------------*/
	/*-------------------------------------------------------*/
	// On test si le champ nom est bien remplis
	if (empty($_POST['nom']))
	{
		$toutok = false;
		$error = $error.$translate->_('Erreur : Il faut impérativement remplir le Nom').'.<br/>';
	}
	// On test si le champ prénom est bien remplis
	if (empty($_POST['prenom']))
	{
		$toutok = false;
		$error = $error.$translate->_('Erreur : Il faut impérativement remplir le Prénom').'.<br/>';
	}
	// On test si le champ de l'adresse mail est bien remplis
	if (empty($_POST['email']))
	{
		$toutok = false;
		$error = $error.$translate->_('Erreur : Il faut impérativement remplir l\'adresse mail').'.<br/>';
	}
	else
	{
		// On test si l'adresse mail est bien valide
		if(!verifierAdresseMail($adresse))
		{
			$toutok = false;
			$error = $error.$translate->_('Erreur : L\'adresse mail n\'est pas valide').'.<br/>';
		}
	}
	// On test si le mot de passe est tremplis
	if (empty($_POST['motdepasse']))
	{
		$toutok = false;
		$error = $error.$translate->_('Erreur : Il faut définir un mot de passe').'.<br/>';
	}
	else
	{
		$nbrcaract = strlen($_POST['motdepasse']);
		$nbrmincaract = 5;
		// On test si le mot de passe est assez long
		if ($nbrcaract < $nbrmincaract)
		{
			$toutok = false;
			$error = $error.$translate->_('Erreur : Il faut un mot de passe de 6 caractères minimum').'.<br/>';
		}
		else
		{
			// On test si le mot de passe de confirmation est bien remplis
			if (empty($_POST['motdepasseconfirme']))
			{
			$toutok = false;
			$error = $error.$translate->_('Erreur : Il faut répéter le mot de passe pour éviter les fautes de frappes').'.<br/>';
			}
			else
			{
				// On test si les deux mot de passe sont égaux
				if ($_POST['motdepasse'] != $_POST['motdepasseconfirme'])
				{
					$toutok = false;
					$error = $error.$translate->_('Erreur : Les mots de passe sont différent').'.<br/>';
				}
			}
		}
	}
	// On test si le numéro de téléphone est remplis
	if (empty($_POST['tel']))
	{
		$tel = "01";
	}

	// On test si le numéro de Fax est correct s'il est remplis 
	if (!empty($_POST['fax']))
	{
		/*if(!preg_match('`[0-9]{10}`',$fax))
		{
			$toutok = false;
			$error = $error.$translate->_('Erreur : Le numéro de fax est incorrect').'.<br/>';
		}*/
	}
	if (empty($_POST['raisonsocial']))
	{
		$toutok = false;
		$error = $error.$translate->_('Erreur : Il faut impérativement remplir la raison sociale de l\'adresse de facturation').'.<br/>';
	}
	if (empty($_POST['nomrue']))
	{
		$toutok = false;
		$error = $error.$translate->_('Erreur : Il faut impérativement remplir le nom de rue de l\'adresse de facturation').'.<br/>';
	}
	if (empty($_POST['cp']))
	{
		$toutok = false;
		$error = $error.$translate->_('Erreur : Il faut impérativement remplir le code postal de l\'adresse de facturation').'.<br/>';
	}
	if (empty($_POST['ville']))
	{
		$toutok = false;
		$error = $error.$translate->_('Erreur : Il faut impérativement remplir la ville de l\'adresse de facturation').'.<br/>';
	}
	if (empty($_POST['pays']))
	{
		$toutok = false;
		$error = $error.$translate->_('Erreur : Il faut impérativement remplir le pays de l\'adresse de facturation').'.<br/>';
	}
	/*------------ FIN DES TESTS ---------------------*/
	/*--------------------------------------------------------*/
	if ($toutok)
	{
		// Tout est bon : On affiche les résultats et on enregistre les données dans la BDD
		$afficheform=false;
		?>
		<div class="center"><h2><?php echo $translate->_('Récapitulatif des données');?></h2></div>
		<div class="info messageBox">
			<p><?php echo $translate->_('Votre nom est'); ?> <?php echo $nom; ?><br/>
			   <?php echo $translate->_('Votre prénom est'); ?> <?php echo $prenom; ?><br/><br/>
			   <?php printf($translate->_('Votre adresse : %s est valide'),$adresse); ?><br/><br/>
			   <?php echo $translate->_('Votre téléphone'); ?> : <?php echo $tel;?><br/>
			   <?php echo $translate->_('Votre fax'); ?> : <?php echo $fax;?><br/><br />
			   <b>Adresse de facturation :</b><br />
			   <?php echo $raisonsocial.'<br />'.$nomrue.'<br />'.$cp.' '.$ville.'<br />'.$pays.'<br />'; ?>
		</p></div>
		<br/><br/>
		<div class="center"><h2><?php echo $translate->_('Résultat d\'inscription'); ?></h2></div>
	<?php
		// Enregistrement dans la BDD avec test si la système à réussit à se connecter et à enregistrer les données
		if(clientDB::getClientByEmail($adresse)) {
			echo '<div class="error messageBox"><p>'.$translate->_('Vous possedez déja un compte client.<br/>Si vous ne vous souvenez pas de votre mot de passe, merci de cliquer sur le lien mot de passe oublié ci-dessus').'</p></div>';
		}
		else {
			$client = new client();
			$client->setEmail($adresse);
			$client->setNom($nom, $prenom);
			$client->setPassword(md5($mdp));
			$client->setTel($tel);
			$client->setFax($fax);
			$date = date("Y-m-d");
			$client->setDatecreate($date);
			$client->setVehiculeprefer("");
			$clientDB = new clientDB();
			// On vérifie que tout c'est bien passé dans l'enregistrement
			if ($clientDB->saveNew($client))
			{
				$af = new af();
				$af->setEmail($client->getEmail());
				$af->setRaisonSocial($raisonsocial);
				$af->setNomRue($nomrue);
				$af->setCodePostal($cp);
				$af->setVille($ville);
				$af->setPays($pays);
				$afDB = new afDB();
				if ($afDB->save($af))
				{
					echo '<div class="valide messageBox"><p>'.$translate->_('Vous êtes inscrit en tant que client avec succès.<br/><br/>Vous allez bientôt reçevoir un mail de confirmation d\'inscription <br /> <b>Attention ! Si vous n\'avez reçu aucun mail merci de vérifier vos courriers indésirables</b>').'.</p>';
				
					// Envoie d'un mail pour avertir le client qu'il est bien inscrit (permet de vérifier aussi que l'email est bien correct
					require_once dirname(__FILE__).'/mail_devenir_client.php';
				} else
				{
					// En cas de problême on affiche un msg d'erreur
					echo '<div class="error messageBox"><p>',$translate->_('Un problême est survenu lors de la création de votre compte, réésayez plus tard'),' .</p>';
				}
			}
			else
			{
				// En cas de problême on affiche un msg d'erreur
				echo '<div class="error messageBox"><p>',$translate->_('Un problême est survenu lors de la création de votre compte, réésayez plus tard'),' .</p>';
			}
			echo '</div>';
		}
		//Fermeture de la connection
		$dbh = null;											
	}
	else
	{
		echo '<div class="error messageBox"><p>'.$error.'</p></div>';
	}
}


if ($afficheform)
{	// Création d'un formulaire utilisant la  méthode POST
	?>
	
	<script>
	function copie()
	{

	document.forms['form1'].elements['f4'].value = document.forms['form1'].elements['f1'].value.toUpperCase() + " " + document.forms['form1'].elements['f2'].value.toLowerCase();

	}
	
	function disabled_prenom()
	{

	document.getElementById('f2').disabled = true;

	}
	
	function test_email(str)
	{
		var xmlhttp = null;
		if (str=="")
		  {
		  document.getElementById("txtHint").innerHTML="";
		  return;
		  }
		if (window.XMLHttpRequest)
		  {// code for IE7+, Firefox, Chrome, Opera, Safari
		  xmlhttp=new XMLHttpRequest();
		  }
		else
		  {// code for IE6, IE5
		  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
		  }
		xmlhttp.onreadystatechange=function()
		  {
		  if (xmlhttp.readyState==4 && xmlhttp.status==200)
			{
			document.getElementById("txtHint").innerHTML=xmlhttp.responseText;
			}
		  }
		xmlhttp.open("GET","<?php echo $basePath; ?>client/test_email.php?mail="+str,true);
		xmlhttp.send();
	} 
		function choix_pays(pays)
	{
		if (pays=="France"){
		
			document.getElementById("ville").style.visibility= 'visible';
			document.getElementById("ville_inter").style.visibility= 'hidden';
			var cp = document.getElementById("f6").value;
			var xmlhttp = null;
			if (cp=="")
			  {
			  document.getElementById("view_ville").innerHTML="";
			  return;
			  }
			if (window.XMLHttpRequest)
			  {// code for IE7+, Firefox, Chrome, Opera, Safari
			  xmlhttp=new XMLHttpRequest();
			  }
			else
			  {// code for IE6, IE5
			  xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");
			  }
			xmlhttp.onreadystatechange=function()
			  {
			  if (xmlhttp.readyState==4 && xmlhttp.status==200)
				{
				document.getElementById("view_ville").innerHTML=xmlhttp.responseText;
				}
			  }
			xmlhttp.open("GET","<?php echo $basePath; ?>client/choix_ville.php?cp="+cp,true);
			xmlhttp.send();
			
		}
		else
		{
			document.getElementById("ville").style.visibility= 'hidden';
			document.getElementById("ville_inter").style.visibility= 'visible';
		}
	} 
	</script>
	<div class="form">
	<form name="form1" action="<?php echo $basePath; ?>utilisateur/s_inscrire" method="POST">
		<fieldset class="input">
		<ol>
			<li>
				<label for="nom"><?php echo $translate->_('Nom'); ?> :</label>
				<input id="f1" type="text" class="indispensable" name="nom" size=30 maxlength=60 value="<?php echo @$_POST['nom']; ?>" onchange="disabled_prenom()"/><b class="reditalique">* </b>
				<script type="text/javascript">
						var f1 = new LiveValidation('f1');
						f1.add( Validate.Presence );
		        </script>
			</li>
			<li>
				<label for="prenom"><?php echo $translate->_('Prénom'); ?> :</label>
				<input id="f2" type="text" class="indispensable" name="prenom" size=30 maxlength=60 value="<?php echo @$_POST['prenom']; ?>" onchange="copie()"  disabled /><b class="reditalique">* </b>
				<script type="text/javascript">
						var f2 = new LiveValidation('f2');
						f2.add( Validate.Presence );
		        </script>
			</li>
			<li>
				<label for="email"><?php echo $translate->_('Email'); ?> :</label>
				<input id="f20" type="text" class="indispensable" onchange="test_email(this.value)" name="email" size=30 maxlength=100 value="<?php echo @$_POST['email']; ?>"/><b class="reditalique">* </b>
				<script type="text/javascript">
						var f20 = new LiveValidation('f20');
						f20.add( Validate.Email );
						f20.add( Validate.Presence );
				</script>
			</li>
			<span id="txtHint"></span>
			<li>
				<label for="password"><?php echo $translate->_('Mot de passe (Minimum 6 caractères)'); ?> :</label>
				<input id="myPasswordField" type="password" class="indispensable" name="motdepasse" size=20 maxlength=20 value=""/><b class="reditalique">* </b>
				<script type="text/javascript">
						var myPasswordField = new LiveValidation('myPasswordField');
						myPasswordField.add( Validate.Length, { minimum: 6 } );
		        </script>
				
			</li>
			<li>
				<label for="password2"><?php echo $translate->_('Retapez le mot de passe'); ?> :</label>
				<input id="f19" type="password" class="indispensable" name="motdepasseconfirme" size=20 maxlength=20 value=""/><b class="reditalique">* </b>
				<script type="text/javascript">
						var f19 = new LiveValidation('f19');
						f19.add(Validate.Confirmation, { match: 'myPasswordField'} );
		        </script>
			</li>
			<li>
				<label for="tel"><?php echo $translate->_('Téléphone'); ?> (ex: 0476644356):</label>
				<input id="f3" type="text" class="indispensable" name="tel" size=20 maxlength=20 value="<?php echo @$_POST['tel']; ?>"/><b class="reditalique">* </b>
				<script type="text/javascript">
						var f3 = new LiveValidation('f3');
						f3.add( Validate.Presence );
						f3.add( Validate.Numericality );
		        </script>
			</li>
			<li>
				<label for="fax"><?php echo $translate->_('Fax ou fixe'); ?> :</label>
				<input type="text" name="fax" size=20 maxlength=20 value="<?php echo @$_POST['fax']; ?>"/>
			</li>
		</ol>
		<center><b>Adresse de facturation</b></center>
		Vous devez spécifier une <b>adresse de facturation</b>.<br />Cette adresse de facturation est fixe, pour la modifier il faudra contacter l'équipe du site par mail ou par téléphone.<br />
		Vous pourrez par la suite spécifier une ou plusieurs adresses de livraison.<br />
		<ol>
			<li>
				<label for="raisonsociale"><?php echo $translate->_('Raison sociale').' (ou Nom et Prénom) :';?></label>
				<input id="f4" type="text" name="raisonsocial" value="" class="indispensable"/><b class="reditalique">* </b>
				<script type="text/javascript">
						var f4 = new LiveValidation('f4');
						f4.add( Validate.Presence );
		        </script>
			</li>
			<li>
				<label for="adresse"><?php echo $translate->_('Adresse').' :';?></label>
				<input id="f5" type="text" name="nomrue" value="" class="indispensable"/><b class="reditalique">* </b>
								<script type="text/javascript">
						var f5 = new LiveValidation('f5');
						f5.add( Validate.Presence );
		            </script>
			</li>

			<li id="cp" >
				<label for="cp"><?php echo $translate->_('Code Postal').' :';?></label>
				<input id="f6" type="text" name="cp" value="" onchange="choix_pays(document.getElementById('pays').options[document.getElementById('pays').selectedIndex].value)" class="indispensable"/><b class="reditalique">* </b>
				<script type="text/javascript">
						var f6 = new LiveValidation('f6');
						f6.add( Validate.Presence );
		        </script>
			</li>
			<li>
				<label for="pays"><?php echo $translate->_('Pays').' :';?></label>
				<select id="pays" name="pays" onchange="choix_pays(this.options[this.selectedIndex].value)" onclick="choix_pays(this.options[this.selectedIndex].value)">
					<?php
				$result = $database->prepare("SELECT * FROM pays order by nom_pays ASC") or die ("requete r1 invalid");
				$result->execute();

				echo '<option value="" selected="selected">Selectionner</option>';

				while ($tab = $result->fetch()) {
				
					echo '<option style="width: 225px;" value="'.$tab['nom_pays'].'">'.$tab['nom_pays'].'</option>';
				
					}
					?>
				</select><b class="reditalique">* </b>
			</li>
			<li id="ville_inter" style="visibility: hidden;">
				<label for="ville"><?php echo $translate->_('Ville').' :';?></label>
				<input id="f7" type="text" name="ville" value="" class="indispensable"/><b class="reditalique">* </b>
			</li>
			<li id="ville" style="visibility: hidden;">
				<label for="ville"><?php echo $translate->_('Ville').' :';?></label>
				<span name="ville" id="view_ville"></span>
			</li>
			<li>
				<label class="reditalique"><?php echo '* ',$translate->_('Champs obligatoires'); ?></label>
			</li>
		</ol>
		</fieldset>
		<fieldset class="submit">
		<ol>
			<li>
				<input type="reset" value="<?php echo $translate->_('Effacer'); ?>" class="btn_submit">
				<input type="submit" name="envoyer" value="<?php echo $translate->_('S\'inscrire'); ?>" class="btn_submit">
			</li>
		</ol>
		</fieldset>
	</form>
	</div>

<?php } ?>
<p class="bouton_retour">
<a href="<?php echo $basePath; ?>"><?php echo $translate->_('Retour');?></a>
</p>