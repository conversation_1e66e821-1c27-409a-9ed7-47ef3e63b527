<?php
	// affichage du detail d'une commande, seulement si c'est commande appartient a l'utilisateur connecté ou si l'utilisateur est admin
	require_once dirname(__FILE__).'/../../class/commande_static_class.php';
	$commande = commande_staticDB::getCommandeById($_GET['id']);
	if($user->isAdmin || $user->getEmail() == $commande->getEmail()){
	


					
		//Titre de la commande			
		echo '<h1>',$translate->_('Détail de la commande'),' (',$translate->_('référence'),' : ',$commande->getId(),')';
		
			if($user->isAdmin){
			// Affiche le numero de facture		
					$idcde = $commande->getId();
					$result = $database->prepare("SELECT count('$idcde') AS cal_idcde FROM facture WHERE id_command = '$idcde'") or die ("requete r1 invalid");
					$result->execute();
					
							while ($tab = $result->fetch()) {
						if ($tab['cal_idcde'] >= "1"){
						
							$result2 = $database->prepare("SELECT * FROM facture WHERE id_command = '$idcde' order by id_facture desc limit 1") or die ("requete r2 invalid");
							$result2->execute();
							
							while ($tab2 = $result2->fetch()) {
								echo ' - Facturée, '.$tab2['id_facture'].'';
								echo "<a href='",$basePath,"admin/facture/modifier/imprim_fact.php?id_facture=".$tab2['id_facture']."' target='_blank'>(Voir <img src='",$basePath,"public/images/zoom.png'/> )</a>";
							}
						} else {
						
						echo "<a style='margin-left:50px;' href='".$basePath."index.php?page=admin&action=facture&type=edit&idcommande=".$idcde."'> <img src='".$basePath."public/images/facture.png'/> Editer la facture</a>";
						
						}
					
					}
					
			}
		echo'</h1>';
		echo "<a style='margin-left:8px; padding:5px; border:1px solid #999999;' href='",$basePath,"client/commande/impression_commande.php?id=",$commande->getId(),"' target='_blank'><img src='",$basePath,"public/images/gtk-print.png'/><b> Imprimer le bon de commande</b></a><br />";
		// affichage du detail de la commande
		// affichage des adresses de livraison et de facturation
		if($commande->getRetraitmagasin()){
			// si la commande est retiré en magasin
			echo '<p class="messageBox info">',$translate->_('Retrait de la commande en magasin'),'</p>';
			?>
			<p style='text-align:center; margin-left:92px; width:735px; margin-top:20px; padding:5px; border:1px solid #999999;'>
			<?php
			if($user->isAdmin){
			?>
			<strong>Signataire : </strong><?php echo $commande->getIscommandeadmin(); ?>&nbsp;
			<a href="<?php echo $basePath; ?>index.php?page=admin&action=gererclient&type=modif&id=<?php echo $commande->getEmail(); ?>"><strong>Raison sociale : </strong><?php echo $commande->getNomprenom(); ?>&nbsp;&nbsp;<img src='<?php echo $basePath; ?>public/images/zoom.png'/></a>&nbsp;&nbsp;&nbsp;
			<a href="mailto:<?php echo $commande->getEmail(); ?>">E-mail : <?php echo $commande->getEmail(); ?></a>
			<?php
			} else {
			?>
			<strong>Raison sociale : </strong><?php echo $commande->getNomprenom(); ?>&nbsp;&nbsp;&nbsp;
			<?php
			}
			?>
			<table class="table_center">
				<tr>
					<td>
						<fieldset class="adresse_recap">
							<legend><?php echo $translate->_('Adresse de facturation');?> :</legend>
							<?php echo stripslashes($commande->getAdressefact()); ?>
						</fieldset>
					</td>
				</tr>
			</table>
			<?php
		} else {
			// sinon, on affiche les 2 adresses
			?>
			<p style='text-align:center; margin-left:92px; width:735px; margin-top:20px; padding:5px; border:1px solid #999999;'>
			<?php
			if($user->isAdmin){
			?>
			<strong>Signataire : </strong><?php echo $commande->getIscommandeadmin(); ?>&nbsp;
			<a href="<?php echo $basePath; ?>index.php?page=admin&action=gererclient&type=modif&id=<?php echo $commande->getEmail(); ?>"><strong>Raison sociale : </strong><?php echo $commande->getNomprenom(); ?>&nbsp;&nbsp;<img src='<?php echo $basePath; ?>public/images/zoom.png'/></a>&nbsp;&nbsp;&nbsp;
			<a href="mailto:<?php echo $commande->getEmail(); ?>"><strong>E-mail : </strong><span style="color: #00C; text-decoration: underline;"><?php echo $commande->getEmail(); ?></span></a>
			<?php
			} else {
			?>
			<strong>Raison sociale : </strong><?php echo $commande->getNomprenom(); ?>&nbsp;&nbsp;&nbsp;
			<?php
			}
			?>
			<strong>Tél : </strong><?php echo $commande->getTel(); ?>&nbsp;&nbsp;&nbsp;<br />
			<strong>Date : </strong><?php echo date('d/m/Y',strtotime($commande->getDate())); ?>&nbsp;&nbsp;&nbsp;
			<strong>Paiement : </strong><?php echo $commande->getModepaiement(); ?>
			</p>
			<?php
							/////////////// AFFICHE LA ZONE SUIVI COLIS ////////////////////
				
				if ($commande->getStatut() == "Envoyée"){
				
					$id = $_GET['id'];
					$result6 = $database->prepare("SELECT COUNT(*) AS nb_fact FROM facture WHERE id_command = '$id'") or die ("requete r6 invalid");
					$result6->execute();
					$nb_fact = $result6->fetch();
					
					if ($nb_fact['nb_fact'] != 0){
						
						$result5 = $database->prepare("SELECT * FROM facture WHERE id_command = '$id'") or die ("requete r5 invalid");
						$result5->execute();
						
						while ($tab5 = $result5->fetch()) {
							$info_sup = explode(";", $tab5['info_sup']);
							$post626 = substr($info_sup[0], 0, 4);
							
							if ($info_sup[1] != "" && $post626 == "626P"){
			?>
			<p class="messageBox info">
						<br />Suivi colis numéro : <strong><?php echo $info_sup[1]; ?> </strong><a href="#" style="text-decoration: underline;" onclick="document.getElementById('suivi').submit();">(Cliquer ici !)</a>
				
					<form id="suivi" action="http://www.colissimo.fr/portail_colissimo/suivre.do?language=fr_FR" method="post" name="numero_de_colis" id="num_colis" target="_blank" >

						<input type="hidden" class="numero_colis" id="parcelnumber" name="parcelnumber" value="<?php echo $info_sup[1]; ?>"/>

					</form>
			</p>
			<?php
							}
						}
					}
				}
			?>
			
			<table class="table_center">
				<tr>
					<td>
						<fieldset class="adresse_recap">
							<legend><?php echo $translate->_('Adresse de livraison');?> :</legend>
							<?php echo stripslashes($commande->getAdresselivr()); ?>
						</fieldset>
					</td>
					<td>
						<fieldset class="adresse_recap">
							<legend><?php echo $translate->_('Adresse de facturation');?> :</legend>
							<?php echo stripslashes($commande->getAdressefact()); ?>
						</fieldset>
					</td>
				</tr>
			</table>
			<?php
		}
	
// affichage du detail de la commande
	$lignePs = $commande->getPanier();
// @todo afficher le detail de la commande
?>
		<table class="liste" style="border-bottom:none; width:80%">
			<tr>
				<th><?php echo $translate->_('Référence'); ?></th>
				<th><?php echo $translate->_('Genre'); ?></th>
				<th><?php echo $translate->_('Groupe'); ?></th>
				<th><?php echo $translate->_('Désignation'); ?></th>
				<th><?php echo $translate->_('Prix HT'); ?></th>
				<th><?php echo $translate->_('Quantité'); ?></th>
				<th><?php echo $translate->_('Somme HT'); ?></th>
				<?php
					if($user->isAdmin){
					?>
				<th><?php echo $translate->_('Image'); ?></th>
					<?php
					}
					if($user->isAdmin){
					?>
				<th><?php echo $translate->_('ND'); ?></th>
					<?php
					}
				?>
				<th></th>
			</tr>
			
			<script language="JavaScript" src="<?php echo $basePath;?>public/js/formlimiterdecompter.js"></script>
			
			<form action="<?php echo $basePath; ?>admin/commande/modif_commande.php" method="post" name="statut" >

			<?php 
			
			$i = 0;
			
			foreach($lignePs->getLignePanier() as $k => $ligneP):	
			
			
			//QUALITE
					
					$reference = $ligneP->getReference();
					$db = Zend_registry::get("database");
					$result_color = $db->prepare("
					SELECT * FROM produits WHERE referenceproduit = '$reference'
					") or die ("requete r1 invalid");
					$result_color->execute();
					$color = $result_color->fetch();
					$image_color = substr(strtolower($color['qualite']),0,3);
							
					if ($image_color == "new"){
					$style_border = "border: 1px solid #3399FF;";
					$qualite = "<strong style='color: #3399FF;'>NEUF</strong>";
					} elseif ($image_color == "nos"){
					$style_border = "border: 1px solid #FF6600;";
					$qualite = "<strong style='color: #FF6600;'>NOS</strong>";
					} elseif ($image_color == "rec"){
					$style_border = "border: 1px solid #00CC33;";
					$qualite = "<strong style='color: #00CC33;'>RENOVÉ</strong>";
					} elseif ($image_color == "use"){
					$style_border = "border: 1px solid #999900;";
					$qualite = "<strong style='color: #999900;'>OCCASION</strong>";
					} else {
					$style_border = "";
					$qualite = "";
					}
				if($user->isAdmin){	
					$result_nd = $db->prepare("SELECT produit_nd FROM static_lignes_commandes WHERE produit_reference = '$reference' AND commande_id = '$idcde'") or die ("requete result_nd invalid");
					$result_nd->execute();
					$nd = $result_nd->fetch();	
				}
				if($user->isAdmin){	
					$result_nondispo = $db->prepare("SELECT dispo FROM non_dispo WHERE reference = '$reference'") or die ("requete result_nondispo invalid");
					$result_nondispo->execute();
					$non_dispo = $result_nondispo->fetch();	
				}
				if ($user->isAdmin && $nd['produit_nd'] == "1" && $non_dispo['dispo'] == "1"){
					echo '<tr style="border-bottom:1px dotted black; background-color:#FF6600;">';
					$ND = "<b>Non Disponible</b>";
				} elseif ($user->isAdmin && $nd['produit_nd'] == "1" && $non_dispo['dispo'] == "2"){
					echo '<tr style="border-bottom:1px dotted black; background-color:#248202;">';
					$ND = "<b>Disponible</b>";
				} else {
				echo '<tr style="border-bottom:1px dotted black;">';
				}
				?>
					<td><?php echo $ligneP->getReference(); ?></td>
					<?php $tab = explode(" / ",$ligneP->getGenregroupe()); ?>
					<td><?php echo $tab[0]; ?></td>
					<td><?php echo $tab[1]; ?></td>
					<?php
					if($user->isAdmin){
						if ($nd['produit_nd'] == "0"){
						?>
						<td><?php echo $ligneP->getDesignation(); ?> <?php echo $qualite; ?></td>
						<?php
						} else {
						?>
						<td><?php echo $ligneP->getDesignation(); ?> <?php echo $qualite; ?> <b><?php echo $ND; ?></b></td>
						<?php
						}
					} else {
					?>
					<td><?php echo $ligneP->getDesignation(); ?> <?php echo $qualite; ?></td>
					<?php
					}
					?>
					<td><?php printf("%.2f", $ligneP->getPuHT());?> €</td>						
					<td align="center"><?php echo $ligneP->getQte();?></td>
					<td><?php printf("%.2f", $ligneP->getSomme());?> €</td>
					<?php
					if($user->isAdmin){
					if (file_exists("public/images/produits/".$ligneP->getReference().".jpg"))
						{
							// IMAGE
						echo "<td>";
						echo "<a href=\"".Zend_Registry::get('basePath')."public/images/produits/".$ligneP->getReference().".jpg\" rel=\"lytebox\" title=\"".$ligneP->getReference()."\" >";
						echo "<img src=\"".Zend_Registry::get('basePath')."public/images/produits/".$ligneP->getReference().".jpg\" style=\"".$style_border." margin-top:3px;\" height=\"40\" />";
						echo "</a>";
						echo "</td>";
			
						}else{
							echo '<td></td>';
						}				
						if ($nd['produit_nd'] == "0"){
						echo '<td><input type="checkbox" name="nd[]" value="'.$ligneP->getReference().'"></td>';
						}else{
						echo '<td><input type="checkbox" name="dispo[]" value="'.$ligneP->getReference().'"></td>';
						}
					
					}
					?>
				 </tr>
			<?php endforeach; ?>
			<tr>
				<td colspan="3" rowspan="5"></td>
				<td align="right" style="border-top:1px dotted black;"><?php echo $translate->_('Montant total HT');?> :</td>
				<td></td>
				<td></td>
				<td align="center"><?php printf('%.2f',$commande->getMontanttotalHT()); ?> €</td>
			</tr>
			<tr>
			<?php
			if (($commande->getRemise_Fdp()) == "0"){
			?>
				<td align="right"><?php echo $translate->_('Frais de port + emballage'); ?> : </td>
			<?php
			} else {
			?>
				<td align="right"><?php echo $translate->_('Frais de port remisé de -'); ?><?php printf('%.2f',$commande->getRemise_Fdp()); ?>€ : </td>
			<?php
			}
			?>
				<td></td>
				<td></td>
				<td align="center"><?php printf('%.2f',$commande->getFdp()); ?> €</td>
			</tr>
			<tr>
				<td align="right">TVA <?php printf('%.2f',$commande->getTauxTVA()); ?>% : </td>
				<td></td>
				<td></td>
				<td align="center"><?php printf('%.2f',$commande->getMontantTVA()); ?> €</td>
			</tr>
			<tr>
				<td style="border-top:2px solid black;border-bottom:2px solid black;" align="right"><b><?php echo $translate->_('Total de la commande TTC'); ?> : </b></td>
				<td style="border-top:2px solid black;border-bottom:2px solid black;"> </td>
				<td style="border-top:2px solid black;border-bottom:2px solid black;"></td>
				<td style="color:white;background-color:black;border-top:2px solid black;border-bottom:2px solid black;" align="center"><b><?php printf('%.2f',$commande->getMontanttotalTTC());//+$commande->getFraisDePort()); ?> €</b></td>
				<td style="background-color:black;border-top:2px solid black;border-bottom:2px solid black;"></td>
			</tr>
		</table>
		<?php
		// si le client est admin, il faudra afficher le formulaire pour qu'il puissent modifier le status de la commande
		if($user->isAdmin){
			// affichage du formulaire de modification
			$message_pb_pay = "
			Bonjour, %0D%0A 
			%0D%0A 
			Nous avons bien recu votre commande numero ".$commande->getId().", cependant nous avons rencontre un probleme lors de la validation de votre paiement.%0D%0A 
			Merci de nous contacter par mail : <EMAIL> ou par telephone : 04.76.64.43.56. afin de pouvoir expedier votre commande dans les plus bref delais.%0D%0A 
			%0D%0A 
			Cordialement, %0D%0A 
			Service commercial.%0D%0A 
			%0D%0A 
			-------------------------------------%0D%0A 
			Surplus Militaires et Industriels %0D%0A 
			D1092, La gare %0D%0A 
			38840 La Sone %0D%0A 
			FRANCE %0D%0A 
			Tel : +33 (0) 476 644 356 %0D%0A 
			Fax : +33 (0) 476 644 555 %0D%0A 
			@ : <EMAIL> %0D%0A 
			Web : www.jeep-dodge-gmc.com %0D%0A 
			-------------------------------------  %0D%0A ";
			
			$message_pb_nd = "
			Bonjour, %0D%0A 
			%0D%0A 
			Nous avons bien reçu votre commande numéro ".$commande->getId().", cependant la ou les pièces suivantes ne sont pas disponibles :%0D%0A 
			%0D%0A 
			%0D%0A 
			Merci de nous contacter par mail : <EMAIL> ou par téléphone : 04.76.64.43.56. afin de savoir si vous souhaitez ou non votre commande partielle.%0D%0A 
			%0D%0A 
			PS : Lors d’un paiement par Carte Bancaire nous débitons que la somme facturée. Les pièces manquantes se sont facturées et donc pas débitées de votre compte. Nous vous contacterons par e-mail dès leur réapprovisionnement.
			%0D%0A 
			Cordialement, %0D%0A 
			Service commercial.%0D%0A 
			%0D%0A 
			-------------------------------------%0D%0A 
			Surplus Militaires et Industriels %0D%0A 
			D1092, La gare %0D%0A 
			38840 La Sone %0D%0A 
			FRANCE %0D%0A 
			Tel : +33 (0) 476 644 356 %0D%0A 
			Fax : +33 (0) 476 644 555 %0D%0A 
			@ : <EMAIL> %0D%0A 
			Web : www.jeep-dodge-gmc.com %0D%0A 
			-------------------------------------  %0D%0A ";
			
			$message_pb_qualite = "
			Bonjour, %0D%0A 
			%0D%0A 
			Nous avons bien reçu votre commande numéro ".$commande->getId().", cependant la ou les pièces suivantes nécessitent une validation de votre part :%0D%0A 
			%0D%0A 
			%0D%0A 
			Merci de nous contacter par mail : <EMAIL> ou par telephone : 04.76.64.43.56. afin de savoir si vous souhaitez maintenir ou non votre commande.%0D%0A 
			%0D%0A 
			Cordialement, %0D%0A 
			Service commercial.%0D%0A 
			%0D%0A 
			-------------------------------------%0D%0A 
			Surplus Militaires et Industriels %0D%0A 
			D1092, La gare %0D%0A 
			38840 La Sone %0D%0A 
			FRANCE %0D%0A 
			Tel : +33 (0) 476 644 356 %0D%0A 
			Fax : +33 (0) 476 644 555 %0D%0A 
			@ : <EMAIL> %0D%0A 
			Web : www.jeep-dodge-gmc.com %0D%0A 
			-------------------------------------  %0D%0A ";
			
			?>
			<script type="text/javascript">
			
			function msgcommentpaiement() {
			var ladate=new Date();
			var date = ladate.getDate()+"/"+(ladate.getMonth()+1)+"/"+ladate.getFullYear();
			var valeur=document.getElementById('commentdetail').value;
			document.getElementById('commentdetail').value=valeur+date+" fait mail pour pb de paiement.";
			
			}
			
			function msgcommentnd() {
			var ladate=new Date();
			var date = ladate.getDate()+"/"+(ladate.getMonth()+1)+"/"+ladate.getFullYear();
			var valeur=document.getElementById('commentdetail').value;
			document.getElementById('commentdetail').value=valeur+date+" fait mail pour pb de nd.";
			
			}
			
			function msgcommentqualite() {
			var ladate=new Date();
			var date = ladate.getDate()+"/"+(ladate.getMonth()+1)+"/"+ladate.getFullYear();
			var valeur=document.getElementById('commentdetail').value;
			document.getElementById('commentdetail').value=valeur+date+" fait mail pour pb de qualite.";
			
			}
			
			
			</script>	
<?php
	
	/////////////// AFFICHE LA ZONE COMMENTAIRE ////////////////////
?>
			<p class="messageBox info">Attention, quand vous changer le statut d'une commande, un email est envoyé au client.<br />
			<label for="comment" style="width: 100px;" >Commentaire : &nbsp;</label><textarea id="commentdetail" name="comment" style="width:500px; height:50px;" maxlength="199" ><?php echo $commande->getComment(); ?></textarea><br /><br />
			
			<script type="text/javascript">
			fieldlimiter.setup({
			//Récupération des données du champ
			thefield: document.statut.comment,
			//On limite le champ à 10 caratères
			maxlength: 199,
			//id pour le retour des informations
			statusids: ["george-status"],
			//Lorsque l'on appuie sur une touche
			//on vérifie si le texte n'est pas trop long
			onkeypress:function(maxlength, curlength){
			if (curlength<maxlength)
			//Bordure du champ en gris si le nombre n'est pas dépasser
			this.style.border="2px solid gray"
			else
			//Bordure du champ en rouge si le nombre est dépasser
			this.style.border="2px solid red"
			}
			})
			</script>		
			

			
			<a href="mailto:<?php echo $commande->getEmail(); ?>?subject=Commande <?php echo $commande->getId(); ?>: Probleme paiment&body=<?php echo $message_pb_pay; ?>" onclick="msgcommentpaiement();"><img src="<?php echo $basePath; ?>public/images/email.png" alt="mail" title="mail"/> Envoyer un E-mail pour un Probleme de Paiement</a><br /><br />
			<a href="mailto:<?php echo $commande->getEmail(); ?>?subject=Commande <?php echo $commande->getId(); ?>: Probleme pieces ND&body=<?php echo $message_pb_nd; ?>" onclick="msgcommentnd();"><img src="<?php echo $basePath; ?>public/images/email.png" alt="mail" title="mail"/> Envoyer un E-mail pour un Probleme de ND</a><br /><br />
			<a href="mailto:<?php echo $commande->getEmail(); ?>?subject=Commande <?php echo $commande->getId(); ?>: Probleme qualite&body=<?php echo $message_pb_qualite; ?>" onclick="msgcommentqualite();"><img src="<?php echo $basePath; ?>public/images/email.png" alt="mail" title="mail"/> Envoyer un E-mail pour un Probleme de Qualité</a>
			</p>
			<fieldset class="form">
				<fieldset class="input">
					<legend>Traiter la commande</legend>
					<?php echo $commande->getStatut();?>
					<ul>
						<li>
							<label for="stat">Etat de la commande :</label>
							<select name="stat">
								<option value="1" <?php echo ($commande->getStatut() == "Attente de paiement")?"selected='selected'":''; ?>>Attente de paiement</option>
								<option value="2" <?php echo ($commande->getStatut() == "Paiement validé")?"selected='selected'":''; ?>>Paiement validé</option>
								<option value="3" <?php echo ($commande->getStatut() == "En cours de préparation")?"selected='selected'":''; ?>>En cours de préparation</option>
								<option value="4" <?php echo ($commande->getStatut() == "Envoyée")?"selected='selected'":''; ?>>Envoyée</option>
								<option value="5" <?php echo ($commande->getStatut() == "En cours de réapprovisionnement")?"selected='selected'":''; ?>>Attente de réappro</option>
								<option value="6" <?php echo ($commande->getStatut() == "Sur place")?"selected='selected'":''; ?>>Sur place</option>
								<option value="-1" <?php echo ($commande->getStatut() == "Annulée")?"selected='selected'":''; ?>>Annulée</option>
								<option value="7" <?php echo ($commande->getStatut() == "Réponse client")?"selected='selected'":''; ?>>Réponse client</option>
							</select>
								
						</li>
						<li><label for="no_send">Changer sans prévenir</label><input style="width: 14px; margin-left: 50px;" type="checkbox" name="no_send" id="no_send" /></li>
						<li><label for="imprim">Cocher si vous avez imprimé</label><input style="width: 14px; margin-left: 50px;" type="checkbox" name="imprim" id="imprim" value="1" /></li>
						
					</ul>
				</fieldset>
				<fieldset class="submit">
					<input type="hidden" name="idcommande" value="<?php echo $commande->getId(); ?>" />
					<?php if (isset($_GET['filtre'])){ ?>
					<input type="hidden" name="filtre" value="<?php echo $_GET['filtre']; ?>" />
					<?php } else { ?>
					<input type="hidden" name="filtre" value="0" />
					<?php } ?>
					<ul>
						<li><input type="submit" name="modifier" value="Modifier" class="btn_submit"/></li>
					</ul>
				</fieldset>
			</fieldset>
			</form>
			<?php	
		}
	}
	else{
		// vous n'avez pas le droit d'afficher cette commande
		echo '<div class="error messageBox"><strong>Vous n\'avez pas l\'accés pour afficher cette commande !<br /><br /></strong>';
		echo "Si il s'agit d'une erreur de notre site Internet merci de prendre contact avec nous par e-mail : <a href='mailto:<EMAIL>'>mailto:<EMAIL></a><br /><br />";
		echo "Sinon votre adresse IP a été enregistrée : ";
		echo $_SERVER["REMOTE_ADDR"];
		echo " elle sera transmise aux autorités compétantes pour tentative de piraterie !";
		echo '</div>';
	}