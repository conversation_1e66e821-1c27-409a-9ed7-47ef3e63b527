	<?php
	
	if(isset($_POST['email']) && $_POST['email'] != "null"){
	$q1_connu = $_POST['connu'];
	$q2_orga = $_POST['orga'];
	$q3_visite = $_POST['1_visite'];
	$q4_accueil = $_POST['accueil'];
	$q5_navig = $_POST['navigation'];
	$q6_amelio = htmlspecialchars($_POST['amelio'], ENT_QUOTES);
	
	$date_enquete = date("Y-m-d");
	$client_enquete = $_POST['email'];
	
	//$client_enquete2 = $_SESSION['user1'];
	//$client_enquete3 = explode(":",$client_enquete2);
	//$client_enquete = explode("\"",$client_enquete3[8]);
	
	$req = $database->prepare("
	INSERT INTO enquete (
	date_enquete,
	client_enquete,
	q1_connu,
	q2_orga,
	q3_visite,
	q4_accueil,
	q5_navig,
	q6_amelio
	) 
	
	VALUES (
	'".$date_enquete."',
	'".$client_enquete."',
	'".$q1_connu."',
	'".$q2_orga."',
	'".$q3_visite."',
	'".$q4_accueil."',
	'".$q5_navig."',
	'".$q6_amelio."'

	)") or die ("requete insert dans enquete invalid");
	$req->execute();
	
echo "<p class='messageBox info'><strong>Merci de votre participation !</strong></p>";
echo "<img style='margin-left: 56px;' src='".$basePath."client/enquete/pub_vercorps.png' />";
	} else {
echo "<p class='messageBox error'><strong>Une erreur est survenue au niveau de l'enquète de satisfaction merci de prendre contact avec nous.</strong></p>";		
	}
	
	?>