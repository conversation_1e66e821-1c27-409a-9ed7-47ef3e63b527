<?php

require_once dirname(__FILE__).'/../class/client_class.php';

//REQUETE
$user = (unserialize($_SESSION['user1']));
//print_r($user);
//echo $user['internaute'][1];

$email = $user->getemail();

$client = clientDB::getClientByEmail2($email);

if($client instanceof client) {
	
if($client->getDate_anni() != ""){
	
$date_anni = explode("-", $client->getDate_anni());	

$bd_j = $date_anni[2]; 
$bd_m = $date_anni[1]; 
$bd_a = $date_anni[0]; 
	
} else {

$bd_j = ""; 
$bd_m = "";
$bd_a = "";

}	

?>

<div class="form-style-10">
<h1 style="padding-top:8px; padding-bottom:22px;" ><img style="padding-top:3px;" src="<?php echo $basePath; ?>public/images/jeeper.png" width="32" height="32" title="grade" /> - <?php echo $client->getNom(); ?> <?php echo $client->getPrenom(); ?></h1>
<form action="index.php?page=user&action=infos" method="POST">
<?php	
/*
	<div class="section"><span>¤</span>Grade</div>
        <table>
            <tr>
                 <td><img src="<?php echo $basePath; ?>public/images/fidelite/matelot.png" width="32" height="32" /></td>
                 <td><div class="progress-bar blue" data-length="50"></div></td>
                 <td><img src="<?php echo $basePath; ?>public/images/fidelite/officier.png" width="32" height="32" /></td>
            </tr>
        </table>
     <div id="cadeau" style="margin-top:20px; margin-bottom:50px;"><img style="float:left; margin-right:10px;" src="<?php echo $basePath; ?>public/images/fidelite/cadeau.png" width="32" height="32" /><div style="padding-top:8px;" class="section"><strong>Votre cadeau dans 200€</strong></div></div>   
*/
?>   
   <div class="section"><span>¤</span>Coordonnées</div>
    <table>
    	<tr>
        <td>
    <div class="inner-wrap" style="width:350px;">
        <label><strong>Adresse facturation</strong></label>
       	  <label>Raison sociale ou  Nom et Prénom<input id="af_raisonsocial" type="text" name="af_raisonsocial" value="<?php echo $client->getAf_Raisonsocial(); ?>" />
		  		<script type="text/javascript">
						var af_raisonsocial = new LiveValidation('af_raisonsocial');
						af_raisonsocial.add( Validate.Presence );
		        </script>
		  </label>
          <label>Rue, voie,... <input id="af_nomue" type="text" name="af_nomue" value="<?php echo $client->getAf_Nomrue(); ?>" />
		  		<script type="text/javascript">
						var af_nomue = new LiveValidation('af_nomue');
						af_nomue.add( Validate.Presence );
		        </script>
		  </label>
          <table>
        	<tr>
            	<td style="width:200px;"><label>Code postal<input id="af_codepostal" type="text" name="af_codepostal" value="<?php echo $client->getAf_Codepostal(); ?>"  />
			    <script type="text/javascript">
						var af_codepostal = new LiveValidation('af_codepostal');
						af_codepostal.add( Validate.Presence );
		        </script>
				</label></td>
            	<td style="width:200px;"><label>Ville<input id="af_ville" type="text" name="af_ville" value="<?php echo $client->getAf_Ville(); ?>" />
			    <script type="text/javascript">
						var af_ville = new LiveValidation('af_ville');
						af_ville.add( Validate.Presence );
		        </script>
				</label></td>
            </tr>
          </table>
          <label>Pays
			<select id="af_pays" name="af_pays">
				<option value="<?php echo $client->getAf_Pays(); ?>" selected="selected"><?php echo $client->getAf_Pays(); ?></option>
<?php
				$result = $database->prepare("SELECT * FROM pays order by nom_pays ASC") or die ("requete r1 invalid");
				$result->execute();

				while ($tab = $result->fetch()) {
				
					echo '<option style="width: 225px;" value="'.$tab['nom_pays'].'">'.$tab['nom_pays'].'</option>';
				
					}
?>
			</select>	
		  </label>
    </div>
    	</td>
        <td>
    <div class="inner-wrap" style="width:350px;">
        <label><strong>Adresse livraison</strong></label>
       	  <label>Raison sociale ou  Nom et Prénom<input id="liv_raisonsocial" type="text" name="liv_raisonsocial" value="<?php echo $client->getRaisonsocial(); ?>" />
		  		<script type="text/javascript">
						var liv_raisonsocial = new LiveValidation('liv_raisonsocial');
						liv_raisonsocial.add( Validate.Presence );
		        </script>
		  </label>
          <label>Rue, voie,... <input id="liv_nomrue" type="text" name="liv_nomrue" value="<?php echo $client->getNomrue(); ?>" />
		  		<script type="text/javascript">
						var liv_nomrue = new LiveValidation('liv_nomrue');
						liv_nomrue.add( Validate.Presence );
		        </script>
		  </label>
          <table>
        	<tr>
            	<td style="width:200px;"><label>Code postal<input id="liv_codepostal" type="text" name="liv_codepostal" value="<?php echo $client->getCodepostal(); ?>" />
				<script type="text/javascript">
						var liv_codepostal = new LiveValidation('liv_codepostal');
						liv_codepostal.add( Validate.Presence );
		        </script>
				</label></td>
            	<td style="width:200px;"><label>Ville<input id="liv_ville" type="text" name="liv_ville" value="<?php echo $client->getVille(); ?>" />
				<script type="text/javascript">
						var liv_ville = new LiveValidation('liv_ville');
						liv_ville.add( Validate.Presence );
		        </script>
				</label></td>
            </tr>
          </table>
          <label>Pays
			<select id="liv_pays" name="liv_pays">
				<option value="<?php echo $client->getPays(); ?>" selected="selected"><?php echo $client->getPays(); ?></option>
<?php
				$result = $database->prepare("SELECT * FROM pays order by nom_pays ASC") or die ("requete r1 invalid");
				$result->execute();

				while ($tab = $result->fetch()) {
				
					echo '<option style="width: 225px;" value="'.$tab['nom_pays'].'">'.$tab['nom_pays'].'</option>';
				
					}
?>
			</select>	
		  </label>
    </div>
   	    </td>
    	</tr>
    </table>
    <div class="section"><span>¤</span>Contact</div>
        <table>
    	<tr>
        <td>
    <div class="inner-wrap" style="width:350px; height: 200px;">
    	<table>
        	<tr>
            	<td style="width:200px;"><label>Nom<input id="nom" type="text" name="nom" value="<?php echo $client->getNom(); ?>" />
				<script type="text/javascript">
						var nom = new LiveValidation('nom');
						nom.add( Validate.Presence );
		        </script>
				</label></td>
            	<td style="width:200px;"><label>Prenom<input id="prenom" type="text" name="prenom" value="<?php echo $client->getPrenom(); ?>" />
				<script type="text/javascript">
						var prenom = new LiveValidation('prenom');
						prenom.add( Validate.Presence );
		        </script>
				</label></td>
            </tr>
        </table>
        <table>
        	<tr>
            	<td style="width:200px;"><label>Telephone Fixe<input id="tel" type="text" name="tel" value="<?php echo $client->getTel(); ?>"  />
				<script type="text/javascript">
						var tel = new LiveValidation('tel');
						tel.add( Validate.Presence );
						tel.add( Validate.Numericality );
		        </script>
				</label></td>
            	<td style="width:200px;"><label>Fax ou Mobile<input type="text" name="fax" value="<?php echo $client->getFax(); ?>" /></label></td>
            </tr>
        </table>
        <label>Autre E-mail<input type="text" name="email_secondaire" value="<?php echo $client->getEmail_second(); ?>" /></label>
    </div>
    	</td>
        <td>

        <div class="inner-wrap" style="width:350px; height: 200px;">
          <label>E-mail (identifiant)<input readonly style="color:gray;" type="email" name="email" value="<?php echo $client->getEmail(); ?>" /></label>
          <label>Mot de passe <input id="password" type="password" name="password" value="<?php echo $client->getPassword(); ?>" onclick="this.value='';" />
		  		<script type="text/javascript">
						var password = new LiveValidation('password');
						password.add( Validate.Presence );
						password.add( Validate.Length, { minimum: 6 } );
		        </script>
		  </label>
          <label>Confirmation <input id="confirm_password" type="password" name="confirm_password"  value="<?php echo $client->getPassword(); ?>" />
		  </label>
        </div>
        </td>
    	</tr>
    </table>
    <div class="section"><span>¤</span>Autres Informations</div>
    <div class="inner-wrap" style="width:400px;">
    <table>
    <tr>
    <td>
        <table style="margin-right:30px; width:200px;">
        <tr>
            <td colspan="3" style="text-align:center;"><label><strong>Mon vehicule preferé</strong></label></td>
        <tr>
        </tr> 
		
            <td style="text-align:center;"><input type="radio" name="vehiculeprefer" <?php if($client->getVehiculeprefer() == "jeep"){echo "checked";}  ?> id="jeep" value="jeep"><label for="jeep">Jeep</label></td>
            <td style="text-align:center;"><input type="radio" name="vehiculeprefer" <?php if($client->getVehiculeprefer() == "dodge"){echo "checked";}  ?> id="dodge" value="dodge"><label for="dodge">Dodge</label></td>
            <td style="text-align:center;"><input type="radio" name="vehiculeprefer" <?php if($client->getVehiculeprefer() == "gmc"){echo "checked";}  ?> id="gmc" value="gmc"><label for="gmc">Gmc</label></td>
        </tr>
        </tr>    
            <td style="text-align:center;"><input type="radio" name="vehiculeprefer" <?php if($client->getVehiculeprefer() == "r2087"){echo "checked";}  ?> id="r2087" value="r2087"><label for="r2087">R2087</label></td>
            <td style="text-align:center;"><input type="radio" name="vehiculeprefer" <?php if($client->getVehiculeprefer() == "blinde"){echo "checked";}  ?> id="blinde" value="blinde"><label for="blinde">Blindé</label></td>
            <td style="text-align:center;"><input type="radio" name="vehiculeprefer" <?php if($client->getVehiculeprefer() == "autre"){echo "checked";}  ?> id="autre" value="autre"><label for="autre">Autre</label></td>
        </tr>
        </table>
    </td>
    <td>    
        <table width="150" >
            <tr>
                <td colspan="3"><label>Ma date d'anniversaire </label></td>
            </tr>  
            <tr>
                <td><input style="text-align:center;" type="text" name="bd_j" value="<?php echo $bd_j; ?>" /></td>
                <td><input style="text-align:center;" type="text" name="bd_m" value="<?php echo $bd_m; ?>" /></td>
                <td><input style="width:50px; text-align:center;" type="text" name="bd_a" value="<?php echo $bd_a; ?>" /></td>
            </tr>
        </table>
    </td>      
    </tr>    
    </table>    
    </div>   
    <div class="button-section">
      <input type="submit" name="modifier" value="Modifier" />
    </div>
</form>
</div>
<?php

}

?>
