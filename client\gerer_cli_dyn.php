<?php

header('Content-type: text/html; charset=iso-8859-1');  

require_once '../init.php';

		$recherche = $_GET['recherche'];
		$result = $database->prepare("
		SELECT * FROM adresses_facturation
		INNER JOIN clients  
		ON emailclient = af_emailclient
		WHERE UPPER(af_emailclient) LIKE UPPER('%".$recherche."%')
		OR UPPER(af_raisonsocial) LIKE UPPER('%".$recherche."%')
		OR UPPER(af_codepostal) LIKE UPPER('%".$recherche."%')
		OR UPPER(af_ville) LIKE UPPER('%".$recherche."%')
		OR UPPER(af_pays) LIKE UPPER('%".$recherche."%')
		OR UPPER(emailclient) LIKE UPPER('%".$recherche."%')
		OR UPPER(nomclient) LIKE UPPER('%".$recherche."%')
		OR UPPER(prenomclient) LIKE UPPER('%".$recherche."%')
		order by af_raisonsocial
		") or die ("requete r1 invalid");
		$result->execute();	

while ($row = $result->fetch()) {

echo $row['af_raisonsocial'] ."\n";

}
