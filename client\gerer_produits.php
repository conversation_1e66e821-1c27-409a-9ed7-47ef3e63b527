<?php

if (isset($_GET['non_dispo']) && $_GET['non_dispo'] == "3") {

include("modif_non_dispo.php");

} else {

/**
 * Programme qui gère l'ensemble de la base de données produits
 */
require_once(dirname(__FILE__).'/../../class/produit_class.php');
require_once(dirname(__FILE__).'/../../class/categorie_class.php');
require_once(dirname(__FILE__).'/../../class/pagination_class.php');
?>
<!-- ajout des fonctions pour charger les listes dynamiquement -->
<script type="text/javascript" src="<?php echo $basePath;?>public/js/fonctions_ajaxcatprod.js"></script>

<?php
if (!isset($_GET['non_dispo'])){
?>
<h1><img src="<?php echo $basePath; ?>public/images/produit.png" alt=""/> <span>Gérer les produits</span></h1>

<a href="<?php echo $basePath;?>index.php?page=admin&action=gererprod&non_dispo=1" ><img style="margin-left:10px;" src="<?php echo $basePath; ?>public/images/non_dispo.png" alt=""/> <span>Afficher les produits non disponibles !</span> </a><br /><br />

<?php
} else {
?>
<h1><img src="<?php echo $basePath; ?>public/images/non_dispo.png" alt=""/> <span>Gérer les produits non disponible</span></h1>
<?php
}

if (isset($_GET['non_dispo']) && $_GET['non_dispo'] == "2") {

$ref = $_GET['id'];

$result2 = $database->prepare("
DELETE FROM non_dispo WHERE reference = '$ref'
") or die ("requete r1 invalid");
$result2->execute();

}
if (isset($_GET['non_dispo']) && $_GET['non_dispo'] == "4") {

$ref = $_GET['id'];

$result2 = $database->prepare("
UPDATE non_dispo SET dispo = '2' WHERE reference = '$ref'
") or die ("requete update r1 invalid");
$result2->execute();

}
if (isset($_GET['non_dispo']) && $_GET['non_dispo'] == "5") {

$ref = $_GET['id'];

$result2 = $database->prepare("
UPDATE non_dispo SET dispo = '1' WHERE reference = '$ref'
") or die ("requete update r1 invalid");
$result2->execute();

}

if (isset($_GET['non_dispo']) && ($_GET['non_dispo'] == "1" || $_GET['non_dispo'] == "2" || $_GET['non_dispo'] == "4" || $_GET['non_dispo'] == "5")) {

// Affiche JEEP

if(isset($_GET['tri'])){

$tri = $_GET['tri'];

} else {

$tri = "genre, idgroupe";

}

$result = $database->prepare("
SELECT *,SUM(qteproduit) AS qte, MIN(date_nd) AS date_nd_min FROM non_dispo INNER JOIN produits ON non_dispo.reference = produits.referenceproduit WHERE genre ='JEEP' GROUP BY reference ORDER BY $tri
") or die ("requete r1 invalid");
$result->execute();
$nb_nd = $result->rowCount();

echo '<h2 style="text-align:left; font-size: 24px; font-weight: bold; color: #0033FF;">JEEP ('.$nb_nd.')</h2>';

echo '<table class="liste">';

echo '<th></th>';
echo '<th><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=1&tri=reference">Reference</a></th>';
echo '<th><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=1&tri=idgroupe">Groupe</a></th>';
echo '<th>Designation</th>';
echo '<th><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=1&tri=statut">Statut</a></th>';
echo '<th><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=1&tri=date_nd_min">Date ND</a></th>';
echo '<th> Qté </th>';
echo '<th> Action</th>';
echo '<th></th>';

	while ($tab = $result->fetch()) {
		if($tab['dispo'] == "1"){
		echo '<tr>';
		echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=4&id='.$tab['reference'].'">ND</a></div></td>';
		} else {
		echo '<tr style="color: #FF6600; font-weight: bold;">';
		echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=5&id='.$tab['reference'].'" style="color: #FF6600;" >Dispo</a></div></td>';
		}
			echo "<td><div align='left'>".$tab['reference']."</div></td>";
			echo '<td><div align="left">'.$tab['genrename'].'</a></div></td>';
			echo "<td><div align='left'>".$tab['designation']."</div></td>";
			if ($tab['statut'] != "attente"){
			echo "<td><div align='left'>".$tab['statut']."</div></td>";
			}else{
			echo "<td><div align='left'></div></td>";
			}
			$date_nd2 = explode("-", $tab['date_nd_min']);
			echo "<td><div align='center'>".$date_nd2[2]."/".$date_nd2[1]."/".$date_nd2[0]."</div></td>";
			echo "<td><div align='center'>".$tab['qte']."</div></td>";
			echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=3&id='.$tab['reference'].'"><img src="'.$basePath.'public/images/zoom.png" alt="Voir" title="Voir"/></a></div></td>';
			echo '<td><div align="center"><a onclick="return(confirm(\'Etes-vous sur de vouloir supprimer ce non-dispo?\'));" href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=2&id='.$tab['reference'].'"> <img src="'.$basePath.'public/images/b_drop.png" alt="delete" title="Supprimer"/></a></div></td>';
	}
echo '</table>';
echo '<br />';

// Affiche DODGE

$result = $database->prepare("
SELECT *,SUM(qteproduit) AS qte, MIN(date_nd) AS date_nd_min FROM non_dispo INNER JOIN produits ON non_dispo.reference = produits.referenceproduit WHERE genre ='DODGE' GROUP BY reference ORDER BY $tri
") or die ("requete r1 invalid");
$result->execute();
$nb_nd = $result->rowCount();

echo '<h2 style="text-align:left; font-size: 24px; font-weight: bold; color: #0033FF;">DODGE ('.$nb_nd.')</h2>';

echo '<table class="liste">';

echo '<th></th>';
echo '<th>Reference</th>';
echo '<th>Groupe</th>';
echo '<th>Designation</th>';
echo '<th>Statut</th>';
echo '<th>Date ND</th>';
echo '<th> Qté </th>';
echo '<th> Action</th>';
echo '<th></th>';

	while ($tab = $result->fetch()) {

		if($tab['dispo'] == "1"){
		echo '<tr>';
		echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=4&id='.$tab['reference'].'">ND</a></div></td>';
		} else {
		echo '<tr style="color: #FF6600; font-weight: bold;">';
		echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=5&id='.$tab['reference'].'" style="color: #FF6600;" >Dispo</a></div></td>';
		}
			echo "<td><div align='left'>".$tab['reference']."</div></td>";
			echo '<td><div align="left">'.$tab['genrename'].'</a></div></td>';
			echo "<td><div align='left'>".$tab['designation']."</div></td>";
			if ($tab['statut'] != "attente"){
			echo "<td><div align='left'>".$tab['statut']."</div></td>";
			}else{
			echo "<td><div align='left'></div></td>";
			}
			$date_nd2 = explode("-", $tab['date_nd_min']);
			
			echo "<td><div align='center'>".$date_nd2[2]."/".$date_nd2[1]."/".$date_nd2[0]."</div></td>";
			echo "<td><div align='center'>".$tab['qte']."</div></td>";
			echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=3&id='.$tab['reference'].'"><img src="'.$basePath.'public/images/zoom.png" alt="Voir" title="Voir"/></a></div></td>';
			echo '<td><div align="center"><a onclick="return(confirm(\'Etes-vous sur de vouloir supprimer ce non-dispo?\'));" href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=2&id='.$tab['reference'].'"> <img src="'.$basePath.'public/images/b_drop.png" alt="delete" title="Supprimer"/></a></div></td>';
	}
echo '</table>';
echo '<br />';

// Affiche GMC

$result = $database->prepare("
SELECT *,SUM(qteproduit) AS qte, MIN(date_nd) AS date_nd_min FROM non_dispo INNER JOIN produits ON non_dispo.reference = produits.referenceproduit WHERE genre ='GMC' GROUP BY reference ORDER BY $tri
") or die ("requete r1 invalid");
$result->execute();
$nb_nd = $result->rowCount();

echo '<h2 style="text-align:left; font-size: 24px; font-weight: bold; color: #0033FF;">GMC ('.$nb_nd.')</h2>';

echo '<table class="liste">';

echo '<th></th>';
echo '<th>Reference</th>';
echo '<th>Groupe</th>';
echo '<th>Designation</th>';
echo '<th>Statut</th>';
echo '<th>Date ND</th>';
echo '<th> Qté </th>';
echo '<th> Action</th>';
echo '<th></th>';

	while ($tab = $result->fetch()) {
		if($tab['dispo'] == "1"){
		echo '<tr>';
		echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=4&id='.$tab['reference'].'">ND</a></div></td>';
		} else {
		echo '<tr style="color: #FF6600; font-weight: bold;">';
		echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=5&id='.$tab['reference'].'" style="color: #FF6600;" >Dispo</a></div></td>';
		};
			echo "<td><div align='left'>".$tab['reference']."</div></td>";
			echo '<td><div align="left">'.$tab['genrename'].'</a></div></td>';
			echo "<td><div align='left'>".$tab['designation']."</div></td>";
			if ($tab['statut'] != "attente"){
			echo "<td><div align='left'>".$tab['statut']."</div></td>";
			}else{
			echo "<td><div align='left'></div></td>";
			}
			$date_nd2 = explode("-", $tab['date_nd_min']);
			
			echo "<td><div align='center'>".$date_nd2[2]."/".$date_nd2[1]."/".$date_nd2[0]."</div></td>";
			echo "<td><div align='center'>".$tab['qte']."</div></td>";
			echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=3&id='.$tab['reference'].'"><img src="'.$basePath.'public/images/zoom.png" alt="Voir" title="Voir"/></a></div></td>';
			echo '<td><div align="center"><a onclick="return(confirm(\'Etes-vous sur de vouloir supprimer ce non-dispo?\'));" href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=2&id='.$tab['reference'].'"> <img src="'.$basePath.'public/images/b_drop.png" alt="delete" title="Supprimer"/></a></div></td>';
	}
echo '</table>';
echo '<br />';

// Affiche R2087

$result = $database->prepare("
SELECT *,SUM(qteproduit) AS qte, MIN(date_nd) AS date_nd_min FROM non_dispo INNER JOIN produits ON non_dispo.reference = produits.referenceproduit WHERE genre ='RENAULT' GROUP BY reference ORDER BY $tri
") or die ("requete r1 invalid");
$result->execute();
$nb_nd = $result->rowCount();

echo '<h2 style="text-align:left; font-size: 24px; font-weight: bold; color: #0033FF;">R2087 ('.$nb_nd.')</h2>';


echo '<table class="liste">';

echo '<th></th>';
echo '<th>Reference</th>';
echo '<th>Groupe</th>';
echo '<th>Designation</th>';
echo '<th>Statut</th>';
echo '<th>Date ND</th>';
echo '<th> Qté </th>';
echo '<th> Action</th>';
echo '<th></th>';

	while ($tab = $result->fetch()) {

		if($tab['dispo'] == "1"){
		echo '<tr>';
		echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=4&id='.$tab['reference'].'">ND</a></div></td>';
		} else {
		echo '<tr style="color: #FF6600; font-weight: bold;">';
		echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=5&id='.$tab['reference'].'" style="color: #FF6600;" >Dispo</a></div></td>';
		}
			echo "<td><div align='left'>".$tab['reference']."</div></td>";
			echo '<td><div align="left">'.$tab['genrename'].'</a></div></td>';
			echo "<td><div align='left'>".$tab['designation']."</div></td>";
			if ($tab['statut'] != "attente"){
			echo "<td><div align='left'>".$tab['statut']."</div></td>";
			}else{
			echo "<td><div align='left'></div></td>";
			}
			$date_nd2 = explode("-", $tab['date_nd_min']);
			
			echo "<td><div align='center'>".$date_nd2[2]."/".$date_nd2[1]."/".$date_nd2[0]."</div></td>";
			echo "<td><div align='center'>".$tab['qte']."</div></td>";
			echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=3&id='.$tab['reference'].'"><img src="'.$basePath.'public/images/zoom.png" alt="Voir" title="Voir"/></a></div></td>';
			echo '<td><div align="center"><a onclick="return(confirm(\'Etes-vous sur de vouloir supprimer ce non-dispo?\'));" href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=2&id='.$tab['reference'].'"> <img src="'.$basePath.'public/images/b_drop.png" alt="delete" title="Supprimer"/></a></div></td>';
	}
echo '</table>';
echo '<br />';

// Affiche AUTRE

$result = $database->prepare("
SELECT *,SUM(qteproduit) AS qte, MIN(date_nd) AS date_nd_min FROM non_dispo INNER JOIN produits ON non_dispo.reference = produits.referenceproduit 
WHERE genre !='RENAULT' AND genre != 'JEEP' AND genre != 'DODGE' AND genre != 'GMC'
GROUP BY reference ORDER BY $tri
") or die ("requete r1 invalid");
$result->execute();
$nb_nd = $result->rowCount();

echo '<h2 style="text-align:left; font-size: 24px; font-weight: bold; color: #0033FF;">AUTRE ('.$nb_nd.')</h2>';

echo '<table class="liste">';

echo '<th></th>';
echo '<th>Reference</th>';
echo '<th>Groupe</th>';
echo '<th>Designation</th>';
echo '<th>Statut</th>';
echo '<th>Date ND</th>';
echo '<th> Qté </th>';
echo '<th> Action</th>';
echo '<th></th>';

	while ($tab = $result->fetch()) {

		if($tab['dispo'] == "1"){
		echo '<tr>';
		echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=4&id='.$tab['reference'].'">ND</a></div></td>';
		} else {
		echo '<tr style="color: #FF6600; font-weight: bold;">';
		echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=5&id='.$tab['reference'].'" style="color: #FF6600;" >Dispo</a></div></td>';
		}
			echo "<td><div align='left'>".$tab['reference']."</div></td>";
			echo '<td><div align="left">'.$tab['genrename'].'</a></div></td>';
			echo "<td><div align='left'>".$tab['designation']."</div></td>";
			if ($tab['statut'] != "attente"){
			echo "<td><div align='left'>".$tab['statut']."</div></td>";
			}else{
			echo "<td><div align='left'></div></td>";
			}
			$date_nd2 = explode("-", $tab['date_nd_min']);
			
			echo "<td><div align='center'>".$date_nd2[2]."/".$date_nd2[1]."/".$date_nd2[0]."</div></td>";
			echo "<td><div align='center'>".$tab['qte']."</div></td>";
			echo '<td><div align="center"><a href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=3&id='.$tab['reference'].'"><img src="'.$basePath.'public/images/zoom.png" alt="Voir" title="Voir"/></a></div></td>';
			echo '<td><div align="center"><a onclick="return(confirm(\'Etes-vous sur de vouloir supprimer ce non-dispo?\'));" href="'.$basePath.'index.php?page=admin&action=gererprod&non_dispo=2&id='.$tab['reference'].'"> <img src="'.$basePath.'public/images/b_drop.png" alt="delete" title="Supprimer"/></a></div></td>';
	}
echo '</table>';

} else {
?>

<!-- Formulaire d'envoi vers la page d'importation -->
			<form name="recherche_ref" method="GET" action="<?php echo $basePath; ?>index.php">
				<input type=hidden name="page" value="admin">
				<input type=hidden name="action" value="gererprod">
				<input type=hidden name="type" value="modif_par_ref">
				<fieldset><legend>Rechercher une réference exacte :</legend>
				<table><tr><td>
					<input type="text" name="ref" value=""/>			
					</td><td>
					<input type="submit" value="Ok" /><br />
				</td></tr></table>
				</fieldset>
			</form>
<br />		


<!-- Formulaire de filtre sur les genres et les groupes -->
<form name="filtre" method="GET" action="<?php echo $basePath; ?>index.php">
	<input type=hidden name="page" value="admin">
	<input type=hidden name="action" value="gererprod">
	<table class="table_center">
		<tr>
			<td>Genre :</td>
			<td>Groupe :</td>
		</tr>
		<tr>
			<td> 
				<select name="filtre_cat" size="5" style="max-width:200px;">
					<option value="all">Tous</option>

					<?php
					$namelist = ProduitDB::getGenresNames();
					foreach($namelist as $name)
					{
						echo '<option value="',$name,'"';
						if(isset($_GET['filtre_cat']) && $_GET['filtre_cat']==$name)
							echo ' selected=true';
						
						echo ' onClick="chargeSousCategories(\'';
						echo $name;
						list($fr, $en) = explodeCategorieName($name);
						echo '\');">',$fr;
					
						
						echo '</option>';
					}
					?>
				</select>
			</td>

			<td>
				<div id="progress_sscat" style="display:none;"><img src="public/images/progressbar.gif" alt="Barre de progression"/></div>
				<div id="msg_sscat" style="<?php echo isset($_GET['filtre_cat'])?'display:none;':'display:normal;'; ?>"><b>Sélectionnez un Genre.</b></div>
					<select id="filtre_sscat" name="filtre_sscat" size="5" style="<?php echo isset($_GET['filtre_cat'])?'display:normal;':'display:none;'; ?>">
						<option value="all">Tous</option>
					</select>
			</td>
			<td>
				<input type=submit value="filtrer" />
			</td>
		</tr>
	</table>
</form>
<br />		
				
<?php
//si un filtre est définit on cahrge les sous catégorie en fonction de la catégorie
if(isset($_GET['filtre_cat'])):
?>
	<script type="text/javascript">chargeSousCategories("<?php echo $_GET['filtre_cat']; ?>", "<?php echo $_GET['filtre_sscat']; ?>")</script>
<?php
endif;

if(isset($_GET['numpage']))
	$numpage = $_GET['numpage'];
else
	$numpage = 1;


//par défaut on affiche tout, donc on ne précise aucune catégorie ou sous catégorie et aucun filtres
$genre = null;
$groupe = null;
$tri = null;
if(isset($_GET['filtre_cat']))
{
	$genre = $_GET['filtre_cat'];
	/*if(!in_array($genre, array('jeep', 'dodge', 'gmc')))
		$genre = strtolower($genre);*/
}
if(isset($_GET['filtre_sscat']))
{
	$groupe = $_GET['filtre_sscat'];
}
if(isset($_GET['tri']))
{
	$tri = $_GET['tri'];
}

//création d'un objet pour paginer la page
$pagination = new Pagination(ProduitDB::getProduitsListCount($genre, $groupe), 100, 20, $numpage);

$liste = new ProduitDisplay($genre, $groupe, ProduitDisplay::$PAGE_TOUS);
$liste->showGroupe(true);
$liste->showGenre(true);
$liste->isClientList(false);
$liste->setPagination($pagination);
if(isset($_GET['tri']))
	$liste->setTri($tri);
if(isset($_GET['sens']))
	$liste->setSens($_GET['sens']);
$liste->setInterval($pagination->getStartPos(), $pagination->getNbLignes());
$liste->displayTable();

?>

<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>index.php?page=admin&action=administration"><?php echo $translate->_('Retour');?></a>
</p>

<?php

}
}
?>