<?php

	require_once dirname(__FILE__).'/../init.php';
	
	$impr_panier_vercorps_id = $_GET['imprim'];

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Ticket du DATE</title>
<style type="text/css" media="print">
<!--
	@page { size:landscape; marks:none;size: 21.0cm;margin: 0cm ;}
-->
</style>
<style type="text/css">
<!--
body {
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-size: 12px;
}
-->
</style>
</head>

<body>
<p><strong>VERCORPS</strong><br />
D1092, La gare<br />
38840 La S&ocirc;ne<br />
T&eacute;l : +33 (0) 476 644 456<br />
Fax : +33 (0) 476 644 555<br />
Mail : <EMAIL><br />
Web : www.vercorps.com</p>

<?php 

$result5 = $database->prepare("
SELECT *,SUM(produit_qte*produit_prix) AS total_ht FROM panier_vercorps WHERE panier_vercorps_id = $impr_panier_vercorps_id GROUP BY panier_vercorps_id
") or die ("requete r5 invalid");
$result5->execute();
while ($tab5 = $result5->fetch()) {

?>
<p>-----------------------------------------------------</p>
<p><strong>TICKET</strong><br />
Date :  <?php echo date('d/m/Y',strtotime($tab5['panier_vercorps_date'])); ?> - Heure : <?php echo $tab5['panier_vercorps_heure']; ?></p>
<p>-----------------------------------------------------</p>
<table width="400" border="0" cellspacing="0" cellpadding="0">
				 <tr>
<?php

$result6 = $database->prepare("
SELECT * FROM panier_vercorps WHERE panier_vercorps_id = $impr_panier_vercorps_id
") or die ("requete r6 invalid");
$result6->execute();
while ($tab6 = $result6->fetch()) {
					
					echo '<td>'.$tab6['produit_reference'].'</td>';
					echo '<td>'.$tab6['produit_designation'].'</td>';
					echo '<td width="50" align="center">'.$tab6['produit_prix'].' &euro;</td>';
					echo '<td width="20" align="center">'.$tab6['produit_qte'].'</td>';
					echo '<td width="50" align="center">'.number_format(($tab6['produit_prix'] * $tab6['produit_qte']),2, '.', '').' &euro;</td>';
?>
				</tr>
<?php
}
?>
  <tr>
  	<td colspan="5" >&nbsp;</td>
  </tr>
  <tr>
  	<td colspan="4" style="text-align:left; border-top: 1px solid #000000; margin-top:10px;" ><strong>TOTAL TTC</strong></td>
	<td style="border-top: 1px solid #000000;" align="center" ><strong><?php echo number_format(($tab5['total_ht'] * 1.196),2, '.', ''); ?>&euro;</strong></td>
  </tr>
</table>
<p>R&eacute;gl&eacute; par 

<?php
if($tab5['panier_vercorps_regl'] == "es"){
echo '<tEsp&egrave;ce';
}
if($tab5['panier_vercorps_regl'] == "cb"){
echo 'Carte bancaire';
}
if($tab5['panier_vercorps_regl'] == "ch"){
echo 'Ch&egrave;que';
}
if($tab5['panier_vercorps_regl'] == "mi"){
echo 'Mixte';
}
?>

</p>
<p><em>Merci de votre visite !</em></p>

<?php

}

?>

</body>
</html>