<?php 
	$content_html = '
<table style="font-family:Arial;" cellspacing="0" cellpadding="0" border="0" bgcolor="#ffffff" width="100%" style="font-size:0; margin: 0px; padding: 0px; border-collapse: collapse; border-spacing: 0;">
  <tr>
    <td valign="top" align=center>
<table cellspacing="0" cellpadding="0" border="0" bgcolor="#cccc66" style="font-size:0; margin: 0px; padding: 0px; border-collapse: collapse; border-spacing: 0;">
  <tr>
    <td valign="top" rowspan="3" ><a href="http://jeep-dodge-gmc.com/smi/" ><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_01.gif" style="display:block;"/></a></td>
    <td colspan="3" valign="top"><a href="mailto:<EMAIL>" ><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_02.gif" style="display:block;"/></a></td>
    <td colspan="1" valign="top"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_03.gif" style="display:block;"/></td>
  </tr>
  <tr>
    <td valign="top"><a href="https://twitter.com/jeep_dodge_gmc"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_04.gif" style="display:block;"/></a></td>
    <td valign="top"><a href="https://plus.google.com/u/0/107839788516299581970/about"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_05.gif" style="display:block;"/></a></td>
    <td valign="top"><a href="https://www.facebook.com/surplus.militaitresetindustriels"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_06.gif" style="display:block;"/></a></td>
    <td valign="top"><a href="http://jeep-dodge-gmc.com/smi/index.php?page=newsletter"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_07.gif" style="display:block;"/></a></td>
  </tr>
  <tr>  
    <td colspan="4" valign="top"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_08.gif" style="display:block;"/></td>
  </tr> 
</table>

<table style="font-family:Arial; background-color:white; margin-left:auto;margin-right:auto; width:650px; border:solid black;">
	<tr ><td style="background-color:black; color:white;">
		<h1 style="margin-top:10px; margin-bottom:10px; text-align:center;">Inscription</h1>
	</td></tr>
	<tr ><td>
<p>Vous &ecirc;tes d&eacute;sormais inscrit sur le site : www.jeep-dodge-gmc.com </p>

<p><strong>Veuillez conserver les donn&eacute;es suivantes :</strong><br />
Adresse E-mail de connexion : '.$client->getEmail().'<br />
Mot de passe : '.$_POST['motdepasse'].'<br />
<br />
<strong>Informations vous concernant :</strong><br />
Nom : '.stripslashes($client->getNom()).'<br />
Pr&eacute;nom : '.stripslashes($client->getPrenom()).'<br />
T&eacute;l. : '.$client->getTel().'<br />
Fax / mobile : '.$client->getFax().'<br />
<br />
<strong>Adresse de FACTURATION :</strong><br />
'.stripslashes($raisonsocial).'<br />
'.stripslashes($nomrue).'<br />
'.$cp.' '.stripslashes($ville).'<br />
'.stripslashes($pays).'<br />
</p>

	</td></tr>
	</table>
</td>
</tr>
</table>

';
		
/*
-- Instruction Module envoi d'un email en php -- 

Les variables :

*/
$client_nom = $client->getEmail();
			
$exp_mail = "<EMAIL>";
$exp_nom = "Jeep-Dodge-Gmc.com";

		
$mail = $client_nom; // Déclaration de l'adresse de destination.
$test_mail = preg_match("#^[a-z0-9._-]+@(hotmail|live|msn).[a-z]{2,4}$#", $mail);
if ($test_mail === "1"){ // On filtre les serveurs qui présentent des bogues.
$passage_ligne = "\r\n";
}else{
$passage_ligne = "\n";
}
//=====Définition du sujet.
$sujet = 'Inscription jeep-dodge-gmc.com';
//=========
 
//=====Création du header de l'e-mail.
$header = 'MIME-Version: 1.0' . "".$passage_ligne;
$header.= 'Content-type: text/html; charset=iso-8859-1' . "".$passage_ligne;
$header.= "From: \"".$exp_nom."\"<".$exp_mail.">".$passage_ligne;
$header.= "Reply-To: \"".$exp_nom."\"<".$exp_mail.">".$passage_ligne;

//=====Envoi de l'e-mail.
mail($mail,$sujet,$content_html,$header);
 
//==========
 ?>