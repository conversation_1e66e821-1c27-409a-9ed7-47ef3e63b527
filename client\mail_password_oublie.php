<?php 
$server_name = 'http://'.$_SERVER['SERVER_NAME'];
 
$content_html = '
<table style="font-family:Arial;" cellspacing="0" cellpadding="0" border="0" bgcolor="#ffffff" width="100%" style="font-size:0; margin: 0px; padding: 0px; border-collapse: collapse; border-spacing: 0;">
  <tr>
    <td valign="top" align=center>
<table cellspacing="0" cellpadding="0" border="0" bgcolor="#cccc66" style="font-size:0; margin: 0px; padding: 0px; border-collapse: collapse; border-spacing: 0;">
  <tr>
    <td valign="top" rowspan="3" ><a href="http://jeep-dodge-gmc.com/smi/" ><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_01.gif" style="display:block;"/></a></td>
    <td colspan="3" valign="top"><a href="mailto:<EMAIL>" ><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_02.gif" style="display:block;"/></a></td>
    <td colspan="1" valign="top"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_03.gif" style="display:block;"/></td>
  </tr>
  <tr>
    <td valign="top"><a href="https://twitter.com/jeep_dodge_gmc"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_04.gif" style="display:block;"/></a></td>
    <td valign="top"><a href="https://plus.google.com/u/0/107839788516299581970/about"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_05.gif" style="display:block;"/></a></td>
    <td valign="top"><a href="https://www.facebook.com/surplus.militaitresetindustriels"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_06.gif" style="display:block;"/></a></td>
    <td valign="top"><a href="http://jeep-dodge-gmc.com/smi/index.php?page=newsletter"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_07.gif" style="display:block;"/></a></td>
  </tr>
  <tr>  
    <td colspan="4" valign="top"><img src="https://jeep-dodge-gmc.com/smi/public/images/entete/NEWSLETTER-MODELE_08.gif" style="display:block;"/></td>
  </tr> 
</table>

<table style="background-color:white; margin-left:auto;margin-right:auto; width:650px; border:solid black;">
	<tr ><td style="background-color:black; color:white;">
		<h1 style="margin-top:10px; margin-bottom:10px; text-align:center;">Changement de mot de passe</h1>
	</td></tr>	
	<tr ><td>
			Nous avons re&ccedil;u une demande de r&eacute;initialisation du mot de passe associ&eacute; &agrave; cette adresse e-mail. Si vous l\'avez demand&eacute;e, veuillez suivre les instructions ci-dessous.<br />
			Cliquez sur le lien ci-dessous afin de r&eacute;initialiser votre mot de passe : <br />
			<a href="'.$server_name.$basePath.'index.php?page=user&action=forgotmodif&uid='.$uid.'">'.$server_name.$basePath.'index.php?page=user&action=forgotmodif&uid='.$uid.'</a><br />
			Si cliquer sur le lien ne semble pas fonctionner, vous pouvez copier et coller ce lien dans la barre d\'adresse de votre navigateur ou y retaper l\'adresse indiqu&eacute;e par le lien.<br />
			<br />
			L\'&eacute;quipe SMI<br />
	</td></tr>
	</table>
</td>
</tr>
</table>

';
		
/*
-- Instruction Module envoi d'un email en php -- 

Les variables :

*/
$client_nom = $client->getEmail();
			
$exp_mail = "<EMAIL>";
$exp_nom = "Jeep-Dodge-Gmc.com";

		
$mail = $client_nom; // Déclaration de l'adresse de destination.
$test_mail = preg_match("#^[a-z0-9._-]+@(hotmail|live|msn).[a-z]{2,4}$#", $mail);
if ($test_mail === "1"){ // On filtre les serveurs qui présentent des bogues.
$passage_ligne = "\r\n";
}else{
$passage_ligne = "\n";
}
 
//=====Définition du sujet.
$sujet = 'Changement de mot de passe';

//=====Création du header de l'e-mail.
$header = 'MIME-Version: 1.0' . "".$passage_ligne;
$header.= 'Content-type: text/html; charset=iso-8859-1' . "".$passage_ligne;
$header.= "From: \"".$exp_nom."\"<".$exp_mail.">".$passage_ligne;
$header.= "Reply-To: \"".$exp_nom."\"<".$exp_mail.">".$passage_ligne;

//=====Envoi de l'e-mail.
mail($mail,$sujet,$content_html,$header);
 
//==========

?>