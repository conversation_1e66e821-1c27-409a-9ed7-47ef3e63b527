<?php if($user->isAuthenticated === true): ?>
<?php 
	require_once dirname(__FILE__).'/../class/annonce_class.php';
	$nbAnnonceAValider = annonceDB::getNbAValiderAnnonceByClient($user); 
?>
	<ul>
		<li><a href="<?php echo $basePath; ?>index.php?page=user&action=infos"><?php echo $translate->_('Modifier mes coordonnées'); ?></a></li>
		<li><a href="<?php echo $basePath; ?>index.php?page=user&action=annonces" title="<?php echo $nbAnnonceAValider; ?> annonce(s) en attente de validation"><?php echo $translate->_('Gérer mes annonces'); ?> (<?php echo $nbAnnonceAValider; ?>)</a></li>
		<li><a href="<?php echo $basePath; ?>commandes"><?php echo $translate->_('Mes commandes'); ?></a></li>
		<li><a href="<?php echo $basePath; ?>panier/mes_paniers"><?php echo $translate->_('Mes paniers'); ?></a></li>
	</ul>
	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="<?php echo $basePath; ?>index.php?page=newsletter"><font color="yellow"><b>NEWSLETTER</b></font></a>
<?php endif; ?>