<?php
// Fichier créé par <PERSON><PERSON><PERSON>

// Ce fichier PHP permet d'ajouter une adresse au client
require_once dirname(__FILE__).'../../class/adresse_class.php';

echo '<h1>'.$translate->_('Gestion du carnet d\'adresse').'</h1>';
// Pour ajouter une adresse
$afficheform = true;
?>
<p class="AnnonceAdd">
</p>
<?php
if ($afficheform)
{
	if ($user->isAuthenticated)
	{
		// Initialisation des variables
		$email=$user->getEmail();

		// Test si le client possède déjà une adresse de facturation
		$adresseDB = new adresseDB;
		$type = true; // Type de l'adresse, est égale à Vrai si il s'agit d'une adresse de facturation
		// Affichage de toutes les adresses de ce client
		$adrlist = array();
		$adrlist = $adresseDB->SelectionneToutesAdresses($email);
		
		if ($adrlist != null)
		{
			?>
			<table class="liste" border="0">
			<?php
			$nbadresse = 0;
			foreach ($adrlist as $ligne)
			{
				
				$adressecourante = $ligne;
				$affId = $adressecourante->getId();
				$affRaisonSocial = $adressecourante->getRaisonSocial();
				$affNomRue = $adressecourante->getNomRue();
				$affcp = $adressecourante->getCodePostal();
				$affville = $adressecourante->getVille();
				$affpays = $adressecourante->getPays();
				$id = $adressecourante->getId();
				?>
				<tr>
					<td align="left">
						<?php
						afficheAdresseEnLigne($affId, $affRaisonSocial,$affNomRue, $affcp, $affville, $affpays);
						?>
					</td>
				<?php
				$nbadresse = $nbadresse + 1;
			}
			?>
			</tr>
			</table>
			<?php
				if ($nbadresse == 1)
				{
					$fin = $translate->_('adresse enregistrée').'.';
				}
				else
				{
					$fin = $translate->_('adresses enregistrées').'.';
				}
			?>
			<p><?php 
			echo '<div class="info messageBox"><p>';
			echo $translate->_('Vous avez exactement'),' ',$nbadresse,' '; 
			echo $fin; 
			echo '</p></div>';
			?> </p>
			<?php
		}
		else
		{
			echo '<div class="info messageBox"><p>'.$translate->_('Vous n\'avez pas encore d\'adresse enregistrée').'</p></div>';
		}		
	}
	else
	{
		// Le client n'est pas connecté ->, message d'erreur.
		echo '<div class="error messageBox"><p>'.$translate->_('Pour ajouter ou modifier votre adresse, vous devez vous connecter, merci').'.</p></div>';
	}
}
?>
<br/>
<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>"><?php $translate->_('Retour');?></a>
</p>