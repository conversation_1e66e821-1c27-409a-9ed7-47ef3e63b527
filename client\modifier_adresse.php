<?php

// Fichier créé par <PERSON><PERSON><PERSON>

// Ce fichier PHP permet d'ajouter une adresse au client
require_once dirname(__FILE__).'../../class/adresse_class.php';

// Création de la page html virtuelle
echo '<h1>'.$translate->_('Carnet d\'adresse : modifications').'</h1><br/><br/>';

$afficheform=true;

if(isset($_POST['nomrue'])) // On se trouve sur la page de traitement de la modification
{
	// Déclaration des variables :
	// Formulaire :
	$nomrue=htmlentities($_POST['nomrue'], ENT_QUOTES, $charSet='UTF-8');
	//$nomrue=stripslashes($nomrue);
	$nomrue=$_POST['nomrue'];
	$cp=htmlentities($_POST['cp']);
	$ville=htmlentities($_POST['ville'], ENT_QUOTES, $charSet='UTF-8');
	$pays=htmlentities($_POST['pays'], ENT_QUOTES, $charSet='UTF-8');
	$nom=$user->getNom();
	$prenom=$user->getPrenom();
	$raisonsocial = htmlentities($_POST['raisonsocial'], ENT_QUOTES, $charSet='UTF-8');
	$email=$user->getEmail();
	
	// On teste si les champs ont bien été remplis 
	if (!empty($_POST['nomrue']) && !empty($_POST['cp']) && !empty($_POST['ville']) && !empty($_POST['pays']))
	{
		$afficheform = false;
		//Affichage d'un récapitulatif des données inscrites
		echo '<h2>'.$translate->_('Récapitulatif de votre adresse').'</h2></center>';
		echo '<div class="info messageBox"><p>';
		afficheAdresse ($raisonsocial, $nomrue, $cp, $ville, @stripslashes($pays));
		echo '</p></div>';
		echo '<br/><br/>';
		echo '<h2>'.$translate->_('Résultat des modifications').'</h2></center>';
		
		// Enregistrement dans la BDD avec test si la système à réussit à se connecter et à enregistrer les données
		$adresse = new adresse();
		$adresse->setId($_POST['idadresse']);
		$adresse->setRaisonSocial($raisonsocial);
		$adresse->setNomRue($nomrue);
		$adresse->setCodePostal($cp);
		$adresse->setVille($ville);
		$adresse->setPays($pays);
		// On vérifie que tout c'est bien passé dans l'enregistrement
		if (adresseDB::save($email, $adresse, false))
		{
			echo '<div class="valide messageBox"><p>'.$translate->_('Vos modifications ont bien été enregistrés').'.</p></div>';
		}
		else
		{
			echo '<div class="error messageBox"><p>'.$translate->_('Aucun changement détécté ou erreur pendant l\'enregistrement').'.</p></div>';
		}
	}
	else
	{
		echo '<div class="error messageBox"><p>'.$translate->_('Vous devez spécifier au moins votre <b>nom de rue</b>, votre <b>code postal</b>, votre <b>ville</b> et votre <b>pays</b>.<br/> Le numéro de rue est à remplir que si necessaire').'.</p></div>';
	}
}


  /////////////////
 // FORMULAIRE //
/////////////////


if ($afficheform)
{
	if ($user->isAuthenticated)
	{
		// Initialisation des variables
		$email=$user->getEmail();
		
		if (isset($_GET['id'])) // On se trouve sur la page de remplissage automatique des champs du formulaire
		{
			$id = $_GET['id'];
			// Contenu du formulaire d'ajout des coordonnées d'un client avec un tableau pour afficher correctement
			echo '<form action="',$basePath,'index.php?page=user&action=modifier_adresse" method="POST">';
			$adresseDB = new adresseDB;
			$adresseselect = $adresseDB->getAdresseById($id);
			// Si le client possède déjà une adresse de facturation : On affiche le formulaire pré-remplis
			if ($adresseselect instanceof adresse)
			{
				$idadresse = $adresseselect->getId();
				$raisonsocial = $adresseselect->getRaisonSocial();
				$nomrue = $adresseselect->getNomRue();
				$cp = $adresseselect->getCodePostal();
				$ville = $adresseselect->getVille();
				$pays = $adresseselect->getPays();
				?>
				<div class="form">
				<fieldset class="input">
					<ol>
						<li>
							<label for="raisonsocial"><?php echo $translate->_('Raison Social : (ou Nom et Prénom)');?></label>
							<input type="text" name="raisonsocial" value="<?php echo @stripslashes($raisonsocial); ?>" class="indispensable"/><b class="reditalique">* </b>
						</li>
						<li>
							<label for="adresse"><?php echo $translate->_('Adresse'),' :';?></label>
							<input type="text" name="nomrue" value="<?php echo @stripslashes($nomrue); ?>" class="indispensable"/><b class="reditalique">* </b>
						</li>
						<li>
							<label for="cp"><?php echo $translate->_('Code Postal'),' :';?></label>
							<input type="text" name="cp" value="<?php echo @stripslashes($cp); ?>" class="indispensable"/><b class="reditalique">* </b>
						</li>
						<li>
							<label for="ville"><?php echo $translate->_('Ville'),' :';?></label>
							<input type="text" name="ville" value="<?php echo @stripslashes($ville); ?>" class="indispensable"/><b class="reditalique">* </b>
						</li>
						<li>
							<label for="pays"><?php echo $translate->_('Pays'),' :';?></label>
							<?php /*<input type="text" name="pays" value="<?php echo $pays; ?>" class="indispensable"/>*/ ?>
							<select name="pays">
								<option value="France" <?php if($pays == "France") echo "selected"; ?>>France</option>
								<option value="Algérie" <?php if($pays == "Algérie") echo "selected"; ?>>Algérie</option>
								<option value="Allemagne" <?php if($pays == "Allemagne") echo "selected"; ?>>Allemagne</option>
								<option value="Arabie Saoudite" <?php if($pays == "Arabie Saoudite") echo "selected"; ?>>Arabie Saoudite</option>
								<option value="Argentine" <?php if($pays == "Argentine") echo "selected"; ?>>Argentine</option>
								<option value="Autriche" <?php if($pays == "Autriche") echo "selected"; ?>>Autriche</option>
								<option value="Belgique" <?php if($pays == "Belgique") echo "selected"; ?>>Belgique</option>
								<option value="Cameroun" <?php if($pays == "Cameroun") echo "selected"; ?>>Cameroun</option>
								<option value="Canada" <?php if($pays == "Canada") echo "selected"; ?>>Canada</option>
								<option value="Chine" <?php if($pays == "Chine") echo "selected"; ?>>Chine</option>
								<option value="Chypre" <?php if($pays == "Chypre") echo "selected"; ?>>Chypre</option>
								<option value="Côte d Ivoire" <?php if($pays == "Côte d Ivoire") echo "selected"; ?>>Côte d Ivoire</option>
								<option value="Danemark" <?php if($pays == "Danemark") echo "selected"; ?>>Danemark</option>
								<option value="Espagne" <?php if($pays == "Espagne") echo "selected"; ?>>Espagne</option>
								<option value="Estonie" <?php if($pays == "Estonie") echo "selected"; ?>>Estonie</option>
								<option value="Etats Unis" <?php if($pays == "Etats Unis") echo "selected"; ?>>Etats Unis</option>
								<option value="Finlande" <?php if($pays == "Finlande") echo "selected"; ?>>Finlande</option>
								<option value="Gabon" <?php if($pays == "Gabon") echo "selected"; ?>>Gabon</option>
								<option value="Grèce" <?php if($pays == "Grèce") echo "selected"; ?>>Grèce</option>
								<option value="Hongrie" <?php if($pays == "Hongrie") echo "selected"; ?>>Hongrie</option>
								<option value="Irlande" <?php if($pays == "Irlande") echo "selected"; ?>>Irlande</option>
								<option value="Italie" <?php if($pays == "Italie") echo "selected"; ?>>Italie</option>
								<option value="Lettonie" <?php if($pays == "Lettonie") echo "selected"; ?>>Lettonie</option>
								<option value="Lituanie" <?php if($pays == "Lituanie") echo "selected"; ?>>Lituanie</option>
								<option value="Luxembourg" <?php if($pays == "Luxembourg") echo "selected"; ?>>Luxembourg</option>
								<option value="Madère" <?php if($pays == "Madère") echo "selected"; ?>>Madère</option>
								<option value="Malte" <?php if($pays == "Malte") echo "selected"; ?>>Malte</option>
								<option value="Maroc" <?php if($pays == "Maroc") echo "selected"; ?>>Maroc</option>
								<option value="Monaco" <?php if($pays == "Monaco") echo "selected"; ?>>Monaco</option>
								<option value="Nouvelle Calédonie" <?php if($pays == "Nouvelle Calédonie") echo "selected"; ?>>Nouvelle Calédonie</option>
								<option value="Pays Bas" <?php if($pays == "Pays Bas") echo "selected"; ?>>Pays Bas</option>
								<option value="Pologne" <?php if($pays == "Pologne") echo "selected"; ?>>Pologne</option>
								<option value="Portugal" <?php if($pays == "Portugal") echo "selected"; ?>>Portugal</option>
								<option value="République Tchèque" <?php if($pays == "République Tchèque") echo "selected"; ?>>République Tchèque</option>
								<option value="Royaume uni" <?php if($pays == "Royaume uni") echo "selected"; ?>>Royaume uni</option>
								<option value="Russie" <?php if($pays == "Russie") echo "selected"; ?>>Russie</option>
								<option value="Sénégal" <?php if($pays == "Sénégal") echo "selected"; ?>>Sénégal</option>
								<option value="Slovaquie" <?php if($pays == "Slovaquie") echo "selected"; ?>>Slovaquie</option>
								<option value="Slovénie" <?php if($pays == "Slovénie") echo "selected"; ?>>Slovénie</option>
								<option value="Suède" <?php if($pays == "Suède") echo "selected"; ?>>Suède</option>
								<option value="Suisse" <?php if($pays == "Suisse") echo "selected"; ?>>Suisse</option>
								<option value="Togo" <?php if($pays == "Togo") echo "selected"; ?>>Togo</option>
								<option value="Tunisie" <?php if($pays == "Tunisie") echo "selected"; ?>>Tunisie</option>
							</select><b class="reditalique">* </b>
						</li>
						<li>
							<label class="reditalique"><?php echo '* ',$translate->_('Champs obligatoires'); ?></label>
						</li>
					</ol>
				</fieldset>
				<fieldset class="submit">
					<ol>
						<li>
						<input type="submit" name="envoyer" value="<?php echo $translate->_('Enregistrer');?>" class="btn_submit">
						<input type="hidden" name="idadresse" value="<?php echo $idadresse; ?>">
						</li>
					</ol>
				</fieldset>
				</form>
				</div>
				<?php
			}
			else
			{
				echo '<div class="error messageBox"><p>'.$transate->_('Erreur d\'ouverture de l\'adresse séléctionnée').'</p></div>';
			}
		}
		else
		{
			echo '<div class="error messageBox"><p>'.$translate->_('Erreur d\'identifiant lors de l\'ouverture de l\'adresse').'.</p></div>';
		}
	}
	else
	{
		// Le client n'est pas connecté ->, message d'erreur.
		echo '<div class="error messageBox"><p>'.$translate->_('Pour modifier votre adresse, vous devez vous connecter, merci').'.</p></div>';
	}
}
?>
<br/>
<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>index.php?page=user&action=infos"><?php echo $translate->_('Retour');?></a>
</p>