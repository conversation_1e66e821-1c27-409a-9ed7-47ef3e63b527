<?php

// Fichier créé par <PERSON><PERSON><PERSON>

// Ce fichier PHP permet de modifier les données d'un client dans la BDD

// Création de la page html virtuelle

/*

echo '<h1>'.$translate->_('Modification de vos données personnelles').'</h1>';

// Déclaration de variables :
$email=$user->getEmail();
$afficheform=true;


// On est dans la partie traitement
if(isset($_POST['motdepasse'])) {
	// Déclaration des variables :
	// Formulaire :
	$nom=stripAccents($_POST['nom']);
	$prenom=stripAccents($_POST['prenom']);
	$mdp=htmlentities($_POST['motdepasse'], ENT_QUOTES, $charSet='UTF-8');
	$mdpc=htmlentities($_POST['motdepasseconfirme'], ENT_QUOTES, $charSet='UTF-8');
	$tel=htmlentities($_POST['tel'], ENT_QUOTES, $charSet='UTF-8');
	$fax=htmlentities($_POST['fax'], ENT_QUOTES, $charSet='UTF-8');
	$toutok = true;
	$error = "";
	
	// On test si le champ nom est bien remplis
	if (empty($_POST['nom']))
	{
		$toutok = false;
		$error = $error.$translate->_('Erreur : Il faut impérativement remplir le Nom').'.<br/>';
	}
	// On test si le champ prénom est bien remplis
	if (empty($_POST['prenom']))
	{
		$toutok = false;
		$error = $error.$translate->_('Erreur : Il faut impérativement remplir le Prénom').'.<br/>';
	}
	// On test si le mot de passe est tremplis
	if (empty($_POST['motdepasse']))
	{
		$toutok = true;
		//$error = $error.$translate->_('Erreur : Il faut définir un mot de passe').'.<br/>';
	}
	else
	{
		$nbrcaract = strlen($_POST['motdepasse']);
		$nbrmincaract = 5;
		// On test si le mot de passe est assez long
		if ($nbrcaract < $nbrmincaract)
		{
			$toutok = false;
			$error = $error.$translate->_('Erreur : Il faut un mot de passe de 6 caractères minimum').'.<br/>';
		}
		else
		{
			// On test si le mot de passe de confirmation est bien remplis
			if (empty($_POST['motdepasseconfirme']))
			{
			$toutok = false;
			$error = $error.$translate->_('Erreur : Il faut répéter le mot de passe pour éviter les fautes de frappes').'.<br/>';
			}
			else
			{
				// On test si les deux mot de passe sont égaux
				if ($_POST['motdepasse'] != $_POST['motdepasseconfirme'])
				{
					$toutok = false;
					$error = $error.$translate->_('Erreur : Les mots de passe sont différent').'.<br/>';
				}
			}
		}
	}
	// On test si le numéro de téléphone est remplis
	if (empty($_POST['tel']))
	{
		$toutok = false;
		$error = $error.$translate->_('Erreur : Il faut remplir le numéro de téléphone').'.<br/>';
	}
	else
	{
		// On test si le numéro de téléphone est correct
		if(!preg_match('`[0-9]{10}`',$tel))
		{
			$toutok = false;
			$error = $error.$translate->_('Erreur : Le numéro de téléphone est incorrect').'.<br/>';
		}
	}
	// On test si le numéro de Fax est correct s'il est remplis 
	if (!empty($_POST['fax']))
	{
		if(!preg_match('`[0-9]{10}`',$fax))
		{
			$toutok = false;
			$error = $error.$translate->_('Erreur : Le numéro de fax est incorrect').'.<br/>';
		}
	}

	if ($toutok) {
		// Tout est bon : On affiche les résultats et on enregistre les données dans la BDD
		$afficheform=false;
		echo '<h2>'.$translate->_('Récapitulatif des données').'</h2>';
		echo '<div class="info messageBox"><p>Données modifiées !<br/>';
		// Enregistrement dans la BDD avec test si le système à réussit à se connecter et à enregistrer les données
		$user->setNom($nom,$prenom);
		if(!empty($mdp)){
			$user->setPassword(md5($mdp));	
		}
		$user->setTel($tel);
		$user->setFax($fax);
		$clientDB = new clientDB();
		// On vérifie que tout c'est bien passé.
		// Si oui, on affiche un message de confirmation de modification
		if ($clientDB->save($user)) {
			echo '<div class="valide messageBox"><p>'.$translate->_('Vos modifications ont été enregistrées avec succès').'.</p></div>';
		//Fermeture de la connection
		} else {
			echo '<div class="error messageBox"><p>'.$translate->_('Aucune modification détéctée').' !</p></div>';
		}
		$dbh = null;
	} else {
		echo '<div class="error messageBox"><p>'.$error.'</p></div>';
	}

?>

	<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>index.php?page=user&action=infos"><?php echo $translate->_('Retour');?></a>
	</p><?php
}


  /////////////////
 // FORMULAIRE //
/////////////////


if($afficheform)
{
	// On cherche si le client est déjà connécté
	if($user->isAuthenticated)
	{
		// Initialisation des variables
		$nom=$user->getNom();
		$prenom=$user->getPrenom();
		//$mdp=$user->getPassword();
		$tel=$user->getTel();
		$fax=$user->getFax();
		
		echo '<div class="info messageBox"><p>'.$translate->_('Voici vos données, vous pouvez les modifier et cliquez sur <b>Enregistrer</b> pour sauvegarder la modification').'.</p></div>';
		
		// Création d'un formulaire utilisant la  méthode POST
		?>
		<div class="form">
		<form action="index.php?page=user&action=infos" method="POST">
		
		<?php // Formualire remplis par les données du client
		
		// Contenu du formulaire initialisé avec les données du client (utilisation d'un tableau pour la mise en page)  ?>
		<fieldset class="input">
		<ol>
			<li>
				<label for="email"><?php echo $translate->_('Email'),' :'; ?></label>
				<?php echo $email;?>
			</li>
			<li>
				<label for="nom"><?php echo $translate->_('Nom'),' :'; ?></label>
				<input type="text" class="indispensable" name="nom" size=30 maxlength=60 value="<?php echo $nom;?>"/><b class="reditalique">* </b>
			</li>
			<li>
				<label for="prenom"><?php echo $translate->_('Prénom'),' :'; ?></label>
				<input type="text" class="indispensable" name="prenom" size=30 maxlength=60 value="<?php echo $prenom;?>"/><b class="reditalique">* </b>
			</li>
			<li>
				<label for="mdp"><?php echo $translate->_('Mot de passe'),' :'; ?></label>
				<input type="password" class="indispensable" name="motdepasse" size=20 maxlength=20 value=""/><b class="reditalique">* </b>
			</li>
			<li>
				<label for="mdpc"><?php echo $translate->_('Retapez le mot de passe'),' :'; ?></label>
				<input type="password" class="indispensable" name="motdepasseconfirme" size=20 maxlength=20 value=""/><b class="reditalique">* </b>
			</li>
			<li>
				<label for="tel"><?php echo $translate->_('Téléphone'),' :'; ?></label>
				<input type="text" class="indispensable" name="tel" size=20 maxlength=20 value="<?php echo $tel;?>"/><b class="reditalique">* </b>
			</li>
			<li>
				<label for="fax"><?php echo $translate->_('Fax'),' :'; ?></label>
				<input type="text" name="fax" size=20 maxlength=20 value="<?php echo $fax;?>"/>
			</li>
			<li>
				<label class="reditalique"><?php echo '* ',$translate->_('Champs obligatoires'); ?></label>
			</li>
		</ol>
		</fieldset>
		<fieldset class="submit">
		<ol>
			<li>
			<input type="submit" name="envoyer" value="<?php echo $translate->_('Enregistrer'); ?>" class="btn_submit"/>
			</li>
		</ol>
		</fieldset>
		</form>
		</div><?php
		include dirname(__FILE__).'/menu_gestion_adresse.php';
	}
	else
	{
		// Le client n'est pas connecté, message d'erreur.
		echo '<div class="error messageBox"><p>'.$translate->_('Pour changer vos données personnelles, vous devez vous connecter, merci').'.</p></div>';
	}?>
	<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>"><?php echo $translate->_('Retour');?></a>
	</p>
	<?php
	
	
}
*/
///////// NEW - MODIF DONNEES CLIENT /////////.
if(isset($_POST['modifier'])) {
	
	include dirname(__FILE__).'/update_client.php';
	
}	

	include dirname(__FILE__).'/form_client.php';
	
 ?>
 
 
 
 
