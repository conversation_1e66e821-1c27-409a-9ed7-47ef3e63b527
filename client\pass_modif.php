<?php
// on a en paramètre un uid
$uid = $_GET["uid"];
$afficheform = true;
if($uid != null && $uid != "") {
	if(strlen($uid) == 32) {
		// on cherche un utilisateur qui correspond à cette uid
		$client = clientDB::getClientByUid($uid);
		if($client != null) {
			if(isset($_POST["envoyer"])) {
				// on traite
				if($_POST["motdepasse"] != "" &&  $_POST["motdepasseconfirme"] != "") {
					if($_POST["motdepasse"] == $_POST["motdepasseconfirme"]) {
						// on peut enregistrer le mot de passe
						$afficheform = false;
						$client->setPassword(MD5($_POST["motdepasse"]));
						clientDB::save($client);
						echo '<div class="info messageBox"><p>'.$translate->_('Félicitations, vous pouvez maintenant vous connecter').'.</p></div>';
					}
					else {
						echo '<div class="error messageBox"><p>'.$translate->_('Le mot de passe et sa répétition ne sont pas égaux !<br/>  Veuillez réctifier celà, merci').' !</p></div>';
					}
				}
				else {
					
					echo '<div class="error messageBox"><p>'.$translate->_('Le mot de passe ne peut être vide !<br/>  Veuillez réctifier celà, merci').' !</p></div>';
				}
			}
			if($afficheform) {
			// on affiche le formulaire pour que le client puisse modifié son mot de passe
			?>
			<div class="info messageBox"><p><?php echo $translate->_('Modification du mot de passe pour le client').' : ';
			echo $client->getEmail(); ?></p></div>
			<form action="index.php?page=user&action=forgotmodif&uid=<?php echo $uid; ?>" method="post" style="border: 2px solid #000000; width:360px; margin-left:300px;">
				<center>
				<table>
					<tr>
						<td><?php echo $translate->_('Mot de passe').' :';?></td>
						<td><input type="password" name="motdepasse" size=20 maxlength=20 value=""><td>
					</tr>
					<tr>
						<td><?php echo $translate->_('Retapez le mot de passe').' :';?></td>
						<td><input type="password" name="motdepasseconfirme" size=20 maxlength=20 value=""></td>
					</tr>
					<tr>
						<td align="center"><input type="reset" value="<?php echo ('Effacer');?>"></td>
						<td align="center"><input type="submit" name="envoyer" value="<?php echo ('Envoyer');?>"></td>
					</tr>
				</table>
			</center>
			</form>
	<?php
			}
		} 
		else {
			// cette uid ne correspond à aucun client
			echo '<div id="error messageBox"><p>'.$translate->_('Cette identifiant ne correspond pas un compte utilisateur existant').'.</p></div>';
		}
	}
	else {
		echo '<div id="error messageBox"><p>'.$translate->_('Cette identifiant ne correspond pas un compte utilisateur existant').'.</p></div>';
	}
}
?>