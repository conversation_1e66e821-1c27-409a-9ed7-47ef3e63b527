<?php
$afficheform = true;
	if(isset($_POST['envoyer'])){
		// on cherche l'utilisateur dans la base de données,
		// si on le trouve: 
		//  -  on genere un lien pour que l'utilisateur puisse changer son mot de passe,
		//	-  on lui envoie un mail contenant le lien
		// sinon on lui dit que l'adresse email ne correspond pas a un compte valide
		if (!empty($_POST['email'])) 
		{
			$email = $_POST["email"];
			if(verifierAdresseMail($email))
			{
				$client = clientDB::getClientByEmail($email);
				if($client != null) {
					// 		le client existe
					$afficheform = false; // on masque le formulaire
					// affichage d'un message pour informer le client
					echo '<div class="info messageBox"><p>Vous allez recevoir dans quelques instants un lien par e-mail vers une page sur laquelle vous pourrez créer facilement un nouveau mot de passe.</p>';
					echo '<br /><p>Si vous ne recevez rien d\'ici 15-20min, merci de prendre contact avec nous par e-mail : '; 
					echo '<a href="mailto:<EMAIL>?subject=Probleme re-initialisation mot de passe" ><EMAIL></a></p>';
					echo '</div>';
					// on genere un identifiant...
					$uid = md5(uniqid(rand(), true));
					// on enregistre cette identifiant dans une table que l'on associe a un utilisateur
					$client->setUid($uid);
					clientDB::save($client);
					// on envoie un email contenant le lien
					// --- demo -- affichage du lien
					//echo "<a href='".$basePath."index.php?page=user&action=forgotmodif&uid=".$uid."'>".$basePath."index.php?page=user&action=forgotmodif&uid=".$uid."</a>";
					
					require_once dirname(__FILE__).'/mail_password_oublie.php';
					
				}
				else {
					// 	le client n'existe pas
					echo '<div class="error messageBox"><p>'.$translate->_('Votre adresse email ne correspond pas à une adresse email connu').'</p></div>';
				}
			}
			else {
				// l'adresse email n'est pas valide
				echo '<div class="error messageBox"><p>'.$translate->_('Votre adresse e-mail').' : '.$email.' '.$translate->_('n\'est pas valide.<br/>  Veuillez recommencer').' !</p></div>';
			}
		}
		else {
			// l'adresse email est vide.
			echo '<div class="error messageBox"><p>'.$translate->_('Il faut mettre une adresse mail').'</p></div>';
		}
		
	}
if($afficheform) {
 ?>
 
 <center>
 	<h1><?php echo ('Vous avez oublié votre mot de passe').' ?';?></h1>
	<form action="<?php echo $basePath; ?>utilisateur/mot_De_Passe_Oublie" method="POST">
		<table cellpadding="6" cellspacing="6">
			<tr>
				<td><?php echo ('Votre E-mail').' :';?></td>
				<td><input class="inputnewstyle" type="text" name="email" size=30 maxlength=100 value=""></td>
				<td align="center"><input class="submitnewstyle" type="submit" name="envoyer" value="Ok"></td>
			</tr>
		</table>
	</form>
	<br/>
</center>
<div class="info messageBox">Si vous ne recevez aucun e-mail, merci de nous adresse votre demande par e-mail : <a href="mailto:<EMAIL>?subject=Mot de passe oublié" ><EMAIL></a> Nous procéderons à une ré-initialisation de votre mot de passe.</div>
<?php
}
 ?>