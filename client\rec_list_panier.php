<h1><?php echo $translate->_('Mes Paniers');?></h1>
<?php

	if (isset($_GET['action2']) && $_GET['action2'] == "suppr"){

	$id_panier = $_GET['id_panier'];

	$r1 = $database->query("DELETE FROM panier WHERE id = '$id_panier'") or die ("error r1");
	$r2 = $database->query("DELETE FROM ligne_panier WHERE panier_id = '$id_panier'") or die ("error r2");

	}
	
	$email = $user->getEmail();
	
	$r1 = $database->prepare("SELECT * FROM panier WHERE client_email = '$email'") or die ("requete r1 invalid");
		// il y a des commandes
		?>
		<table class="table-fill" style="width:95%;">
			<tr>
				<th><?php echo $translate->_('Numéro');?></th>
				<th><?php echo $translate->_('Date du panier');?></th>
				<th><?php echo $translate->_('Commentaire');?></th>
				<th></th>
				<th></th>
			</tr>
		<?php
			$r1->execute();
			while ($tab = $r1->fetch()) {
				?>
				<tr>
					<td class="text-center"><?php echo $tab['id']; ?></td>
		<?php
		$datepanier = explode("-",$tab['panier_date']);
		$date_panier = $datepanier[2]."/".$datepanier[1]."/".$datepanier[0];
		?>	
					<td class="text-center"><?php echo $date_panier; ?></td>
					<td><?php echo $tab['comment']; ?></td>
					<td class="text-center">
						<a href="<?php echo $basePath; ?>?page=panier&action=charger&id_panier=<?php echo $tab['id']; ?>">
							<img src="<?php echo $basePath; ?>public/images/zoom.png" title="<?php echo $translate->_('Charge le panier');?>"/>
						</a>
					</td>
					<td class="text-center">
						<a href="<?php echo $basePath; ?>?page=panier&action=mes_paniers&action2=suppr&id_panier=<?php echo $tab['id']; ?>">
							<img src="<?php echo $basePath; ?>public/images/cross.png" title="<?php echo $translate->_('Supprimer le panier');?>"/>
						</a>
					</td>
				</tr>
						<?php
	}
?>
		</table>
<?php

if($user->isAdmin){

?>

<h1><center><a href="<?php echo $basePath; ?>index.php?page=panier&action=panier_vercorps"><img src="<?php echo $basePath; ?>public/images/panier_hover.png" title="<?php echo $translate->_('panier');?>"/> Voir le panier Vercorps <img src="<?php echo $basePath; ?>public/images/panier.png" title="<?php echo $translate->_('panier');?>"/></a></center></h1>

<?php

}

?>

