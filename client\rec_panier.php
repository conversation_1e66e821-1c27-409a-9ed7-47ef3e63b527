<?php
//inclued

require_once dirname(__FILE__).'/../class/produit_class.php';

//definition des $_POST

$panier_date = date("Y-m-d"); 
$client_email = $user->getEmail();

$comment = $_GET['comment'];

//insertion dans la table panier

	$r1 = $database->query("
	INSERT INTO panier (
	panier_date,
	client_email,
	comment
	
	) 
	
	VALUES (
	'".$panier_date."',
	'".$client_email."',
	'".$comment."'
	
	)") or die ("error requete insert panier");
	
// recupere numero dernier enregistrement

$r2 = $database->prepare("SELECT id FROM panier ORDER BY id DESC LIMIT 1") or die ("error requete insert ligne_panier");

$r2->execute();
$panier_id2 = $r2->fetch();
$panier_id = $panier_id2['id'];

// boucle des insertion dans la table ligne_panier

$lignePs = $user->getPanier()->getLignePanier();

	foreach($lignePs as $k => $ligneP){
	
	$p = $ligneP->getProduit();
	$ligneP->getQte();
		
	$r3 = $database->query("
	INSERT INTO ligne_panier (
	panier_id,
	produit_reference,
	produit_genre,
	produit_groupe,
	produit_designation,
	produit_qte
	) 
	
	VALUES (
	'".$panier_id."',
	'".$p->getReference()."',
	'".$p->getGenreName()."',
	'".$p->getGroupeName()."',
	'".htmlentities($p->getDesignation(), ENT_QUOTES, "UTF-8")."',
	'".$ligneP->getQte()."'

	)") or die ("error requete insert ligne_panier");
	
}
echo "<div class='valide messageBox'>Panier enregistr&eacute;!<br />";
echo 'cliquer <a href="'.$basePath.'panier/mes_paniers" > ICI </a> pour consulter vos panier enregistr&eacute;s';
echo "</div>";

?>