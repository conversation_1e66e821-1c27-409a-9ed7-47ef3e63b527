<?php
//included

require_once dirname(__FILE__).'/../class/produit_class.php';

//supprimer un panier vercorps
if (isset($_GET['id_panier'])){

	$id_panier = $_GET['id_panier'];
	$delete = $database->query("DELETE FROM panier_vercorps WHERE panier_vercorps_id = '".$id_panier."'") or die ("requete delete invalid");

}
//ajouter un panier vercorps
if (isset($_GET['regl'])){

$panier_vercorps_date = date("Y-m-d"); 
$panier_vercorps_heure = date("H:m");
$panier_vercorps_regl = $_GET['regl']; 

// recupere numero dernier enregistrement

$r2 = $database->prepare("SELECT panier_vercorps_id FROM panier_vercorps ORDER BY panier_vercorps_id DESC LIMIT 1") or die ("error requete insert panier_vercorps");

$r2->execute();
$panier_id2 = $r2->fetch();
$panier_vercorps_id = $panier_id2['panier_vercorps_id']+1;

// boucle des insertion dans la table panier_vercorps

$lignePs = $user->getPanier()->getLignePanier();

	foreach($lignePs as $k => $ligneP){
	
	$p = $ligneP->getProduit();
	$ligneP->getQte();
		
	$r3 = $database->query("
	INSERT INTO panier_vercorps (
	panier_vercorps_id,
	panier_vercorps_date,
	panier_vercorps_heure,
	panier_vercorps_regl,
	produit_reference,
	produit_genre,
	produit_groupe,
	produit_designation,
	produit_qte,
	produit_prix
	) 
	
	VALUES (
	'".$panier_vercorps_id."',
	'".$panier_vercorps_date."',
	'".$panier_vercorps_heure."',
	'".$panier_vercorps_regl."',
	'".$p->getReference()."',
	'".$p->getGenreName()."',
	'".$p->getGroupeName()."',
	'".htmlentities($p->getDesignation(), ENT_QUOTES, "UTF-8")."',
	'".$ligneP->getQte()."',
	'".$p->getPrixHT()."'

	)") or die ("error requete insert panier_vercorps");
	
}
}
?>
<h1> Paniers Vercorps </h1>
<table class="liste" style="width:90%;" >
			<tr>
				<th><?php echo $translate->_('N&deg;'); ?></th>
				<th><?php echo $translate->_('Date'); ?></th>
				<th><?php echo $translate->_('Regl'); ?></th>
				<th><?php echo $translate->_('Total HT'); ?></th>
				<th></th>
				<th></th>
			</tr>
			
<?php		
$result1 = $database->prepare("
SELECT *,SUM(produit_qte*produit_prix) AS total_ht FROM panier_vercorps GROUP BY panier_vercorps_id
") or die ("requete r1 invalid");
$result1->execute();
while ($tab = $result1->fetch()) {

$tab_panier_vercorps_id = $tab['panier_vercorps_id'];

echo '<tr style="text-align:center; background-color: #CCCCCC;">';
echo '<td>'.$tab['panier_vercorps_id'].'</td>';
echo '<td>'.date('d/m/Y',strtotime($tab['panier_vercorps_date'])).'</td>';
if($tab['panier_vercorps_regl'] == "es"){
echo '<td>Esp&egrave;ce</td>';
}
if($tab['panier_vercorps_regl'] == "cb"){
echo '<td>Carte bancaire</td>';
}
if($tab['panier_vercorps_regl'] == "ch"){
echo '<td>Ch&egrave;que</td>';
}
if($tab['panier_vercorps_regl'] == "mi"){
echo '<td>Mixte</td>';
}
echo '<td>'.$tab['total_ht'].' &euro;</td>';
?>
					<td>
						<a href="<?php echo $basePath; ?>index.php?page=panier&action=panier_vercorps&id_panier=<?php echo $tab['panier_vercorps_id']; ?>" onclick="return(confirm('Etes-vous sur de vouloir supprimer ce panier ?'));">
							<img src="<?php echo $basePath; ?>public/images/cross.png" title="<?php echo $translate->_('Supprimer le panier');?>"/>
						</a>
					</td>
					<td>
						<a href="<?php echo $basePath; ?>client/impr_panier_vercorps.php?imprim=<?php echo $tab['panier_vercorps_id']; ?>" target="_blank">
							<img src="<?php echo $basePath; ?>public/images/gtk-print.png" title="<?php echo $translate->_('Imprimer le panier');?>"/>
						</a>
					</td>
				</tr>
<?php
	echo '<tr>';
		echo '<td colspan="5">';
			echo '<table class="liste" style="border:none; width:80%;" >';
			
$result2 = $database->prepare("
SELECT * FROM panier_vercorps WHERE panier_vercorps_id = $tab_panier_vercorps_id
") or die ("requete r2 invalid");
$result2->execute();
while ($tab2 = $result2->fetch()) {


				echo '<tr style="text-align:center;">';
					if (file_exists("public/images/produits/".$tab2['produit_reference'].".jpg")){
							// IMAGE
						echo "<td>";
						echo "<a href=\"".Zend_Registry::get('basePath')."public/images/produits/".$tab2['produit_reference'].".jpg\" rel=\"lytebox\" title=\"".$tab2['produit_reference']."\" >";
						echo "<img src=\"".Zend_Registry::get('basePath')."public/images/produits/".$tab2['produit_reference'].".jpg\" height=\"40\" width=\"60\" />";
						echo "</a>";
						echo "</td>";
						} else {
							echo '<td></td>';
						}
					echo '<td>'.$tab2['produit_reference'].'</td>';
					echo '<td>'.$tab2['produit_genre'].'</td>';
					echo '<td>'.$tab2['produit_groupe'].'</td>';
					echo '<td>'.$tab2['produit_designation'].'</td>';
					echo '<td>'.$tab2['produit_prix'].' &euro;</td>';
					echo '<td>'.$tab2['produit_qte'].'</td>';
					echo '<td>'.number_format(($tab2['produit_prix'] * $tab2['produit_qte']),2, '.', '').' &euro;</td>';
				echo '</tr>';	
}
			echo '</table>';
		echo '</td>';
	echo '</tr>';
}
?>	
		</table>