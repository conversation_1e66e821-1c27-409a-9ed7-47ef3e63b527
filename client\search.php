<?php
require_once '../init.php';
require_once(dirname(__FILE__).'/../class/produit_class.php');
require_once(dirname(__FILE__).'/../class/pagination_class.php');
require_once(dirname(__FILE__).'/../class/categorie_class.php');
		
		function stripAccents($str, $charset='utf-8')
		{
			$str = htmlentities($str, ENT_NOQUOTES, $charset);
			
			$str = preg_replace('#&([A-za-z])(?:acute|cedil|circ|grave|orn|ring|slash|th|tilde|uml);#', '\1', $str);
			$str = preg_replace('#&([A-za-z]{2})(?:lig);#', '\1', $str); // pour les ligatures e.g. '&oelig;'
			$str = preg_replace('#&[^;]+;-,#', '', $str); 
			$str = strtr($str, array('(' => ' ', ')' => ' '));
			
			return $str;
		}
		
		function stripAccents2($str, $charset='utf-8')
		{
		
			$str = preg_replace('#&([A-za-z])(?:acute|cedil|circ|grave|orn|ring|slash|th|tilde|uml);#', '\1', $str);
			$str = preg_replace('#&([A-za-z]{2})(?:lig);#', '\1', $str); // pour les ligatures e.g. '&oelig;'
			$str = preg_replace('#&[^;]+;-,#', '', $str); 
			$str = strtr($str, array('(' => ' ', ')' => ' '));
			
			return $str;
		}
		
if (isset($_GET['term'])){
    $return_arr = array();

    try {
        
		$search = strtoupper(stripAccents($_GET['term']));
		$search2 = explode(" ",$search);
		$search3 = "";
		
		foreach($search2 as $recherche){
			$search3 .= '%'.$recherche.'%';
		}
		
        $stmt = $database->prepare('SELECT * FROM produits WHERE CONCAT(descriptionproduit, genre, groupe, referenceproduit) LIKE :term ORDER BY genre DESC, groupe, descriptionproduit');
        $stmt->execute(array('term' => $search3));
        
        while($row = $stmt->fetch()) {
			$groupe = explode("|", $row['groupe']);
			$return_arr[] =  $row['genre'].' '.$groupe[0].stripAccents2($row['descriptionproduit']);
        }

    } catch(PDOException $e) {
        echo 'ERROR: ' . $e->getMessage();
    }

    echo json_encode($return_arr);
}

