<?php

// Fichier créé par <PERSON><PERSON><PERSON>

// Ce fichier PHP permet d'ajouter une adresse au client
require_once dirname(__FILE__).'../../class/adresse_class.php';

// Création de la page html virtuelle
echo '<h1>'.$translate->_('Carnet d\'adresse : suppression').'</h1><br/><br/>';


if($user->isAuthenticated) // On se trouve sur la page de traitement
{
	echo '<br/>';
	if (isset($_GET['id']))
	{
		$id = $_GET['id'];
		// Formulaire de suppression
		echo '<table cellpadding="6" cellspacing="6">';
		$adresseDB = new adresseDB;
		$adresseselect = $adresseDB->getAdresseById($id);
		// Si le client possède déjà une adresse de facturation : On affiche le formulaire pré-remplis
		if ($adresseselect instanceof adresse)
		{
			$Id = $adresseselect->getId();
			$RaisonSocial = $adresseselect->getRaisonSocial();
			$NomRue = $adresseselect->getNomRue();
			$cp = $adresseselect->getCodePostal();
			$ville = $adresseselect->getVille();
			$pays = $adresseselect->getPays();
			// Affichage du lien grâce à une fonction prédéfinie
			echo afficheAdresseEnLigne($Id, $RaisonSocial,$NomRue, $cp, $ville, $pays);
			echo '<br/><br/>';
			$adresseDB = new adresseDB();
			if ($adresseDB->deleteAdresseById($id))
			{
				echo '<div class="valide messageBox"><p>'.$translate->_('Suppression effectuée').'</p></div>';
			}
			else
			{
				echo '<div class="error messageBox"><p>'.$translate->_('Erreur dans la suppression...').'</p></div>';
			}
		}
		else
		{
			echo '<div class="error messageBox"><p>'.$translate->_('Erreur d\'ouverture de l\'adresse séléctionnée').'</p></div>';
		}
	}
	else
	{
		echo '<div class="error messageBox"><p>'.$translate->_('Erreur d\'indentifiant lors de l\'ouverture de l\'adresse').'.</p></div>';
	}
}


?>
<br/>
<p class="bouton_retour">
	<a href="<?php echo $basePath; ?>index.php?page=user&action=infos"><?php echo $translate->_('Retour');?></a>
</p>