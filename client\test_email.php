<?php

header('Content-type: text/html; charset=iso-8859-1');  

require_once '../init.php';

$email = $_GET['mail'];

$result = $database->prepare("SELECT COUNT(emailclient) AS cli_inscrit FROM clients WHERE emailclient = '$email'") or die ("requete r1 invalid");

$result->execute();

while ($tab = $result->fetch()) {

	if ($tab['cli_inscrit'] == "0"){
	
	} else {

	echo "<li><label><img src='".$basePath."public/images/attention.png' alt='attention'/></label> : E-mail d&eacute;j&agrave; pris !</li>";

	}


}