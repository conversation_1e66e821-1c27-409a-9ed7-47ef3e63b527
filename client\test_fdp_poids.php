<?php

header('Content-type: text/html; charset=iso-8859-1');  

require_once '../init.php';

$poids = $_GET['poids'];
$pays = $_GET['pays'];
$fdp = $_GET['fdp'];

if ($pays == "France"){
$result = $database->prepare("SELECT * FROM tarif_poste WHERE poids_poste >= '$poids' LIMIT 1") or die ("requete r1 invalid");

$result->execute();

	while ($tab = $result->fetch()) {
		
		$tarif = $tab['france_poste'];

		if ( $fdp > $tab['france_poste']){
			
			echo "<span style='color:blue;'>FRAIS DE PORT POSITIF : ".number_format($fdp-$tarif,2, '.', '')."&euro;</span>";
			
		} else {
			
			echo "<span style='color:red;'>FRAIS DE PORT NEGATIF : ".number_format($fdp-$tarif,2, '.', '')."&euro;</span>";
			
		}

	}
}