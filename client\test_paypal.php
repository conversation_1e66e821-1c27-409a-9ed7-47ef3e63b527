<?php

header('Content-type: text/html; charset=iso-8859-1');  

require_once '../init.php';

$numero = $_GET['numero'];

$result = $database->prepare("SELECT * FROM facture WHERE regl LIKE '%$numero%'") or die ("requete r1 invalid");

$result->execute();

while ($tab = $result->fetch()) {

	if ($tab['id'] == ""){
	
	} else {

	echo "<li><label><img src='".$basePath."public/images/attention.png' alt='attention'/></label> : Numero paypal d&eacute;j&agrave; utilis&eacute; sur ".$tab['id_facture']." !</li>";

	}


}