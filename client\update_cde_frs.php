<?php

header('Content-type: text/html; charset=iso-8859-1');  

require_once '../init.php';

$id_dde_frs = $_GET['id_dde_frs'];
$name_value = $_GET['name_value'];
$valeur = $_GET['valeur'];

if ($name_value == "date_dde_frs"){
	$valeur2 = explode("/",$valeur);
	$valeur = $valeur2[2].'-'.$valeur2[1].'-'.$valeur2[0];
}

$result2 = $database->prepare("
UPDATE demande_fournisseur
SET $name_value = '$valeur'
WHERE id_dde_frs = '$id_dde_frs'
") or die ("r2 invalid");
$result2->execute();
