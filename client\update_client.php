<?php
	function delSpace($str2){
		$str2 = preg_replace('#&[^;]+;-_.#', '', $str2); // supprime les caractères speciaux
		$str2 = preg_replace('# #', '', $str2); // supprime les espaces			
		return $str2;
	}
	function stripAccents($str, $charset='utf-8'){
		$str = htmlentities($str, ENT_NOQUOTES, $charset);		
		$str = preg_replace('#&([A-za-z])(?:acute|cedil|circ|grave|orn|ring|slash|th|tilde|uml);#', '\1', $str);
		$str = preg_replace('#&([A-za-z]{2})(?:lig);#', '\1', $str); // pour les ligatures e.g. '&oelig;'
		$str = preg_replace('#&[^;]+;#', '', $str); // supprime les autres caractères			
		return $str;
	}
	// Déclaration des variables :
	// Formulaire :
	$nom = strtoupper(stripAccents($_POST['nom']));
	$prenom = strtolower(stripAccents($_POST['prenom']));
	
	$password = $_POST['password'];
	$confirm_password = $_POST['confirm_password'];
	
	$tel = delSpace($_POST['tel']);
	$fax = delSpace($_POST['fax']);
	
	$af_raisonsocial = stripAccents($_POST['af_raisonsocial']);
	$af_nomue = strtolower(stripAccents($_POST['af_nomue']));
	$af_codepostal = delSpace($_POST['af_codepostal']);
	$af_ville = strtoupper(stripAccents($_POST['af_ville']));
	$af_pays = $_POST['af_pays'];
	
	$liv_raisonsocial = stripAccents($_POST['liv_raisonsocial']);
	$liv_nomue = strtolower(stripAccents($_POST['liv_nomrue']));
	$liv_codepostal = delSpace($_POST['liv_codepostal']);
	$liv_ville = strtoupper(stripAccents($_POST['liv_ville']));
	$liv_pays = $_POST['liv_pays'];
	
	$email_secondaire = strtolower($_POST['email_secondaire']);
	
	if (isset($_POST['vehiculeprefer'])){
		$vehiculeprefer = $_POST['vehiculeprefer'];
	} else {
		$vehiculeprefer = "";		
	}
	
	if (isset($_POST['bd_j']) && isset($_POST['bd_m']) && isset($_POST['bd_m'])){
		$bd_j = delSpace($_POST['bd_j']);
		$bd_m = delSpace($_POST['bd_m']);
		$bd_a = delSpace($_POST['bd_a']);
		$date_anni = $bd_a."-".$bd_m."-".$bd_j;
	} else {
		$date_anni = "";
	}
	
	$toutok = true;
	$error = "";
	
	/*----------- DEBUT DES TESTS -------------*/

	if (empty($_POST['nom']) || empty($_POST['prenom']) || empty($_POST['password']) || empty($_POST['tel'])){
		$toutok = false;
		echo '<div class="error messageBox"><p>Erreur : Il manque une donnée obligatoire !</p></div>';
	}
	/*------------ FIN DES TESTS ---------------*/
	if ($toutok) {
		
		echo '<div class="info messageBox"><p>Données modifiées !</p></div>';
		
		$user->setNom($nom);
		$user->setPrenom($prenom);
		$user->setPassword(md5($password));	
		$user->setTel($tel);
		$user->setFax($fax);
		
		$user->setEmail_second($email_secondaire);
		$user->setVehiculeprefer($vehiculeprefer);
		$user->setDate_anni($bd_a."-".$bd_m."-".$bd_j);
		
		$user->setRaisonsocial($liv_raisonsocial);
		$user->setNomrue($liv_nomue);
		$user->setCodepostal($liv_codepostal);
		$user->setVille($liv_ville);
		$user->setPays($liv_pays);
		
		$user->setAf_Raisonsocial($af_raisonsocial);
		$user->setAf_Nomrue($af_nomue);
		$user->setAf_Codepostal($af_codepostal);
		$user->setAf_Ville($af_ville);
		$user->setAf_Pays($af_pays);
				
		$clientDB = new clientDB();
		$clientDB->save2($user);
	}
?>	