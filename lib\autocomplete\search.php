<?php
require_once '../init2.php';
/*
if (isset($_GET['valuetosearch'])){
    $return_arr = array();

    try {
        
        $stmt = $database->prepare('SELECT descriptionproduit FROM produits WHERE descriptionproduit LIKE :valuetosearch');
        $stmt->execute(array('valuetosearch' => '%'.$_GET['valuetosearch'].'%'));
        
        while($row = $stmt->fetch()) {
            $return_arr[] =  $row['descriptionproduit'];
        }

    } catch(PDOException $e) {
        echo 'ERROR: ' . $e->getMessage();
    }

    echo json_encode($return_arr);
}
*/

$sql = connect_sql();
$q = strtolower($_GET["valuetosearch"]);
if (!$q) return;
mysql_query("SET NAMES utf8");
$sql2 = mysql_query("SELECT descriptionproduit FROM produits WHERE descriptionproduit LIKE '%$q%' ");
while($rs = mysql_fetch_array($sql2)) 
{
	$res = $rs['descriptionproduit'];
	echo stripslashes($res)."\n";
}